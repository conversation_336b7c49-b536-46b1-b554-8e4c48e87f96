package com.digiwin.escloud.aiocmdb.asset.model;

import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * AIOps采集数据目标表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@ApiModel(value = "AiopsCollectSink对象", description = "AIOps采集数据目标表")
public class AiopsCollectSink implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private Long id;

    private String modelCode;

    private String dataScene;

    private String sinkName;

    private LocalDateTime createDate;

    private LocalDateTime updateDate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getModelCode() {
        return modelCode;
    }

    public void setModelCode(String modelCode) {
        this.modelCode = modelCode;
    }

    public String getDataScene() {
        return dataScene;
    }

    public void setDataScene(String dataScene) {
        this.dataScene = dataScene;
    }

    public String getSinkName() {
        return sinkName;
    }

    public void setSinkName(String sinkName) {
        this.sinkName = sinkName;
    }

    public LocalDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

    public LocalDateTime getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(LocalDateTime updateDate) {
        this.updateDate = updateDate;
    }

    @Override
    public String toString() {
        return "AiopsCollectSink{" +
            "id = " + id +
            ", modelCode = " + modelCode +
            ", dataScene = " + dataScene +
            ", sinkName = " + sinkName +
            ", createDate = " + createDate +
            ", updateDate = " + updateDate +
        "}";
    }
}
