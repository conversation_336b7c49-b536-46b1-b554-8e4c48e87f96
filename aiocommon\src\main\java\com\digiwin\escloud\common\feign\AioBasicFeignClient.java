package com.digiwin.escloud.common.feign;

import com.digiwin.escloud.aiobasic.controller.dto.WorkingInfoRes;
import com.digiwin.escloud.aiobasic.log.model.LogOperateRecord;
import com.digiwin.escloud.aiobasic.operatelog.model.OperateLogSaveParam;
import com.digiwin.escloud.aiomail.Mail;
import com.digiwin.escloud.common.constant.AioConstant;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.model.VulnerabilityProjectDTO;
import com.digiwin.escloud.common.response.BaseResponse;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(value = "aiobasic", url = "${feign.aiobasic.url:}")
public interface AioBasicFeignClient {
    @RequestMapping(
            value = {"/api/calendar/{productCode}/working/info"},
            method = {RequestMethod.GET}
    )
    WorkingInfoRes checkIsWorkingDayForDispatchStaff(
            @RequestHeader(AioConstant.SID) Long sid,
            @PathVariable("productCode") String productCode,
            @RequestParam(value = "serviceRegion", required = false) String serviceRegion
    );

    @ApiOperation(value = "同步租户相关信息")
    @PostMapping(value = "/api/sync/tenant")
    ResponseBase syncTenant(@RequestParam(value = "serviceCode") String serviceCode);

    @ApiOperation(value = "同步用户相关信息")
    @PostMapping(value = "/api/sync/user")
    ResponseBase syncUser(@RequestParam(value = "userId") String userId);

    @ApiOperation(value = "获取edr主机列表")
    @GetMapping(value = "/edr/config/server/list")
    ResponseBase getServerList(
            @ApiParam(value = "运维商Id")
            @RequestHeader(AioConstant.SID) Long sid,
            @ApiParam(value = "页码")
            @RequestParam(value = "page", required = false, defaultValue = "1") int page,
            @ApiParam(value = "条数")
            @RequestParam(value = "size", required = false, defaultValue = "0") int size);

    @ApiOperation(value = "新增操作日志纪录")
    @PostMapping(value = "/log/operate")
    BaseResponse addLogOperate(
            @ApiParam("是否需要填充时间")
            @RequestParam(value = "needFillTime", required = false, defaultValue = "true") Boolean needFillTime,
            @ApiParam(value = "日志纪录", required = true)
            @RequestBody() LogOperateRecord logOperateRecord);

    @ApiOperation(value = "批量新增操作日志纪录")
    @PostMapping(value = "/log/operate/batch")
    BaseResponse batchAddLogOperate(
            @ApiParam("是否需要填充时间")
            @RequestParam(value = "needFillTime", required = false, defaultValue = "true") Boolean needFillTime,
            @ApiParam(value = "日志纪录列表", required = true)
            @RequestBody() List<LogOperateRecord> logOperateRecordList);

    @ApiOperation(value = "发起行业调查问卷的邮件")
    @GetMapping(value = "/basic/mail/industryPlan/sendMail")
    BaseResponse sendMail(@RequestParam("receiver")String receiver,
                                    @RequestParam("serviceCode")String serviceCode,
                                    @RequestParam("serviceCodeSize")Integer serviceCodeSize,
                                    @RequestParam("tableName")String tableName,
                          @RequestParam("address")String address,
                          @RequestParam("name")String name);

    @ApiOperation("服务运维平台查询漏扫看板")
    @PostMapping("/vulnerability/project/getProjectInfo")
    ResponseBase<VulnerabilityProjectDTO> getProjectInfo(@RequestBody List<Long> eidList);

    @PostMapping("/basic/mail/industryPlan/sendMailV2")
    BaseResponse sendMailV2(@RequestBody Mail mail);

    @PostMapping("/vulnerability/project/getProjectEidList")
    ResponseBase<List<Long>> getProjectEidList();

    @ApiOperation("留存記錄")
    @PostMapping("/operatelog/save")
    BaseResponse saveRecord(@RequestBody OperateLogSaveParam operateLogSaveParam);
}
