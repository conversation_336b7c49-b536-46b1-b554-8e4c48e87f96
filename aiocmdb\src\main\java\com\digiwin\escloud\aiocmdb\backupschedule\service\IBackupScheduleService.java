package com.digiwin.escloud.aiocmdb.backupschedule.service;

import com.alibaba.fastjson.JSONObject;
import com.digiwin.escloud.common.response.BaseResponse;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;

public interface IBackupScheduleService {
    List<Map<String, Object>> getAssetList(String modelCode,long eid,String deviceStatus,String filter,int pageNum,int pageSize,boolean auto, JSONObject jsonObject);
    long getAssetCount(String modelCode,long eid,String deviceStatus,String filter);
    BaseResponse addBackupschedule(String modelCode, long sid, String serviceCode,long eid,String deviceCatalog, String sourceDeviceName, JSONObject jsonObject);
    BaseResponse batchUpload(String nickName,String modelCode, String serviceCode, long eid, MultipartFile file) throws IOException;
    void deleteAssetAndRelateAsset(String modelCode, String deviceId, String serviceCode, long eid, String modelGroupCode, boolean deleteRelateAsset);
    BaseResponse batchDeleteAssetAndRelateAsset(List<Map<String,String>> list, long eid, String modelGroupCode, boolean deleteRelateAsset);

    BaseResponse getBackupScheduleSoftwareList();
}
