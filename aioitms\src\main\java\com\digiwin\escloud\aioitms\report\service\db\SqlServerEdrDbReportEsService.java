package com.digiwin.escloud.aioitms.report.service.db;

import com.digiwin.escloud.aioitms.es.service.EsService;
import com.digiwin.escloud.aioitms.report.annotation.EsDbServiceCode;
import com.digiwin.escloud.aioitms.report.model.db.SqlServerErpReport;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

@EsDbServiceCode("MSSQL_ERP") // 都要一樣才會配得到
@Service
public class SqlServerEdrDbReportEsService extends EsService<SqlServerErpReport> {
    @PostConstruct
    public void init() {
        indexCreate(SqlServerErpReport.class);
    }
}
