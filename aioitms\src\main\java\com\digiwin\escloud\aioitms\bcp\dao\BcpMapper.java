package com.digiwin.escloud.aioitms.bcp.dao;

import com.digiwin.escloud.aioitms.bcp.model.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface BcpMapper {
    List<BcpSurveyData> selectBcpSurveyData(@Param("version") String version);
    List<BcpSurveyResult> selectBcpSurveyResult(@Param("surveyId") String surveyId);
    List<BcpSurveyRecord> selectBcpSurveyRecord(BcpRequestParam param);
    Integer getAccountResetCount();
    Integer getNonResetCount();
    List<BcpCustomer> getCustomerList();
    BcpSurveyResult getLastResult(@Param("eid") String eid);
    Integer insertBcpSurveyRecord(BcpSurveyRecord bcpSurveyRecord);
    List<Map<String, Object>> getBcpSurveyScore(@Param("answerIdList") List<String> answerIdList);
    Integer insertBcpSurveyDataRecord(@Param("bcpSurveyDataRecordList") List<BcpSurveyDataRecord> bcpSurveyDataRecordList);
    List<Map<String, Object>> getBcpSurveyCategoryScore(@Param("answerIdList") List<String> answerIdList);
    Integer insertBcpSurveyResultRecord(@Param("bcpSurveyResultRecordList") List<BcpSurveyResultRecord> bcpSurveyResultRecordList);
    int updateBcpSurveyRecord(BcpSurveyRecord param);
    List<String> serviceCodeCheck(@Param("email") String email);
}
