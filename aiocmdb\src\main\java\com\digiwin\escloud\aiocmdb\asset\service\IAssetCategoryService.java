package com.digiwin.escloud.aiocmdb.asset.service;

import com.digiwin.escloud.aiocmdb.asset.model.AssetCategory;
import com.digiwin.escloud.aiocmdb.asset.model.AssetCategoryClassification;
import com.digiwin.escloud.aiocmdb.asset.model.AssetCategoryCodingRule;
import com.digiwin.escloud.aiocmdb.asset.model.CmdbModelDataFieldRelationMapping;
import com.digiwin.escloud.common.model.ResponseBase;

import java.util.List;

/**
 * <p>
 * 资产类别分类表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
public interface IAssetCategoryService  {

    // AssetCategoryClassification 相关方法
    ResponseBase saveAssetCategoryClassification(AssetCategoryClassification classification);

    ResponseBase updateAssetCategoryClassification(AssetCategoryClassification classification);

    ResponseBase deleteAssetCategoryClassification(Long id);

    ResponseBase getAssetCategoryClassificationList();

    // AssetCategory 相关方法
    ResponseBase saveAssetCategory(AssetCategory category);

    ResponseBase updateAssetCategory(AssetCategory category);

    ResponseBase updateAssetCategoryStatus(Long id, String status);

    ResponseBase deleteAssetCategory(Long id);

    ResponseBase getAssetCategoryList(Long classificationId);

    // CmdbModelDataFieldRelationMapping 相关方法
    ResponseBase saveCmdbModelDataFieldRelationMapping(List<CmdbModelDataFieldRelationMapping> mappingList);

    ResponseBase getCmdbModelDataFieldRelationMappingList(String targetModelCode);

    // AssetCategoryCodingRule 相关方法
    ResponseBase getAllAssetCategoryCodingRule();

}
