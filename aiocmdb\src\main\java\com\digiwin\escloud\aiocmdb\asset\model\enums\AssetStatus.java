package com.digiwin.escloud.aiocmdb.asset.model.enums;

/**
 * 资产状态枚举
 */
public enum AssetStatus {
    
    ENABLED("启用", "1"),
    DISABLED("停用", "0");
    
    private final String description;
    private final String code;
    
    AssetStatus(String description, String code) {
        this.description = description;
        this.code = code;
    }
    
    public String getDescription() {
        return description;
    }
    
    public String getCode() {
        return code;
    }
    
    /**
     * 根据代码获取枚举
     */
    public static AssetStatus getByCode(String code) {
        for (AssetStatus status : AssetStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
