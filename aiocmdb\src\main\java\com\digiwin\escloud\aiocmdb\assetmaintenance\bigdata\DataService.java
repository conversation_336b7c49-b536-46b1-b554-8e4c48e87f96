package com.digiwin.escloud.aiocmdb.assetmaintenance.bigdata;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.digiwin.escloud.aiocmdb.constant.Constant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * @Date 2021/10/8 17:14
 * @Created yanggld
 * @Description
 */
@Service
public class DataService {

    @Autowired
    private BigDataUtil bigDataUtil;

    public final long dayOfMillis1 = 86400000;
    public final long dayOfMillis7 = 604800000;

    /**
     * model中json数据查询
     * {
     *     "DataContent":{
     *         "timestamp":"2021-07-05T10:30:56.2332051+08:00"
     *     }
     * }
     * @return
     */
    public List<Map<String, Object>> queryModelV2(String modelCode,Map<String,Object> params){
        Map<String,Object> newParams = convertParams(params);
        if (newParams.isEmpty()) {
            return null;
        }
        return queryModelDataList(modelCode, newParams);
    }

    /**
     * model中json数据删除
     * 入参：
     * {
     *     "DataContent.timestamp":"2021-07-05T10:30:56.2332051+08:00"
     * }
     * @return
     */
    public List<String> deleteModel(String modelCode,Map<String,Object> params){
        List<String> keyList = new ArrayList<>();
        List<Map<String, Object>> dataList = queryModelDataList(modelCode, params);
        for (Map<String, Object> map : dataList) {
            Object key = map.get("key");
            if (key != null) {
                keyList.add(key.toString());
            }
        }
        bigDataUtil.deleteByRowKeyList(modelCode,keyList);
        return keyList;
    }

    /**
     * model中json数据删除
     * {
     *     "DataContent":{
     *         "timestamp":"2021-07-05T10:30:56.2332051+08:00"
     *     }
     * }
     * @return
     */
    public List<String> deleteModelV2(String modelCode,Map<String,Object> params){
        Map<String, Object> newParams = convertParams(params);
        if (newParams.isEmpty()) {
            return null;
        }
        List<String> keyList = new ArrayList<>();
        List<Map<String, Object>> dataList = queryModelDataList(modelCode, newParams);
        for (Map<String, Object> map : dataList) {
            Object key = map.get("key");
            if (key != null) {
                keyList.add(key.toString());
            }
        }
        bigDataUtil.deleteByRowKeyList(modelCode,keyList);
        return keyList;
    }

    public List<Map<String, Object>> queryModelDataList(String tableName,Map<String,Object> params){
        String sqlCondition = "";
        int index = 0;
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            if (index != 0) {
                sqlCondition +=" and ";
            }
            sqlCondition += " get_json_object(model,'$."+key+"') = '"+value+"' ";
            index++;
        }
        String sql = "select * from "+tableName+ " where "+sqlCondition;
        List<Map<String, Object>> dataList = bigDataUtil.query(sql);
        return dataList;
    }

    public Map<String,Object> convertParams(Map<String,Object> params){
        Map<String,Object> newParams = new HashMap<>();
        if (params != null) {
            Set<Map.Entry<String, Object>> entries = params.entrySet();
            for (Map.Entry<String, Object> entry : entries) {
                String key = entry.getKey();
                Object value = entry.getValue();
                if (value != null) {
                    Map<String,Object> map = (Map)value;
                    Set<Map.Entry<String, Object>> entrySet = map.entrySet();
                    for (Map.Entry<String, Object> objectEntry : entrySet) {
                        String key1 = objectEntry.getKey();
                        Object value1 = objectEntry.getValue();
                        newParams.put(key+"."+key1,value1);
                    }
                }
            }
        }
        return newParams;
    }

    public int generateAssetTableAsync(String modelCode){
        StringBuilder sb = new StringBuilder();
        sb.append("create table if not exists ");
        sb.append(Constant.resourcestorage_start + modelCode);
        sb.append(" (rowKey varchar primary key desc,eid varchar,deviceId varchar,model varchar,relation varchar) column_encoded_bytes=0");

        return bigDataUtil.phoenixUpsert(sb.toString());
    }
    public int generateMaintenanceTableAsync(String modelCode){
        StringBuilder sb = new StringBuilder();
        sb.append("create table if not exists ");
        sb.append(Constant.resourcestorage_maintenance_start + modelCode);
        sb.append(" (rowKey varchar primary key desc,eid varchar,deviceId varchar,model varchar,relation varchar,maintenanceCode VARCHAR,maintenanceTime VARCHAR,maintenanceUser VARCHAR,maintenanceYearMonth VARCHAR) column_encoded_bytes=0");

        return bigDataUtil.phoenixUpsert(sb.toString());
    }

    public List<Map<String, Object>> getMaintenanceList(String modelCode,Long eid,String deviceId,List<String> deviceIds, String queryField,int pageNum,int pageSize) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ROWKEY, DEVICEID as \"deviceId\",  EID as \"eid\", MAINTENANCECODE as \"maintenanceCode\", MAINTENANCETIME as \"maintenanceTime\", MAINTENANCEUSER as \"maintenanceUser\", MAINTENANCEYEARMONTH as \"maintenanceYearMonth\", RELATION as \"relation\"");
        if(!StringUtils.isEmpty(queryField)){
            sb.append(", ").append(queryField);
        }
        sb.append(" from " + Constant.resourcestorage_maintenance_start + modelCode);
        sb.append(" where 1=1 ");
        if(eid != 0) {
            sb.append(" and EID = '" + eid).append("' ");
        }
        if(!StringUtils.isEmpty(deviceId)){
            sb.append(" and DEVICEID = '").append(deviceId).append("' ");
        }
        if(!CollectionUtils.isEmpty(deviceIds)){
            StringBuffer didSb = new StringBuffer();
            deviceIds.stream().forEach(k->didSb.append("'").append(k).append("',"));
            if(!StringUtils.isEmpty(didSb.toString())){
                sb.append(" and DEVICEID in ( ").append(didSb.toString().substring(0,didSb.toString().length()-1)).append(") ");
            }
        }
        sb.append(" order by MAINTENANCEYEARMONTH DESC");
        if(pageNum!=0 && pageSize!=0) {//都为0时，查所有
            sb.append(" limit ").append(pageSize).append(" OFFSET ").append((pageNum-1)* pageSize);
        }

        List<Map<String, Object>> dataList = bigDataUtil.phoenixQuery(sb.toString());
        return dataList;
    }
    public List<Map<String, Object>> getRelateMaintenanceList(String modelCode,Long eid,String deviceId,List<String> deviceIds, String queryField,String maintenanceYearMonth, int pageNum,int pageSize) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ROWKEY, DEVICEID as \"deviceId\",  EID as \"eid\", MAINTENANCECODE as \"maintenanceCode\", MAINTENANCETIME as \"maintenanceTime\", MAINTENANCEUSER as \"maintenanceUser\", MAINTENANCEYEARMONTH as \"maintenanceYearMonth\" ");
        if(!StringUtils.isEmpty(queryField)){
            sb.append(", ").append(queryField);
        }
        sb.append(" from " + Constant.resourcestorage_maintenance_start + modelCode);
        sb.append(" where 1=1 ");
        if(eid != 0) {
            sb.append(" and EID = '" + eid).append("' ");
        }
        if(!StringUtils.isEmpty(deviceId)){
            sb.append(" and DEVICEID = '").append(deviceId).append("' ");
        }
        if(!CollectionUtils.isEmpty(deviceIds)){
            StringBuffer didSb = new StringBuffer();
            deviceIds.stream().forEach(k->didSb.append("'").append(k).append("',"));
            if(!StringUtils.isEmpty(didSb.toString())){
                sb.append(" and DEVICEID in ( ").append(didSb.toString().substring(0,didSb.toString().length()-1)).append(") ");
            }
        }
        if(!StringUtils.isEmpty(maintenanceYearMonth)){
            sb.append(" and maintenanceYearMonth = '").append(maintenanceYearMonth).append("' ");
        }
        sb.append(" order by MAINTENANCEYEARMONTH DESC");
        if(pageNum!=0 && pageSize!=0) {//都为0时，查所有
            sb.append(" limit ").append(pageSize).append(" OFFSET ").append((pageNum-1)* pageSize);
        }

        List<Map<String, Object>> dataList = bigDataUtil.phoenixQuery(sb.toString());
        return dataList;
    }
    public List<Map<String, Object>> getMaintenanceReportList(String modelCode,Long eid,String deviceId,List<String> deviceIds, String queryField,String yearMonthStart,String yearMonth) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ROWKEY, DEVICEID as \"deviceId\",  EID as \"eid\", MAINTENANCECODE as \"maintenanceCode\", MAINTENANCETIME as \"maintenanceTime\", MAINTENANCEUSER as \"maintenanceUser\", MAINTENANCEYEARMONTH as \"maintenanceYearMonth\" ");
        if(!StringUtils.isEmpty(queryField)){
            sb.append(", ").append(queryField);
        }
        sb.append(" from " + Constant.resourcestorage_maintenance_start + modelCode);
        sb.append(" where 1=1 ");
        if(eid != 0) {
            sb.append(" and EID = '" + eid).append("' ");
        }
        if(!StringUtils.isEmpty(deviceId)){
            sb.append(" and DEVICEID = '").append(deviceId).append("' ");
        }
        if(!CollectionUtils.isEmpty(deviceIds)){
            StringBuffer didSb = new StringBuffer();
            deviceIds.stream().forEach(k->didSb.append("'").append(k).append("',"));
            if(!StringUtils.isEmpty(didSb.toString())){
                sb.append(" and DEVICEID in ( ").append(didSb.toString().substring(0,didSb.toString().length()-1)).append(") ");
            }
        }

        if(!StringUtils.isEmpty(yearMonthStart) && !StringUtils.isEmpty(yearMonth)) {
            sb.append(" and maintenanceYearMonth >= '").append(yearMonthStart).append("' and maintenanceYearMonth <='").append(yearMonth).append("' ");
        }
        sb.append(" order by MAINTENANCEYEARMONTH DESC");
        List<Map<String, Object>> dataList = bigDataUtil.phoenixQuery(sb.toString());
        return dataList;
    }

    public Map<String, Object> getMaintenanceDetail(String modelCode,long sid, long eid,String maintenanceCode,String maintenanceYearMonth,String deviceId,String sort) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ROWKEY, EID as \"eid\", DEVICEID as \"deviceId\", get_json_object(model,'$.BasicInfo.deviceName') as \"deviceName\",MAINTENANCECODE as \"maintenanceCode\", MAINTENANCETIME as \"maintenanceTime\", MAINTENANCEUSER as \"maintenanceUser\", MAINTENANCEYEARMONTH as \"maintenanceYearMonth\", MODEL , RELATION as \"relation\"");
        sb.append(" from " + Constant.resourcestorage_maintenance_start + modelCode);
        sb.append(" where 1=1 ");
        if(eid != 0) {
            sb.append(" and EID = '" + eid).append("' ");
        }
        if(!StringUtils.isEmpty(maintenanceCode)){
            sb.append(" and MAINTENANCECODE = '").append(maintenanceCode).append("' ");
        }
        if(!StringUtils.isEmpty(maintenanceYearMonth)){
            sb.append(" and MAINTENANCEYEARMONTH = '").append(maintenanceYearMonth).append("' ");
        }
        if(!StringUtils.isEmpty(deviceId)){
            sb.append(" and DEVICEID = '").append(deviceId).append("' ");
        }
        if(!StringUtils.isEmpty(sort)){
            sb.append(" order by " + sort );
        }
        List<Map<String, Object>> dataList = bigDataUtil.phoenixQuery(sb.toString());
        if(!CollectionUtils.isEmpty(dataList)){
            return dataList.get(0);
        }
        return null;
    }

    public Map<String, Object> getLastMaintenanceByDeviceId(String modelCode, String eid, String deviceId, String maintenanceYearMonth) {
        String sqlFormat = "select ROWKEY, EID as \"eid\", DEVICEID as \"deviceId\", get_json_object(model,'$.BasicInfo.deviceName') as \"deviceName\",MAINTENANCECODE as \"maintenanceCode\", MAINTENANCETIME as \"maintenanceTime\", MAINTENANCEUSER as \"maintenanceUser\", MAINTENANCEYEARMONTH as \"maintenanceYearMonth\", MODEL , RELATION as \"relation\"" +
                " from " + Constant.resourcestorage_maintenance_start + modelCode +
                " where EID = '%s'\n" +
                " and DEVICEID = '%s' \n" +
                " and MAINTENANCEYEARMONTH <> '%s' \n" +
                " order by MAINTENANCETIME desc \n" +
                " limit 1";

        String sql = String.format(sqlFormat, eid, deviceId, maintenanceYearMonth);
        List<Map<String, Object>> dataList = bigDataUtil.phoenixQuery(sql);
        if(!CollectionUtils.isEmpty(dataList)){
            return dataList.get(0);
        }
        return null;
    }

    public Integer deleteMaintenanceByDeviceId(String modelCode, long eid, String deviceId, String maintenanceYearMonth){
        StringBuilder sb = new StringBuilder();
        sb.append("delete from ");
        sb.append(Constant.resourcestorage_maintenance_start + modelCode);
        sb.append(" where 1=1 ");
        if(!StringUtils.isEmpty(deviceId)){
            sb.append(" and deviceId = '").append(deviceId).append("'");
        }
        if(!StringUtils.isEmpty(maintenanceYearMonth)){
            sb.append(" and maintenanceYearMonth = '").append(maintenanceYearMonth).append("'");
        }
        if(eid != 0){
            sb.append(" and EID = '").append(eid).append("'");
        }

        return bigDataUtil.phoenixDelete(sb.toString());

    }

    public Integer deleteMaintenance(String modelCode, long eid, String maintenanceCode){
        StringBuilder sb = new StringBuilder();
        sb.append("delete from ");
        sb.append(Constant.resourcestorage_maintenance_start + modelCode);
        sb.append(" where 1=1 ");
        if(!StringUtils.isEmpty(maintenanceCode)){
            sb.append(" and MAINTENANCECODE = '").append(maintenanceCode).append("'");
        }
        if(eid != 0){
            sb.append(" and EID = '").append(eid).append("'");
        }

        return bigDataUtil.phoenixDelete(sb.toString());

    }

    public Integer insertMaintenance(
            String modelCode, String rowKey, long eid, String deviceId, String maintenanceCode, String maintenanceYearMonth,
            String maintenanceUser, String maintenanceTime,
            String modelJS, String relationJS
    ) {
        StringBuilder sb = new StringBuilder();
        sb.append("upsert into ");
        sb.append(Constant.resourcestorage_maintenance_start + modelCode);
        sb.append(" ( ROWKEY,EID, DEVICEID,maintenanceCode,maintenanceYearMonth,maintenanceUser,maintenanceTime,MODEL,RELATION )");
        sb.append(" values ");
        sb.append(" ('").append(rowKey).append("','").append(eid).append("','").append(deviceId).append("','").append(maintenanceCode).append("','").append(maintenanceYearMonth).append("','").append(maintenanceUser).append("','").append(maintenanceTime).append("','").append(StringUtils.isEmpty(modelJS) ? "" : modelJS.replaceAll("\\\\", "\\\\\\\\")).append("','").append(StringUtils.isEmpty(relationJS) ? "" : relationJS.replaceAll("\\\\", "\\\\\\\\")).append("')");

        return bigDataUtil.phoenixUpsert(sb.toString());
    }
    public Integer upsertMaintenance(String modelCode,String rowKey,long eid, String deviceId, String maintenanceCode , String maintenanceYearMonth, String maintenanceUser,String  maintenanceTime,String modelJS,String relationJS){
        StringBuilder sb = new StringBuilder();
        sb.append("upsert into ");
        sb.append(Constant.resourcestorage_maintenance_start + modelCode);
        sb.append(" ( ROWKEY,EID, DEVICEID,maintenanceCode,maintenanceYearMonth,maintenanceUser,maintenanceTime,MODEL");
        if(!StringUtils.isEmpty(relationJS)){
            sb.append(" ,RELATION ) ");
        }
        sb.append(" ) ");
        sb.append(" values ");
        sb.append(" ('").append(rowKey).append("','").append(eid).append("','").append(deviceId).append("','").append(maintenanceCode).append("','").append(maintenanceYearMonth).append("','").append(maintenanceUser).append("','").append(maintenanceTime).append("','").append(StringUtils.isEmpty(modelJS) ? "" : modelJS.replaceAll("\\\\","\\\\\\\\"));
        if(!StringUtils.isEmpty(relationJS)){
            sb.append("','").append(StringUtils.isEmpty(relationJS) ? "" : relationJS.replaceAll("\\\\","\\\\\\\\"));
        }
        sb.append("')");
        return bigDataUtil.phoenixUpsert(sb.toString());
    }

    public Integer upsertMaintenanceRelation(String modelCode, String rowKey, long eid, String deviceId, String maintenanceCode , String maintenanceYearMonth, String maintenanceUser, String  maintenanceTime, String relation){
        StringBuilder sb = new StringBuilder();
        sb.append("upsert into ");
        sb.append(Constant.resourcestorage_maintenance_start + modelCode);
        sb.append(" ( ROWKEY,EID, DEVICEID,maintenanceCode,maintenanceYearMonth,maintenanceUser,maintenanceTime,relation ) ");
        sb.append(" values ");
        sb.append(" ('").append(rowKey).append("','").append(eid).append("','").append(deviceId).append("','").append(maintenanceCode).append("','").append(maintenanceYearMonth).append("','").append(maintenanceUser).append("','").append(maintenanceTime).append("','").append(StringUtils.isEmpty(relation) ? "" : relation.replaceAll("\\\\","\\\\\\\\")).append("')");

        return bigDataUtil.phoenixUpsert(sb.toString());
    }
    public long getMaintenanceCount(String modelCode,long eid,String deviceStatus,String deviceId,String deviceName,String filter) {
        StringBuilder sb = new StringBuilder();
        sb.append("select count(*) count ");
        sb.append("from " + Constant.resourcestorage_maintenance_start + modelCode);
        sb.append(" where 1=1 ");
        if(eid != 0) {
            sb.append(" and EID = '" + eid).append("'");
        }
        if(!StringUtils.isEmpty(deviceStatus)){
            sb.append("and DEVICESTATUS = '").append(deviceStatus).append("'");
        }
        if(!StringUtils.isEmpty(deviceId)){
            sb.append("and DEVICEID = '").append(deviceId).append("'");
        }
        if (!StringUtils.isEmpty(deviceName)) {
            sb.append("and DEVICENAME = ' ").append(deviceName).append("'");
        }
        if (!StringUtils.isEmpty(filter)) {
            sb.append("and DEVICENAME like '%").append(deviceName).append("%'");
        }
        List<Map<String, Object>> dataList = bigDataUtil.phoenixQuery(sb.toString());
        if(!CollectionUtils.isEmpty(dataList)){
            return Long.valueOf(dataList.get(0).get("COUNT").toString());
        }
        return 0;
    }
    public List<Map<String, Object>> getAssetList(String modelCode,long eid,String deviceStatus,String needMaintenance,String deviceId, String filter, String deviceIdFilter,String deviceNameFilter,String ownerFilter,String queryField, String whereStr, String sortStr, int pageNum,int pageSize) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ROWKEY, EID as \"eid\" , get_json_object(model,'$.BasicInfo.assetFrom')  as \"assetFrom\" ");
        if(!StringUtils.isEmpty(queryField)){
            sb.append(", ").append(queryField);
        }
        if(!StringUtils.isEmpty(deviceStatus)){
            sb.append(" , get_json_object(model,'$.BasicInfo.deviceStatus')  as \"deviceStatus\"");
        }
        if(!StringUtils.isEmpty(needMaintenance)){
            sb.append(" , get_json_object(model,'$.BasicInfo.needMaintenance')  as \"needMaintenance\"");
        }
        if(!StringUtils.isEmpty(deviceIdFilter)){
            sb.append(" , get_json_object(model,'$.BasicInfo.deviceId')  as \"deviceId\"");
        }
        if(!StringUtils.isEmpty(deviceNameFilter)){
            sb.append(" , get_json_object(model,'$.BasicInfo.deviceName')  as \"deviceName\"");
        }
        if(!StringUtils.isEmpty(ownerFilter)){
            sb.append(" , get_json_object(model,'$.BasicInfo.owner')  as \"owner\"");
        }
        sb.append(" , get_json_object(model,'$.BasicInfo.snmpServiceIP')  as \"snmpServiceIP\"");
        sb.append(" , get_json_object(model,'$.BasicInfo.computerPlacement')  as \"computerPlacement\"");
        sb.append(" from " + Constant.resourcestorage_start + modelCode);
        sb.append(" where 1=1 ");
        if(eid != 0) {
            sb.append(" and EID = '" + eid).append("'");
        }
        if(!StringUtils.isEmpty(deviceStatus)){
            sb.append(" and get_json_object(model,'$.BasicInfo.deviceStatus') = '").append(deviceStatus).append("'");
        }
        if(!StringUtils.isEmpty(needMaintenance)){
            if("true".equals(needMaintenance)){
                sb.append(" and get_json_object(model,'$.BasicInfo.needMaintenance') = '").append(needMaintenance).append("'");
            }else if("false".equals(needMaintenance)){
                sb.append(" and ( get_json_object(model,'$.BasicInfo.needMaintenance') != 'true' or get_json_object(model,'$.BasicInfo.needMaintenance') is null )");
            }
        }
        if (!StringUtils.isEmpty(filter)) {
            sb.append(" and get_json_object(model,'$.BasicInfo.deviceName') like '%").append(filter).append("%'");
        }
        if(!StringUtils.isEmpty(deviceId)){
            sb.append(" and get_json_object(model,'$.BasicInfo.deviceId')  = '").append(deviceId).append("'");
        }
        if(!StringUtils.isEmpty(deviceIdFilter)){
            sb.append(" and get_json_object(model,'$.BasicInfo.deviceId') like '%").append(deviceIdFilter).append("%'");
        }
        if(!StringUtils.isEmpty(deviceNameFilter)){
            sb.append(" and get_json_object(model,'$.BasicInfo.deviceName') like '%").append(deviceNameFilter).append("%'");
        }
        if(!StringUtils.isEmpty(ownerFilter)){
            sb.append(" and get_json_object(model,'$.BasicInfo.owner') like '%").append(ownerFilter).append("%'");
        }
        if (!StringUtils.isEmpty(whereStr)) {
            sb.append( whereStr );
        }
        if (!StringUtils.isEmpty(sortStr)) {
            sb.append(" order by ").append(sortStr);
        }
        if(pageNum!=0 && pageSize!=0) {//都为0时，查所有
            sb.append(" limit ").append(pageSize).append(" OFFSET ").append((pageNum-1)* pageSize);
        }

        List<Map<String, Object>> dataList = bigDataUtil.phoenixQuery(sb.toString());
        return dataList;
    }


    public Map<String, Object> getAssetDetailByRowKey(String modelCode,String rowKey) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ROWKEY,MODEL");

        sb.append(" from " + Constant.resourcestorage_start + modelCode);
        sb.append(" where rowKey = '" + rowKey).append("' ");

        List<Map<String, Object>> dataList = bigDataUtil.phoenixQuery(sb.toString());
        if(!CollectionUtils.isEmpty(dataList)){
            return dataList.get(0);
        }
        return null;
    }

    public Map<String, Object> getAssetDetail(String modelCode,long sid, long eid,String deviceId,String queryField) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ROWKEY,EID as \"eid\", get_json_object(model,'$.BasicInfo.assetFrom')  as \"assetFrom\" ,MODEL,RELATION as \"relation\" ");
        if(!StringUtils.isEmpty(queryField)){
            sb.append(",").append(queryField);
        }
        sb.append(" from " + Constant.resourcestorage_start + modelCode);
        sb.append(" where 1=1 ");
        if(eid != 0) {
            sb.append(" and EID = '" + eid).append("' ");
        }

        if(!StringUtils.isEmpty(deviceId)){
            sb.append("and DEVICEID = '").append(deviceId).append("' ");
        }

        List<Map<String, Object>> dataList = bigDataUtil.phoenixQuery(sb.toString());
        if(!CollectionUtils.isEmpty(dataList)){
            return dataList.get(0);
        }
        return null;
    }

    public Integer upsertAsset(String modelCode, String rowKey, long eid, String deviceId, String modelStr){
        StringBuilder sb = new StringBuilder();
        sb.append("upsert into ");
        sb.append(Constant.resourcestorage_start + modelCode);
        sb.append(" ( ROWKEY,DEVICEID,EID,MODEL ) ");
        sb.append(" values ");
        sb.append(" ('").append(rowKey).append("','").append(deviceId).append("','").append(eid).append("','").append(StringUtils.isEmpty(modelStr) ? "" : modelStr.replaceAll("\\\\","\\\\\\\\")).append("')");

        return bigDataUtil.phoenixUpsert(sb.toString());
    }

    public Integer upsertMtAsset(String modelCode, String rowKey, String modelStr) {
        StringBuilder sb = new StringBuilder();
        sb.append("upsert into ");
        sb.append(Constant.resourcestorage_maintenance_start + modelCode);
        sb.append(" ( ROWKEY,MODEL) ");
        sb.append(" values ");
        sb.append(" ('").append(rowKey).append("','").append(StringUtils.isEmpty(modelStr) ? "" : modelStr.replaceAll("\\\\", "\\\\\\\\")).append("')");

        return bigDataUtil.phoenixUpsert(sb.toString());
    }

    public Integer upsertAssetRelation(String modelCode, String rowKey, long eid, String deviceId, String relation){
        StringBuilder sb = new StringBuilder();
        sb.append("upsert into ");
        sb.append(Constant.resourcestorage_start + modelCode);
        sb.append(" ( ROWKEY,DEVICEID,EID,RELATION ) ");
        sb.append(" values ");
        sb.append(" ('").append(rowKey).append("','").append(deviceId).append("','").append(eid).append("','").append(StringUtils.isEmpty(relation) ? "" : relation.replaceAll("\\\\","\\\\\\\\")).append("')");

        return bigDataUtil.phoenixUpsert(sb.toString());
    }
    public Integer deleteAsset(String modelCode, String deviceId, long eid){
        StringBuilder sb = new StringBuilder();
        sb.append("delete from ");
        sb.append(Constant.resourcestorage_start + modelCode);
        sb.append(" where 1=1 ");
        if(!StringUtils.isEmpty(deviceId)){
            sb.append(" and DEVICEID = '").append(deviceId).append("'");
        }
        if(!StringUtils.isEmpty(eid)){
            sb.append(" and EID = '").append(eid).append("'");
        }

        return bigDataUtil.phoenixDelete(sb.toString());

    }

    public Integer updateAssetStatus(String modelCode, String rowKey,String modelStr) {
        StringBuilder sb = new StringBuilder();
        sb.append("upsert into ");
        sb.append(Constant.resourcestorage_start + modelCode);
        sb.append(" ( ROWKEY,MODEL ) ");
        sb.append(" values ");
        sb.append(" ('").append(rowKey).append("','").append(StringUtils.isEmpty(modelStr) ? "" : modelStr.replaceAll("\\\\","\\\\\\\\")).append("')");

        return bigDataUtil.phoenixUpsert(sb.toString());

    }

    public Map<String, Object> generateAllAssetCount(long eid,String deviceStatus) {
        StringBuilder sb = new StringBuilder();
        sb.append("select * ");
        sb.append("from (select count(*) as \"hostCount\" from Rs_host where eid ='" + eid + "' and  get_json_object(model,'$.BasicInfo.deviceStatus') = '" + deviceStatus + "' ) a ");
        sb.append("left join(select count(*) as \"clientCount\" from Rs_client where eid ='" + eid + "' and  get_json_object(model,'$.BasicInfo.deviceStatus') = '" + deviceStatus + "' ) b on 1=1 ");
        sb.append("left join(select count(*) as \"nasCount\" from Rs_NAS where eid ='" + eid + "' and  get_json_object(model,'$.BasicInfo.deviceStatus') = '" + deviceStatus + "' ) c on 1=1 ");
        sb.append("left join(select count(*) as \"firewallCount\" from Rs_Firewall where eid ='" + eid + "' and  get_json_object(model,'$.BasicInfo.deviceStatus') = '" + deviceStatus + "' ) d on 1=1 ");
        sb.append("left join(select count(*) as \"mailserverCount\" from Rs_MailServer where eid ='" + eid + "' and  get_json_object(model,'$.BasicInfo.deviceStatus') = '" + deviceStatus + "' ) e on 1=1 ");
        sb.append("left join(select count(*) as \"esxiCount\" from Rs_Esxi where eid ='" + eid + "' and  get_json_object(model,'$.BasicInfo.deviceStatus') = '" + deviceStatus + "' ) f on 1=1");
        sb.append("left join(select count(*) as \"esxi_storageCount\" from Rs_Esxi_Storage where eid ='" + eid + "' and  get_json_object(model,'$.BasicInfo.deviceStatus') = '" + deviceStatus + "' ) g on 1=1");
        sb.append("left join(select count(*) as \"esxi_hostCount\" from Rs_Esxi_Host where eid ='" + eid + "' and  get_json_object(model,'$.BasicInfo.deviceStatus') = '" + deviceStatus + "' ) h on 1=1");
        List<Map<String, Object>> dataList = bigDataUtil.phoenixQuery(sb.toString());
        if(!CollectionUtils.isEmpty(dataList)){
            return dataList.get(0);
        }
        return null;
    }

    public long getAssetCount(String modelCode,long eid,String deviceStatus, String needMaintenance, String deviceIdFilter,String deviceId,String deviceNameFilter,String deviceName,String ownerFilter,String owner,String filter) {
        StringBuilder sb = new StringBuilder();
        sb.append("select count(*) count ");
        sb.append("from " + Constant.resourcestorage_start + modelCode);
        sb.append(" where 1=1 ");
        if(eid != 0) {
            sb.append(" and EID = '" + eid).append("' ");
        }
        if(!StringUtils.isEmpty(deviceStatus)){
            sb.append(" and get_json_object(model,'$.BasicInfo.deviceStatus') = '").append(deviceStatus).append("' ");
        }
        if(!StringUtils.isEmpty(needMaintenance)){
            if("true".equals(needMaintenance)){
                sb.append(" and get_json_object(model,'$.BasicInfo.needMaintenance') = '").append(needMaintenance).append("'");
            }else if("false".equals(needMaintenance)){
                sb.append(" and ( get_json_object(model,'$.BasicInfo.needMaintenance') != 'true' or get_json_object(model,'$.BasicInfo.needMaintenance') is null )");
            }
        }
        /*if(!StringUtils.isEmpty(deviceIdFilter)){
            sb.append(" and DEVICEID = '").append(deviceIdFilter).append("' ");
        }*/
        if(!StringUtils.isEmpty(deviceId)){
            sb.append(" and DEVICEID = '").append(deviceId).append("' ");
        }
        if(!StringUtils.isEmpty(deviceIdFilter)){
            sb.append(" and DEVICEID like '%").append(deviceIdFilter).append("%' ");
        }
        if(!StringUtils.isEmpty(deviceName)){
            sb.append(" and get_json_object(model,'$.BasicInfo.deviceName') = '").append(deviceName).append("' ");
        }
        if (!StringUtils.isEmpty(deviceNameFilter)) {
            sb.append(" and get_json_object(model,'$.BasicInfo.deviceName') like '%").append(deviceNameFilter).append("%' ");
        }
        if (!StringUtils.isEmpty(owner)) {
            sb.append(" and get_json_object(model,'$.BasicInfo.owner') = '").append(owner).append("' ");
        }
        if (!StringUtils.isEmpty(ownerFilter)) {
            sb.append(" and get_json_object(model,'$.BasicInfo.owner') like '%").append(ownerFilter).append("%' ");
        }
        if (!StringUtils.isEmpty(filter)) {
            sb.append(" and get_json_object(model,'$.BasicInfo.deviceName') like '%").append(deviceNameFilter ).append("%' ");
        }
        List<Map<String, Object>> dataList = bigDataUtil.phoenixQuery(sb.toString());
        if(!CollectionUtils.isEmpty(dataList)){
            return Long.valueOf(dataList.get(0).get("COUNT").toString());
        }
        return 0;
    }

    public List<Map<String, Object>> getBackupScheduleList(String modelCode, long eid, String deviceStatus, String filter, String queryField, String whereStr, String sortStr, int pageNum, int pageSize, JSONObject jsonObject) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ROWKEY,DEVICEID as \"deviceId\" ,EID as \"eid\" ,MODEL ");
       /* sb.append(" get_json_object(model,'$.BasicInfo.f_angentRole') as \"f_angentRole\" , ");
        sb.append(" get_json_object(model,'$.BasicInfo.f_backupCycle') as \"f_backupCycle\" , ");
        sb.append(" get_json_object(model,'$.BasicInfo.f_backupDest') as \"f_backupDest\" , ");
        sb.append(" get_json_object(model,'$.BasicInfo.f_backupDestDevice') as \"f_backupDestDevice\" , ");
        sb.append(" get_json_object(model,'$.BasicInfo.f_backupSchName') as \"f_backupSchName\" , ");
        sb.append(" get_json_object(model,'$.BasicInfo.f_backupSoftware') as \"f_backupSoftware\" , ");
        sb.append(" get_json_object(model,'$.BasicInfo.f_backupSourceLocation') as \"f_backupSourceLocation\" , ");
        sb.append(" get_json_object(model,'$.BasicInfo.f_backupType') as \"f_backupType\" , ");
        sb.append(" get_json_object(model,'$.BasicInfo.f_deservedDays') as \"f_deservedDays\" , ");
        sb.append(" get_json_object(model,'$.BasicInfo.f_deviceCatalog') as \"f_deviceCatalog\" , ");
        sb.append(" get_json_object(model,'$.BasicInfo.f_existMaintenanceList') as \"f_existMaintenanceList\" , ");
        sb.append(" get_json_object(model,'$.BasicInfo.f_sourceDeviceName') as \"f_sourceDeviceName\" ");
*/
        if(!StringUtils.isEmpty(queryField)){
            sb.append(", ").append(queryField);
        }
        sb.append(" from " + Constant.resourcestorage_start + modelCode);
        sb.append(" where 1=1 ");
        if(eid != 0) {
            sb.append(" and EID = '" + eid).append("' ");
        }
        if(!StringUtils.isEmpty(deviceStatus)){
            sb.append(" and get_json_object(model,'$.BasicInfo.deviceStatus') = '").append(deviceStatus).append("' ");
        }
        if (!StringUtils.isEmpty(filter)) {
            sb.append(" and get_json_object(model,'$.BasicInfo.f_sourceDeviceName') like '%").append(filter).append("%' ");
        }
        if (!StringUtils.isEmpty(whereStr)) {
            sb.append( whereStr );
        }

        // 模型是BackupSchedule且orderFields有值，由前端傳入的值排序
        if (modelCode.equals("BackupSchedule") && !StringUtils.isEmpty(jsonObject)) {
            JSONArray orderFieldsArray = jsonObject.getJSONArray("orderFields");
            if (orderFieldsArray != null && !orderFieldsArray.isEmpty()) {
                sb.append(" order by ");
                for (int i = 0; i < orderFieldsArray.size(); i++) {
                    JSONObject field = orderFieldsArray.getJSONObject(i);
                    String column = field.getString("column");
                    String ord = field.getString("ord");
                    if (i > 0) {
                        sb.append(", ");
                    }
                    sb.append(column).append(" ").append(ord);
                }
            }
        } else {
            sb.append(" order by get_json_object(model,'$.BasicInfo.f_deviceCatalog') asc, get_json_object(model,'$.BasicInfo.f_sourceDeviceName') asc, get_json_object(model,'$.BasicInfo.deviceName') asc");
        }
        if(pageNum!=0 && pageSize!=0) {//都为0时，查所有
            sb.append(" limit ").append(pageSize).append(" OFFSET ").append((pageNum-1)* pageSize);
        }

        List<Map<String, Object>> dataList = bigDataUtil.phoenixQuery(sb.toString());
        return dataList;
    }


    public long getBackupScheduleCount(String modelCode,long eid,String deviceStatus,String deviceId,String deviceName,String filter) {
        StringBuilder sb = new StringBuilder();
        sb.append("select count(*) count ");
        sb.append("from " + Constant.resourcestorage_start + modelCode);
        sb.append(" where 1=1 ");
        if(eid != 0) {
            sb.append(" and EID = '" + eid).append("' ");
        }
        if(!StringUtils.isEmpty(deviceStatus)){
            sb.append(" and get_json_object(model,'$.BasicInfo.deviceStatus') = '").append(filter).append("' ");
        }
        if(!StringUtils.isEmpty(deviceId)){
            sb.append(" and DEVICEID = '").append(deviceId).append("' ");
        }
        if (!StringUtils.isEmpty(deviceName)) {
            sb.append(" and get_json_object(model,'$.BasicInfo.deviceName') = '").append(filter).append("' ");
        }
        if (!StringUtils.isEmpty(filter)) {
            sb.append(" and get_json_object(model,'$.BasicInfo.f_sourceDeviceName') like '%").append(filter).append("%' ");
        }
        List<Map<String, Object>> dataList = bigDataUtil.phoenixQuery(sb.toString());
        if(!CollectionUtils.isEmpty(dataList)){
            return Long.valueOf(dataList.get(0).get("COUNT").toString());
        }
        return 0;
    }


    public Map<String, Object> getBackupScheduleDetail(String modelCode, long eid, String deviceCatalog, String sourceDeviceName, String backupSchName,String deviceName) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ROWKEY,EID as \"eid\",DEVICEID as \"deviceId\",MODEL,RELATION as \"relation\" ");
        sb.append(" from " + Constant.resourcestorage_start + modelCode);
        sb.append(" where 1=1 ");
        if(eid != 0) {
            sb.append(" and EID = '" + eid).append("' ");
        }
        if(!StringUtils.isEmpty(deviceCatalog)){
            sb.append(" and get_json_object(model,'$.BasicInfo.f_deviceCatalog') = '").append(deviceCatalog).append("' ");
        }
        if(!StringUtils.isEmpty(sourceDeviceName)){
            sb.append(" and get_json_object(model,'$.BasicInfo.f_sourceDeviceName') = '").append(sourceDeviceName).append("' ");
        }
        if(!StringUtils.isEmpty(backupSchName)){
            sb.append(" and get_json_object(model,'$.BasicInfo.f_backupSchName') = '").append(backupSchName).append("' ");
        }
        if(!StringUtils.isEmpty(deviceName)){
            sb.append(" and get_json_object(model,'$.BasicInfo.deviceName') = '").append(deviceName).append("' ");
        }
        List<Map<String, Object>> dataList = bigDataUtil.phoenixQuery(sb.toString());
        if(!CollectionUtils.isEmpty(dataList)){
            return dataList.get(0);
        }
        return null;
    }




    public List<Map<String, Object>> getHostReport_baseInfo(String modelCode,String item, String eid,String deviceId,List<String> deviceIds, String queryField,String yearMonthStart,String yearMonth) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ROWKEY, EID as \"eid\",  DEVICEID as \"deviceId\", DEVICENAME as \"deviceName\", MAINTENANCEYEARMONTH as \"maintenanceYearMonth\", ");
        sb.append("F_OSVERSION as \"f_osVersion\", F_OSBIT as \"f_osBit\", SNMPSERVICEIP as \"snmpServiceIP\", F_DEVICETYPE_KW as \"f_deviceType_kw\", F_DEVICEROLE_KW as \"f_deviceRole_kw\", ");
        sb.append("F_PHYSICALHOSTIPMI as \"f_physicalHostIPMI\", F_RAID as \"f_raid\", CPUUSAGE as \"cpuUsage\", RAMUSAGE as \"ramUsage\", F_ANTIDRUGBRAND as \"f_antiDrugBrand\", ");
        sb.append("F_KWCONTRACTEXPRITYDATE as \"f_kwContractExprityDate\", F_CONTRACTINSPECTION as \"f_contractInspection\", F_UPDATESTATUS as \"f_updateStatus\",  ");
        sb.append("F_SQLSCHEDULELOGCHECK as \"f_sqlScheduleLogCheck\", F_HARDWAREHEALTH as \"f_hardwareHealth\", F_HARDWAREHEALTHSTATUS as \"f_hardwareHealthStatus\", ");
        sb.append("F_BACKUPOPERATIONSTATUS as \"f_backupOperationStatus\", F_REMARK as \"f_remark\" ");
        if(!StringUtils.isEmpty(queryField)){
            sb.append(", ").append(queryField);
        }
        sb.append(" from " + item);
        sb.append(" where 1=1 ");
        if(!StringUtils.isEmpty(eid)) {
            sb.append(" and EID = '" + eid).append("' ");
        }
        if(!StringUtils.isEmpty(deviceId)){
            sb.append(" and DEVICEID = '").append(deviceId).append("' ");
        }
        if(!CollectionUtils.isEmpty(deviceIds)){
            StringBuffer didSb = new StringBuffer();
            deviceIds.stream().forEach(k->didSb.append("'").append(k).append("',"));
            if(!StringUtils.isEmpty(didSb.toString())){
                sb.append(" and DEVICEID in ( ").append(didSb.toString().substring(0,didSb.toString().length()-1)).append(") ");
            }
        }

        if(!StringUtils.isEmpty(yearMonthStart) && !StringUtils.isEmpty(yearMonth)) {
            sb.append(" and maintenanceYearMonth >= '").append(yearMonthStart).append("' and maintenanceYearMonth <='").append(yearMonth).append("' ");
        }
        sb.append(" order by MAINTENANCEYEARMONTH DESC");
        List<Map<String, Object>> dataList = bigDataUtil.phoenixQuery(sb.toString());
        return dataList;
    }


    public List<Map<String, Object>> getHostReport_diskInfo(String modelCode,String item, String eid,String deviceId,List<String> deviceIds, String queryField,String yearMonthStart,String yearMonth) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ROWKEY, EID as \"eid\",  DEVICEID as \"deviceId\", MAINTENANCEYEARMONTH as \"maintenanceYearMonth\", ");
        sb.append("F_DISKAVAILABLECAPACITY as \"f_diskAvailableCapacity\", F_DISKINFO as \"f_diskInfo\", F_DISKTYPE as \"f_diskType\", F_DISKCAPACITY as \"f_diskCapacity\", F_DISKUSEDCAPACITY as \"f_diskUsedCapacity\", F_DISKCODE as \"f_diskCode\"");
        if(!StringUtils.isEmpty(queryField)){
            sb.append(", ").append(queryField);
        }
        sb.append(" from " + item);
        sb.append(" where 1=1 ");
        if(!StringUtils.isEmpty(eid)) {
            sb.append(" and EID = '" + eid).append("' ");
        }
        if(!StringUtils.isEmpty(deviceId)){
            sb.append(" and DEVICEID = '").append(deviceId).append("' ");
        }
        if(!CollectionUtils.isEmpty(deviceIds)){
            StringBuffer didSb = new StringBuffer();
            deviceIds.stream().forEach(k->didSb.append("'").append(k).append("',"));
            if(!StringUtils.isEmpty(didSb.toString())){
                sb.append(" and DEVICEID in ( ").append(didSb.toString().substring(0,didSb.toString().length()-1)).append(") ");
            }
        }

        if(!StringUtils.isEmpty(yearMonthStart) && !StringUtils.isEmpty(yearMonth)) {
            sb.append(" and maintenanceYearMonth >= '").append(yearMonthStart).append("' and maintenanceYearMonth <='").append(yearMonth).append("' ");
        }
        sb.append(" order by MAINTENANCEYEARMONTH DESC");
        List<Map<String, Object>> dataList = bigDataUtil.phoenixQuery(sb.toString());
        return dataList;
    }


    public List<Map<String, Object>> getHostReport_year(String modelCode,String item, String eid,String deviceId,List<String> deviceIds, String queryField,String yearMonthStart,String yearMonth) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ROWKEY, EID as \"eid\",  DEVICEID as \"deviceId\", MAINTENANCEYEARMONTH as \"maintenanceYearMonth\", ");
        sb.append("CPUUSAGE as \"cpuUsage\", RAMUSAGE as \"ramUsage\" ");
        if(!StringUtils.isEmpty(queryField)){
            sb.append(", ").append(queryField);
        }
        sb.append(" from " + item);
        sb.append(" where 1=1 ");
        if(!StringUtils.isEmpty(eid)) {
            sb.append(" and EID = '" + eid).append("' ");
        }
        if(!StringUtils.isEmpty(deviceId)){
            sb.append(" and DEVICEID = '").append(deviceId).append("' ");
        }
        if(!CollectionUtils.isEmpty(deviceIds)){
            StringBuffer didSb = new StringBuffer();
            deviceIds.stream().forEach(k->didSb.append("'").append(k).append("',"));
            if(!StringUtils.isEmpty(didSb.toString())){
                sb.append(" and DEVICEID in ( ").append(didSb.toString().substring(0,didSb.toString().length()-1)).append(") ");
            }
        }

        if(!StringUtils.isEmpty(yearMonthStart) && !StringUtils.isEmpty(yearMonth)) {
            sb.append(" and maintenanceYearMonth >= '").append(yearMonthStart).append("' and maintenanceYearMonth <='").append(yearMonth).append("' ");
        }
        sb.append(" order by MAINTENANCEYEARMONTH DESC");
        List<Map<String, Object>> dataList = bigDataUtil.phoenixQuery(sb.toString());
        return dataList;
    }


    public List<Map<String, Object>> getHostReport_data_file_sql(String modelCode,String item, String eid,String deviceId,List<String> deviceIds, String queryField,String yearMonthStart,String yearMonth) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ROWKEY, EID as \"eid\",  DEVICEID as \"deviceId\", MAINTENANCEYEARMONTH as \"maintenanceYearMonth\", ");
        sb.append("F_SQLDATAFILECAPACITY as \"f_sqlDataFileCapacity\", F_SQLDATAFILECOUNT as \"f_sqlDataFileCount\", f_BACKUPSTATUS as \"f_backupStatus\" ");
        if(!StringUtils.isEmpty(queryField)){
            sb.append(", ").append(queryField);
        }
        sb.append(" from " + item);
        sb.append(" where 1=1 ");
        if(!StringUtils.isEmpty(eid)) {
            sb.append(" and EID = '" + eid).append("' ");
        }
        if(!StringUtils.isEmpty(deviceId)){
            sb.append(" and DEVICEID = '").append(deviceId).append("' ");
        }
        if(!CollectionUtils.isEmpty(deviceIds)){
            StringBuffer didSb = new StringBuffer();
            deviceIds.stream().forEach(k->didSb.append("'").append(k).append("',"));
            if(!StringUtils.isEmpty(didSb.toString())){
                sb.append(" and DEVICEID in ( ").append(didSb.toString().substring(0,didSb.toString().length()-1)).append(") ");
            }
        }

        if(!StringUtils.isEmpty(yearMonthStart) && !StringUtils.isEmpty(yearMonth)) {
            sb.append(" and maintenanceYearMonth >= '").append(yearMonthStart).append("' and maintenanceYearMonth <='").append(yearMonth).append("' ");
        }
        sb.append(" order by MAINTENANCEYEARMONTH DESC");
        List<Map<String, Object>> dataList = bigDataUtil.phoenixQuery(sb.toString());
        return dataList;
    }


    public List<Map<String, Object>> getHostReport_ups_sql(String modelCode,String item, String eid,String deviceId,List<String> deviceIds, String queryField,String yearMonthStart,String yearMonth) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ROWKEY, EID as \"eid\",  DEVICEID as \"deviceId\", MAINTENANCEYEARMONTH as \"maintenanceYearMonth\", ");
        sb.append("F_CONNECTIONMODE as \"f_connectionMode\", F_UPSCONNECTIONSTATUS as \"f_upsConnectionStatus\" ");
        if(!StringUtils.isEmpty(queryField)){
            sb.append(", ").append(queryField);
        }
        sb.append(" from " + item);
        sb.append(" where 1=1 ");
        if(!StringUtils.isEmpty(eid)) {
            sb.append(" and EID = '" + eid).append("' ");
        }
        if(!StringUtils.isEmpty(deviceId)){
            sb.append(" and DEVICEID = '").append(deviceId).append("' ");
        }
        if(!CollectionUtils.isEmpty(deviceIds)){
            StringBuffer didSb = new StringBuffer();
            deviceIds.stream().forEach(k->didSb.append("'").append(k).append("',"));
            if(!StringUtils.isEmpty(didSb.toString())){
                sb.append(" and DEVICEID in ( ").append(didSb.toString().substring(0,didSb.toString().length()-1)).append(") ");
            }
        }

        if(!StringUtils.isEmpty(yearMonthStart) && !StringUtils.isEmpty(yearMonth)) {
            sb.append(" and maintenanceYearMonth >= '").append(yearMonthStart).append("' and maintenanceYearMonth <='").append(yearMonth).append("' ");
        }
        sb.append(" order by MAINTENANCEYEARMONTH DESC");
        List<Map<String, Object>> dataList = bigDataUtil.phoenixQuery(sb.toString());
        return dataList;
    }

    public List<Map<String, Object>> getHostReport_folder_sql(String modelCode,String item, String eid,String deviceId,List<String> deviceIds, String queryField,String yearMonthStart,String yearMonth) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ROWKEY, EID as \"eid\",  DEVICEID as \"deviceId\", MAINTENANCEYEARMONTH as \"maintenanceYearMonth\", ");
        sb.append(" F_BACKUPSTATUS as \"f_backupStatus\" , F_BACKUPFOLDER as \"f_backupFolder\",F_BACKUPMETHOD as \"f_backupMethod\"");
        if(!StringUtils.isEmpty(queryField)){
            sb.append(", ").append(queryField);
        }
        sb.append(" from " + item);
        sb.append(" where 1=1 ");
        if(!StringUtils.isEmpty(eid)) {
            sb.append(" and EID = '" + eid).append("' ");
        }
        if(!StringUtils.isEmpty(deviceId)){
            sb.append(" and DEVICEID = '").append(deviceId).append("' ");
        }
        if(!CollectionUtils.isEmpty(deviceIds)){
            StringBuffer didSb = new StringBuffer();
            deviceIds.stream().forEach(k->didSb.append("'").append(k).append("',"));
            if(!StringUtils.isEmpty(didSb.toString())){
                sb.append(" and DEVICEID in ( ").append(didSb.toString().substring(0,didSb.toString().length()-1)).append(") ");
            }
        }

        if(!StringUtils.isEmpty(yearMonthStart) && !StringUtils.isEmpty(yearMonth)) {
            sb.append(" and maintenanceYearMonth >= '").append(yearMonthStart).append("' and maintenanceYearMonth <='").append(yearMonth).append("' ");
        }
        sb.append(" order by MAINTENANCEYEARMONTH DESC");
        List<Map<String, Object>> dataList = bigDataUtil.phoenixQuery(sb.toString());
        return dataList;
    }


    public List<Map<String, Object>> getNasReport_baseInfo(String modelCode,String item, String eid,String deviceId,List<String> deviceIds, String queryField,String yearMonthStart,String yearMonth) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ROWKEY, EID as \"eid\",  DEVICEID as \"deviceId\",DEVICENAME as \"deviceName\", MAINTENANCEYEARMONTH as \"maintenanceYearMonth\", ");
        sb.append("SNMPSERVICEIP as \"snmpServiceIP\", SNMPSERVICEPORT as \"snmpServicePort\", F_FIRMWAREVERSION as \"f_firmwareVersion\", F_PURPOSE as \"f_purpose\", F_SYSTEMLOGCHECK as \"f_systemLogCheck\", ");
        sb.append("F_BACKUPPROFILEBACKUP as \"f_backupProfilebackup\", F_CHECKRESULT as \"f_checkResult\", CPUUSAGE as \"cpuUsage\", RAMUSAGE as \"ramUsage\", F_REMARK as \"f_remark\" ");
        if(!StringUtils.isEmpty(queryField)){
            sb.append(", ").append(queryField);
        }
        sb.append(" from " + item);
        sb.append(" where 1=1 ");
        if(!StringUtils.isEmpty(eid)) {
            sb.append(" and EID = '" + eid).append("' ");
        }
        if(!StringUtils.isEmpty(deviceId)){
            sb.append(" and DEVICEID = '").append(deviceId).append("' ");
        }
        if(!CollectionUtils.isEmpty(deviceIds)){
            StringBuffer didSb = new StringBuffer();
            deviceIds.stream().forEach(k->didSb.append("'").append(k).append("',"));
            if(!StringUtils.isEmpty(didSb.toString())){
                sb.append(" and DEVICEID in ( ").append(didSb.toString().substring(0,didSb.toString().length()-1)).append(") ");
            }
        }

        if(!StringUtils.isEmpty(yearMonthStart) && !StringUtils.isEmpty(yearMonth)) {
            sb.append(" and maintenanceYearMonth >= '").append(yearMonthStart).append("' and maintenanceYearMonth <='").append(yearMonth).append("' ");
        }
        sb.append(" order by MAINTENANCEYEARMONTH DESC");
        List<Map<String, Object>> dataList = bigDataUtil.phoenixQuery(sb.toString());
        return dataList;
    }

    public List<Map<String, Object>> getNasReport_space(String modelCode,String item, String eid,String deviceId,List<String> deviceIds, String queryField,String yearMonthStart,String yearMonth) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ROWKEY, EID as \"eid\",  DEVICEID as \"deviceId\", MAINTENANCEYEARMONTH as \"maintenanceYearMonth\", ");
        sb.append("DISKDESCR as \"diskDescr\", DISKSIZE as \"diskSize\", DISKFREESPACE as \"diskFreeSpace\", F_DISKHEALTHSMART as \"f_diskHealthSMART\", DISKUSAGE as \"diskUsage\" ");
        if(!StringUtils.isEmpty(queryField)){
            sb.append(", ").append(queryField);
        }
        sb.append(" from " + item);
        sb.append(" where 1=1 ");
        if(!StringUtils.isEmpty(eid)) {
            sb.append(" and EID = '" + eid).append("' ");
        }
        if(!StringUtils.isEmpty(deviceId)){
            sb.append(" and DEVICEID = '").append(deviceId).append("' ");
        }
        if(!CollectionUtils.isEmpty(deviceIds)){
            StringBuffer didSb = new StringBuffer();
            deviceIds.stream().forEach(k->didSb.append("'").append(k).append("',"));
            if(!StringUtils.isEmpty(didSb.toString())){
                sb.append(" and DEVICEID in ( ").append(didSb.toString().substring(0,didSb.toString().length()-1)).append(") ");
            }
        }

        if(!StringUtils.isEmpty(yearMonthStart) && !StringUtils.isEmpty(yearMonth)) {
            sb.append(" and maintenanceYearMonth >= '").append(yearMonthStart).append("' and maintenanceYearMonth <='").append(yearMonth).append("' ");
        }
        sb.append(" order by MAINTENANCEYEARMONTH DESC");
        List<Map<String, Object>> dataList = bigDataUtil.phoenixQuery(sb.toString());
        return dataList;
    }


    public List<Map<String, Object>> getNasReport_usb(String modelCode,String item, String eid,String deviceId,List<String> deviceIds, String queryField,String yearMonthStart,String yearMonth) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ROWKEY, EID as \"eid\",  DEVICEID as \"deviceId\", MAINTENANCEYEARMONTH as \"maintenanceYearMonth\", ");
        sb.append("DISKDESCR as \"diskDescr\", DISKSIZE as \"diskSize\", DISKDESCR as \"diskDescr\", DISKFREESPACE as \"diskFreeSpace\", DISKUSAGE as \"diskUsage\" ");
        if(!StringUtils.isEmpty(queryField)){
            sb.append(", ").append(queryField);
        }
        sb.append(" from " + item);
        sb.append(" where 1=1 ");
        if(!StringUtils.isEmpty(eid)) {
            sb.append(" and EID = '" + eid).append("' ");
        }
        if(!StringUtils.isEmpty(deviceId)){
            sb.append(" and DEVICEID = '").append(deviceId).append("' ");
        }
        if(!CollectionUtils.isEmpty(deviceIds)){
            StringBuffer didSb = new StringBuffer();
            deviceIds.stream().forEach(k->didSb.append("'").append(k).append("',"));
            if(!StringUtils.isEmpty(didSb.toString())){
                sb.append(" and DEVICEID in ( ").append(didSb.toString().substring(0,didSb.toString().length()-1)).append(") ");
            }
        }

        if(!StringUtils.isEmpty(yearMonthStart) && !StringUtils.isEmpty(yearMonth)) {
            sb.append(" and maintenanceYearMonth >= '").append(yearMonthStart).append("' and maintenanceYearMonth <='").append(yearMonth).append("' ");
        }
        sb.append(" order by MAINTENANCEYEARMONTH DESC");
        List<Map<String, Object>> dataList = bigDataUtil.phoenixQuery(sb.toString());
        return dataList;
    }


    public List<Map<String, Object>> getFirewallReport_baseInfo(String modelCode,String item, String eid,String deviceId,List<String> deviceIds, String queryField,String yearMonthStart,String yearMonth) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ROWKEY, EID as \"eid\",  DEVICEID as \"deviceId\", MAINTENANCEYEARMONTH as \"maintenanceYearMonth\", ");
        sb.append("SNMPSERVICEIP as \"snmpServiceIP\", SNMPSERVICEPORT as \"snmpServicePort\", F_BACKUPPROFILEBACKUP as \"f_backupProfilebackup\", F_PROFILEBACKUPPATH as \"f_profileBackupPath\", F_REMARK as \"f_remark\", ");
        sb.append("F_SIGNATURERESULTSANDJUDGMENT as \"f_signatureResultsAndJudgment\", F_FIRMWAREVERSION as \"f_firmwareVersion\", CPUUSAGE as \"cpuUsage\", RAMUSAGE as \"ramUsage\" ");
        if(!StringUtils.isEmpty(queryField)){
            sb.append(", ").append(queryField);
        }
        sb.append(" from " + item);
        sb.append(" where 1=1 ");
        if(!StringUtils.isEmpty(eid)) {
            sb.append(" and EID = '" + eid).append("' ");
        }
        if(!StringUtils.isEmpty(deviceId)){
            sb.append(" and DEVICEID = '").append(deviceId).append("' ");
        }
        if(!CollectionUtils.isEmpty(deviceIds)){
            StringBuffer didSb = new StringBuffer();
            deviceIds.stream().forEach(k->didSb.append("'").append(k).append("',"));
            if(!StringUtils.isEmpty(didSb.toString())){
                sb.append(" and DEVICEID in ( ").append(didSb.toString().substring(0,didSb.toString().length()-1)).append(") ");
            }
        }

        if(!StringUtils.isEmpty(yearMonthStart) && !StringUtils.isEmpty(yearMonth)) {
            sb.append(" and maintenanceYearMonth >= '").append(yearMonthStart).append("' and maintenanceYearMonth <='").append(yearMonth).append("' ");
        }
        sb.append(" order by MAINTENANCEYEARMONTH DESC");
        List<Map<String, Object>> dataList = bigDataUtil.phoenixQuery(sb.toString());
        return dataList;
    }

    public List<Map<String, Object>> getMailServerReport(String modelCode,String item, String eid,String deviceId,List<String> deviceIds, String queryField,String yearMonthStart,String yearMonth) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ROWKEY, EID as \"eid\",  DEVICEID as \"deviceId\", MAINTENANCEYEARMONTH as \"maintenanceYearMonth\", ");
        sb.append("F_DOMAINNAME as \"f_domainName\", SNMPSERVICEIP as \"snmpServiceIP\", SNMPSERVICEPORT as \"snmpServicePort\", F_BACKUPPROFILEBACKUP as \"f_backupProfilebackup\", F_PROFILEBACKUPPATH as \"f_profileBackupPath\", ");
        sb.append("F_FIRMWAREVERSION as \"f_firmwareVersion\", F_CHECKRESULT as \"f_checkResult\", CPUUSAGE as \"cpuUsage\", RAMUSAGE as \"ramUsage\", F_DISKAVAILABLECAPACITYREQ as \"f_diskAvailableCapacityReq\", F_REMARK as \"f_remark\" ");
        if(!StringUtils.isEmpty(queryField)){
            sb.append(", ").append(queryField);
        }
        sb.append(" from " + item);
        sb.append(" where 1=1 ");
        if(!StringUtils.isEmpty(eid)) {
            sb.append(" and EID = '" + eid).append("' ");
        }
        if(!StringUtils.isEmpty(deviceId)){
            sb.append(" and DEVICEID = '").append(deviceId).append("' ");
        }
        if(!CollectionUtils.isEmpty(deviceIds)){
            StringBuffer didSb = new StringBuffer();
            deviceIds.stream().forEach(k->didSb.append("'").append(k).append("',"));
            if(!StringUtils.isEmpty(didSb.toString())){
                sb.append(" and DEVICEID in ( ").append(didSb.toString().substring(0,didSb.toString().length()-1)).append(") ");
            }
        }

        if(!StringUtils.isEmpty(yearMonthStart) && !StringUtils.isEmpty(yearMonth)) {
            sb.append(" and maintenanceYearMonth >= '").append(yearMonthStart).append("' and maintenanceYearMonth <='").append(yearMonth).append("' ");
        }
        sb.append(" order by MAINTENANCEYEARMONTH DESC");
        List<Map<String, Object>> dataList = bigDataUtil.phoenixQuery(sb.toString());
        return dataList;
    }

    public List<Map<String, Object>> getMailServerReport_space(String modelCode,String item, String eid,String deviceId,List<String> deviceIds, String queryField,String yearMonthStart,String yearMonth) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ROWKEY, EID as \"eid\",  DEVICEID as \"deviceId\", MAINTENANCEYEARMONTH as \"maintenanceYearMonth\", ");
        sb.append("F_DISKFREESPACE as \"f_diskFreeSpace\", F_DISKDESCR as \"f_diskDescr\", F_DISKHEALTHSMART_M as \"f_diskHealthSMART_M\" ");

        if(!StringUtils.isEmpty(queryField)){
            sb.append(", ").append(queryField);
        }
        sb.append(" from " + item);
        sb.append(" where 1=1 ");
        if(!StringUtils.isEmpty(eid)) {
            sb.append(" and EID = '" + eid).append("' ");
        }
        if(!StringUtils.isEmpty(deviceId)){
            sb.append(" and DEVICEID = '").append(deviceId).append("' ");
        }
        if(!CollectionUtils.isEmpty(deviceIds)){
            StringBuffer didSb = new StringBuffer();
            deviceIds.stream().forEach(k->didSb.append("'").append(k).append("',"));
            if(!StringUtils.isEmpty(didSb.toString())){
                sb.append(" and DEVICEID in ( ").append(didSb.toString().substring(0,didSb.toString().length()-1)).append(") ");
            }
        }

        if(!StringUtils.isEmpty(yearMonthStart) && !StringUtils.isEmpty(yearMonth)) {
            sb.append(" and maintenanceYearMonth >= '").append(yearMonthStart).append("' and maintenanceYearMonth <='").append(yearMonth).append("' ");
        }
        sb.append(" order by MAINTENANCEYEARMONTH DESC");
        List<Map<String, Object>> dataList = bigDataUtil.phoenixQuery(sb.toString());
        return dataList;
    }

    public List<Map<String, Object>> getClientReport(String modelCode,String item, String eid,String deviceId,List<String> deviceIds, String queryField,String yearMonthStart,String yearMonth) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ROWKEY, EID as \"eid\",  DEVICEID as \"deviceId\",DEVICENAME as \"deviceName\", MAINTENANCEYEARMONTH as \"maintenanceYearMonth\", ");
        sb.append("SNMPSERVICEIP as \"snmpServiceIP\", COMPUTERPLACEMENT as \"computerPlacement\", F_CPUWARNINGCOUNT as \"f_cpuWarningCount\", F_RAMWARNINGCOUNT as \"f_ramWarningCount\", F_HDDWARNINGCOUNT as \"f_hddWarningCount\", " +
                "NET__BYTES_SENT_PER_SEC_WAININGCOUNT as \"net__bytes_sent_per_sec_wainingCount\", NET__BYTES_RECV_PER_SEC_WARNINGCOUNT as \"net__bytes_recv_per_sec_warningCount\", SNMPSERVICEIP as \"snmpserviceIp\",RAMSIZE as \"ramSize\", RAMUSAGE as \"ramUsage\", F_REMARK as \"f_remark\"");
        if(!StringUtils.isEmpty(queryField)){
            sb.append(", ").append(queryField);
        }
        sb.append(" from " + item);
        sb.append(" where 1=1 ");
        if(!StringUtils.isEmpty(eid)) {
            sb.append(" and EID = '" + eid).append("' ");
        }
        if(!StringUtils.isEmpty(deviceId)){
            sb.append(" and DEVICEID = '").append(deviceId).append("' ");
        }
        if(!CollectionUtils.isEmpty(deviceIds)){
            StringBuffer didSb = new StringBuffer();
            deviceIds.stream().forEach(k->didSb.append("'").append(k).append("',"));
            if(!StringUtils.isEmpty(didSb.toString())){
                sb.append(" and DEVICEID in ( ").append(didSb.toString().substring(0,didSb.toString().length()-1)).append(") ");
            }
        }

        if(!StringUtils.isEmpty(yearMonthStart) && !StringUtils.isEmpty(yearMonth)) {
            sb.append(" and maintenanceYearMonth >= '").append(yearMonthStart).append("' and maintenanceYearMonth <='").append(yearMonth).append("' ");
        }
        sb.append(" order by MAINTENANCEYEARMONTH DESC");
        List<Map<String, Object>> dataList = bigDataUtil.phoenixQuery(sb.toString());
        return dataList;
    }

    public List<Map<String, Object>> getClientReport_disk(String modelCode,String item, String eid,String deviceId,List<String> deviceIds, String queryField,String yearMonthStart,String yearMonth) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ROWKEY, EID as \"eid\",  DEVICEID as \"deviceId\", MAINTENANCEYEARMONTH as \"maintenanceYearMonth\", ");
        sb.append("F_DISKAVAILABLECAPACITY as \"f_diskAvailableCapacity\", F_DISKCAPACITY as \"f_diskCapacity\", F_DISKUSEDCAPACITY as \"f_diskUsedCapacity\", F_DISKCODE as \"f_diskCode\"");
        if(!StringUtils.isEmpty(queryField)){
            sb.append(", ").append(queryField);
        }
        sb.append(" from " + item);
        sb.append(" where 1=1 ");
        if(!StringUtils.isEmpty(eid)) {
            sb.append(" and EID = '" + eid).append("' ");
        }
        if(!StringUtils.isEmpty(deviceId)){
            sb.append(" and DEVICEID = '").append(deviceId).append("' ");
        }
        if(!CollectionUtils.isEmpty(deviceIds)){
            StringBuffer didSb = new StringBuffer();
            deviceIds.stream().forEach(k->didSb.append("'").append(k).append("',"));
            if(!StringUtils.isEmpty(didSb.toString())){
                sb.append(" and DEVICEID in ( ").append(didSb.toString().substring(0,didSb.toString().length()-1)).append(") ");
            }
        }

        if(!StringUtils.isEmpty(yearMonthStart) && !StringUtils.isEmpty(yearMonth)) {
            sb.append(" and maintenanceYearMonth >= '").append(yearMonthStart).append("' and maintenanceYearMonth <='").append(yearMonth).append("' ");
        }
        sb.append(" order by MAINTENANCEYEARMONTH DESC");
        List<Map<String, Object>> dataList = bigDataUtil.phoenixQuery(sb.toString());
        return dataList;
    }


    public List<Map<String, Object>> getBackupScheduleReport(String modelCode,String item, String eid,String deviceId,List<String> deviceIds, String queryField,String yearMonthStart,String yearMonth) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ROWKEY, EID as \"eid\",  DEVICEID as \"deviceId\", MAINTENANCEYEARMONTH as \"maintenanceYearMonth\", ");
        sb.append("F_BACKUPSOFTWARE as \"f_backupSoftware\", F_SOURCEDEVICENAME as \"f_sourceDeviceName\", F_BACKUPSCHNAME as \"f_backupSchName\", F_BACKUPSOURCELOCATION as \"f_backupSourceLocation\", F_BACKUPTYPE as \"f_backupType\", " +
                "F_BACKUPCYCLE as \"f_backupCycle\", F_BACKUPDESTDEVICE as \"f_backupDestDevice\" , F_BACKUPDEST as \"f_backupDest\", F_DESERVEDDAYS as \"f_deservedDays\"");
        if(!StringUtils.isEmpty(queryField)){
            sb.append(", ").append(queryField);
        }
        sb.append(" from " + item);
        sb.append(" where 1=1 ");
        if(!StringUtils.isEmpty(eid)) {
            sb.append(" and EID = '" + eid).append("' ");
        }
        if(!StringUtils.isEmpty(deviceId)){
            sb.append(" and DEVICEID = '").append(deviceId).append("' ");
        }
        if(!CollectionUtils.isEmpty(deviceIds)){
            StringBuffer didSb = new StringBuffer();
            deviceIds.stream().forEach(k->didSb.append("'").append(k).append("',"));
            if(!StringUtils.isEmpty(didSb.toString())){
                sb.append(" and DEVICEID in ( ").append(didSb.toString().substring(0,didSb.toString().length()-1)).append(") ");
            }
        }

        if(!StringUtils.isEmpty(yearMonthStart) && !StringUtils.isEmpty(yearMonth)) {
            sb.append(" and maintenanceYearMonth >= '").append(yearMonthStart).append("' and maintenanceYearMonth <='").append(yearMonth).append("' ");
        }
        sb.append(" order by DEVICEID DESC");
        List<Map<String, Object>> dataList = bigDataUtil.phoenixQuery(sb.toString());
        return dataList;
    }


    public List<Map<String, Object>> getSoftwareReport(String modelCode,String item, String eid,String deviceId,List<String> deviceIds, String queryField,String yearMonthStart,String yearMonth) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ROWKEY, EID as \"eid\",  DEVICEID as \"deviceId\",DEVICENAME as \"deviceName\", MAINTENANCEYEARMONTH as \"maintenanceYearMonth\", ");
        sb.append("INSTALLDATE as \"installDate\", VERSION as \"version\", F_SOFTWARECAPTION as \"f_softwareCaption\" ");
        if(!StringUtils.isEmpty(queryField)){
            sb.append(", ").append(queryField);
        }
        sb.append(" from " + item);
        sb.append(" where 1=1 ");
        if(!StringUtils.isEmpty(eid)) {
            sb.append(" and EID = '" + eid).append("' ");
        }
        if(!StringUtils.isEmpty(deviceId)){
            sb.append(" and DEVICEID = '").append(deviceId).append("' ");
        }
        if(!CollectionUtils.isEmpty(deviceIds)){
            StringBuffer didSb = new StringBuffer();
            deviceIds.stream().forEach(k->didSb.append("'").append(k).append("',"));
            if(!StringUtils.isEmpty(didSb.toString())){
                sb.append(" and DEVICEID in ( ").append(didSb.toString().substring(0,didSb.toString().length()-1)).append(") ");
            }
        }

        if(!StringUtils.isEmpty(yearMonthStart) && !StringUtils.isEmpty(yearMonth)) {
            sb.append(" and maintenanceYearMonth >= '").append(yearMonthStart).append("' and maintenanceYearMonth <='").append(yearMonth).append("' ");
        }
        sb.append(" order by MAINTENANCEYEARMONTH DESC");
        List<Map<String, Object>> dataList = bigDataUtil.phoenixQuery(sb.toString());
        return dataList;
    }

    public List<Map<String, Object>> getHardwareReport(String modelCode,String item, String eid,String deviceId,List<String> deviceIds, String queryField,String yearMonthStart,String yearMonth) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ROWKEY, EID as \"eid\",  DEVICEID as \"deviceId\", MAINTENANCEYEARMONTH as \"maintenanceYearMonth\", ");
        sb.append("DEVICENAME as \"deviceName\", COMPUTERPLACEMENT as \"computerPlacement\",  COMPUTERADDRESS as \"computerAddress\", ASSETCATEGORY as \"assetCategory\",PLATFORM as \"platform\", ");
        sb.append("HDDCAPTION as \"hddCaption\", CPUCAPTION as \"cpuCaption\", MEMORYCPATION as \"memoryCpation\", NETWORKCAPTION as \"networkCaption\"");
        if(!StringUtils.isEmpty(queryField)){
            sb.append(", ").append(queryField);
        }
        sb.append(" from " + item);
        sb.append(" where 1=1 ");
        if(!StringUtils.isEmpty(eid)) {
            sb.append(" and EID = '" + eid).append("' ");
        }
        if(!StringUtils.isEmpty(deviceId)){
            sb.append(" and DEVICEID = '").append(deviceId).append("' ");
        }
        if(!CollectionUtils.isEmpty(deviceIds)){
            StringBuffer didSb = new StringBuffer();
            deviceIds.stream().forEach(k->didSb.append("'").append(k).append("',"));
            if(!StringUtils.isEmpty(didSb.toString())){
                sb.append(" and DEVICEID in ( ").append(didSb.toString().substring(0,didSb.toString().length()-1)).append(") ");
            }
        }

        if(!StringUtils.isEmpty(yearMonthStart) && !StringUtils.isEmpty(yearMonth)) {
            sb.append(" and maintenanceYearMonth >= '").append(yearMonthStart).append("' and maintenanceYearMonth <='").append(yearMonth).append("' ");
        }
        sb.append(" order by MAINTENANCEYEARMONTH DESC");
        List<Map<String, Object>> dataList = bigDataUtil.phoenixQuery(sb.toString());
        return dataList;
    }

    public List<Map<String, Object>> getEsxiReport(String modelCode,String item, String eid,String deviceId,List<String> deviceIds, String queryField,String yearMonthStart,String yearMonth) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ROWKEY, EID as \"eid\",  DEVICEID as \"deviceId\", DEVICENAME as \"deviceName\", MAINTENANCEYEARMONTH as \"maintenanceYearMonth\", ");
        sb.append("F_HASTATUS as \"f_HAStatus\", F_ESXICHECKRESULT_NONE as \"f_esxiCheckResult_none\", FS_PERFORMANCESTATUS_F_ESXICHECKRESULT as \"fs_performanceStatus_f_esxiCheckResult\", FS_ESXILOGCHECK_F_ESXICHECKRESULT as \"fs_esxiLogCheck_f_esxiCheckResult\", F_HDDSTATUS as \"f_hddStatus\", " +
                "F_REMARK as \"f_remark\" ");
        if(!StringUtils.isEmpty(queryField)){
            sb.append(", ").append(queryField);
        }
        sb.append(" from " + item);
        sb.append(" where 1=1 ");
        if(!StringUtils.isEmpty(eid)) {
            sb.append(" and EID = '" + eid).append("' ");
        }
        if(!StringUtils.isEmpty(deviceId)){
            sb.append(" and DEVICEID = '").append(deviceId).append("' ");
        }
        if(!CollectionUtils.isEmpty(deviceIds)){
            StringBuffer didSb = new StringBuffer();
            deviceIds.stream().forEach(k->didSb.append("'").append(k).append("',"));
            if(!StringUtils.isEmpty(didSb.toString())){
                sb.append(" and DEVICEID in ( ").append(didSb.toString().substring(0,didSb.toString().length()-1)).append(") ");
            }
        }

        if(!StringUtils.isEmpty(yearMonthStart) && !StringUtils.isEmpty(yearMonth)) {
            sb.append(" and maintenanceYearMonth >= '").append(yearMonthStart).append("' and maintenanceYearMonth <='").append(yearMonth).append("' ");
        }
        sb.append(" order by MAINTENANCEYEARMONTH DESC");
        List<Map<String, Object>> dataList = bigDataUtil.phoenixQuery(sb.toString());
        return dataList;
    }


    public List<Map<String, Object>> getEsxiHostReport(String modelCode,String item, String eid,String deviceId,List<String> deviceIds, String queryField,String yearMonthStart,String yearMonth) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ROWKEY, EID as \"eid\",  DEVICEID as \"deviceId\", DEVICENAME as \"deviceName\", MAINTENANCEYEARMONTH as \"maintenanceYearMonth\", ");
        sb.append("SNMPSERVICEIP as \"snmpServiceIP\", F_HYPERVISOR as \"f_hypervisor\", DEVICENAME as \"deviceName\", F_CPUUSAGEAVGREQ as \"f_cpuUsageAvgReq\", F_RAMUSAGEAVGREQ as \"f_ramUsageAvgReq\"");
        if(!StringUtils.isEmpty(queryField)){
            sb.append(", ").append(queryField);
        }
        sb.append(" from " + item);
        sb.append(" where 1=1 ");
        if(!StringUtils.isEmpty(eid)) {
            sb.append(" and EID = '" + eid).append("' ");
        }
        if(!StringUtils.isEmpty(deviceId)){
            sb.append(" and DEVICEID = '").append(deviceId).append("' ");
        }
        if(!CollectionUtils.isEmpty(deviceIds)){
            StringBuffer didSb = new StringBuffer();
            deviceIds.stream().forEach(k->didSb.append("'").append(k).append("',"));
            if(!StringUtils.isEmpty(didSb.toString())){
                sb.append(" and DEVICEID in ( ").append(didSb.toString().substring(0,didSb.toString().length()-1)).append(") ");
            }
        }

        if(!StringUtils.isEmpty(yearMonthStart) && !StringUtils.isEmpty(yearMonth)) {
            sb.append(" and maintenanceYearMonth >= '").append(yearMonthStart).append("' and maintenanceYearMonth <='").append(yearMonth).append("' ");
        }
        sb.append(" order by MAINTENANCEYEARMONTH DESC");
        List<Map<String, Object>> dataList = bigDataUtil.phoenixQuery(sb.toString());
        return dataList;
    }


    public List<Map<String, Object>> getEsxiStorageReport(String modelCode,String item, String eid,String deviceId,List<String> deviceIds, String queryField,String yearMonthStart,String yearMonth) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ROWKEY, EID as \"eid\",  DEVICEID as \"deviceId\", DEVICENAME as \"deviceName\", MAINTENANCEYEARMONTH as \"maintenanceYearMonth\", ");
        sb.append("SNMPSERVICEIP as \"snmpServiceIP\", DEVICENAME as \"deviceName\"");
        if(!StringUtils.isEmpty(queryField)){
            sb.append(", ").append(queryField);
        }
        sb.append(" from " + item);
        sb.append(" where 1=1 ");
        if(!StringUtils.isEmpty(eid)) {
            sb.append(" and EID = '" + eid).append("' ");
        }
        if(!StringUtils.isEmpty(deviceId)){
            sb.append(" and DEVICEID = '").append(deviceId).append("' ");
        }
        if(!CollectionUtils.isEmpty(deviceIds)){
            StringBuffer didSb = new StringBuffer();
            deviceIds.stream().forEach(k->didSb.append("'").append(k).append("',"));
            if(!StringUtils.isEmpty(didSb.toString())){
                sb.append(" and DEVICEID in ( ").append(didSb.toString().substring(0,didSb.toString().length()-1)).append(") ");
            }
        }

        if(!StringUtils.isEmpty(yearMonthStart) && !StringUtils.isEmpty(yearMonth)) {
            sb.append(" and maintenanceYearMonth >= '").append(yearMonthStart).append("' and maintenanceYearMonth <='").append(yearMonth).append("' ");
        }
        sb.append(" order by MAINTENANCEYEARMONTH DESC");
        List<Map<String, Object>> dataList = bigDataUtil.phoenixQuery(sb.toString());
        return dataList;
    }


    public List<Map<String, Object>> getEsxiHostSpaceReport(String modelCode,String item, String eid,String deviceId,List<String> deviceIds, String queryField,String yearMonthStart,String yearMonth) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ROWKEY, EID as \"eid\",  DEVICEID as \"deviceId\", MAINTENANCEYEARMONTH as \"maintenanceYearMonth\", ");
        sb.append("F_LOCALDISKNAME as \"f_localDiskName\", F_LOCALDISKCONFIG as \"f_localDiskConfig\", F_LOCALDISKUSAGE as \"f_localDiskUsage\", F_LOCALDISKREMAINING as \"f_localDiskRemaining\", F_LOCALDISKREMAININGREAL as \"f_localDiskRemainingReal\", " +
                "F_LOCALDISKISCALCULATED as \"f_localDiskIsCalculated\"");
        if(!StringUtils.isEmpty(queryField)){
            sb.append(", ").append(queryField);
        }
        sb.append(" from " + item);
        sb.append(" where 1=1 ");
        if(!StringUtils.isEmpty(eid)) {
            sb.append(" and EID = '" + eid).append("' ");
        }
        if(!StringUtils.isEmpty(deviceId)){
            sb.append(" and DEVICEID = '").append(deviceId).append("' ");
        }
        if(!CollectionUtils.isEmpty(deviceIds)){
            StringBuffer didSb = new StringBuffer();
            deviceIds.stream().forEach(k->didSb.append("'").append(k).append("',"));
            if(!StringUtils.isEmpty(didSb.toString())){
                sb.append(" and DEVICEID in ( ").append(didSb.toString().substring(0,didSb.toString().length()-1)).append(") ");
            }
        }

        if(!StringUtils.isEmpty(yearMonthStart) && !StringUtils.isEmpty(yearMonth)) {
            sb.append(" and maintenanceYearMonth >= '").append(yearMonthStart).append("' and maintenanceYearMonth <='").append(yearMonth).append("' ");
        }
        sb.append(" order by MAINTENANCEYEARMONTH DESC");
        List<Map<String, Object>> dataList = bigDataUtil.phoenixQuery(sb.toString());
        return dataList;
    }


    public List<Map<String, Object>> getEsxiStorageLunReport(String modelCode,String item, String eid,String deviceId,List<String> deviceIds, String queryField,String yearMonthStart,String yearMonth) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ROWKEY, EID as \"eid\",  DEVICEID as \"deviceId\", MAINTENANCEYEARMONTH as \"maintenanceYearMonth\", ");
        sb.append("F_LUNZONENAME as \"f_lunZoneName\", F_LUNCONFIG as \"f_lunConfig\", F_LUNUSED as \"f_lunUsed\", F_LUNREMAINING as \"f_lunRemaining\", F_LUNREMAININGREAL as \"f_lunRemainingReal\" ") ;
        if(!StringUtils.isEmpty(queryField)){
            sb.append(", ").append(queryField);
        }
        sb.append(" from " + item);
        sb.append(" where 1=1 ");
        if(!StringUtils.isEmpty(eid)) {
            sb.append(" and EID = '" + eid).append("' ");
        }
        if(!StringUtils.isEmpty(deviceId)){
            sb.append(" and DEVICEID = '").append(deviceId).append("' ");
        }
        if(!CollectionUtils.isEmpty(deviceIds)){
            StringBuffer didSb = new StringBuffer();
            deviceIds.stream().forEach(k->didSb.append("'").append(k).append("',"));
            if(!StringUtils.isEmpty(didSb.toString())){
                sb.append(" and DEVICEID in ( ").append(didSb.toString().substring(0,didSb.toString().length()-1)).append(") ");
            }
        }

        if(!StringUtils.isEmpty(yearMonthStart) && !StringUtils.isEmpty(yearMonth)) {
            sb.append(" and maintenanceYearMonth >= '").append(yearMonthStart).append("' and maintenanceYearMonth <='").append(yearMonth).append("' ");
        }
        sb.append(" order by MAINTENANCEYEARMONTH DESC");
        List<Map<String, Object>> dataList = bigDataUtil.phoenixQuery(sb.toString());
        return dataList;
    }
}
