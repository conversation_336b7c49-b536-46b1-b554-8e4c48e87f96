package com.digiwin.escloud.aioitms.bigdata.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.digiwin.escloud.aioitms.bigdata.BigDataUtil;
import com.digiwin.escloud.aioitms.bigdata.model.*;
import com.digiwin.escloud.aioitms.bigdata.service.IOracleV3Service;
import com.digiwin.escloud.common.model.UnitType;
import com.digiwin.escloud.common.util.*;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.Comparator;
import java.util.*;
import java.util.stream.IntStream;
import java.util.stream.LongStream;

import static com.digiwin.escloud.aioitms.bigdata.model.QueryWrapperHelper.buildBusinessCondition4Sql;

/**
 * <AUTHOR>
 * @Date: 2022-10-10 16:52
 * @Description
 */
@Slf4j
@Service
public class OracleV3Service implements IOracleV3Service {

    @Autowired
    private BigDataUtil bigDataUtil;
    @Autowired
    private StringHttpMessageConverter stringHttpMessageConverter;
    @Autowired
    SQLTop10FactoryService sqlTop10FactoryService;
    @Value("${aio.service.area}")
    String area;

    @Override
    public List<Map<String, Object>> getBufferHitRate(String startTime, String endTime, String dbId) {
        QueryWrapper qw = new QueryWrapper(bigDataUtil.getSrDbName(), "MESOracleBufferHitRateCollected_sr_duplicate");
        qw.select("collectedTime", "buffer_hit_rate");
        qw.eq("source_db_id", dbId).between("collectedTime", startTime, endTime).orderByAsc("collectedTime");
        String sql = QueryWrapperHelper.getSql4Phoenix(qw);
        List<Map<String, Object>> dataList = bigDataUtil.srQuery(sql);
        return dataList;
    }

    @Override
    public List<Map<String, Object>> getSharedPoolHitRate(String startTime, String endTime, String dbId) {
        QueryWrapper qw = new QueryWrapper(bigDataUtil.getSrDbName(), "MESOracleSharedPoolHitRateCollected_sr_duplicate");
        qw.select("collectedTime", "shared_pool_hit_rate");
        qw.eq("source_db_id", dbId).between("collectedTime", startTime, endTime).orderByAsc("collectedTime");
        String sql = QueryWrapperHelper.getSql4Phoenix(qw);
        List<Map<String, Object>> dataList = bigDataUtil.srQuery(sql);
        return dataList;
    }

    @Override
    public List<Map<String, Object>> getSortedPoolHitRate(String startTime, String endTime, String dbId) {
        QueryWrapper qw = new QueryWrapper(bigDataUtil.getSrDbName(), "MESOracleSortedPoolHitRateCollected_sr_duplicate");
        qw.select("collectedTime", "sorted_pool_hit_rate");
        qw.eq("source_db_id", dbId).between("collectedTime", startTime, endTime).orderByAsc("collectedTime");
        String sql = QueryWrapperHelper.getSql4Phoenix(qw);
        List<Map<String, Object>> dataList = bigDataUtil.srQuery(sql);
        return dataList;
    }

    @Override
    public List<Map<String, Object>> getLogBufferRate(String startTime, String endTime, String dbId) {
        QueryWrapper qw = new QueryWrapper(bigDataUtil.getSrDbName(), "MESOracleLogBufferRateCollected_sr_duplicate");
        qw.select("collectedTime", "log_buffer_rate");
        qw.eq("source_db_id", dbId).between("collectedTime", startTime, endTime).orderByAsc("collectedTime");
        String sql = QueryWrapperHelper.getSql4Phoenix(qw);
        List<Map<String, Object>> dataList = bigDataUtil.srQuery(sql);
        return dataList;
    }

    @Override
    public String getLastCollectedTime(String dbId, String memoryName) {
        QueryWrapper qw = new QueryWrapper(bigDataUtil.getSrDbName(), "T100OracleDatabaseMemory_sr_duplicate");
        qw.select("max(collectedTime) collectedTime");
        qw.eq("source_db_id", dbId).eq("ora_name", memoryName);
        String sql = QueryWrapperHelper.getSql4Phoenix(qw);
        List<Map<String, Object>> dataList = bigDataUtil.srQuery(sql);
        String maxCollectTime = null;
        if (!CollectionUtils.isEmpty(dataList)) {
            maxCollectTime = (String) dataList.get(0).get("collectedTime");
        }
        return maxCollectTime;
    }

    @Override
    public List<Map<String, Object>> getOracleMemory(String startTime, String endTime, String dbId, String memoryName) {
        QueryWrapper qw = new QueryWrapper(bigDataUtil.getSrDbName(), "T100OracleDatabaseMemory_sr_duplicate");
        qw.select("collectedTime", "ora_total", "ora_pctused");
        qw.eq("source_db_id", dbId).eq("ora_name", memoryName).between("collectedTime", startTime, endTime).orderByAsc("collectedTime");
        String sql = QueryWrapperHelper.getSql4Phoenix(qw);
        List<Map<String, Object>> dataList = bigDataUtil.srQuery(sql);
        return dataList;
    }

    @Override
    public Map<String, Object> getOracleInfo(String startTime, String endTime, String dbId) {
        String maxCollectTime = bigDataUtil.getMaxTime("T100OracleDatabaseBasicInformation_sr_duplicate", "collectedTime", "source_db_id",
                dbId, "", endTime);
        if (StringUtils.isEmpty(maxCollectTime)) {
            log.warn("OracleInfo dbId:{} max collectedTime is null", dbId);
            return null;
        }
        QueryWrapper qw = new QueryWrapper(bigDataUtil.getSrDbName(), "T100OracleDatabaseBasicInformation_sr_duplicate");
        qw.select("*");
        qw.eq("source_db_id", dbId).eq("collectedTime", maxCollectTime);
        String sql = QueryWrapperHelper.getSql4Phoenix(qw);
        List<Map<String, Object>> dataList = bigDataUtil.srQuery(sql);
        return CollectionUtils.isEmpty(dataList) ? null : dataList.get(0);
    }

    @Override
    public Map<String, Object> getOracleParameter(String startTime, String endTime, String dbId, String parameterName,
                                                  int pageNum, int pageSize) {
        String maxCollectTime = bigDataUtil.getMaxTime("T100OracleParameterSet_sr_duplicate", "collectedTime", "source_db_id",
                dbId, "", endTime);
        if (StringUtils.isEmpty(maxCollectTime)) {
            log.warn("OracleParameter dbId:{} max collectedTime is null", dbId);
            Map<String, Object> map = new HashMap<>();
            map.put("list", new ArrayList<>());
            map.put("total", 0);
            return map;
        }
        QueryWrapper countQw = new QueryWrapper(bigDataUtil.getSrDbName(), "T100OracleParameterSet_sr_duplicate");
        countQw.select("count(*) total");
        countQw.eq("source_db_id", dbId).eq("collectedTime", maxCollectTime);
        if (!StringUtils.isEmpty(parameterName)) {
            countQw.like("ora_name", parameterName);
        }
        String countSql = QueryWrapperHelper.getSql4Phoenix(countQw);
        List<Map<String, Object>> countDataList = bigDataUtil.srQuery(countSql);
        if (CollectionUtils.isEmpty(countDataList)) {
            Map<String, Object> map = new HashMap<>();
            map.put("list", new ArrayList<>());
            map.put("total", 0);
            return map;
        }
        Map<String, Object> map = countDataList.get(0);
        Integer total = (Integer) map.get("total");
        if (total != null && total == 0) {
            map.put("list", new ArrayList<>());
            return map;
        }
        QueryWrapper qw = new QueryWrapper(bigDataUtil.getSrDbName(), "T100OracleParameterSet_sr_duplicate");
        qw.select("collectedTime", "ora_name", "ora_value", "ora_display_value", "ora_default_value", "ora_isdefault",
                "ora_isbasic", "ora_description");
        qw.eq("source_db_id", dbId).eq("collectedTime", maxCollectTime).orderByAsc("ora_name").page(pageNum, pageSize);
        if (!StringUtils.isEmpty(parameterName)) {
            qw.like("ora_name", parameterName);
        }
        String sql = QueryWrapperHelper.getSql4Phoenix(qw);
        List<Map<String, Object>> dataList = bigDataUtil.srQuery(sql);
        map.put("list", dataList);
        return map;
    }

    @Override
    public List<Map<String, Object>> getResourceUsage(String startTime, String endTime, String dbId, String resourceName) {
        QueryWrapper qw = new QueryWrapper(bigDataUtil.getSrDbName(), "T100OracleDbResourceUsage_sr_duplicate");
        qw.select("collectedTime", "ora_resource_name", "ora_current_utilization");
        qw.eq("source_db_id", dbId).eq("ora_resource_name", resourceName).between("collectedTime", startTime, endTime)
                .orderByAsc("collectedTime");
        String sql = QueryWrapperHelper.getSql4Phoenix(qw);
        List<Map<String, Object>> dataList = bigDataUtil.srQuery(sql);
        return dataList;
    }

    @Override
    public Map<String, Object> getFileStatus(String startTime, String endTime, String dbId, int pageNum, int pageSize) {
        String maxCollectTime = bigDataUtil.getMaxTime("MESOracleFileStatusCollected_sr_duplicate", "collectedTime", "source_db_id",
                dbId, "", endTime);
        if (StringUtils.isEmpty(maxCollectTime)) {
            log.warn("FileStatus dbId:{} max collectedTime is null", dbId);
            Map<String, Object> map = new HashMap<>();
            map.put("list", new ArrayList<>());
            map.put("total", 0);
            return map;
        }
        QueryWrapper countQw = new QueryWrapper(bigDataUtil.getSrDbName(), "MESOracleFileStatusCollected_sr_duplicate");
        countQw.select("count(*) total");
        countQw.eq("source_db_id", dbId).eq("collectedTime", maxCollectTime);
        String countSql = QueryWrapperHelper.getSql4Phoenix(countQw);
        List<Map<String, Object>> countDataList = bigDataUtil.srQuery(countSql);
        if (CollectionUtils.isEmpty(countDataList)) {
            Map<String, Object> map = new HashMap<>();
            map.put("list", new ArrayList<>());
            map.put("total", 0);
            return map;
        }
        Map<String, Object> map = countDataList.get(0);
        Integer total = (Integer) map.get("total");
        if (total != null && total == 0) {
            map.put("list", new ArrayList<>());
            return map;
        }
        QueryWrapper qw = new QueryWrapper(bigDataUtil.getSrDbName(), "MESOracleFileStatusCollected_sr_duplicate");
        qw.select("collectedTime", "db_file_tablespace_name", "db_file_name", "db_file_autoextensible", "db_file_current_size_mb",
                "db_file_max_size_mb", "db_file_status");
        qw.eq("source_db_id", dbId).eq("collectedTime", maxCollectTime)
                .orderByDesc("db_file_current_size_mb").page(pageNum, pageSize);
        String sql = QueryWrapperHelper.getSql4Phoenix(qw);
        List<Map<String, Object>> dataList = bigDataUtil.srQuery(sql);
        map.put("list", dataList);
        return map;
    }

    @Override
    public Map<String, Object> getControlDocument(String startTime, String endTime, String dbId, int pageNum, int pageSize) {
        String maxCollectTime = bigDataUtil.getMaxTime("T100OracleControlDocuments_sr_duplicate", "collectedTime", "source_db_id",
                dbId, "", endTime);
        if (StringUtils.isEmpty(maxCollectTime)) {
            log.warn("ControlDocument dbId:{} max collectedTime is null", dbId);
            Map<String, Object> map = new HashMap<>();
            map.put("list", new ArrayList<>());
            map.put("total", 0);
            return map;
        }
        QueryWrapper countQw = new QueryWrapper(bigDataUtil.getSrDbName(), "T100OracleControlDocuments_sr_duplicate");
        countQw.select("count(*) total");
        countQw.eq("source_db_id", dbId).eq("collectedTime", maxCollectTime);
        String countSql = QueryWrapperHelper.getSql4Phoenix(countQw);
        List<Map<String, Object>> countDataList = bigDataUtil.srQuery(countSql);
        if (CollectionUtils.isEmpty(countDataList)) {
            Map<String, Object> map = new HashMap<>();
            map.put("list", new ArrayList<>());
            map.put("total", 0);
            return map;
        }
        Map<String, Object> map = countDataList.get(0);
        Integer total = (Integer) map.get("total");
        if (total != null && total == 0) {
            map.put("list", new ArrayList<>());
            return map;
        }
        QueryWrapper qw = new QueryWrapper(bigDataUtil.getSrDbName(), "T100OracleControlDocuments_sr_duplicate");
        qw.select("collectedTime", "source_db_id", "ora_file_name", "ora_block_size", "ora_file_size", "ora_rate", "ora_recovery_dest_file");
        qw.eq("source_db_id", dbId).eq("collectedTime", maxCollectTime)
                .orderByAsc("ora_file_name").page(pageNum, pageSize);
        String sql = QueryWrapperHelper.getSql4Phoenix(qw);
        List<Map<String, Object>> dataList = bigDataUtil.srQuery(sql);
        map.put("list", dataList);
        return map;
    }

    @Override
    public Map<String, Object> getRedoLogFile(String startTime, String endTime, String dbId, int pageNum, int pageSize) {
        String maxCollectTime = bigDataUtil.getMaxTime("T100OracleRedoLogFile_sr_duplicate", "collectedTime", "source_db_id",
                dbId, "", endTime);
        if (StringUtils.isEmpty(maxCollectTime)) {
            log.warn("RedoLogFile dbId:{} max collectedTime is null", dbId);
            Map<String, Object> map = new HashMap<>();
            map.put("list", new ArrayList<>());
            map.put("total", 0);
            return map;
        }
        QueryWrapper countQw = new QueryWrapper(bigDataUtil.getSrDbName(), "T100OracleRedoLogFile_sr_duplicate");
        countQw.select("count(*) total");
        countQw.eq("source_db_id", dbId).eq("collectedTime", maxCollectTime);
        String countSql = QueryWrapperHelper.getSql4Phoenix(countQw);
        List<Map<String, Object>> countDataList = bigDataUtil.srQuery(countSql);
        if (CollectionUtils.isEmpty(countDataList)) {
            Map<String, Object> map = new HashMap<>();
            map.put("list", new ArrayList<>());
            map.put("total", 0);
            return map;
        }
        Map<String, Object> map = countDataList.get(0);
        Integer total = (Integer) map.get("total");
        if (total != null && total == 0) {
            map.put("list", new ArrayList<>());
            return map;
        }
        QueryWrapper qw = new QueryWrapper(bigDataUtil.getSrDbName(), "T100OracleRedoLogFile_sr_duplicate");
        qw.select("collectedTime", "source_db_id", "ora_group", "ora_logtype", "ora_member", "ora_bytessm", "ora_blocksize",
                "ora_archived", "ora_status");
        qw.eq("source_db_id", dbId).eq("collectedTime", maxCollectTime)
                .orderByDesc("ora_bytessm").orderByAsc("ora_member").page(pageNum, pageSize);
        String sql = QueryWrapperHelper.getSql4Phoenix(qw);
        List<Map<String, Object>> dataList = bigDataUtil.srQuery(sql);
        map.put("list", dataList);
        return map;
    }

    @Override
    public Map<String, Object> getInvalidObject(String startTime, String endTime, String content, String objType, String dbId, int pageNum, int pageSize) {
        String maxCollectTime = bigDataUtil.getMaxTime("T100OracleInvalidObject_sr_duplicate", "collectedTime", "source_db_id",
                dbId, "", endTime);
        if (StringUtils.isEmpty(maxCollectTime)) {
            log.warn("InvalidObject dbId:{} max collectedTime is null", dbId);
            Map<String, Object> map = new HashMap<>();
            map.put("list", new ArrayList<>());
            map.put("total", 0);
            return map;
        }
        QueryWrapper countQw = new QueryWrapper(bigDataUtil.getSrDbName(), "T100OracleInvalidObject_sr_duplicate");
        countQw.select("count(*) total");
        countQw.eq("source_db_id", dbId).eq("collectedTime", maxCollectTime);
        if (!StringUtils.isEmpty(objType)) {
            countQw.like("ora_object_type", objType);
        }
        String countSql = QueryWrapperHelper.getSql4Phoenix(countQw);
        String likeSql = " and (ora_owner like '%" + content + "%' or ora_object_name like '%" + content + "%')";
        if (!StringUtils.isEmpty(content)) {
            countSql += likeSql;
        }
        List<Map<String, Object>> countDataList = bigDataUtil.srQuery(countSql);
        if (CollectionUtils.isEmpty(countDataList)) {
            Map<String, Object> map = new HashMap<>();
            map.put("list", new ArrayList<>());
            map.put("total", 0);
            return map;
        }
        Map<String, Object> map = countDataList.get(0);
        Integer total = (Integer) map.get("total");
        if (total != null && total == 0) {
            map.put("list", new ArrayList<>());
            return map;
        }
        QueryWrapper qw = new QueryWrapper(bigDataUtil.getSrDbName(), "T100OracleInvalidObject_sr_duplicate");
        qw.select("collectedTime", "source_db_id", "ora_owner", "ora_object_name", "ora_object_type", "ora_created", "ora_last_ddl_time");
        qw.eq("source_db_id", dbId).eq("collectedTime", maxCollectTime);
        if (!StringUtils.isEmpty(objType)) {
            qw.like("ora_object_type", objType);
        }
        String sql = QueryWrapperHelper.getSql4Phoenix(qw);
        if (!StringUtils.isEmpty(content)) {
            sql += likeSql;
        }
        sql = sql + " order by ora_object_type asc, ora_object_name asc " +
                " limit " + (pageNum - 1) * pageSize + "," + pageSize;
        List<Map<String, Object>> dataList = bigDataUtil.srQuery(sql);
        map.put("list", dataList);
        return map;
    }

    @Override
    public Map<String, Object> getBigTable(String startTime, String endTime, String dbId, String tsName, String tbName, String tbType, int pageNum, int pageSize) {
        String maxCollectTime = bigDataUtil.getMaxTime("T100OracleBigtable_sr_duplicate", "collectedTime", "source_db_id",
                dbId, "", endTime);
        if (StringUtils.isEmpty(maxCollectTime)) {
            log.warn("InvalidObject dbId:{} max collectedTime is null", dbId);
            Map<String, Object> map = new HashMap<>();
            map.put("list", new ArrayList<>());
            map.put("total", 0);
            return map;
        }
        QueryWrapper countQw = new QueryWrapper(bigDataUtil.getSrDbName(), "T100OracleBigtable_sr_duplicate");
        countQw.select("count(*) total");
        countQw.eq("source_db_id", dbId).eq("collectedTime", maxCollectTime);
        if (!StringUtils.isEmpty(tsName)) {
            countQw.like("ora_tablespace_name", tsName);
        }
        if (!StringUtils.isEmpty(tbName)) {
            countQw.like("ora_segment_name", tbName);
        }
        if (!StringUtils.isEmpty(tbType)) {
            countQw.like("ora_segment_type", tbType);
        }
        String countSql = QueryWrapperHelper.getSql4Phoenix(countQw);
        List<Map<String, Object>> countDataList = bigDataUtil.srQuery(countSql);
        if (CollectionUtils.isEmpty(countDataList)) {
            Map<String, Object> map = new HashMap<>();
            map.put("list", new ArrayList<>());
            map.put("total", 0);
            return map;
        }
        Map<String, Object> map = countDataList.get(0);
        Integer total = (Integer) map.get("total");
        if (total != null && total == 0) {
            map.put("list", new ArrayList<>());
            return map;
        }
        QueryWrapper qw = new QueryWrapper(bigDataUtil.getSrDbName(), "T100OracleBigtable_sr_duplicate");
        qw.select("collectedTime", "source_db_id", "ora_tablespace_name", "ora_owner", "ora_segment_type", "ora_segment_name", "ora_bytesg");
        qw.eq("source_db_id", dbId).eq("collectedTime", maxCollectTime);
        if (!StringUtils.isEmpty(tsName)) {
            qw.like("ora_tablespace_name", tsName);
        }
        if (!StringUtils.isEmpty(tbName)) {
            qw.like("ora_segment_name", tbName);
        }
        if (!StringUtils.isEmpty(tbType)) {
            qw.like("ora_segment_type", tbType);
        }
        qw.orderByDesc("ora_bytesg").page(pageNum, pageSize);
        String sql = QueryWrapperHelper.getSql4Phoenix(qw);
        List<Map<String, Object>> dataList = bigDataUtil.srQuery(sql);
        map.put("list", dataList);
        return map;
    }

    @Override
    public Map<String, Object> getArchiveSpace(String startTime, String endTime, String dbId) {
        String maxCollectTime = bigDataUtil.getMaxTime("T100OracleArchiveSpace_sr_duplicate", "collectedTime", "source_db_id",
                dbId, "", endTime);
        if (StringUtils.isEmpty(maxCollectTime)) {
            log.warn("ArchiveSpace dbId:{} max collectedTime is null", dbId);
            return null;
        }
        QueryWrapper qw = new QueryWrapper(bigDataUtil.getSrDbName(), "T100OracleArchiveSpace_sr_duplicate");
        qw.select("*");
        qw.eq("source_db_id", dbId).eq("collectedTime", maxCollectTime);
        String sql = QueryWrapperHelper.getSql4Phoenix(qw);
        List<Map<String, Object>> dataList = bigDataUtil.srQuery(sql);
        return CollectionUtils.isEmpty(dataList) ? null : dataList.get(0);
    }

    @Override
    public Map<String, Object> getTableSpace(String startTime, String endTime, String dbId, int pageNum, int pageSize) {
//        String dbName = "TW".equalsIgnoreCase(area) ? "OracleDBTableSpaceV2" : "OracleDBTableSpaceCollected_sr_duplicate";
        String dbName = "OracleDBTableSpaceV2";
        String maxCollectTime = bigDataUtil.getMaxTime(dbName, "collectedTime", "source_db_id",
                dbId, "", endTime);
        if (StringUtils.isEmpty(maxCollectTime)) {
            log.warn("TableSpace dbId:{} max collectedTime is null", dbId);
            Map<String, Object> map = new HashMap<>();
            map.put("list", new ArrayList<>());
            map.put("total", 0);
            return map;
        }
        String countSelectSql = " select count(*) total";
        String fromSql = " from " + bigDataUtil.getSrDbName() + "." + dbName + " osd";
        String whereSql = " where osd.source_db_id='" + dbId + "' and osd.collectedTime='" + maxCollectTime + "'";
        String countSql = countSelectSql + fromSql + whereSql;
        List<Map<String, Object>> countDataList = bigDataUtil.srQuery(countSql);
        if (CollectionUtils.isEmpty(countDataList)) {
            Map<String, Object> map = new HashMap<>();
            map.put("list", new ArrayList<>());
            map.put("total", 0);
            return map;
        }
        Map<String, Object> map = countDataList.get(0);
        Integer total = (Integer) map.get("total");
        if (total != null && total == 0) {
            map.put("list", new ArrayList<>());
            return map;
        }
        LocalDateTime maxDateTime = DateUtil.tryParseLocalDateTime(maxCollectTime).get();
        String beforeDate = DateUtil.getSomeDateFormatString(maxDateTime.minusDays(1L), DateUtil.DATE_FORMATTER);
        String maxBeforeCollectTime = bigDataUtil.getMaxTime(dbName, "collectedTime", "source_db_id",
                dbId, beforeDate + " 00:00:00", beforeDate + " 23:59:59");

        String selectSql = " select osd.source_db_id,osd.collectedTime,osd.sum_MB, osd.used_MB,osd.free_MB, osd.percent_used, " +
                " osd.tablespace_name,osd.tbs_type,osd.tablespace_status";
        String orderSql = " order by osd.tbs_type asc,osd.percent_used desc";
        String limitSql = " limit " + (pageNum - 1) * pageSize + "," + pageSize;
        String sql;
        if (!StringUtils.isEmpty(maxBeforeCollectTime)) {
            selectSql += ",osd2.used_MB before_used_MB,osd.used_MB-osd2.used_MB addData";
            String joinSql = " left join (select source_db_id,collectedTime,sum_MB, used_MB,free_MB, percent_used, tablespace_name" +
                    " from " + bigDataUtil.getSrDbName() + "." + dbName +
                    " where source_db_id='" + dbId + "' and collectedTime='" + maxBeforeCollectTime + "') osd2" +
                    " on osd.source_db_id=osd2.source_db_id and osd.tablespace_name=osd2.tablespace_name";
            sql = selectSql + fromSql + joinSql + whereSql;
        } else {
            selectSql += ",0 before_used_MB,0 addData";
            sql = selectSql + fromSql + whereSql;
        }
        sql = sql + orderSql + limitSql;
        List<Map<String, Object>> data = bigDataUtil.srQuery(sql);
        map.put("list", data);
        return map;
    }

    @Override
    public Map<String, Object> getSchemaUsage(Query query) {
        String fullTableName = query.getDbName() + "." + query.getTableName();
        String finialSelectSQL = "SELECT \n" +
                "  totalName.oracle_schema oracle_schema,\n" +
                "  \n" +
                "  today.current_size_mb today_used,\n" +
                "  COALESCE(today.default_tablespace, 30day.default_tablespace, 60day.default_tablespace, 90day.default_tablespace, 180day.default_tablespace, 360day.default_tablespace) default_tablespace,\n" +
                "  \n" +
                "  COALESCE(today.collectedTime, NOW()) todayTime,\n" +
                "  \n" +
                "  COALESCE(30day.collectedTime, NOW() - INTERVAL 30 DAY) past30dayTime,\n" +
                "  30day.current_size_mb 30day_used,\n" +
                "  \n" +
                "  COALESCE(60day.collectedTime, NOW() - INTERVAL 60 DAY) past60dayTime,\n" +
                "  60day.current_size_mb 60day_used,\n" +
                "  \n" +
                "  COALESCE(90day.collectedTime, NOW() - INTERVAL 90 DAY) past90dayTime,\n" +
                "  90day.current_size_mb 90day_used,\n" +
                "  \n" +
                "  COALESCE(180day.collectedTime, NOW() - INTERVAL 180 DAY) past180dayTime,\n" +
                "  180day.current_size_mb 180day_used,\n" +
                "  \n" +
                "  COALESCE(360day.collectedTime, NOW() - INTERVAL 360 DAY) past360dayTime,\n" +
                "  360day.current_size_mb 360day_used\n" +
                "FROM\n";
        String commonSelectSQL = "  SELECT \n" +
                "    oracle_schema,\n" +
                "    current_size_mb,\n" +
                "    collectedTime,\n" +
                "    default_tablespace,\n" +
                "    PCT_USED\n" +
                "  FROM\n" + fullTableName;
        String commonWhereSQL = " WHERE\n" + QueryWrapperHelper.getBusinessCondition4Sql(query.getBusinessCondition());

        //TIME_RANGE2 > TIME_RANGE1
        String collectedTimeSQL = " AND collectedTime = ( SELECT MAX(collectedTime) FROM " + fullTableName +
                commonWhereSQL + " AND collectedTime BETWEEN TIME_RANGE1 AND TIME_RANGE2 " + ")";
        String time = query.getAdditionalInfoParamList().get(0).getSourceTargetMap().get("Time");
        if (!"NOW()".equals(time)) {
            time = "'" + time + "'";
        }

        String totalTablespaceNameSQL = "  (\n" +
                "  SELECT DISTINCT\n" +
                "    oracle_schema\n" +
                "  FROM\n" +
                fullTableName +
                commonWhereSQL +
                "    AND\n" +
                "    collectedTime BETWEEN " + time + " - INTERVAL 360 DAY AND " + time + "\n" +
                "  ) totalName";

        String orderSQL = "\nORDER BY oracle_schema";

        // 組合資料查詢區間
        String SQL = finialSelectSQL + totalTablespaceNameSQL;
        //今天
        SQL = SQL + "\nLEFT JOIN\n(" +
                commonSelectSQL +
                commonWhereSQL +
                collectedTimeSQL.replace("TIME_RANGE1", time + " - INTERVAL 1 DAY")
                        .replace("TIME_RANGE2", time) + ") today ON today.oracle_schema = totalName.oracle_schema";
        //要以最靠近XX天前的時間點為準
        collectedTimeSQL = collectedTimeSQL.replace("MAX", "MIN");
        //30天
        SQL = SQL + "\nLEFT JOIN\n(" +
                commonSelectSQL +
                commonWhereSQL +
                collectedTimeSQL.replace("TIME_RANGE1", time + " - INTERVAL 30 DAY")
                        .replace("TIME_RANGE2", time + " - INTERVAL 1 DAY") + ") 30day ON 30day.oracle_schema = totalName.oracle_schema";
        //60天
        SQL = SQL + "\nLEFT JOIN\n(" +
                commonSelectSQL +
                commonWhereSQL +
                collectedTimeSQL.replace("TIME_RANGE1", time + " - INTERVAL 60 DAY")
                        .replace("TIME_RANGE2", time + " - INTERVAL 30 DAY") + ") 60day ON 60day.oracle_schema = totalName.oracle_schema";
        //90天
        SQL = SQL + "\nLEFT JOIN\n(" +
                commonSelectSQL +
                commonWhereSQL +
                collectedTimeSQL.replace("TIME_RANGE1", time + " - INTERVAL 90 DAY")
                        .replace("TIME_RANGE2", time + " - INTERVAL 60 DAY") + ") 90day ON 90day.oracle_schema = totalName.oracle_schema";
        //180天
        SQL = SQL + "\nLEFT JOIN\n(" +
                commonSelectSQL +
                commonWhereSQL +
                collectedTimeSQL.replace("TIME_RANGE1", time + " - INTERVAL 180 DAY")
                        .replace("TIME_RANGE2", time + " - INTERVAL 90 DAY") + ") 180day ON 180day.oracle_schema = totalName.oracle_schema";
        //360天
        SQL = SQL + "\nLEFT JOIN\n(" +
                commonSelectSQL +
                commonWhereSQL +
                collectedTimeSQL.replace("TIME_RANGE1", time + " - INTERVAL 360 DAY")
                        .replace("TIME_RANGE2", time + " - INTERVAL 180 DAY") + ") 360day ON 360day.oracle_schema = totalName.oracle_schema";

        SQL += orderSQL;
        List<Map<String, Object>> dataList = bigDataUtil.srQuery(SQL);

        //取出採集資料的時間點，查無資料帶入default時間
        List<String> dateKey = Arrays.asList("todayTime", "past30dayTime", "past60dayTime", "past90dayTime", "past180dayTime", "past360dayTime");
        Map<String, Object> defaultValues = new HashMap<>();
        LocalDateTime todayDateTime = DateUtil.tryParseLocalDateTime(time).orElse(LocalDateTime.now());
        defaultValues.put("todayTime", DateUtil.getSomeDateFormatString(todayDateTime, DateUtil.DATE_TIME_FORMATTER));
        defaultValues.put("past30dayTime", DateUtil.getSomeDateFormatString(todayDateTime.minusDays(30), DateUtil.DATE_TIME_FORMATTER));
        defaultValues.put("past60dayTime", DateUtil.getSomeDateFormatString(todayDateTime.minusDays(60), DateUtil.DATE_TIME_FORMATTER));
        defaultValues.put("past90dayTime", DateUtil.getSomeDateFormatString(todayDateTime.minusDays(90), DateUtil.DATE_TIME_FORMATTER));
        defaultValues.put("past180dayTime", DateUtil.getSomeDateFormatString(todayDateTime.minusDays(180), DateUtil.DATE_TIME_FORMATTER));
        defaultValues.put("past360dayTime", DateUtil.getSomeDateFormatString(todayDateTime.minusDays(360), DateUtil.DATE_TIME_FORMATTER));

        if (CollectionUtil.isEmpty(dataList)) {
            Map<String, Object> map = new HashMap<>();
            map.put("totalUsage", new HashMap<>());
            map.put("list", new ArrayList<>());
            map.put("date", defaultValues);
            map.put("total", 0);
            return map;
        }

        Map<String, Object> dateMap = dataList.get(0).entrySet()
                .stream()
                .filter(entry -> dateKey.contains(entry.getKey()))
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> Objects.nonNull(entry.getValue()) ? entry.getValue() : defaultValues.getOrDefault(entry.getKey(), "defaultValues") // Get the default value based on the key
                ));

        List<Map<String, Object>> usageInfo = dataList.stream().map(dataMap -> {
            Map<String, Object> result = new HashMap<>();

            String oracle_schema = StringUtil.toString(dataMap.getOrDefault("oracle_schema", null));
            String default_tablespace = StringUtil.toString(dataMap.getOrDefault("default_tablespace", null));
            Double today = DoubleUtil.objectToDouble(dataMap.get("today_used"), null);
            Double past30day = DoubleUtil.objectToDouble(dataMap.get("30day_used"), null);
            Double past60day = DoubleUtil.objectToDouble(dataMap.get("60day_used"), null);
            Double past90day = DoubleUtil.objectToDouble(dataMap.get("90day_used"), null);
            Double past180day = DoubleUtil.objectToDouble(dataMap.get("180day_used"), null);
            Double past360day = DoubleUtil.objectToDouble(dataMap.get("360day_used"), null);

            result.put("oracle_schema", oracle_schema);
            result.put("today", today);
            result.put("default_tablespace", default_tablespace);

            result.put("past30day", past30day);
            result.put("past30dayUpgrade", calculatedUpgrade(today, past30day));

            result.put("past60day", past60day);
            result.put("past60dayUpgrade", calculatedUpgrade(today, past60day));

            result.put("past90day", past90day);
            result.put("past90dayUpgrade", calculatedUpgrade(today, past90day));

            result.put("past180day", past180day);
            result.put("past180dayUpgrade", calculatedUpgrade(today, past180day));

            result.put("past360day", past360day);
            result.put("past360dayUpgrade", calculatedUpgrade(today, past360day));
            return result;
        }).collect(Collectors.toList());


        //排序條件
        if (CollectionUtil.isNotEmpty(query.getOrderFields())) {
            List<String> stringFields = Arrays.asList("oracle_schema", "default_tablespace");

            Comparator<Map<String, Object>> combinedComparator = query.getOrderFields().stream()
                    .map(orderField -> {
                        String column = orderField.getColumn();
                        Boolean isAscending = "asc".equalsIgnoreCase(orderField.getOrd());

                        Comparator<Map<String, Object>> comparator = stringFields.contains(column)
                                ? Comparator.comparing(
                                map -> StringUtil.toString(map.get(column)), Comparator.nullsFirst(Comparator.naturalOrder()))
                                : Comparator.comparing(
                                map -> DoubleUtil.objectToDouble(map.get(column), null), Comparator.nullsFirst(Comparator.naturalOrder()));
                        return isAscending ? comparator : comparator.reversed();
                    }).reduce(Comparator::thenComparing)
                    .orElseThrow(() -> new IllegalStateException("無比較器可用"));

            usageInfo.sort(combinedComparator);
        } else {
            //沒有排序條件時，預設以today desc排序
            usageInfo.sort(Comparator.comparing((Map<String, Object> o) -> DoubleUtil.objectToDouble(o.get("today"))).reversed());
        }

        Map<String, Object> map = new HashMap<>();
        int startIndex = (query.getPageIndex() - 1) * query.getPageSize();
        int endIndex = Math.min(query.getPageIndex() * query.getPageSize(), usageInfo.size());

        Map<String, Double> totalUsage = getTotalSchemaUsage(usageInfo);

        map.put("totalUsage", totalUsage);
        map.put("list", usageInfo.subList(startIndex, endIndex));
        map.put("date", dateMap);
        map.put("total", usageInfo.size());
        return map;
    }

    private Double calculatedUpgrade(Double num1, Double num2) {
        if (Objects.isNull(num1) || Objects.isNull(num2) || num1 == 0 || num2 == 0) {
            return null;
        }
        return Math.round((num1 - num2) * 100 / num1 * 100) / 100.0;

    }


    private Map<String, Double> getTotalSchemaUsage(List<Map<String, Object>> list) {
        Map<String, Double> totalUsage = new HashMap<>();
        //預設為null
        totalUsage.put("today", null);
        totalUsage.put("past30day", null);
        totalUsage.put("past60day", null);
        totalUsage.put("past90day", null);
        totalUsage.put("past180day", null);
        totalUsage.put("past360day", null);
        //若是加總後有值則修改
        list.stream()
                .filter(stringObjectMap -> Objects.nonNull(stringObjectMap.get("today")))
                .mapToDouble(map -> DoubleUtil.objectToDouble(map.get("today")))
                .reduce(Double::sum)
                .ifPresent(sum -> totalUsage.put("today", DoubleUtil.objectToDouble(Math.round(sum / 1000 * 100)) / 100));
        list.stream()
                .filter(stringObjectMap -> Objects.nonNull(stringObjectMap.get("past30day")))
                .mapToDouble(map -> DoubleUtil.objectToDouble(map.get("past30day")))
                .reduce(Double::sum)
                .ifPresent(sum -> totalUsage.put("past30day", DoubleUtil.objectToDouble(Math.round(sum / 1000 * 100)) / 100));
        list.stream()
                .filter(stringObjectMap -> Objects.nonNull(stringObjectMap.get("past60day")))
                .mapToDouble(map -> DoubleUtil.objectToDouble(map.get("past60day")))
                .reduce(Double::sum)
                .ifPresent(sum -> totalUsage.put("past60day", DoubleUtil.objectToDouble(Math.round(sum / 1000 * 100)) / 100));
        list.stream()
                .filter(stringObjectMap -> Objects.nonNull(stringObjectMap.get("past90day")))
                .mapToDouble(map -> DoubleUtil.objectToDouble(map.get("past90day")))
                .reduce(Double::sum)
                .ifPresent(sum -> totalUsage.put("past90day", DoubleUtil.objectToDouble(Math.round(sum / 1000 * 100)) / 100));
        list.stream()
                .filter(stringObjectMap -> Objects.nonNull(stringObjectMap.get("past180day")))
                .mapToDouble(map -> DoubleUtil.objectToDouble(map.get("past180day")))
                .reduce(Double::sum)
                .ifPresent(sum -> totalUsage.put("past180day", DoubleUtil.objectToDouble(Math.round(sum / 1000 * 100)) / 100));
        list.stream()
                .filter(stringObjectMap -> Objects.nonNull(stringObjectMap.get("past360day")))
                .mapToDouble(map -> DoubleUtil.objectToDouble(map.get("past360day")))
                .reduce(Double::sum)
                .ifPresent(sum -> totalUsage.put("past360day", DoubleUtil.objectToDouble(Math.round(sum / 1000 * 100)) / 100));


        totalUsage.put("past30dayUpgrade", calculatedUpgrade(totalUsage.get("today"), totalUsage.get("past30day")));

        totalUsage.put("past60dayUpgrade", calculatedUpgrade(totalUsage.get("today"), totalUsage.get("past60day")));

        totalUsage.put("past90dayUpgrade", calculatedUpgrade(totalUsage.get("today"), totalUsage.get("past90day")));

        totalUsage.put("past180dayUpgrade", calculatedUpgrade(totalUsage.get("today"), totalUsage.get("past180day")));

        totalUsage.put("past360dayUpgrade", calculatedUpgrade(totalUsage.get("today"), totalUsage.get("past360day")));

        return totalUsage;
    }

    @Override
    public List<String> getAllSchema(String dbId) {
        String selectSQL = "SELECT DISTINCT oracle_schema FROM " + bigDataUtil.getSrDbName() + ".OracleSchemaStorageCollected ";
        String whereSQL = "WHERE source_db_id = '" + dbId + "' AND collectedTime BETWEEN NOW() - INTERVAL 360 DAY AND NOW()";
        String orderSQL = " ORDER BY oracle_schema";
        List<Map<String, Object>> mapList = bigDataUtil.srQuery(selectSQL + whereSQL + orderSQL);
        return mapList.stream()
                .map(map -> StringUtil.toString(map.get("oracle_schema")))
                .collect(Collectors.toList());
    }

    @Override
    public List<String> getAllTablespace(String dbId) {
        String selectSQL = "SELECT DISTINCT tablespace_name FROM " + bigDataUtil.getSrDbName() + ".OracleStorageCollected ";
        String whereSQL = "WHERE source_db_id = '" + dbId + "' AND collectedTime BETWEEN NOW() - INTERVAL 360 DAY AND NOW()";
        String orderSQL = " ORDER BY tablespace_name";
        List<Map<String, Object>> mapList = bigDataUtil.srQuery(selectSQL + whereSQL + orderSQL);
        return mapList.stream()
                .map(map -> StringUtil.toString(map.get("tablespace_name")))
                .collect(Collectors.toList());
    }

    @Override
    public List<String> getAllDefaultTablespace(String dbId) {
        String selectSQL = "SELECT DISTINCT default_tablespace FROM " + bigDataUtil.getSrDbName() + ".OracleSchemaStorageCollected ";
        String whereSQL = "WHERE source_db_id = '" + dbId + "' AND collectedTime BETWEEN NOW() - INTERVAL 360 DAY AND NOW() AND default_tablespace IS NOT NULL";
        String orderSQL = " ORDER BY default_tablespace";

        List<Map<String, Object>> mapList = bigDataUtil.srQuery(selectSQL + whereSQL + orderSQL);
        return mapList.stream()
                .map(map -> StringUtil.toString(map.get("default_tablespace")))
                .collect(Collectors.toList());
    }

    @Override
    public List<Map<String, Object>> getRedoCnt(String startTime, String endTime, String dbId,
                                                long interval, UnitType timeUnit) {
        List<Map<String, Object>> dataList;
        String realStartTime, realEndTime;
        LocalDateTime fillStartTime, fillEndTime;
        if (startTime.equals(endTime)) {
            QueryWrapper qw = new QueryWrapper(bigDataUtil.getSrDbName(), "T100OracleSwitchingTimes_sr_primary");
            qw.select("ora_datetime", "ora_cnt");
            realStartTime = startTime + " 00:00:00";
            realEndTime = startTime + " 23:59:59";
            fillStartTime = DateUtil.tryParseLocalDateTime(realStartTime).get();
            fillEndTime = DateUtil.tryParseLocalDateTime(realEndTime).get();
            qw.eq("source_db_id", dbId).between("ora_datetime", realStartTime, realEndTime).orderByAsc("ora_datetime");
            String sql = QueryWrapperHelper.getSql4Phoenix(qw);
            dataList = bigDataUtil.srQuery(sql);
        } else {
            realEndTime = bigDataUtil.getMaxTime("T100OracleSwitchingTimes_sr_primary", "ora_datetime", "source_db_id",
                    dbId, "", endTime);
            if (StringUtils.isEmpty(realEndTime)) {
                log.warn("RedoCnt dbId:{} max ora_datetime is null", dbId);
                return null;
            }
            LocalDateTime maxDateTime = DateUtil.tryParseLocalDateTime(realEndTime).get();
            realStartTime = DateUtil.getSomeDateFormatString(maxDateTime.minusDays(1L), DateUtil.DATE_TIME_FORMATTER);
            fillStartTime = DateUtil.tryParseLocalDateTime(realStartTime).get().plusHours(1L);
            fillEndTime = DateUtil.tryParseLocalDateTime(realEndTime).get().plusHours(1L);
            QueryWrapper qw = new QueryWrapper(bigDataUtil.getSrDbName(), "T100OracleSwitchingTimes_sr_primary");
            qw.select("ora_datetime", "ora_cnt");
            qw.eq("source_db_id", dbId).LE("ora_datetime", realEndTime).GT("ora_datetime", realStartTime).orderByAsc("ora_datetime");
            String sql = QueryWrapperHelper.getSql4Phoenix(qw);
            dataList = bigDataUtil.srQuery(sql);
        }
        return bigDataUtil.fillChartData(dataList, "ora_datetime", "ora_cnt", 0, fillStartTime,
                fillEndTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"), interval, timeUnit);
    }

    @Override
    public List<Map<String, Object>> getRedoDayCnt(String startTime, String endTime, String dbId) {
        String realEndTime = bigDataUtil.getMaxTime("T100OracleSwitchingTimes_sr_primary", "ora_datetime", "source_db_id",
                dbId, "", endTime);
        if (StringUtils.isEmpty(realEndTime)) {
            log.warn("RedoDayCnt dbId:{} max ora_datetime is null", dbId);
            return null;
        }
        LocalDateTime endDateTime = DateUtil.tryParseLocalDateTime(realEndTime).get();
        LocalDateTime beginDateTime = endDateTime.minusDays(6);
        LocalDate endDate = endDateTime.toLocalDate();
        LocalDate beginDate = endDate.minusDays(7);
        String beginDateStr = DateUtil.getSomeDateFormatString(beginDate, DateUtil.DATE_FORMATTER) + " 00:00:00";
        String endDateStr = DateUtil.getSomeDateFormatString(endDate, DateUtil.DATE_FORMATTER) + " 23:59:59";
        QueryWrapper qw = new QueryWrapper(bigDataUtil.getSrDbName(), "T100OracleSwitchingTimes_sr_primary");
        qw.select("to_date(ora_datetime) ora_date", "source_db_id", "sum(ora_cnt) ora_cnt");
        qw.eq("source_db_id", dbId).between("ora_datetime", beginDateStr, endDateStr).groupBy("ora_date", "source_db_id").orderByAsc("ora_date");
        String sql = QueryWrapperHelper.getSql4Phoenix(qw);
        List<Map<String, Object>> dataList = bigDataUtil.srQuery(sql);
        List<Map<String, Object>> chartData = bigDataUtil.fillChartData(dataList, "ora_date", "ora_cnt",
                0, beginDate.atTime(0, 0, 0), endDate.plusDays(1L).atTime(0, 0, 0),
                DateTimeFormatter.ofPattern("yyyy-MM-dd"), 1, UnitType.DAY);
        return chartData;
    }

    @Override //活動會話
    public Map<String, Object> getRedoLineTotelLog(Integer page, Integer size, String dbId, String startTime, String endTime, Integer maxConnection, String resourceName) {
        String adjustedStartTime = ""; // 調整後開始時間 (string)
        String adjustedEndTime = ""; // 調整後結束時間 (string)
        LocalDateTime adjustedStart; // 調整後開始時間 (LocalDateTime)
        LocalDateTime adjustedEnd; // 調整後結束時間 (LocalDateTime)
        // 當 startTime 和 endTime 有輸入值時，執行時間調整
        if (!StringUtils.isEmpty(startTime)) {
            LocalDateTime originalStart = LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            // 往前拉 20 分鐘
            adjustedStart = originalStart.minusMinutes(20);
            // 將調整後的時間轉回字符串格式
            adjustedStartTime = DateUtil.getSomeDateFormatString(adjustedStart, DateUtil.DATE_TIME_FORMATTER);
        }

        if (!StringUtils.isEmpty(endTime)) {
            LocalDateTime originalEnd = LocalDateTime.parse(endTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            // 往後拉 20 分鐘
            adjustedEnd = originalEnd.plusMinutes(20);
            // 將調整後的時間轉回字符串格式
            adjustedEndTime = DateUtil.getSomeDateFormatString(adjustedEnd, DateUtil.DATE_TIME_FORMATTER);
        }

        // 查詢第一個表 (使用調用方法)
        QueryWrapper qw = new QueryWrapper(bigDataUtil.getSrDbName(), "T100OracleDbResourceUsage_sr_duplicate");
        qw.select("collectedTime", "ora_resource_name", "ora_current_utilization", "ora_limit_value", "source_db_id");
        qw.eq("source_db_id", dbId);
        qw.eq("ora_resource_name", resourceName);
        // 加入調整後的時間範圍
        if (!StringUtils.isEmpty(adjustedEndTime))
            qw.GE("collectedTime", adjustedStartTime);
        if (!StringUtils.isEmpty(adjustedEndTime))
            qw.LE("collectedTime", adjustedEndTime);
        qw.orderByDesc("collectedTime");

        String sql = QueryWrapperHelper.getSql4Phoenix(qw);
        List<Map<String, Object>> dataList = bigDataUtil.srQuery(sql);

        // 分組處理：按10分鐘區間分組，找到每組最大值
        Map<String, Map<String, Object>> groupedData = new LinkedHashMap<>();
        dataList.forEach(row -> {
            String collectedTime = ObjectUtils.toString(row.get("collectedTime")); // 收集時間
            // 計算分組鍵 (10分鐘區間)
            String groupKey = getIntervaTime(collectedTime, "start");
            // 取10分鐘區間內最大值
            if (StringUtils.isNotEmpty(groupKey)) {
                Map<String, Object> existingRow = groupedData.get(groupKey);
                if (existingRow == null || IntegerUtil.objectToInteger(row.get("ora_current_utilization")) > IntegerUtil.objectToInteger(existingRow.get("ora_current_utilization"))) {
                    groupedData.put(groupKey, row);
                }
            }
        });

        // 提取分組後的資料
        List<Map<String, Object>> filteredDataList = new ArrayList<>(groupedData.values());


        filteredDataList.forEach(row -> {
            // 計算是否超過 90%
            if (IntegerUtil.isNotEmpty(maxConnection)) {
                Integer currentUtilization = IntegerUtil.objectToInteger(row.get("ora_current_utilization"), null); // 當前使用量

                if (IntegerUtil.isNotEmpty(currentUtilization) && maxConnection > 0)
                    row.put("exceedsConnection90Percent", currentUtilization >= maxConnection * 0.9);
                else
                    row.put("exceedsConnection90Percent", false);
            }

            // 計算並新增 intervalTime
            String collectedTime = (String) row.get("collectedTime");
            row.put("intervalTime", getIntervaTime(collectedTime, "end"));
        });

        // 將 startTime 和 endTime 轉換成 LocalDateTime 格式
        LocalDateTime start = null;
        LocalDateTime end;
        if (!StringUtils.isEmpty(startTime))
            start = LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        if (!StringUtils.isEmpty(endTime))
            end = LocalDateTime.parse(endTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        else
            end = LocalDateTime.now();
        // 根據 startTime 和 endTime 篩選輸出列表 (不讓值超出範圍)
        LocalDateTime fromTime = start;
        LocalDateTime toTime = end;
        filteredDataList = filteredDataList.stream()
                .filter(row -> {
                    String intervalTime = ObjectUtils.toString(row.get("intervalTime"));
                    LocalDateTime interval = LocalDateTime.parse(intervalTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    // 檢查 interval 是否在 start 和 end 之間
                    return (Objects.isNull(fromTime) || !interval.isBefore(fromTime)) && !interval.isAfter(toTime);
                })
                .collect(Collectors.toList());

        // 計算分頁條件
        int total = filteredDataList.size();
        int offset = (page - 1) * size;
        int fromIndex = Math.min(offset, total);
        int toIndex = Math.min(fromIndex + size, total);
        List<Map<String, Object>> paginatedResult = filteredDataList.subList(fromIndex, toIndex);

        // 封裝返回結果
        Map<String, Object> result = new HashMap<>();
        result.put("code", "0");
        result.put("errMsg", "success");

        Map<String, Object> data = new HashMap<>();
        data.put("page", page);
        data.put("pagesize", size);
        data.put("total", total);
        data.put("list", paginatedResult);

        result.put("data", data);
        return result;
    }

    @Override
    public Map<String, Object> getRedoSwitchLog(int page, int size, String dbId, String startTime, String endTime) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 初始化查詢條件
        QueryWrapper qw = new QueryWrapper("servicecloud.T100OracleSwitchingTimes_sr_primary");
        qw.select("ora_cnt", "ora_datetime") // 只選擇所需的欄位
                .eq("source_db_id", dbId);          // 篩選資料庫 ID
        // 創建完整數據的查詢條件
        QueryWrapper fullQw = new QueryWrapper("servicecloud.T100OracleSwitchingTimes_sr_primary");
        fullQw.select("ora_cnt", "ora_datetime")
                .eq("source_db_id", dbId);  // 與 `qw` 相同的初始條件

        if (startTime != null && !startTime.isEmpty() && endTime != null && !endTime.isEmpty()) {
            qw.between("ora_datetime", startTime, endTime); // 篩選時間範圍
        }
        qw.orderByDesc("ora_datetime");      // 以時間降序排列

        // 完整數據不受時間範圍限制
        fullQw.orderByDesc("ora_datetime");
        // 查詢數據
        String fullSql = QueryWrapperHelper.getSql4Phoenix(fullQw);
        List<Map<String, Object>> dataListAll = bigDataUtil.srQuery(fullSql);

        // 打印生成的 SQL
        String sql = QueryWrapperHelper.getSql4Phoenix(qw);
        List<Map<String, Object>> dataList = bigDataUtil.srQuery(sql);

        if (dataList == null || dataList.isEmpty()) {
            Map<String, Object> paginatedData = new HashMap<>();
            paginatedData.put("total", 0);
            paginatedData.put("pagesize", size);
            paginatedData.put("page", page);
            paginatedData.put("list", Collections.emptyList());

            Map<String, Object> result = new HashMap<>();
            result.put("code", "0");
            result.put("errMsg", "success");
            result.put("data", paginatedData);
            return result;
        }
        // 總筆數
        int totalCount = dataList.size();
        // 處理分頁邏輯
        int offset = (page - 1) * size;
        List<Map<String, Object>> paginatedList = dataList.stream()
                .skip(offset)
                .limit(size)
                .map(data -> {
                    // 計算當前數據的 ora_datetime 前 14 天的平均值
                    String currentDatetime = (String) data.get("ora_datetime");
                    LocalDateTime currentTime = LocalDateTime.parse(currentDatetime, formatter);
                    LocalDateTime startRange = currentTime.minusDays(14);
                    // 篩選前 14 天的數據
                    List<Map<String, Object>> pastData = dataListAll.stream()
                            .filter(past -> {
                                String pastDatetime = (String) past.get("ora_datetime");
                                LocalDateTime pastTime = LocalDateTime.parse(pastDatetime, formatter);
                                return !pastTime.isBefore(startRange) && pastTime.isBefore(currentTime);
                            })
                            .collect(Collectors.toList());
                    // 計算平均值並無條件捨棄
                    int avgSwitchCount = (int) Math.ceil(pastData.stream()
                            .mapToDouble(past -> ((Number) past.get("ora_cnt")).doubleValue())
                            .average()
                            .orElse(0.0));
                    // 新增平均值欄位到資料
                    data.put("avgSwitchCount", avgSwitchCount);
                    // 判斷是否超過平均值
                    int currentSwitchCount = ((Number) data.get("ora_cnt")).intValue();
                    if (avgSwitchCount == 0) {
                        data.put("switchalert", false); // 如果平均值為 0，設為安全值
                    } else {
                        data.put("switchalert", currentSwitchCount >= avgSwitchCount * 3);
                    }
                    return data;
                })
                .collect(Collectors.toList());

        // 計算當天日期的前 14 天平均值
        int avgSwitchCountForToday = 0;
        if (!dataListAll.isEmpty()) {
            LocalDateTime today = LocalDateTime.now();
            LocalDateTime startRange = today.minusDays(14);
            List<Map<String, Object>> pastDataForToday = dataListAll.stream()
                    .filter(past -> {
                        String pastDatetime = (String) past.get("ora_datetime");
                        LocalDateTime pastTime = LocalDateTime.parse(pastDatetime, formatter);
                        return !pastTime.isBefore(startRange) && pastTime.isBefore(today);
                    })
                    .collect(Collectors.toList());
            avgSwitchCountForToday = (int) Math.ceil(pastDataForToday.stream()
                    .mapToDouble(past -> ((Number) past.get("ora_cnt")).doubleValue())
                    .average()
                    .orElse(0.0));
        }

        // 封裝分頁資料
        Map<String, Object> paginatedData = new HashMap<>();
        paginatedData.put("total", totalCount);
        paginatedData.put("size", size);
        paginatedData.put("page", page);
        paginatedData.put("avgSwitchCount", avgSwitchCountForToday);
        paginatedData.put("list", paginatedList);

        Map<String, Object> result = new HashMap<>();
        result.put("code", "0");
        result.put("errMsg", "success");
        result.put("data", paginatedData);
        return result;
    }

    @Override
    public List<Map<String, Object>> getLockWait(String startTime, String endTime, String dbId) {
        QueryWrapper qw = new QueryWrapper(bigDataUtil.getSrDbName(), "T100OracleLockWaitCollected_sr_agg");
        qw.select("collectedTime", "source_db_id", "lock_cnt");
        qw.eq("source_db_id", dbId).between("collectedTime", startTime, endTime).orderByAsc("collectedTime");
        String sql = QueryWrapperHelper.getSql4Phoenix(qw);
        List<Map<String, Object>> dataList = bigDataUtil.srQuery(sql);
        return dataList;
    }

    @Override
    public Map<String, Object> getLockWaitList(String startTime, String endTime, String dbId, String content, int pageNum, int pageSize, List<Query.QueryOrder> orders) {
        String countSelectSql = " select count(*) total";
        String fromSql = " from " + bigDataUtil.getSrDbName() + ".T100OracleLockWaitCollected_sr_duplicate ";
        String whereSql = " where source_db_id='" + dbId + "' and collectedTime between '" + startTime + "' and '" + endTime + "'";

        if (!StringUtils.isEmpty(content)) {
            whereSql = whereSql + " and (lockwait_program like '%" + content + "%' or lockwait_object_process like '%" + content +
                    "%' or lockwait_machine like '%" + content + "%')";
        }
        String countSql = countSelectSql + fromSql + whereSql;
        List<Map<String, Object>> countDataList = bigDataUtil.srQuery(countSql);
        if (CollectionUtils.isEmpty(countDataList)) {
            Map<String, Object> map = new HashMap<>();
            map.put("list", new ArrayList<>());
            map.put("total", 0);
            return map;
        }
        Map<String, Object> map = countDataList.get(0);
        Integer total = (Integer) map.get("total");
        if (total != null && total == 0) {
            map.put("list", new ArrayList<>());
            return map;
        }
        String selectSql = " select *";
        String orderSql = " order by " + QueryWrapperHelper.getOrder4Phoenix(orders);
        String limitSql = " limit " + (pageNum - 1) * pageSize + "," + pageSize;
        String sql = selectSql + fromSql + whereSql + orderSql + limitSql;
        List<Map<String, Object>> dataList = bigDataUtil.srQuery(sql);
        map.put("list", dataList);
        return map;
    }

    @Override
    public List<Map<String, Object>> getTop10ExecutionSql(String startTime, String endTime, String dbId) {
        String realEndTime = bigDataUtil.getMaxTime("OracleExecutionTimeSort_sr_duplicate", "collectedTime", "source_db_id",
                dbId, "", endTime);
        if (StringUtils.isEmpty(realEndTime)) {
            log.warn("Top10ExecutionSql dbId:{} max collectedTime is null", dbId);
            return null;
        }
        QueryWrapper qw = new QueryWrapper(bigDataUtil.getSrDbName(), "OracleExecutionTimeSort_sr_duplicate");
        qw.select("collectedTime", "source_db_id", "sql_id", "executions", "total_execution_time", "average_execution_time",
                "sql_txt", "sql_fulltext", "sql_id", "command_type");
        qw.eq("source_db_id", dbId).eq("collectedTime", realEndTime).orderByDesc("average_execution_time");
        String sql = QueryWrapperHelper.getSql4Phoenix(qw);
        List<Map<String, Object>> dataList = bigDataUtil.srQuery(sql);
        return dataList;
    }

    @Override
    public Map<String, Object> getSchemaPasswordExpiryDate(String type, SchemaPasswordExpiryDateParam param) {
        Map<String, Object> map = new HashMap<>();
        if ("list".equals(type)) {
            map.put("list", getSchemaPasswordExpiryDateList(param, "list"));
            Integer total = getSchemaPasswordExpiryDateList(param, "total").stream()
                    .filter(CollectionUtil::isNotEmpty).map(x -> IntegerUtil.objectToInteger(x.get("total"))).findFirst().orElse(0);
            map.put("total", total);
        }
        if ("total".equals(type)) {
            List<Map<String, Object>> list = getSchemaPasswordExpiryDateList(param, "count");
            map.putAll(list.get(0));
            map.putAll(list.get(1));
        }
        if ("schema".equals(type)) {
            map.put("schema", getSchemaPasswordExpiryDateList(param, "schema").stream()
                    .map(entry -> entry.get("account")).collect(Collectors.toList()));
        }
        return map;
    }

    @Override
    public Map<String, Object> getSQLTop10(Query query) {
        return sqlTop10FactoryService.getSQLTop10DataList(query);
    }

    @Override
    public List<Map<String, Object>> getExpDashBoard(Long eid, String dbId, LocalDateTime startTime, LocalDateTime endTime, Boolean isNewCollected) {
        StringBuilder sb = new StringBuilder();
        sb.append("SELECT ora_date, fs_orabackcheckdetails AS backcheckdetails, backUpSize, UPPER(backUpCheck) backUpCheck");
        sb.append(" FROM servicecloud.T100BackupStatusCheck_sr_primary");
        sb.append(" WHERE 1=1");
        sb.append(" AND eid = " + eid);
        sb.append(" AND source_db_id = '").append(dbId).append("'");
        if (Objects.nonNull(startTime)) {
            String startTimeString = DateUtil.getSomeDateFormatString(startTime, DateUtil.DATE_FORMATTER);
            sb.append(" AND ora_date >= '" + startTimeString + " 00:00:00'");
        }
        if (Objects.nonNull(endTime)) {
            String endTimeString = DateUtil.getSomeDateFormatString(endTime, DateUtil.DATE_FORMATTER);
            sb.append(" AND ora_date <= '" + endTimeString + " 23:59:59'");
        }
        if (Objects.isNull(startTime) && Objects.isNull(endTime)) {
            String startTimeString = DateUtil.getSomeDateFormatString(LocalDateTime.now().minusDays(7), DateUtil.DATE_FORMATTER);
            String endTimeString = DateUtil.getSomeDateFormatString(LocalDateTime.now(), DateUtil.DATE_FORMATTER);
            sb.append(" AND ora_date >= '" + startTimeString + " 00:00:00' AND ora_date <= '" + endTimeString + " 23:59:59'");
        }
        if (Boolean.TRUE.equals(isNewCollected)) {
            sb.append(" AND startBackupDate IS NOT NULL");
        }
        if (Boolean.FALSE.equals(isNewCollected)) {
            sb.append(" AND startBackupDate IS NULL");
        }
        sb.append(" ORDER BY ora_date DESC");
        sb.append(" LIMIT 7");

        List<Map<String, Object>> datalist = bigDataUtil.srQuery(StringUtil.toString(sb));
        List<Map<String, Object>> result = new ArrayList<>();
        datalist.forEach(map -> {
            String jsonString = Objects.toString(map.get("backcheckdetails"), null);
            JSONArray jsonArray = JSONObject.parseArray(jsonString);
            Long count = jsonArray.stream()
                    .map(JSONObject.class::cast)
                    .filter(detail -> detail.containsKey("hasFoundFile") && Objects.nonNull(detail.get("hasFoundFile")) && detail.getBoolean("hasFoundFile"))
                    .count();

            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("ora_date", map.get("ora_date"));
            resultMap.put("backUpSize", map.get("backUpSize"));
            resultMap.put("backUpCheck", map.get("backUpCheck"));
            resultMap.put("count", count);
            result.add(resultMap);
        });

        return result;
    }

    private List<Map<String, Object>> getSchemaPasswordExpiryDateList(SchemaPasswordExpiryDateParam param, String type) {
        StringBuilder srSql = new StringBuilder();
        if ("list".equals(type)) {
            srSql.append("SELECT oped.account, oped.tabel_user_id AS 'table_user_id', status_category, ");
            srSql.append("account_status, lock_date, expiry_date, ");
            srSql.append("COALESCE(CAST(DATEDIFF(expiry_date, CURDATE()) AS SIGNED), 0) AS remaining_days, default_table_space, ");
            srSql.append("temporary_tablespace, profile_source, password_expiration_setting, encrypted_version, ");
            srSql.append("e10_create_date AS 'create_date' ");
        } else if ("total".equals(type)) {
            srSql.append("SELECT COUNT(*) AS 'total' ");
        } else if ("count".equals(type)) {
            srSql.append("SELECT status_category, COUNT(*) AS 'count', max_collectedTime ");
        } else if ("schema".equals(type)) {
            srSql.append("SELECT oped.account ");
        }

        // 取最新蒐集時間
        srSql.append("FROM ( ");
        srSql.append("SELECT account, tabel_user_id, MAX(collectedTime) AS max_collectedTime ");
        srSql.append("FROM servicecloud.OraclePwdExpiryDate ");
        srSql.append("WHERE source_db_id = '").append(param.getDb_id()).append("' ");
        srSql.append("GROUP BY account, tabel_user_id ");
        srSql.append(") oped_max_collectedTime ");

        // 取狀態類別
        srSql.append("LEFT JOIN ( SELECT account, tabel_user_id, collectedTime, ");
        srSql.append("CASE ")
                .append("WHEN expiry_date IS NOT NULL ")
                .append("AND DATEDIFF(expiry_date, CURDATE()) < 14 ")
                .append("AND DATEDIFF(expiry_date, CURDATE()) > 0 ")
                .append("AND (account_status = 'OPEN' OR account_status = 'EXPIRED(GRACE)') THEN 'about_to_expire' ")
                .append("WHEN expiry_date IS NOT NULL ")
                .append("AND ((DATEDIFF(expiry_date, CURDATE()) <= 0 ")
                .append("AND (account_status = 'OPEN' OR account_status = 'EXPIRED(GRACE)')) OR account_status = 'EXPIRED') THEN 'expired' ")
                .append("WHEN account_status = 'EXPIRED & LOCKED' THEN 'expired_and_locked' ")
                .append("WHEN account_status IN ('LOCKED', 'LOCKED(TIMED)', 'EXPIRED(GRACE) & LOCKED(TIMED)', 'EXPIRED(GRACE) & LOCKED') THEN 'locked' ")
                .append("ELSE 'open' ")
                .append("END AS status_category ");
        srSql.append("FROM servicecloud.OraclePwdExpiryDate) oped_status_category ON ");
        srSql.append("oped_status_category.collectedTime = oped_max_collectedTime.max_collectedTime ");
        srSql.append("AND oped_max_collectedTime.account = oped_status_category.account ");
        srSql.append("AND oped_max_collectedTime.tabel_user_id = oped_status_category.tabel_user_id ");

        srSql.append("LEFT JOIN servicecloud.OraclePwdExpiryDate oped ON ");
        srSql.append("oped_max_collectedTime.max_collectedTime = oped.collectedTime ");
        srSql.append("AND oped.account = oped_max_collectedTime.account ");
        srSql.append("AND oped.tabel_user_id = oped_max_collectedTime.tabel_user_id ");

        srSql.append("WHERE 1 = 1");

        if (!"count".equals(type) && !"schema".equals(type)) {
            // where 條件
            if (!CollectionUtils.isEmpty(param.getBusinessCondition())) {
                param.getBusinessCondition().forEach(businessCondition -> {
                    switch (businessCondition.getFieldCode()) {
                        case "remaining_days":
                            businessCondition.setFieldCode("COALESCE(CAST(DATEDIFF(expiry_date, CURDATE()) AS SIGNED), 0)");
                            break;
                        case "account":
                            businessCondition.setFieldCode("oped.account");
                            break;
                        case "table_user_id":
                            businessCondition.setFieldCode("oped.tabel_user_id");
                            break;
                        case "create_date":
                            businessCondition.setFieldCode("e10_create_date");
                            break;
                    }
                    srSql.append(" AND ").append(buildBusinessCondition4Sql(businessCondition)).append(" ");
                });
            }
            // order by 排序
            if (!"total".equals(type)) {
                if (!CollectionUtils.isEmpty(param.getOrderFields())) {
                    srSql.append(" ORDER BY oped.collectedTime DESC");
                    param.getOrderFields().forEach(orderField -> {
                        if (!"remaining_days".equals(orderField.getColumn()) && !"status_category".equals(orderField.getColumn()) && !orderField.getColumn().startsWith("oped."))
                            orderField.setColumn("oped." + orderField.getColumn());
                        srSql.append(", ").append(orderField.getColumn()).append(" ").append(orderField.getOrd());
                    });
                }
            }
        }

        // 依狀態類別取數量
        if ("count".equals(type)) {
            srSql.append(" GROUP BY status_category, max_collectedTime ");
        }

        // schema 排序
        if ("schema".equals(type)) {
            srSql.append(" ORDER BY oped.account ");
        }

        // 分頁
        if (!"total".equals(type) && !"count".equals(type) && !"schema".equals(type)) {
            srSql.append(" LIMIT ").append(param.getPageSize()).append(" ");
            srSql.append("OFFSET ").append((param.getPageNum() - 1) * param.getPageSize());
        }

        String sql = srSql.toString().replace("\\", "\\\\");
        List<Map<String, Object>> dataList = bigDataUtil.srQuery(sql);

        // 各種類數量無賦值欄位加上預設值 = 0
        if ("count".equals(type)) {
            // 各種類數量
            Map<String, Object> totalData = new HashMap<>();
            Map<String, Object> countDataList = new HashMap<>();
            List<String> statusCategories = Arrays.asList("expired_and_locked", "expired", "about_to_expire", "locked", "open");
            statusCategories.forEach(category -> countDataList.put(category, dataList.stream()
                    .filter(x -> category.equals(x.get("status_category")))
                    .findFirst()
                    .map(x -> x.get("count"))
                    .orElse(0)));
            totalData.put("total", countDataList);

            // 採集時間
            Map<String, Object> collectedTimeData = new HashMap<>();
            String collectedTime = dataList.isEmpty() ?
                    DateUtil.getSomeDateFormatString(LocalDateTime.now(), DateUtil.DATE_TIME_FORMATTER) :
                    Objects.toString(dataList.get(0).get("max_collectedTime"));
            collectedTimeData.put("collectedTime", collectedTime);

            dataList.clear();
            dataList.add(totalData);
            dataList.add(collectedTimeData);
        }

        return dataList;
    }

    private String getIntervaTime(String collectedTime, String type) {
        try {
            LocalDateTime time = LocalDateTime.parse(collectedTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            int minute = time.getMinute();
            int groupMinute = "start".equals(type)
                    ? (minute / 10) * 10
                    : (minute / 10 + 1) * 10;

            LocalDateTime groupTime = groupMinute == 60 && "end".equals(type)
                    ? time.plusHours(1).withMinute(0)
                    : time.withMinute(groupMinute);

            return groupTime.withSecond(0).withNano(0).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        } catch (Exception e) {
            log.error("getIntervaTime error", e);
            return null;
        }
    }

    public Map<String, Object> getRMANDetail(Query query) {
        //先換算後再做select，就可用換算過的資料做where條件判斷
        String SQL = "SELECT eid, source_db_id, collectedTime, status, input_type, out_type," +
                "operation, FLOOR(INPUT_MB / 1024 * 100) / 100 AS input_gb, compression_ratio, FLOOR(OUT_MB / 1024 * 100) / 100 AS out_gb," +
                "start_time, end_time, elapsed_seconds, " +
                "RANK_NUM" +
                " FROM " + query.getDbName() + "." + query.getTableName() +
                " WHERE ORACLE_LOG_MODE = 'ARCHIVELOG' AND input_type = 'ARCHIVELOG' AND status != 'UNKNOWN'";

        String DBFULL_rank_num= "RANK() OVER (" +
                "PARTITION BY eid, source_db_id, input_type, out_type, operation,start_time" +
                " ORDER BY end_time DESC, " +
                " CASE status " +
                " WHEN 'COMPLETED' THEN 1 " +
                " WHEN 'COMPLETED WITH WARNINGS' THEN 1 " +
                " WHEN 'COMPLETED WITH ERRORS' THEN 1" +
                " WHEN 'FAILED' THEN 1" +
                " ELSE 999 END," +
                " collectedTime ASC" +
                ") AS rank_num";
        String ARCHIVELOG_rank_num = "RANK() OVER (" +
                "PARTITION BY eid, source_db_id, status, input_type, out_type, operation," +
                "FLOOR(INPUT_MB / 1024 * 100) / 100, compression_ratio," +
                "FLOOR(OUT_MB / 1024 * 100) / 100, start_time, end_time, elapsed_seconds" +
                " ORDER BY end_time DESC, collectedTime DESC" +
                ") AS rank_num";


        List<Query.BusinessCondition> temp = query.getBusinessCondition();
        //重新配置businessCondition，將含有elapsed_seconds的做換算
        query.setBusinessCondition(
                temp.stream().map(x -> {
                    if (!"elapsed_seconds".equals(x.getFieldCode())) {
                        return x;
                    }
                    x.setFieldType("INT");
                    if (Objects.nonNull(x.getOperatorValue())) {
                        String operatorValue = StringUtil.toString(x.getOperatorValue());
                        String sec = formatedTimeToSec(operatorValue);
                        x.setOperatorValue(sec);
                    }
                    if (Objects.nonNull(x.getRightOperatorValue())) {
                        String operatorValue = StringUtil.toString(x.getRightOperatorValue());
                        String sec = formatedTimeToSec(operatorValue);
                        x.setRightOperatorValue(sec);

                    }
                    if (Objects.nonNull(x.getLeftOperatorValue())) {
                        String operatorValue = StringUtil.toString(x.getLeftOperatorValue());
                        String sec = formatedTimeToSec(operatorValue);
                        x.setLeftOperatorValue(sec);
                    }
                    return x;
                }).collect(Collectors.toList())
        );
        // COMPLETE與RUNNING各有三種狀態，皆須帶出
        query.setBusinessCondition(
                temp.stream().map(x -> {
                    if (!"status".equals(x.getFieldCode())) {
                        return x;
                    }
                    List<String> positive = Arrays.asList("=","exist","start","end");
                    List<String> negative = Arrays.asList("!=","notexist","notstart","notend");
                    List<String> mutiOperator = Arrays.asList("in", "notin",">","<",">=","<=");
                    Boolean isMutiOperator = mutiOperator.contains(x.getOperator());
                    if (positive.contains(x.getOperator())) {
                        x.setOperator("in");
                    }
                    if (negative.contains(x.getOperator())) {
                        x.setOperator("notin");
                    }
                    if (Objects.nonNull(x.getOperatorValue())) {
                        String value = StringUtil.toString(x.getOperatorValue());
                        List<String> valueList = Arrays.asList(value.split(","));
                        value = value.replace("COMPLETED", "COMPLETED,COMPLETED WITH WARNINGS,COMPLETED WITH ERRORS");
                        value = value.replace("RUNNING", "RUNNING,RUNNING WITH WARNINGS,RUNNING WITH ERRORSRUNNING,RUNNING WITH WARNINGS,RUNNING WITH ERRORS");
                        if (!isMutiOperator && valueList.size()>1) {
                            x.setOperatorValue("");
                        }
                        else
                            x.setOperatorValue(value);
                    }
                    return x;
                }).collect(Collectors.toList())
        );

        SQL = SQL.replace("RANK_NUM", ARCHIVELOG_rank_num) + " UNION " +
                SQL.replace("RANK_NUM", DBFULL_rank_num)
                        .replace("input_type = 'ARCHIVELOG'", "input_type = 'DB FULL'");

        SQL = SQL + ") AS T WHERE " + QueryWrapperHelper.getBusinessCondition4Sql(query.getBusinessCondition()) + " AND rank_num = 1";
        //rank_num不取出來
        SQL = "SELECT eid, source_db_id, collectedTime, status, input_type, out_type," +
                "operation, input_gb, compression_ratio, out_gb," +
                "start_time, end_time, elapsed_seconds FROM (" + SQL;
    //取得總資料筆數後再做LIMIT與ORDER，為0則直接回傳
        List<Map<String, Object>> rmanDetailList = bigDataUtil.srQuery(SQL);
        int total = rmanDetailList.size();
        if (total == 0) {
            Map<String, Object> map = new HashMap<>();
            map.put("total", 0);
            map.put("list", new ArrayList<>());
            return map;
        }
        if (!query.getOrderFields().isEmpty()) {
            SQL = SQL + " ORDER BY " + QueryWrapperHelper.getOrder4Phoenix(query.getOrderFields());
        }
        if (Objects.isNull(query.getPageIndex()) && Objects.nonNull(query.getPageSize())) {
            SQL = SQL + " LIMIT " + query.getPageSize();
        } else if (Objects.nonNull(query.getPageIndex()) && Objects.nonNull(query.getPageSize())) {
            SQL = SQL + " LIMIT " + (query.getPageIndex() - 1) * query.getPageSize() + "," + query.getPageSize();
        }
        rmanDetailList = bigDataUtil.srQuery(SQL);
        List<Map<String, Object>> dataList = rmanDetailList.stream().map(map -> {
            Map<String, Object> newMap = new HashMap<>(map);
            int sec = IntegerUtil.objectToInteger(map.get("elapsed_seconds"));
            //將秒數轉為hh:mm:ss格式
            newMap.put("elapsed_seconds", String.format("%02d:%02d:%02d", sec / 3600, (sec % 3600) / 60, sec % 60));
            return newMap;
        }).collect(Collectors.toList());

        Map<String, Object> map = new HashMap<>();
        map.put("total", total);
        map.put("list", dataList);
        return map;
    }

    private String formatedTimeToSec(String time) {
        int timeFormatLength = 3, timeUnit = 60;
        String[] timeArray = time.split(":");
        if (timeArray.length != timeFormatLength) {
            return time;
        }
        int sec = IntStream.range(0, timeArray.length)
                .map(i -> IntegerUtil.objectToInteger(timeArray[i]) * (int) Math.pow(timeUnit, timeFormatLength - i - 1))
                .sum();

        return StringUtil.toString(sec);
    }

    @Override
    public List<Map<String, Object>> getRMANDashboard(String dbId, String startTime, String endTime) {
        LocalDateTime startDateTime = DateUtil.tryParseLocalDateTime(startTime)
                .orElseThrow(() -> new IllegalArgumentException("Invalid startTime format: " + startTime));
        LocalDateTime endDateTime = DateUtil.tryParseLocalDateTime(endTime)
                .orElseThrow(() -> new IllegalArgumentException("Invalid endTime format: " + endTime));

        LocalDate startDate = DateUtil.parseToLocalDate(startDateTime);
        LocalDate endDate = DateUtil.parseToLocalDate(endDateTime);

        //DB FULL 需判斷7天內的資料，故多取前7天的資料
        String sql = "SELECT end_time, input_type, status FROM servicecloud.Oracle_RMAN_Backup" +
                " WHERE collectedTime BETWEEN '" + startDate.atStartOfDay() + "' AND '" + endDate.atStartOfDay().plusDays(1).minusMinutes(1) +
                "' AND source_db_id = '" + dbId + "'" +
                " AND input_type IN ('ARCHIVELOG', 'DB FULL')" +
                " AND ORACLE_LOG_MODE = 'ARCHIVELOG'";
        List<Map<String, Object>> dataList = bigDataUtil.srQuery(sql);

        if (CollectionUtil.isEmpty(dataList)) {
            return new ArrayList<>();
        }

        // 取得開始時間到結束時間中間的日數List
        List<String> dateList = LongStream.rangeClosed(0, endDate.toEpochDay() - startDate.toEpochDay())
                .mapToObj(endDate::minusDays)
                .map(LocalDate::toString)
                .collect(Collectors.toList());

        List<Map<String, Object>> resultList = dateList.stream().map(date -> {
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("date", date);
            LocalDateTime subDateTime = DateUtil.tryParseLocalDate(date, DateUtil.DATE_FORMATTER).get().atStartOfDay();
            //判斷ARCHIVELOG，以當天資料來看
            List<Map<String, Object>> subDataList = dataList.stream()
                    .filter(m -> Objects.equals("ARCHIVELOG", m.get("input_type")))
                    .filter(m -> {
                        String timeStr = StringUtil.toString(m.get("end_time"));
                        if (StringUtil.isEmpty(timeStr)) {
                            return Boolean.FALSE;
                        }
                        LocalDateTime time = DateUtil.tryParseLocalDateTime(timeStr, DateUtil.DATE_TIME_FORMATTER).get();
                        //時間範圍           下限                           上限
                        return time.isAfter(subDateTime) && time.isBefore(subDateTime.plusDays(1).minusMinutes(1));
                    })
                    .collect(Collectors.toList());
            if (CollectionUtil.isEmpty(subDataList)) {
                dataMap.put("archivelog", null);
            } else {
                Boolean isMatch = subDataList.stream().anyMatch(map -> Objects.equals("COMPLETED", map.get("status")));
                dataMap.put("archivelog", isMatch ? "COMPLETED" : "FAILED");
            }

            //判斷DB FULL，需取當天及其7天內的資料作判斷
            subDataList = dataList.stream()
                    .filter(m -> Objects.equals("DB FULL", m.get("input_type")))
                    .filter(m -> {
                        String timeStr = StringUtil.toString(m.get("end_time"));
                        if (StringUtil.isEmpty(timeStr)) {
                            return Boolean.FALSE;
                        }
                        LocalDateTime time = DateUtil.tryParseLocalDateTime(timeStr, DateUtil.DATE_TIME_FORMATTER).get();
                        //時間範圍           下限                                        上限
                        return time.isAfter(subDateTime.minusDays(7)) && time.isBefore(subDateTime.plusDays(1).minusMinutes(1));
                    })
                    .collect(Collectors.toList());
            if (CollectionUtil.isEmpty(subDataList)) {
                dataMap.put("dbfull", null);
            } else {
                Boolean isMatch = subDataList.stream().anyMatch(map -> Objects.equals("COMPLETED", map.get("status")));
                dataMap.put("dbfull", isMatch ? "COMPLETED" : "FAILED");
            }
            return dataMap;
        }).collect(Collectors.toList());

        //archivelog與dbfull若為null代表在SQL中沒有資料，故需過濾掉
        return resultList.stream()
                .filter(map -> Objects.nonNull(map.get("archivelog")) || Objects.nonNull(map.get("dbfull")))
                .collect(Collectors.toList());
    }
}
