package com.digiwin.escloud.issueservice.model;

public class IssueKbshare {
    private long issueId;
    private String crmId;
    private String productCode;
    private String searchText;
    private String kbid;
    private String shareContent;
    private String shareUrl;
    private String submitTime;

    private  String finishSearchChatFile;
    private String  aiSource;
    private String chatFileContent;
    private String chatFileSearchText;

    private String  chatFileErrorInfo;

    private boolean invalidChatFileAnswer;

    private String  chatfileKnowledgeList;
    private Boolean invalidChatFileKnowledgeNo;
    private String  chatfileHelp;
    public long getIssueId() {
        return issueId;
    }
    public void setId(long issueId) {
        this.issueId = issueId;
    }

    public String getCrmId() {
        return crmId;
    }
    public void setCrmId(String crmId) {
        this.crmId = crmId;
    }

    public String getProductCode() {
        return productCode;
    }
    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getSearchText() {
        return searchText;
    }
    public void setSearchText(String searchText) {
        this.searchText = searchText;
    }

    public String getKbid() {
        return kbid;
    }
    public void setKbid(String kbid) {
        this.kbid = kbid;
    }

    public String getShareContent() {
        return shareContent;
    }
    public void setShareContent(String shareContent) {
        this.shareContent = shareContent;
    }

    public String getShareUrl() {
        return shareUrl;
    }
    public void setShareUrl(String shareUrl) {
        this.shareUrl = shareUrl;
    }

    public String getSubmitTime() {
        return submitTime;
    }
    public void setSubmitTime(String submitTime) {
        this.submitTime = submitTime;
    }

    public String getFinishSearchChatFile() {
        return finishSearchChatFile;
    }

    public void setFinishSearchChatFile(String finishSearchChatFile) {
        this.finishSearchChatFile = finishSearchChatFile;
    }

    public String getChatFileContent() {
        return chatFileContent;
    }

    public void setChatFileContent(String chatFileContent) {
        this.chatFileContent = chatFileContent;
    }

    public String getChatFileSearchText() {
        return chatFileSearchText;
    }

    public void setChatFileSearchText(String chatFileSearchText) {
        this.chatFileSearchText = chatFileSearchText;
    }

    public String getChatFileErrorInfo() {
        return chatFileErrorInfo;
    }

    public void setChatFileErrorInfo(String chatFileErrorInfo) {
        this.chatFileErrorInfo = chatFileErrorInfo;
    }

    public boolean isInvalidChatFileAnswer() {
        return invalidChatFileAnswer;
    }

    public void setInvalidChatFileAnswer(boolean invalidChatFileAnswer) {
        this.invalidChatFileAnswer = invalidChatFileAnswer;
    }

    public void setIssueId(long issueId) {
        this.issueId = issueId;
    }

    public String getChatfileKnowledgeList() {
        return chatfileKnowledgeList;
    }

    public void setChatfileKnowledgeList(String chatfileKnowledgeList) {
        this.chatfileKnowledgeList = chatfileKnowledgeList;
    }

    public Boolean getInvalidChatFileKnowledgeNo() {
        return invalidChatFileKnowledgeNo;
    }

    public void setInvalidChatFileKnowledgeNo(Boolean invalidChatFileKnowledgeNo) {
        this.invalidChatFileKnowledgeNo = invalidChatFileKnowledgeNo;
    }

    public String getChatfileHelp() {
        return chatfileHelp;
    }

    public void setChatfileHelp(String chatfileHelp) {
        this.chatfileHelp = chatfileHelp;
    }

    public String getAiSource() {
        return aiSource;
    }

    public void setAiSource(String aiSource) {
        this.aiSource = aiSource;
    }
}
