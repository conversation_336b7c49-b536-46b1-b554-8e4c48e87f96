package com.digiwin.escloud.aioitms.report.model.db;

import lombok.Data;

import java.util.Objects;

@Data
public class SendDbReportParam {
    private Long edrReportRecordId;
    private String reportSender;
    private Boolean isAppointment = false;
    private Long eid;
    private String userId;
    private String userName;
    private Integer reportType;

    public static SendDbReportParam objectToSendDbReportParam(Object object) {
        if (Objects.nonNull(object) && object instanceof SendDbReportParam) {
            return (SendDbReportParam) object;
        }
        return null;
    }
}
