package com.digiwin.escloud.aiocmdb.asset.service.impl;

import cn.hutool.core.util.BooleanUtil;
import com.digiwin.escloud.aiocmdb.asset.dao.AssetCategoryMapper;
import com.digiwin.escloud.aiocmdb.asset.model.*;
import com.digiwin.escloud.aiocmdb.asset.service.IAssetCategoryService;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.util.RequestUtil;
import com.digiwin.escloud.common.util.SnowFlake;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 资产类别分类表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Service
public class AssetCategoryServiceImpl implements IAssetCategoryService {

    private static final String CATEGORY_DEFAULT_SCOPE_ID = "CATEGORY_DEFAULT_SCOPE_ID";

    @Autowired
    private AssetCategoryMapper assetCategoryMapper;

    @Override
    public ResponseBase saveAssetCategoryClassification(AssetCategoryClassification classification) {
        // 校验categoryName唯一性
        int count = assetCategoryMapper.countByCategoryName(classification.getCategoryName(), null);
        if (count > 0) {
            return ResponseBase.error(ResponseCode.ASSET_CATEGORY_CLASSIFICATION_NAME_EXISTS);
        }
        classification.setId(SnowFlake.getInstance().newId());
        int result = assetCategoryMapper.insertAssetCategoryClassification(classification);
        if (result > 0) {
            return ResponseBase.ok(classification);
        }
        return ResponseBase.error(ResponseCode.INSERT_FAILD);
    }

    @Override
    public ResponseBase updateAssetCategoryClassification(AssetCategoryClassification classification) {

        AssetCategoryClassification categoryClassification = assetCategoryMapper.selectAssetCategoryClassificationById(classification.getId());

        if (BooleanUtil.isFalse(categoryClassification.getCanEdit())) {
            return ResponseBase.error(ResponseCode.ASSET_CATEGORY_CLASSIFICATION_NOT_EDIT_DELETE);
        }
        // 校验categoryName唯一性（排除自己）
        int count = assetCategoryMapper.countByCategoryName(classification.getCategoryName(), classification.getId());
        if (count > 0) {
            return ResponseBase.error(ResponseCode.ASSET_CATEGORY_CLASSIFICATION_NAME_EXISTS);
        }

        int result = assetCategoryMapper.updateAssetCategoryClassification(classification);
        if (result > 0) {
            return ResponseBase.ok(classification);
        }
        return ResponseBase.error(ResponseCode.UPDATE_FAILD);
    }

    @Override
    public ResponseBase deleteAssetCategoryClassification(Long id) {

        AssetCategoryClassification categoryClassification = assetCategoryMapper.selectAssetCategoryClassificationById(id);

        if (BooleanUtil.isFalse(categoryClassification.getCanDelete())) {
            return ResponseBase.error(ResponseCode.ASSET_CATEGORY_CLASSIFICATION_NOT_EDIT_DELETE);
        }

        // 校验是否有关联的AssetCategory数据
        int count = assetCategoryMapper.countAssetCategoryByClassificationId(id);
        if (count > 0) {
            return ResponseBase.error(ResponseCode.ASSET_CATEGORY_CLASSIFICATION_HAS_CATEGORIES);
        }

        int result = assetCategoryMapper.deleteAssetCategoryClassification(id);
        if (result > 0) {
            return ResponseBase.ok();
        }
        return ResponseBase.error(ResponseCode.DELETE_FAILD);
    }

    @Override
    public ResponseBase getAssetCategoryClassificationList() {
        List<AssetCategoryClassification> list = assetCategoryMapper.selectAssetCategoryClassificationList();
        return ResponseBase.ok(list);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseBase saveAssetCategory(AssetCategory category) {
        // 校验sid、scopeId、categoryNumber三字段联合唯一性
        int count = assetCategoryMapper.countBySidScopeIdCategoryNumber(
                category.getSid(), category.getScopeId(), category.getCategoryNumber(), null);
        if (count > 0) {
            return ResponseBase.error(ResponseCode.ASSET_CATEGORY_UNIQUE_CONSTRAINT_VIOLATION);
        }
        category.setId(SnowFlake.getInstance().newId());
        category.setSid(RequestUtil.getHeaderSid());
        category.setScopeId(CATEGORY_DEFAULT_SCOPE_ID);
        category.setModelCode(category.getCategoryNumber());
        category.setStatus(AssetCategory.Status.ENABLED);

        int result = assetCategoryMapper.insertAssetCategory(category);

        if (result > 0) {
            return ResponseBase.ok(category);
        }

        // 保存资产类别编号规则
        for (AssetCategoryCodingRuleSettingResult rule : category.getAccrsrList()) {
            assetCategoryMapper.insertAssetCategoryCodingRuleSettingResult(rule);
        }

        return ResponseBase.error(ResponseCode.INSERT_FAILD);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseBase updateAssetCategory(AssetCategory category) {
        // categoryNumber不可编辑，这里不更新categoryNumber字段
        AssetCategory existingCategory = assetCategoryMapper.selectAssetCategoryById(category.getId());
        if (existingCategory == null) {
            return ResponseBase.error(ResponseCode.QUERY_VERIFY);
        }

        int result = assetCategoryMapper.updateAssetCategory(category);
        if (result > 0) {
            return ResponseBase.ok(category);
        }

        assetCategoryMapper.deleteAssetCategoryCodingRuleSettingResult(category.getId());

        // 保存资产类别编号规则
        for (AssetCategoryCodingRuleSettingResult rule : category.getAccrsrList()) {
            assetCategoryMapper.insertAssetCategoryCodingRuleSettingResult(rule);
        }
        return ResponseBase.error(ResponseCode.UPDATE_FAILD);
    }

    @Override
    public ResponseBase updateAssetCategoryStatus(Long id, String status) {
        AssetCategory category = assetCategoryMapper.selectAssetCategoryById(id);
        if (category == null) {
            return ResponseBase.error(ResponseCode.QUERY_VERIFY);
        }

        // TODO: 在编辑为停用状态的时候，需要进行额外的校验，预留位置
        if (AssetCategory.Status.DISABLED.name().equals(status)) { // 停用状态
            // 预留额外校验位置
        }

        category.setStatus(AssetCategory.Status.DISABLED);

        int result = assetCategoryMapper.updateAssetCategory(category);
        if (result > 0) {
            return ResponseBase.ok(category);
        }
        return ResponseBase.error(ResponseCode.UPDATE_FAILD);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseBase deleteAssetCategory(Long id) {
        // TODO: 删除前需要进行额外的校验，预留位置

        int result = assetCategoryMapper.deleteAssetCategory(id);

        if (result > 0) {
            return ResponseBase.ok();
        }
        assetCategoryMapper.deleteAssetCategoryCodingRuleSettingResult(id);
        return ResponseBase.error(ResponseCode.DELETE_FAILD);
    }

    @Override
    public ResponseBase getAssetCategoryList(Long classificationId) {
        // TODO 分页 查询加搜索
        List<AssetCategory> list = assetCategoryMapper.selectAssetCategoryList(classificationId);
        return ResponseBase.ok(list);
    }

    @Override
    @Transactional
    public ResponseBase saveCmdbModelDataFieldRelationMapping(List<CmdbModelDataFieldRelationMapping> mappingList) {
        if (mappingList == null || mappingList.isEmpty()) {
            return ResponseBase.error(ResponseCode.PARAM_VERIFY);
        }

        String targetModelCode = mappingList.get(0).getTargetModelCode();
        if (!StringUtils.hasText(targetModelCode)) {
            return ResponseBase.error(ResponseCode.PARAM_VERIFY);
        }

        // 根据targetModelCode删除表里所有的数据
        assetCategoryMapper.deleteCmdbModelDataFieldRelationMappingByTargetModelCode(targetModelCode);

        // 新增数据，同时校验targetModelCode和targetModelFieldName的联合唯一性
        for (CmdbModelDataFieldRelationMapping mapping : mappingList) {
            int count = assetCategoryMapper.countByTargetModelCodeAndFieldName(
                    mapping.getTargetModelCode(), mapping.getTargetModelFieldName(), null);
            if (count > 0) {
                return ResponseBase.error(ResponseCode.SOMTHING_ALREADY_EXISTS,
                        "targetModelCode and targetModelFieldName combination");
            }

            mapping.setCreateDate(LocalDateTime.now());
            mapping.setUpdateDate(LocalDateTime.now());
            assetCategoryMapper.insertCmdbModelDataFieldRelationMapping(mapping);
        }

        return ResponseBase.ok(mappingList);
    }

    @Override
    public ResponseBase getCmdbModelDataFieldRelationMappingList(String targetModelCode) {
        if (!StringUtils.hasText(targetModelCode)) {
            return ResponseBase.error(ResponseCode.PARAM_VERIFY);
        }

        List<CmdbModelDataFieldRelationMapping> list =
                assetCategoryMapper.selectCmdbModelDataFieldRelationMappingByTargetModelCode(targetModelCode);
        return ResponseBase.ok(list);
    }

    @Override
    public ResponseBase getAllAssetCategoryCodingRule() {
        List<AssetCategoryCodingRule> list =
                assetCategoryMapper.selectAllAssetCategoryCodingRule();
        return ResponseBase.ok(list);
    }
}
