package com.digiwin.escloud.aioitms.bcp.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class BcpSurveyRecord {
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime recordTime;
    @JsonIgnore
    private String id;
    private String eid;
    private String serviceCode;
    private String surveyId;
    private String companyName;
    private String taxId;
    private String userId;
    private String userName;
    private String userTel;
    private Double surveyScore;
}
