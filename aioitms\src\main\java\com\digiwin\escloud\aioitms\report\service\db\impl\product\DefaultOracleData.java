package com.digiwin.escloud.aioitms.report.service.db.impl.product;

import com.digiwin.escloud.aioitms.report.annotation.DbTypeCode;
import com.digiwin.escloud.aioitms.report.model.db.DbReportRecord;
import com.digiwin.escloud.aioitms.report.model.db.product.T100OracleDbReport;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;

@DbTypeCode("9999_ORACLE")
@Service
public class DefaultOracleData extends TOracleDataBase<T100OracleDbReport> {

    @Override
    protected List<Callable<Object>> getReportItems(DbReportRecord dbReportRecord) {
        List<Callable<Object>> reportItems = new ArrayList<>();
        Long eid = dbReportRecord.getEid();
        List<String> deviceIdList = dbReportRecord.getDeviceIdList();
        List<Map<String, Object>> deviceAppInstanceInfoList = getDeviceAppInstanceInfoList(eid,
                dbReportRecord.getProductCode(),dbReportRecord.getOriProductCode(), deviceIdList);
        setModelName(deviceAppInstanceInfoList, dbReportRecord.getApiToken());
        deviceAppInstanceInfoList = getDistinctInstance(deviceAppInstanceInfoList);
        List<Map<String, Object>> dbInstanceInfoList = getDBInstanceInfoList(eid, deviceIdList,
                dbReportRecord.getDbId());
        this.calRefValue(dbReportRecord);
        String appCode = dbReportRecord.getAppCode();
        LocalDate startDate = dbReportRecord.getDataStartDate();
        LocalDate endDate = dbReportRecord.getDataEndDate();

        //服务器
        reportItems.add(buildServerStatusInfo(appCode, eid, startDate, endDate, deviceAppInstanceInfoList));

        //数据库
        reportItems.add(buildDbStatusInfo(appCode, eid, dbReportRecord.getDbId(), startDate, endDate,
                dbInstanceInfoList));

        //产品应用/服务
        reportItems.add(buildAppServiceStatusInfo(appCode, eid, startDate, endDate, deviceAppInstanceInfoList));

        //每日预警总览
        reportItems.add(buildDailyWarningInfo(appCode, eid, startDate, endDate));

        //服务器运行状况
        reportItems.add(buildServerHealthInfo(dbReportRecord, deviceAppInstanceInfoList));

        //应用服务器运行状况
        reportItems.add(buildAppHealthInfo(dbReportRecord, deviceAppInstanceInfoList));

        //数据库运行状况
        reportItems.add(buildDBHealthInfo(dbReportRecord, dbInstanceInfoList));

        return reportItems;
    }

//    @Override
//    public List<Map<String, Object>> getRecycleBinCheckValue(LocalDateTime startTime, LocalDateTime endTime, String dbId) {
//        return super.getRecycleBinCheckValue(startTime, endTime, dbId);
//    }
}
