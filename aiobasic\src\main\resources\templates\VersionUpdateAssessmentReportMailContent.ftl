<#import "./common/common.macro.ftl" as common>
<@common.commonScript />
<td width=960 style="padding-left: 10px;">
    <p style="font-size: 20px;">${I18n.versionUpdateReport_DigiwinCloud}</p>
    <p class="title" style="text-align: center;padding-bottom: 20px;font-size: 30px;">${I18n.versionUpdateReport_assessmentReportTitle}</p>
    <p style="font-weight: bold;font-size:16px;">${I18n.versionUpdateReport_customerWelcomeMessage}</p>
    <div style="font-size: 16px;">
        <p><span style="font-weight: bold;">${I18n.versionUpdateReport_assessmentReportTimeTitle} <span style="color: blue">【${reportStartTimeStr}至${reportEndTimeStr}】</span></span></p>
        <p>${I18n.versionUpdateReport_assessmentContent}</p>
        <p>
        <span style="font-weight: bold;" on_lick="javaxcript:{var xhr = new XMLHttpRequest();xhr.open('GET','${aiobasicAddress}/edr/report/saveEdrMailLog?serviceCode=${serviceCode}&datasource=edrMailFileRead&reportId=${reportId?c}',true);xhr.send();}">
            ${I18n.versionUpdateReport_clickTitle}：<a href="${misRoot}#/report/product-maintenance-report/version-evaluation-report/report-details/${reportId?c}?dbType=${dbType}&reportType=${reportType}">${I18n.versionUpdateReport_assessmentLinkText}</a>
        </span>
        </p>
        <p>${description}</p>
        <#if serviceArea == "TW">
            <div style="height:30px"></div>
            <p>
                <span style="line-height: 24px;">${I18n.versionUpdateReport_assessmentPlatFormRouteTitle}：</span><br>
                <span style="line-height: 24px;">${I18n.versionUpdateReport_assessmentPlatFormRouteTitle1}：</span><br>
                <a href="${downloadAddress}"><span style="font-weight: bold;color: orange">${I18n.versionUpdateReport_assessmentPlatFormRoute1}</span></a>
                <img width=1px height=1px src="${downloadAddress}"/><br>
                <span style="line-height: 24px;">${I18n.versionUpdateReport_assessmentPlatFormRouteTitle2}：</span><br>
                <a href="${misAddress}"><span style="font-weight: bold;color: orange">${I18n.versionUpdateReport_assessmentPlatFormRoute2}</span></a>
                <img width=1px height=1px src="${misAddress}"/>
            </p>
            <div style="height:10px"></div>
            <p>
                ${I18n.versionUpdateReport_assessmentSupportContactInfo}
            </p>
        </#if>
        <table>
            <tr>
                <td height=20px></td>
            </tr>
        </table>
    </div>
</td>