package com.digiwin.escloud.aiocmdb.asset.model;

import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 资产类别自动获取资产关系映射表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@ApiModel(value = "AssetCategoryAutoDiscoverAssetRelationMapping对象", description = "资产类别自动获取资产关系映射表")
public class AssetCategoryAutoDiscoverAssetRelationMapping implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private Long id;

    private String aiopsItem;

    @ApiModelProperty("自动获取资产表")
    private String autoDiscoverAssetTable;

    @ApiModelProperty("自动获取资产表位置(mysql或者SR)")
    private String autoDiscoverAssetTableLocation;

    private LocalDateTime createDate;

    private LocalDateTime updateDate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAiopsItem() {
        return aiopsItem;
    }

    public void setAiopsItem(String aiopsItem) {
        this.aiopsItem = aiopsItem;
    }

    public String getAutoDiscoverAssetTable() {
        return autoDiscoverAssetTable;
    }

    public void setAutoDiscoverAssetTable(String autoDiscoverAssetTable) {
        this.autoDiscoverAssetTable = autoDiscoverAssetTable;
    }

    public String getAutoDiscoverAssetTableLocation() {
        return autoDiscoverAssetTableLocation;
    }

    public void setAutoDiscoverAssetTableLocation(String autoDiscoverAssetTableLocation) {
        this.autoDiscoverAssetTableLocation = autoDiscoverAssetTableLocation;
    }

    public LocalDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

    public LocalDateTime getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(LocalDateTime updateDate) {
        this.updateDate = updateDate;
    }

    @Override
    public String toString() {
        return "AssetCategoryAutoDiscoverAssetRelationMapping{" +
            "id = " + id +
            ", aiopsItem = " + aiopsItem +
            ", autoDiscoverAssetTable = " + autoDiscoverAssetTable +
            ", autoDiscoverAssetTableLocation = " + autoDiscoverAssetTableLocation +
            ", createDate = " + createDate +
            ", updateDate = " + updateDate +
        "}";
    }
}
