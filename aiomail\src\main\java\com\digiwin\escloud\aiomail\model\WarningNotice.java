package com.digiwin.escloud.aiomail.model;

import com.digiwin.escloud.aiomail.util.MessageUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * @Date 2021/5/27 17:47
 * @Created yanggld
 * @Description
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
public class WarningNotice {

    private WarningMail warningMail;

    private WarningWechat warningWechat;

    private WarningSms warningSms;

    private WarningDigiwinApp warningDigiwinApp;

    @Data
    public static class WarningMail {
        private String i18n = MessageUtils.ZH_TW_STANDARD;
        private String from;
        private String[] to;
        private String[] cc;
        private String subject;
        private String text;
    }

    @Data
    public static class WarningWechat {
        private List<String> tousers;
        private Map<String, Object> params;
        private Map<String, String> userUrls;
    }

    @Data
    public static class WarningSms {
        private String eventId;
        private List<String> phoneNumbers;
        private Map<String, Object> params;
    }

    @Data
    public static class WarningDigiwinApp {
        private List<String> userIdList;
        private Map<String, Object> params;
    }
}
