package com.digiwin.escloud.aioitms.report.handler.impl;

import com.digiwin.escloud.aiobasic.operatelog.model.OperateLogSaveParam;
import com.digiwin.escloud.aiobasic.operatelog.model.OperateLogType;
import com.digiwin.escloud.aioitms.report.handler.DbReportParamHandler;
import com.digiwin.escloud.aioitms.report.model.db.SendDbReportParam;
import com.digiwin.escloud.common.util.DateUtil;
import com.digiwin.escloud.common.util.IntegerUtil;
import com.digiwin.escloud.common.util.LongUtil;
import com.digiwin.escloud.common.util.StringUtil;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

import static com.digiwin.escloud.common.util.DateUtil.DATE_TIME_FORMATTER;

@Component
public class Send2MISReportParamHandler implements DbReportParamHandler {
    @Override
    public boolean isSupport(OperateLogType op) {
        return OperateLogType.Send2MIS.equals(op);
    }

    @Override
    public void handle(List<Object> param, OperateLogSaveParam logParam) {
        Long id = LongUtil.objectToLong(param.get(0));
        SendDbReportParam dto = SendDbReportParam.objectToSendDbReportParam(param.get(1));

        // 檢查參數不為空及是否為版更評估(reportType = 4)
        if (Objects.isNull(dto) || IntegerUtil.isEmpty(dto.getReportType()) || dto.getReportType() != 4) {
            return;
        }

        logParam.setEid(StringUtil.toString(dto.getEid()));
        logParam.setProcessUserId(dto.getUserId());
        logParam.setProcessUserName(dto.getUserName());
        logParam.setReportId(StringUtil.toString(id));
        logParam.setOperateType(OperateLogType.Send2MIS.getCode());
        logParam.setStartTime(DateUtil.getNowFormatString(DATE_TIME_FORMATTER));
    }
}
