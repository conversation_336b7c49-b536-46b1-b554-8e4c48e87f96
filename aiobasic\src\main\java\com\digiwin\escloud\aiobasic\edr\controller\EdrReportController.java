package com.digiwin.escloud.aiobasic.edr.controller;

import com.digiwin.escloud.aiobasic.edr.model.aio.*;
import com.digiwin.escloud.aiobasic.edr.model.base.ReportRecord;
import com.digiwin.escloud.aiobasic.edr.model.base.ReportRecordAutoSet;
import com.digiwin.escloud.aiobasic.edr.model.base.SecType;
import com.digiwin.escloud.aiobasic.edr.model.base.edr.EdrCollectorDeleteParam;
import com.digiwin.escloud.aiobasic.edr.model.base.edr.EdrCollectorParam;
import com.digiwin.escloud.aiobasic.edr.model.base.edr.EdrCollectorSetGroupParam;
import com.digiwin.escloud.aiobasic.edr.model.base.edr.EdrEventRawParam;
import com.digiwin.escloud.aiobasic.edr.model.base.fine.SetPolicyPermissionParam;
import com.digiwin.escloud.aiobasic.edr.model.edr.*;
import com.digiwin.escloud.aiobasic.edr.service.edr.IEdrReportService;
import com.digiwin.escloud.aiobasic.edr.service.edr.impl.EdrExpireService;
import com.digiwin.escloud.aiobasic.edr.service.edr.impl.ReportItemFactoryService;
import com.digiwin.escloud.aiobasic.operatelog.annotation.OperateLog;
import com.digiwin.escloud.aiobasic.operatelog.model.OperateLogType;
import com.digiwin.escloud.common.controller.ControllerBase;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.RequestUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

@Api(value = "/edr/report", protocols = "HTTP", tags = {"edr报表相关接口"}, description = "edr报表相关接口")
@Slf4j
@RestController
@RequestMapping("/edr/report")
public class EdrReportController extends ControllerBase {

    @Value("${digiwin.supplier.defaultsid:241199971893824}")
    private long defaultSid;
    @Autowired
    private IEdrReportService edrReportService;
    @Autowired
    private ReportItemFactoryService reportItemFactoryService;

    @Resource
    private EdrExpireService edrExpireService;

    @ApiOperation(value = "查询资安EDR报告记录")
    @GetMapping(value = "/records")
    public ResponseBase getReportRecords(@ApiParam(value = "客代", required = true) @RequestParam(value = "serviceCode", required = true) String serviceCode,
                                         @ApiParam(value = "发送状态", required = false) @RequestParam(value = "sendStatus", required = false) String sendStatus,
                                         @ApiParam(required = false, value = "报告类型") @RequestParam(value = "type", required = false) String type,
                                         @ApiParam(required = false, value = "组织中文名称") @RequestParam(value = "orgCnNames", required = false) String orgCnNames,
                                         @ApiParam(required = false, value = "操作人员") @RequestParam(value = "userName", required = false) String userName,
                                         @ApiParam(required = false, value = "发送开始时间") @RequestParam(value = "sendTimeStart", required = false) String sendTimeStart,
                                         @ApiParam(required = false, value = "发送结束时间") @RequestParam(value = "sendTimeEnd", required = false) String sendTimeEnd,
                                         @ApiParam(required = false, value = "生成开始时间") @RequestParam(value = "reportGenerateTimeStart", required = false) String reportGenerateTimeStart,
                                         @ApiParam(required = false, value = "生成结束时间") @RequestParam(value = "reportGenerateTimeEnd", required = false) String reportGenerateTimeEnd,
                                         @ApiParam(required = false, value = "页码") @RequestParam(value = "page", required = false, defaultValue = "1") int page,
                                         @ApiParam(required = false, value = "条数") @RequestParam(value = "size", required = false, defaultValue = "0") int size,
                                         @ApiParam(required = false, value = "模組") @RequestParam(value = "modelVersion", required = false, defaultValue = "1.0") String modelVersion,
                                         @ApiParam(required = false, value = "id") @RequestParam(value = "id", required = false) Long id) {
        return this.getResponse(res -> {
            res.setData(new ReportRecordsGetRes(edrReportService.getReportRecords(serviceCode, sendStatus, type, orgCnNames, userName, sendTimeStart, sendTimeEnd, reportGenerateTimeStart, reportGenerateTimeEnd, page, size, modelVersion, id), edrReportService.getReportRecordCount(serviceCode, sendStatus, type, orgCnNames, userName, sendTimeStart, sendTimeEnd, reportGenerateTimeStart, reportGenerateTimeEnd, modelVersion, id)));
            return res;
        });
    }

    @GetMapping(value = "/saveEdrMailLog")
    public ResponseBase saveEdrMailLog(@ApiParam(value = "客代", required = true) @RequestParam(value = "serviceCode", required = true) String serviceCode,
                                       @ApiParam(value = "发送状态", required = false) @RequestParam(value = "reportId", required = false) String reportId,
                                       @ApiParam(required = false, value = "报告类型") @RequestParam(value = "datasource", required = false) String datasource) {
        return this.getResponse(res -> {
            edrReportService.saveEdrMailLog(serviceCode, reportId, datasource);
            return res;
        });
    }

    @ApiOperation(value = "查询资安EDR报告最新的记录详情")
    @GetMapping(value = "/record/detail")
    public ResponseBase getReportRecordDetail(@ApiParam(value = "id", required = true) @RequestParam(value = "id", required = true) long id) {
        return this.getResponse(res -> {
            res.setData(edrReportService.getReportRecordDetail(id));
            return res;
        });
    }

    @ApiOperation(value = "查询报告类型")
    @GetMapping(value = "/type")
    public ResponseBase getReportTypes() {
        return this.getResponse(res -> {
            res.setData(edrReportService.getReportTypes());
            return res;
        });
    }

    @ApiOperation(value = "生成资安EDR报告记录")
    @PostMapping(value = "/generate")
    public ResponseBase generateReportRecord(@ApiParam(value = "报告记录", required = true) @RequestBody ReportRecord reportRecord) {
        return this.getResponse(res -> {
            res.setData(edrReportService.generateReportRecord(reportRecord));
            return res;
        });
    }

    @ApiOperation(value = "查询自动寄信的配置")
    @GetMapping(value = "/auto/set/detail")
    public BaseResponse getReportRecordAutoSetDetail(@ApiParam(value = "客代", required = true) @RequestParam(value = "serviceCode", required = true) String serviceCode,
                                                     @ApiParam(value = "type") @RequestParam(value = "type", required = false) String type,
                                                     @ApiParam(value = "自動寄信排程id") @RequestParam(value = "id", required = false) Long id,
                                                     @ApiParam(value = "模組") @RequestParam(value = "modelVersion", required = false) String modelVersion) {
        return edrReportService.getReportRecordAutoSetDetail(serviceCode, type, id, modelVersion);
    }

    @ApiOperation(value = "配置自动寄信")
    @PutMapping(value = "/auto/set")
    public BaseResponse autoSet(@ApiParam(value = "客代", required = true) @RequestBody ReportRecordAutoSet reportRecordAutoSet) {
        return edrReportService.autoSet(reportRecordAutoSet);
    }

    @ApiOperation(value = "定时任务：生成上个周、上个月、上一季度的报告记录")
    @PostMapping(value = "/month/generate")
    public void autoGenerateReportRecord(@RequestBody(required = false) AutoSendParam param) {
        edrReportService.autoGenerateReportRecord(param);
    }

    @ApiOperation(value = "定时任务：过期edr提醒")
    @PostMapping(value = "/edrExpireSend")
    public void edrExpireSend() {
        edrExpireService.edrExpireSend();
    }

    // 已不再定時調度
    @ApiOperation(value = "定时任务：自动寄信")
    @PostMapping(value = "/auto/send")
    public void autoSend() {
        edrReportService.autoSend();
    }

    @ApiOperation(value = "保存草稿")
    @PostMapping(value = "/generate/save")
    public ResponseBase saveReportRecord(@ApiParam(value = "报告记录", required = true) @RequestBody ReportRecord reportRecord) {
        return this.getResponse(res -> {
            res.setData(edrReportService.saveReportRecord(reportRecord));
            return res;
        });
    }

    @OperateLog(op = OperateLogType.SendMail)
    @ApiOperation(value = "保存并发送")
    @PostMapping(value = "/generate/save/send")
    public ResponseBase saveAndSendReportRecord(@ApiParam(value = "报告记录", required = true) @RequestBody ReportRecord reportRecord) {
        return this.getResponse(res -> {
            res.setData(edrReportService.saveAndSendReportRecord(reportRecord));
            return res;
        });
    }

    @ApiOperation(value = "定时任务：预约发送")
    @RequestMapping(value = "/appointment/send", method = RequestMethod.POST)
    public ResponseBase getAndAppointmentReport(@RequestBody(required = false) AutoSendParam param) {
        return edrReportService.getAndAppointmentReport(param);
    }

    /*@ApiOperation(value = "发送报告")
    @PostMapping(value = "/send")
    public ResponseBase sendReport(@ApiParam(value = "报告记录", required = true) @RequestBody ReportRecord reportRecord) {
        ReportQryParam reportQryParam = new ReportQryParam();
        reportQryParam.setServiceCode(reportRecord.getServiceCode());
        Object object = reportItemFactoryService.getReportItemData("auth",reportQryParam);
        List<CustomerOrgMap> ofOrgs = (List<CustomerOrgMap>)(object) ;

        return this.getResponse(res -> {

            res.setData(edrReportService.sendReport(reportRecord, ofOrgs));
            return res;
        });
    }*/

    @ApiOperation(value = "删除资安EDR报告记录")
    @DeleteMapping(value = "/del")
    public ResponseBase deleteReportRecord(@ApiParam(value = "记录id", required = true) @RequestParam(value = "id", required = true) long id) {
        return this.getResponse(res -> {
            res.setData(edrReportService.deleteReportRecord(id));
            return res;
        });
    }

    @ApiOperation(value = "查询资安EDR报告数据")
    @GetMapping(value = "/data")
    public ResponseBase getReportData(@ApiParam(value = "记录id", required = true) @RequestParam(value = "reportRecordId", required = true) long reportRecordId) {
        return this.getResponse(res -> {
            res.setData(edrReportService.getReportData(reportRecordId));
            return res;
        });
    }

    @ApiOperation(value = "查询资安EDR报告节点数据")
    @GetMapping(value = "/item/data")
    public ResponseBase getReportItemData(@ApiParam(value = "记录id", required = true) @RequestParam(value = "reportTempId", required = true) long reportTempId,
                                          @ApiParam(value = "节点", required = true) @RequestParam(value = "item", required = true) String item) {
        return this.getResponse(res -> {
            res.setData(edrReportService.getReportItemData(reportTempId, item));
            return res;
        });
    }

    @ApiOperation(value = "修改资安报告某一属性值")
    @PostMapping(value = "/field/update")
    public ResponseBase updateFieldValue(@ApiParam(value = "资安类型", required = true) @RequestParam(value = "secType", required = true) SecType secType,
                                         @ApiParam(value = "字段", required = true) @RequestParam(value = "fieldName", required = true) String fieldName,
                                         @ApiParam(value = "字段oldvalue", required = true) @RequestParam(value = "oldValue", required = true) String oldValue,
                                         @ApiParam(value = "字段newvalue", required = true) @RequestParam(value = "newValue", required = true) String newValue) {
        return this.getResponse(res -> {
            res.setData(edrReportService.updateFieldValue(secType, fieldName, oldValue, newValue));
            return res;
        });
    }

    @ApiOperation(value = "EDR报告数据转换")
    @PostMapping(value = "/data/to/new")
    public ResponseBase convertDataToNew(@ApiParam(value = "记录数", required = true) @RequestParam(value = "size", required = true) int size) {
        return this.getResponse(res -> {
            edrReportService.convertDataToNew(size);
            res.setCode(ResponseCode.SUCCESS.toString());
            return res;
        });
    }

    @ApiOperation(value = "查询aio-edr相关统计")
    @PostMapping(value = "/{itemCode}")
    public ResponseBase getEdrStatistic(@ApiParam(value = "相关值(event/device/process/product/auth/operatingSystem/ip/ipCount)", required = true)
                                        @PathVariable(value = "itemCode", required = true) String itemCode,
                                        @ApiParam(value = "查询参数", required = true) @RequestBody ReportQryParam reportQryParam) {
        return this.getResponse(res -> {
            res.setData(reportItemFactoryService.getReportItemData(itemCode, reportQryParam));
            return res;
        });
    }

    @ApiOperation(value = "查询事件列表")
    @PostMapping(value = "/event/list")
    public ResponseBase getEventList(@ApiParam(value = "查询参数", required = true) @RequestBody EventQryParam eventQryParam) {
        return edrReportService.getEventList(eventQryParam, RequestUtil.getHeaderSid());
    }

    @ApiOperation(value = "查询事件详情列表")
    @PostMapping(value = "/event/detail/list")
    public ResponseBase getEventDetailList(@ApiParam(value = "查询参数", required = true) @RequestBody EventQryParam eventQryParam) {
        return edrReportService.getEventDetailList(eventQryParam, null , RequestUtil.getHeaderSid());
    }

    @ApiOperation(value = "查詢事件详情列表事件數量")
    @PostMapping(value = "/event/detail/list/count")
    public ResponseBase getEventDetailCount(@ApiParam(value = "查询参数", required = true) @RequestBody EventQryParam eventQryParam){
        return edrReportService.getEventDetailListCount(eventQryParam);
    }

    @ApiOperation(value = "查询事件详情")
    @PostMapping(value = "/event/detail/info")
    public ResponseBase getEventDetailinfo(@ApiParam(value = "查询参数", required = true) @RequestBody EventQryParam eventQryParam) {
        return edrReportService.getEventDetailInfo(eventQryParam);
    }

    @ApiOperation(value = "查询原始事件数据")
    @PostMapping(value = "/event/raw/data/list")
    public ResponseBase getEventRawDataList(@ApiParam(value = "查询参数", required = true) @RequestBody EdrEventRawParam edrEventRawParam) {
        try {
            return ResponseBase.ok(edrReportService.getEventRawDataList(edrEventRawParam));
        } catch (Exception ex) {
            log.error("getCollectorGroupList", ex);
            return ResponseBase.error(ex);
        }
    }

    @ApiOperation(value = "验证edr事件是否能立案(支持批量查询多个事件)")
    @PostMapping(value = "/event/issue/check")
    public ResponseBase checkEventIssue(@ApiParam(value = "查询参数", required = true) @RequestBody List<EdrEventRawParam> edrEventRawParams) {
        try {
            return edrReportService.checkEventIssue(edrEventRawParams);
        } catch (Exception ex) {
            log.error("getBatchEventRawDataList", ex);
            return ResponseBase.error(ex);
        }
    }

    @ApiOperation(value = "查询事件最近的rawId(支持批量查询多个事件)")
    @PostMapping(value = "/event/latest/rawId")
    public ResponseBase getEventLatestRawId(@ApiParam(value = "查询参数", required = true) @RequestBody List<EdrEventRawParam> edrEventRawParams) {
        try {
            return edrReportService.getEventLatestRawId(edrEventRawParams);
        } catch (Exception ex) {
            log.error("getBatchEventRawDataList", ex);
            return ResponseBase.error(ex);
        }
    }

    @ApiOperation(value = "事件處理紀錄")
    @PostMapping(value = "/event/process/record/save")
    public ResponseBase saveEventProcessRecord(@ApiParam(value = "處理紀錄参数", required = true) @RequestBody List<Map<String, String>> paramList) {
        List<EventRecordParam> eventRecordParamList = new ArrayList<>();
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date now = new Date();
        for (Map<String, String> map : paramList) {
            EventRecordParam eventRecordParam = new EventRecordParam();

            eventRecordParam.setSid(RequestUtil.getHeaderSidOrDefault(defaultSid));
            eventRecordParam.setServerId(Long.valueOf(map.get("serverId")));
            eventRecordParam.setEventId(Long.valueOf(map.get("eventId")));
            eventRecordParam.setProcessStatus(map.get("processStatus"));
            eventRecordParam.setProcessUserId(map.get("processUserId"));
            eventRecordParam.setDescription(map.get("description"));

            eventRecordParam.setCreateTime(dateFormat.format(now));
            eventRecordParamList.add(eventRecordParam);
        }
        return edrReportService.saveEventProcessRecord(eventRecordParamList);
    }

    @ApiOperation(value = "事件處理紀錄查詢")
    @PostMapping(value = "/event/process/record/search")
    public ResponseBase getEventProcessRecord(@ApiParam(value = "查询参数", required = true) @RequestBody EdrEventRawParam edrEventRawParam) {
        try {
            return ResponseBase.ok(edrReportService.getEventProcessRecord(edrEventRawParam));
        } catch (Exception ex) {
            log.error("getEventProcessRecord", ex);
            return ResponseBase.error(ex);
        }
    }

    @ApiOperation(value = "事件立即放行")
    @PostMapping(value = "/event/release/immediately")
    public ResponseBase setEventReleaseImmediately(@ApiParam(value = "查询参数", required = true) @RequestBody EventReleaseParam eventReleaseParam) {
        return edrReportService.setEventReleaseImmediately(eventReleaseParam);
    }

    @ApiOperation(value = "取消放行")
    @PostMapping(value = "/event/release/cancel")
    public ResponseBase setEventReleaseCancel(@ApiParam(value = "查询参数", required = true) @RequestBody EventReleaseParam eventReleaseParam) {
        return edrReportService.setEventReleaseCancel(eventReleaseParam);
    }
    @ApiOperation(value = "事件立即放行紀錄--匯入用")
    @PostMapping(value = "/event/release/record")
    public ResponseBase setEventReleaseRecord(@ApiParam(value = "查询参数", required = true) @RequestBody List<Map<String, String>> paramList,
                                              @ApiParam(value = "是否事件處理紀錄", required = true) @RequestParam(value = "saveProcessRecord", required = false) boolean saveProcessRecord,
                                              @ApiParam(value = "是否紀錄事件處理紀錄之建立時間", required = false) @RequestParam(value = "saveProcessRecordCreateTime", required = false) boolean saveProcessRecordCreateTime) {
        //EventReleaseParam eventReleaseParam
        List<EventReleaseParam> eventReleaseParamList = new ArrayList<>();
        for (Map<String, String> map : paramList) {
            EventReleaseParam eventReleaseParam = new EventReleaseParam();

            eventReleaseParam.setSid(RequestUtil.getHeaderSidOrDefault(defaultSid));
            eventReleaseParam.setServerId(Long.valueOf(map.get("serverId")));
            eventReleaseParam.setEventId(Long.valueOf(map.get("eventId")));
            eventReleaseParam.setReleaseRemark(map.get("releaseRemark"));
            eventReleaseParam.setReleaseStatus("release");

            eventReleaseParamList.add(eventReleaseParam);
        }
        return edrReportService.setEventReleaseRecord(eventReleaseParamList, saveProcessRecord, saveProcessRecordCreateTime);
    }

    @ApiOperation(value = "查询设备分组")
    @PostMapping(value = "/collector/group/list")
    public ResponseBase getCollectorGroupList(@ApiParam(value = "查询参数", required = true) @RequestBody QryParamBase qryParamBase) {
        try {
            return ResponseBase.ok(edrReportService.getCollectorGroupList(qryParamBase));
        } catch (Exception ex) {
            log.error("getCollectorGroupList", ex);
            return ResponseBase.error(ex);
        }
    }

    @ApiOperation(value = "查询设备列表")
    @PostMapping(value = "/collector/list")
    public ResponseBase getCollectorList(@ApiParam(value = "客代", required = false) @RequestParam(value = "serviceCode", required = false) String serviceCode,
                                         @ApiParam(value = "查询参数", required = true) @RequestBody EdrCollectorParam edrCollectorParam) {
        try {
            return ResponseBase.ok(edrReportService.getCollectorList(serviceCode, edrCollectorParam));
        } catch (Exception ex) {
            log.error("getCollectorList", ex);
            return ResponseBase.error(ex);
        }
    }

    @ApiOperation(value = "开启/关闭设备")
    @PostMapping(value = "/collector/operate")
    public ResponseBase operateCollector(@ApiParam(value = "设置参数", required = true) @RequestBody EdrCollectorParam edrCollectorParam) {
        try {
            edrReportService.operateCollector(edrCollectorParam);
            return ResponseBase.ok();
        } catch (Exception ex) {
            log.error("operateCollector", ex);
            return ResponseBase.error(ex);
        }
    }

    @ApiOperation(value = "查询设备系统")
    @PostMapping(value = "/collector/operating/system/list")
    public ResponseBase getCollectorOperatingSystemList(@ApiParam(value = "查询参数", required = true) @RequestBody QryParamBase qryParamBase) {
        try {
            return ResponseBase.ok(edrReportService.getCollectorOperatingSystemList(qryParamBase));
        } catch (Exception ex) {
            log.error("getCollectorList", ex);
            return ResponseBase.error(ex);
        }
    }

    @ApiOperation(value = "查询product列表")
    @PostMapping(value = "/product/list")
    public ResponseBase getProductList(@ApiParam(value = "查询参数", required = true) @RequestBody ProductQryParam productQryParam) {
        return edrReportService.getProductList(productQryParam);
    }

    @ApiOperation(value = "查询product详情列表")
    @PostMapping(value = "/product/detail/list")
    public ResponseBase getProductDetailList(@ApiParam(value = "查询参数", required = true) @RequestBody ProductQryParam productQryParam) {
        return edrReportService.getProductDetailList(productQryParam);
    }

    @ApiOperation(value = "设定应用程序策略")
    @PostMapping(value = "/product/permission/set")
    public ResponseBase setProductPermission(@ApiParam(value = "设定参数", required = true) @RequestBody SetPolicyPermissionParam setPolicyPermissionParam) {
        try {
            return ResponseBase.ok(edrReportService.setProductPermission(setPolicyPermissionParam));
        } catch (Exception ex) {
            log.error("getCollectorList", ex);
            return ResponseBase.error(ex);
        }
    }

    @ApiOperation(value = "获取应用程序策略")
    @PostMapping(value = "/product/permission/list")
    public ResponseBase getProductPermissionList(@ApiParam(value = "设定参数", required = true) @RequestBody QryParamBase qryParamBase) {
        try {
            return ResponseBase.ok(edrReportService.getProductPermissionList(qryParamBase));
        } catch (Exception ex) {
            log.error("getProductPermissionList", ex);
            return ResponseBase.error(ex);
        }
    }

    @ApiOperation(value = "端點防護設備操作記錄")
    @PostMapping(value = "/process/record")
    public ResponseBase saveProcessRecord(@RequestBody EdrOrgCollectorProcessRecordSaveDTO dto) {
        try {
            return ResponseBase.ok(edrReportService.saveProcessRecord(dto));
        } catch (Exception ex) {
            log.error("saveProcessRecord", ex);
            return ResponseBase.error(ex);
        }
    }

    @ApiOperation(value = "批量端點防護設備操作記錄")
    @PostMapping(value = "/process/records")
    public ResponseBase saveProcessRecords(@RequestBody List<EdrOrgCollectorProcessRecordSaveDTO> dtoList) {
        try {
            return ResponseBase.ok(edrReportService.saveProcessRecords(dtoList));
        } catch (Exception ex) {
            log.error("saveProcessRecord", ex);
            return ResponseBase.error(ex);
        }
    }

    @ApiOperation(value = "端點防護設備操作記錄")
    @GetMapping(value = "/process/record")
    public ResponseBase getProcessRecord(EdrOrgCollectorProcessRecord model) {
        return edrReportService.getProcessRecord(model);
    }


    @ApiOperation(value = "端點防護設備操作最後一筆关闭記錄")
    @GetMapping(value = "/process/record/latest")
    public ResponseBase getProcessRecordLatest(EdrOrgCollectorProcessRecord model) {
        try {
            return ResponseBase.ok(edrReportService.getProcessRecordLatest(model));
        } catch (Exception ex) {
            log.error("getProductPermissionList", ex);
            return ResponseBase.error(ex);
        }
    }

    @ApiOperation(value = "設備批量移動至其他群組")
    @PutMapping(value = "/collector/group/set")
    public BaseResponse setCollectorGroup(@RequestBody EdrCollectorSetGroupParam dto){
        return edrReportService.setCollectorGroup(dto);
    }

    @ApiOperation(value = "設備刪除")
    @DeleteMapping(value = "/collector/del")
    public BaseResponse deleteCollectorGroup(@RequestBody EdrCollectorDeleteParam dto){
        return edrReportService.deleteCollectorGroup(dto);
    }

    @ApiOperation(value = "端點防護1.0-事件列表匯出")
    @PostMapping(value = "/event/list/export")
    public BaseResponse exportExcel(HttpServletResponse response, @RequestBody EventQryParam eventQryParam) {
        try {
            edrReportService.exportExcel(response, eventQryParam, RequestUtil.getHeaderSid());
            return BaseResponse.ok();
        }
        catch (Exception e) {
            log.error("EDR Event List Export Failed: ", e);
            BaseResponse.error(e);
        }
        return BaseResponse.ok();
    }

    @ApiOperation(value = "添加操作記錄")
    @PostMapping("/auto/process/record")
    public BaseResponse saveAutoProcessRecord(@RequestBody AutoProcessRecord autoProcessRecord){
        try {
            return edrReportService.saveAutoProcessRecord(autoProcessRecord);
        } catch (Exception ex) {
            log.error("saveAutoProcessRecord", ex);
            return BaseResponse.error(ex);
        }
    }

    @ApiOperation(value = "取得操作記錄")
    @GetMapping("/auto/process/record")
    public BaseResponse getAutoProcessRecord(@RequestParam String eid,
                                             @RequestParam String id){
        try {
            return edrReportService.getAutoProcessRecord(eid, id);
        } catch (Exception ex) {
            log.error("getAutoProcessRecord", ex);
            return BaseResponse.error(ex);
        }
    }

    @ApiOperation(value = "刪除自動寄信排程")
    @DeleteMapping("/auto/del")
    public BaseResponse autoSetDel(@RequestParam String id){
        try {
            return edrReportService.autoSetDel(id);
        } catch (Exception ex) {
            log.error("autoSetDel", ex);
            return BaseResponse.error(ex);
        }
    }

    @ApiOperation(value = "自動寄信排程列表查詢")
    @GetMapping("/auto/set/list")
    public BaseResponse getAutoMailSendList(@RequestParam(value = "page", required = false) Integer page,
                                    @RequestParam(value = "size", required = false) Integer size,
                                    @RequestParam(value = "modelCode", required = false) List<String> modelCode,
                                    @RequestParam(value = "type", required = false) List<Integer> type,
                                    @RequestParam(value = "cycle", required = false) List<String> cycle,
                                    @RequestParam(value = "status", required = false) Integer status,
                                    @RequestParam(value = "receiverMail", required = false) String receiverMail,
                                    @RequestParam(value = "processUserName", required = false) String processUserName,
                                    @RequestParam(value = "serviceCode", required = true) String serviceCode) {

        AutoMailSendListParams autoMailSendListParams = new AutoMailSendListParams(page, size, modelCode, type,
                cycle, status, receiverMail, processUserName,serviceCode);

        try {
            return edrReportService.getAutoMailSendList(autoMailSendListParams);
        } catch (Exception ex) {
            log.error("getAutoMailSendList", ex);
            return BaseResponse.error(ex);
        }
    }
}
