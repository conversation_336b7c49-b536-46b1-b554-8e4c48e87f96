package com.digiwin.escloud.aioitms.report.service.db;

import com.digiwin.escloud.aioitms.es.service.EsService;
import com.digiwin.escloud.aioitms.report.annotation.DbTypeCode;
import com.digiwin.escloud.aioitms.report.annotation.EsDbServiceCode;
import com.digiwin.escloud.aioitms.report.annotation.EsIndexCode;
import com.digiwin.escloud.aioitms.report.model.base.AppInfo;
import com.digiwin.escloud.aioitms.report.model.base.AppType;
import com.digiwin.escloud.aioitms.report.model.base.DbReport;
import com.digiwin.escloud.aioitms.report.model.db.DbReportRecord;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.data.elasticsearch.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.Callable;

@Service
public class DbDataFactory implements ApplicationContextAware, InitializingBean {

    private static Map<String, BaseDbData> itemMap = null;

    private static Map<String, EsService> esServiceMap = null;

    private static Map<String, DbReport> dbReportMap = null;

    private ApplicationContext applicationContext;

    @Override
    public void afterPropertiesSet() throws Exception {
        Map<String, Object> dbTypeBeans = applicationContext.getBeansWithAnnotation(DbTypeCode.class);
        if (dbTypeBeans != null) {
            itemMap = new HashMap<>();
            dbTypeBeans.forEach((key, value) -> {
                String bizType = value.getClass().getAnnotation(DbTypeCode.class).value();
                itemMap.put(bizType, (BaseDbData) value);
            });
        }

        Map<String, Object> esDbServiceBeans = applicationContext.getBeansWithAnnotation(EsDbServiceCode.class);
        if (esDbServiceBeans != null) {
            esServiceMap = new HashMap<>();
            esDbServiceBeans.forEach((key, value) -> {
                String bizType = value.getClass().getAnnotation(EsDbServiceCode.class).value();
                esServiceMap.put(bizType, (EsService) value);
            });
        }

        Map<String, Object> esIndexBeans = applicationContext.getBeansWithAnnotation(EsIndexCode.class);
        if (esIndexBeans != null) {
            dbReportMap = new HashMap<>();
            esIndexBeans.forEach((key, value) -> {
                String bizType = value.getClass().getAnnotation(EsIndexCode.class).value();
                dbReportMap.put(bizType, (DbReport) value);

                // 另外指定ORACLE_V2也指向ORACLE
                if ("ORACLE".equals(bizType)) {
                    dbReportMap.put("ORACLE_V2", (DbReport) value);
                }
            });
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    public List<Callable<Object>> getReportItemData(String dbTypeCode, DbReportRecord dbReportRecord) {

        List<Callable<Object>> callables = new ArrayList<>();
        BaseDbData baseReport = itemMap.get(dbTypeCode);
        DbReportRecord cloneDbReportRecord = dbReportRecord.cloneDbReportRecord();
        cloneDbReportRecord.setOriProductCode(dbReportRecord.getProductCode());
        callables.addAll(baseReport.getReportItems(cloneDbReportRecord));
        return callables;



//        BaseDbData report = itemMap.get(dbTypeCode);
//        boolean isNotFoundReport = Objects.isNull(report);
//        if (isNotFoundReport) {
////            return new ArrayList<>(0);
//            // 啟用預設產品線的分析, 啟用後, 在調用getCurrentType() 時, 會直接取預設產線
//            // 找不到 Report 就要走預設的產線的報告生成 logic.
//            dbReportRecord.setUseDefReportType(true);
//            String defDbTypeCode = dbReportRecord.getCurrentType();
//            report = itemMap.get(defDbTypeCode);
//        }

        //FIXME 進行設備平台分類
        //FIXME 先暫不啟用, 先跟著產線跑.
//        if (report instanceof AdvBaseDbData) {
//            Map<String, AppInfo> deviceNameMap = ((AdvBaseDbData<?>) report).getDeviceType(dbReportRecord);
//            List<String> winDevice = new ArrayList<>();
//            List<String> linuxDevice = new ArrayList<>();
//            dbReportRecord.getDeviceIdList()
//                    .forEach(deviceId -> {
//                        AppInfo ai = deviceNameMap.getOrDefault(deviceId, new AppInfo());
//                        switch (ai.getPlatform()) {
//                            case LINUX:
//                                linuxDevice.add(deviceId);
//                                break;
//                            case WINDOWS:
//                                winDevice.add(deviceId);
//                                break;
//                        }
//                    });
//
//
//
//            if (!winDevice.isEmpty()) {
//                BaseDbData defSqlServerReport = itemMap.get("9999_MSSQL");
//                DbReportRecord cloneDbReportRecord = dbReportRecord.cloneDbReportRecord();
//                cloneDbReportRecord.setDeviceIdList(winDevice);
//                //todo 需要调整
//                cloneDbReportRecord.setProductCode("9999");
//                cloneDbReportRecord.setOriProductCode(dbReportRecord.getProductCode());
//                cloneDbReportRecord.setDbType(AppType.MSSQL.name());
//                callables.addAll(defSqlServerReport.getReportItems(cloneDbReportRecord));
//            }
//
//            if (!linuxDevice.isEmpty()) {
//                BaseDbData defOracleReport = itemMap.get("9999_ORACLE");
//                DbReportRecord cloneDbReportRecord = dbReportRecord.cloneDbReportRecord();
//                cloneDbReportRecord.setDeviceIdList(linuxDevice);
//                //todo 需要调整
//                cloneDbReportRecord.setProductCode("9999");
//                cloneDbReportRecord.setOriProductCode(dbReportRecord.getProductCode());
//                cloneDbReportRecord.setDbType(AppType.ORACLE.name());
//                callables.addAll(defOracleReport.getReportItems(cloneDbReportRecord));
//            }
//            return callables;
//        }

    }

    public <T extends DbReport> Class getDbReportClass(String dbTypeCode) {
        DbReport dbReport = dbReportMap.get(dbTypeCode);
        if (Objects.isNull(dbReport)) {
            return DbReport.class;
        }
        return dbReport.getClass();
    }

    public Object getDbReportData(String id, String dbTypeCode) {
        Class<? extends DbReport> clazz = getDbReportClass(dbTypeCode);
        EsService esService = esServiceMap.get(dbTypeCode);
        if (Objects.isNull(esService)) {
            return new Object();
        }
        Object obj = esService.findById(id, clazz);
        return obj;
    }

    public List<Object> getDbReportData(Query query, String dbTypeCode) {
        Class<? extends DbReport> clazz = getDbReportClass(dbTypeCode);
        EsService esService = esServiceMap.get(dbTypeCode);
        if (Objects.isNull(esService)) {
            return new ArrayList<>();
        }
        List<Object> list = esService.find(query, clazz);
        return list;
    }

    public <T extends DbReport> T saveDbReportData(T t, String dbTypeCode, boolean isAdd) {
        EsService esService = esServiceMap.get(dbTypeCode);
        if (Objects.isNull(esService)) {
            return null;
        }
        return (T) esService.save(t, isAdd);
    }

    public <T extends DbReport> void saveDbReportItem(String id, String field, Object value, String dbTypeCode,
                                                      boolean useScript,String reportReferenceScoreType) {
        Class<? extends DbReport> clazz = getDbReportClass(dbTypeCode);
        EsService esService = esServiceMap.get(dbTypeCode);
        if (Objects.isNull(esService)) {
            return;
        }
        if (useScript) {
            esService.updateByScript(id, field, value, clazz,reportReferenceScoreType);
        } else {
            esService.update(id, field, value, clazz);
        }
    }

    public <T extends DbReport> void saveDbReportItems(String id, Map<String, Object> UpdateFields,String dbTypeCode) {
        Class<? extends DbReport> clazz = getDbReportClass(dbTypeCode);
        EsService esService = esServiceMap.get(dbTypeCode);
        esService.bulkUpdateByScript(id, UpdateFields, clazz);
    }

    public String deleteDbReport(String id, String dbTypeCode) {
        Class<? extends DbReport> clazz = getDbReportClass(dbTypeCode);
        EsService esService = esServiceMap.get(dbTypeCode);
        if (Objects.isNull(esService)) {
            return "";
        }
        return esService.deleteById(id, clazz);
    }

    // 檢查報告類型是不是存
    public boolean checkReportIsExist(String dbTypeCode) {
        return Optional.ofNullable(dbReportMap.get(dbTypeCode)).isPresent();
    }
}
