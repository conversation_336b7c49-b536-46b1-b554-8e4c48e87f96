package com.digiwin.escloud.aioitms.bcp.service.impl;

import com.digiwin.escloud.aioitms.bcp.dao.BcpMapper;
import com.digiwin.escloud.aioitms.bcp.model.*;
import com.digiwin.escloud.aioitms.bcp.service.IBcpService;
import com.digiwin.escloud.aioitms.bigdata.BigDataUtil;
import com.digiwin.escloud.aioitms.util.MessageUtil;
import com.digiwin.escloud.common.controller.ParamCheckHelp;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.CollectionUtil;
import com.digiwin.escloud.common.util.IntegerUtil;
import com.digiwin.escloud.common.util.SnowFlake;
import com.digiwin.escloud.common.util.StringUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.apache.commons.lang.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class BcpServiceImpl implements IBcpService, ParamCheckHelp {
    @Autowired
    BcpMapper bcpMapper;
    @Autowired
    MessageUtil messageUtil;
    @Autowired
    BigDataUtil bigDataUtil;

    @Override
    public BaseResponse getData(String version, String lang) {

        // 查詢問卷資訊
        List<BcpSurveyData> bcpSurveyData = bcpMapper.selectBcpSurveyData(version);

        // 處理i18n
        processDataListLanguage(bcpSurveyData, lang);

        // 回傳
        return BaseResponse.ok(bcpSurveyData);
    }

    @Override
    public BaseResponse getResultBySurveyId(String surveyId, String lang) {

        // 取得自評數據
        List<BcpSurveyResult> bcpSurveyResult = bcpMapper.selectBcpSurveyResult(surveyId);

        // 處理i18n
        processResultListLanguage(bcpSurveyResult, lang);

        // 回傳
        return BaseResponse.ok(bcpSurveyResult);
    }

    @Override
    public BaseResponse getResultList(BcpRequestParam param) {

        // 分頁查詢清單
        Page page = PageHelper.startPage(param.getPageNum(), param.getPageSize());
        bcpMapper.selectBcpSurveyRecord(param);
        PageInfo<BcpSurveyRecord> pageInfo = new PageInfo<BcpSurveyRecord>(page);

        // 將bcp分頁數據倒到回傳格式中
        BcpSurveyListRes res = new BcpSurveyListRes();
        BcpSurveyPageInfo bcpSurveyPageInfo = convert(pageInfo);

        // 取得額外欄位數據
        Integer accountResetCount = bcpMapper.getAccountResetCount();
        if (IntegerUtil.isNotEmpty(accountResetCount)) {
            bcpSurveyPageInfo.setAccountResetCount(accountResetCount);
        }
        Integer nonResetCount = bcpMapper.getNonResetCount();
        if (IntegerUtil.isNotEmpty(nonResetCount)) {
            bcpSurveyPageInfo.setNoneResetCount(nonResetCount);
        }
        List<BcpCustomer> customerList = bcpMapper.getCustomerList();
        if (CollectionUtil.isNotEmpty(customerList)) {
            bcpSurveyPageInfo.setCustomerList(customerList);
        }

        // 若eid有傳入，取得最新一筆紀錄，否則為null
        if (StringUtil.isNotEmpty(param.getEid())) {
            BcpSurveyResult lastResult = bcpMapper.getLastResult(param.getEid());

            // 處理i18n
            if (Objects.nonNull(lastResult)) {
                processResultLanguage(lastResult, param.getLang());
            }

            res.setLast(lastResult);
        }

        // 寫回到回傳參數中
        res.setBcp(bcpSurveyPageInfo);

        return BaseResponse.ok(res);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseResponse saveRecord(BcpSurveyRecordSave bcpSurveyRecordSave) {
        try {
            // 設置自評記錄-主檔 id
            String recordId = String.valueOf(SnowFlake.getInstance().newId());
            bcpSurveyRecordSave.setId(recordId);

            // 儲存自評記錄-主檔
            Optional.ofNullable(saveBcpSurveyRecord(bcpSurveyRecordSave))
                    .filter(tasksAffectedRow -> tasksAffectedRow > 0)
                    .orElseThrow(() -> new RuntimeException("bcp survey record save error"));
            // 儲存自評記錄-明細
            Optional.ofNullable(saveBcpSurveyDataRecord(bcpSurveyRecordSave))
                    .filter(tasksAffectedRow -> tasksAffectedRow > 0)
                    .orElseThrow(() -> new RuntimeException("bcp survey data record save error"));
            // 儲存自評記錄-結果
            Optional.ofNullable(saveBcpSurveyResultRecord(bcpSurveyRecordSave))
                    .filter(tasksAffectedRow -> tasksAffectedRow > 0)
                    .orElseThrow(() -> new RuntimeException("bcp survey result record save error"));

            return BaseResponse.ok();
        } catch (Exception e) {
            log.error("Save BCP Result Error:", e);
            return BaseResponse.error(ResponseCode.SAVE_BCP_RESULT_ERROR);
        }

    }

    private static <T> BcpSurveyPageInfo convert(PageInfo<T> pageInfo) {
        BcpSurveyPageInfo result = new BcpSurveyPageInfo();
        BeanUtils.copyProperties(pageInfo, result);
        return result;
    }

    private void processResultLanguage(BcpSurveyResult bcpSurveyResult, String lang) {
        if (CollectionUtil.isNotEmpty(bcpSurveyResult.getSurvey())) {
            bcpSurveyResult.getSurvey().forEach(category -> {
                translateStringFields(category, lang);
            });
        }
    }

    private void processResultListLanguage(List<BcpSurveyResult> bcpSurveyResult, String lang) {
        bcpSurveyResult.forEach(data -> {
            translateStringFields(data, lang);

            if (CollectionUtil.isNotEmpty(data.getSurvey())) {
                data.getSurvey().forEach(category -> {
                    translateStringFields(category, lang);

                    if (CollectionUtil.isNotEmpty(category.getTags())) {
                        category.getTags().forEach(tag -> translateStringFields(tag, lang));
                    }
                });
            }
        });
    }

    private void processDataListLanguage(List<BcpSurveyData> bcpSurveyData, String lang) {
        bcpSurveyData.forEach(data -> {
            translateStringFields(data, lang);

            if (CollectionUtil.isNotEmpty(data.getQuestions())) {
                data.getQuestions().forEach(question -> {
                    translateStringFields(question, lang);

                    if (CollectionUtil.isNotEmpty(question.getAnswers())) {
                        question.getAnswers().forEach(answer -> translateStringFields(answer, lang));
                    }
                });
            }
        });
    }

    private void translateStringFields(Object object, String lang) {
        Arrays.stream(object.getClass().getDeclaredFields())
                .filter(field -> field.getType() == String.class)
                .forEach(field -> {
                    field.setAccessible(true);
                    try {
                        String original = (String) field.get(object);
                        if (StringUtil.isNotEmpty(original)) {
                            String translated = messageUtil.noExceptionLogGet(original, lang);
                            field.set(object, translated);
                        }
                    } catch (IllegalAccessException e) {
                        log.warn("Translation failed for field: " + field.getName(), e);
                    }
                });
    }

    private Integer saveBcpSurveyRecord(BcpSurveyRecordSave bcpSurveyRecordSave) {
        // 取得客戶統編
        if (!bcpSurveyRecordSave.getEid().isEmpty() && !bcpSurveyRecordSave.getServiceCode().isEmpty()) {
            String sql = "select customerTaxId from servicecloud.ACP_Customer_Info where eid='" + bcpSurveyRecordSave.getEid() + "' limit 1";
            List<Map<String, Object>> map = bigDataUtil.srQuery(sql);
            bcpSurveyRecordSave.setTaxId(map.isEmpty() ? "" : ObjectUtils.toString(map.get(0).get("customerTaxId"), ""));
        }

        // 儲存自評記錄-主檔
        bcpSurveyRecordSave.setEid(StringUtil.isEmpty(bcpSurveyRecordSave.getEid()) ? null : bcpSurveyRecordSave.getEid());
        return bcpMapper.insertBcpSurveyRecord(bcpSurveyRecordSave);
    }

    private Integer saveBcpSurveyDataRecord(BcpSurveyRecordSave bcpSurveyRecordSave) {
        // 匯集answerId
        List<String> answerIdList = bcpSurveyRecordSave.getList().stream()
                .map(BcpSurveyDataRecord::getAnswerId).collect(Collectors.toList());

        // 計算"權重得分"及"答案得分"
        List<Map<String, Object>> sourceList = bcpMapper.getBcpSurveyScore(answerIdList);

        // 儲存自評記錄-明細
        bcpSurveyRecordSave.getList().forEach(answer -> {
            answer.setBsrId(bcpSurveyRecordSave.getId());
            sourceList.stream().filter(source ->
                            ObjectUtils.toString(source.get("answerId"), "").equals(answer.getAnswerId()))
                    .forEach(source -> {
                        answer.setWeightScore(Double.valueOf(ObjectUtils.toString(source.get("weightScore"))));
                        answer.setAnswerScore(Double.valueOf(ObjectUtils.toString(source.get("answerScore"))));
                    });
        });
        return bcpMapper.insertBcpSurveyDataRecord(bcpSurveyRecordSave.getList());
    }

    private Integer saveBcpSurveyResultRecord(BcpSurveyRecordSave bcpSurveyRecordSave){
        // 匯集answerId
        List<String> answerIdList = bcpSurveyRecordSave.getList().stream()
                .map(BcpSurveyDataRecord::getAnswerId).collect(Collectors.toList());

        // 計算"類別得分"
        List<Map<String, Object>> categoryScoreList = bcpMapper.getBcpSurveyCategoryScore(answerIdList);

        // 儲存自評記錄-結果
        List<BcpSurveyResultRecord> bcpSurveyResultRecordList = new ArrayList<>();
        categoryScoreList.forEach(source -> {
            BcpSurveyResultRecord bcpSurveyResultRecord = new BcpSurveyResultRecord();
            bcpSurveyResultRecord.setBsrId(bcpSurveyRecordSave.getId());
            bcpSurveyResultRecord.setCategoryId(ObjectUtils.toString(source.get("categoryId"), ""));
            bcpSurveyResultRecord.setCategoryScore(ObjectUtils.toString(source.get("categoryScore"), ""));
            bcpSurveyResultRecordList.add(bcpSurveyResultRecord);
        });
        return bcpMapper.insertBcpSurveyResultRecord(bcpSurveyResultRecordList);
    }

    @Override
    public BaseResponse saveCustomerData(BcpSurveyRecord param) {
        // region 參數檢查
        List<String> params = Stream.of(
                        new AbstractMap.SimpleEntry<>("surveyId", checkParamIsEmpty(param.getSurveyId(), "surveyId")),
                        new AbstractMap.SimpleEntry<>("companyName", checkParamIsEmpty(param.getCompanyName(), "companyName")),
                        new AbstractMap.SimpleEntry<>("taxId", checkParamIsEmpty(param.getTaxId(), "taxId")),
                        new AbstractMap.SimpleEntry<>("userId", checkParamIsEmpty(param.getUserId(), "userId")),
                        new AbstractMap.SimpleEntry<>("userName", checkParamIsEmpty(param.getUserName(), "userName")))
                .filter(entry -> entry.getValue().isPresent())
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(params)) {
            return BaseResponse.dynamicError(ResponseCode.PARAM_IS_EMPTY, String.join(", ", params));
        }
        // endregion

        // 檢查serviceCode
        List<String> serviceCodes = bcpMapper.serviceCodeCheck(param.getUserId());
        if(serviceCodes.size() == 1){
            param.setServiceCode(serviceCodes.get(0));
        }

        bcpMapper.updateBcpSurveyRecord(param);
        return BaseResponse.ok();
    }

}
