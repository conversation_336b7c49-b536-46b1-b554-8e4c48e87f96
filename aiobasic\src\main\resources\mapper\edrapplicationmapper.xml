<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.digiwin.escloud.aiobasic.edrv2.dao.EdrApplicationMapper">
    <insert id="upsertApplication" parameterType="com.digiwin.escloud.aiobasic.edrv2.model.EdrApplication">
        INSERT INTO sentinelone_applications(id, eid, siteId, applicationId, name, vendor, cveCount, daysDetected,
                                             detectionDate, endpointCount, highestNvdBaseScore, highestSeverity, estimate)
        VALUES
        <foreach collection="edrApplicationList" item="application" separator=",">
            (#{application.id}, #{application.eid},#{application.siteId},#{application.applicationId},#{application.name},
            #{application.vendor},#{application.cveCount},#{application.daysDetected}, #{application.detectionDate},
            #{application.endpointCount},#{application.highestNvdBaseScore},#{application.highestSeverity}, #{application.estimate})
        </foreach>
        ON DUPLICATE KEY UPDATE
        eid = VALUES(eid),
        siteId = VALUES(siteId),
        applicationId = VALUES(applicationId),
        name = VALUES(name),
        vendor = VALUES(vendor),
        cveCount = VALUES(cveCount),
        daysDetected = VALUES(daysDetected),
        detectionDate = VALUES(detectionDate),
        endpointCount = VALUES(endpointCount),
        highestNvdBaseScore = VALUES(highestNvdBaseScore),
        highestSeverity = VALUES(highestSeverity),
        estimate = VALUES(estimate);
    </insert>

    <select id="getApplicationList" parameterType="com.digiwin.escloud.aiobasic.edrv2.model.EdrApplicationParam"
            resultType="com.digiwin.escloud.aiobasic.edrv2.model.EdrApplication">
        SELECT id, eid, siteId, applicationId, name, vendor, cveCount, daysDetected, detectionDate,
               endpointCount, highestNvdBaseScore, highestSeverity, estimate, createdAt, updatedAt
        FROM sentinelone_applications
        WHERE eid=#{eid}
        <if test="siteId != null and siteId != ''">
            AND siteId = #{siteId}
        </if>
        <if test="name != null and name != ''">
            AND LOWER(name) LIKE LOWER(CONCAT('%', #{name}, '%'))
        </if>
        <if test="highestSeverity != null and !highestSeverity.isEmpty()">
            AND LOWER(highestSeverity) in (
                <foreach collection="highestSeverity" item="severity" separator=",">
                    LOWER(#{severity})
                </foreach>
            )
        </if>
        <if test="highestNvdBaseScore != null and !highestNvdBaseScore.isEmpty()">
            AND FLOOR(highestNvdBaseScore) in (
            <foreach collection="highestNvdBaseScore" item="score" separator=",">
                #{score}
            </foreach>
            )
        </if>
        <if test="detectionStartDate != null and detectionStartDate != '' and detectionEndDate != null and detectionEndDate != ''">
            AND detectionDate &gt;=#{detectionStartDate} AND detectionDate &lt;=#{detectionEndDate}
        </if>
        ORDER BY detectionDate ${detectionDateSort},
        FIELD(highestSeverity, 'CRITICAL', 'HIGH', 'MEDIUM', 'LOW', 'NONE') ASC
    </select>

    <select id="queryCriticalVulnerabilityTotalAmount" resultType="java.util.Map">
        SELECT COUNT(*) count
        FROM sentinelone_applications
        WHERE eid=#{eid} AND highestSeverity = 'CRITICAL'
        <if test="timeFrom != null and timeTo != null">
            AND detectionDate &gt;=#{timeFrom} AND detectionDate &lt;=#{timeTo}
        </if>
    </select>

    <select id="queryAllSeverityTotalAmount" resultType="java.util.Map">
        SELECT highestSeverity title, COUNT(highestSeverity) count
        FROM sentinelone_applications
        WHERE eid=#{eid}
        <if test="timeFrom != null and timeTo != null">
            AND detectionDate &gt;=#{timeFrom} AND detectionDate &lt;=#{timeTo}
        </if>
        GROUP BY highestSeverity
    </select>

    <delete id="removeApplication">
        DELETE FROM sentinelone_applications WHERE siteId=#{siteId}
        <if test="applicationIdList != null">
            AND applicationId in (
                <foreach collection="applicationIdList" item="id" separator=",">
                    #{id}
                </foreach>
            )
        </if>
    </delete>

    <select id="getAccountIdsBySiteId" resultType="java.lang.String">
        SELECT accountId FROM sentinelone_sites WHERE id = #{siteId}
    </select>

    <select id="getAccountIdListBySiteIds" resultType="com.digiwin.escloud.aiobasic.edrv2.model.EdrCustomerOrg">
        SELECT id, accountId
        FROM sentinelone_sites
        WHERE id IN (
            <foreach collection="siteIds" item="siteId" separator=",">
                #{siteId}
            </foreach>
        )
    </select>

    <select id="getAgentIp" resultType="java.util.Map">
        SELECT agentId, lastReportedIP FROM sentinelone_agents WHERE
        <foreach collection="agentIpParamsList" item="agentIpParams" separator=" OR " open="(" close=")">
            agentId = #{agentIpParams.agentId}
        </foreach>
    </select>

</mapper>