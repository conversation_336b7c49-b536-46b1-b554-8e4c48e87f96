package com.digiwin.escloud.aiobasic.report.model;

import com.digiwin.escloud.common.util.DateUtil;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Data
public class EdrAgentField {
    private String id;
    private String eid;
    private String accountId;
    private String siteId;
    private String agentId;
    private String agentUUId;
    private String agentType;
    private String siteName;
    private String endpointName;
    private String lastReportedIP;
    private String groupTitle;
    private String deviceType;
    private String modelName;
    private String os;
    private String architecture;
    private String osName;
    private String osRevision;
    private Date lastRebootDate;
    private String operationalState;
    private String healthState;
    private String managmentConnectivity;
    private Date subscribedOn;
    private Date lastActive;
    private Date agentClosedTime;
    private Date agentPreActiveTime;
    private Date agentLastActiveTime;
    private String scanStatus;  // 全機掃描狀態
    private Date lastSuccessfulScanDate; // 狀態時間
    private Date finalSuccessfulScanDate; // 最後一次完成掃描時間
    private Boolean isPendingUninstall; // 是否待移除
    private Boolean isUninstalled;  // 是否已移除
    private String networkStatus;   // 網路狀態
    private Boolean planStatus; // 是否未進行排程計劃之設備 1 = 已執行 ， 0 = 未執行 -1 = 未納入排程之設備
    private Date nextExcuteTime; // 下次掃描時間
    private Date createdAt;
    private Date updatedAt;
    private Boolean isWarningEnable;
    private Date pendingUninstallTime;

    @JsonIgnore
    private Date lastExecutionTime;
    @JsonIgnore
    private String crons;

    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("id", id);
        map.put("eid", eid);
        map.put("siteId", siteId);
        map.put("agentId", agentId);
        map.put("agentUUId", agentUUId);
        map.put("agentType", agentType);
        map.put("siteName", siteName);
        map.put("endpointName", endpointName);
        map.put("lastReportedIP", lastReportedIP);
        map.put("groupTitle", groupTitle);
        map.put("deviceType", deviceType);
        map.put("modelName", modelName);
        map.put("os", os);
        map.put("architecture", architecture);
        map.put("osName", osName);
        map.put("osRevision", osRevision);
        map.put("lastRebootDate", formatDate(lastRebootDate));
        map.put("operationalState", operationalState);
        map.put("healthState", healthState);
        map.put("managmentConnectivity", managmentConnectivity);
        map.put("subscribedOn", formatDate(subscribedOn));
        map.put("lastActive", formatDate(lastActive));
        map.put("agentClosedTime", formatDate(agentClosedTime));
        map.put("agentPreActiveTime", formatDate(agentPreActiveTime));
        map.put("agentLastActiveTime", formatDate(agentLastActiveTime));
        map.put("scanStatus", scanStatus);
        map.put("planStatus", planStatus);
        map.put("lastSuccessfulScanDate", formatDate(lastSuccessfulScanDate));
        map.put("finalSuccessfulScanDate",formatDate(finalSuccessfulScanDate));
        map.put("nextExcuteTime", formatDate(nextExcuteTime));
        map.put("isPendingUninstall", isPendingUninstall);
        map.put("isUninstalled", isUninstalled);
        map.put("networkStatus", networkStatus);
        map.put("createdAt", formatDate(createdAt));
        map.put("updatedAt", formatDate(updatedAt));
        return map;
    }

    private String formatDate(Date date) {
        return Objects.nonNull(date) ? DateUtil.getSomeDateFormatString(date, DateUtil.DATE_TIME_FORMATTER) : null;
    }
}
