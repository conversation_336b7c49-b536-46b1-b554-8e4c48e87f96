package com.digiwin.escloud.aiocmdb.asset.dao;

import com.digiwin.escloud.aiocmdb.asset.model.AssetCategory;
import com.digiwin.escloud.aiocmdb.asset.model.AssetCategoryClassification;
import com.digiwin.escloud.aiocmdb.asset.model.AssetCategoryCodingRule;
import com.digiwin.escloud.aiocmdb.asset.model.AssetCategoryCodingRuleSettingResult;
import com.digiwin.escloud.aiocmdb.asset.model.CmdbModelDataFieldRelationMapping;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 资产类别分类表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
public interface AssetCategoryMapper  {

    // AssetCategoryClassification 相关方法
    int insertAssetCategoryClassification(AssetCategoryClassification classification);

    int updateAssetCategoryClassification(AssetCategoryClassification classification);

    int deleteAssetCategoryClassification(@Param("id") Long id);

    List<AssetCategoryClassification> selectAssetCategoryClassificationList();

    AssetCategoryClassification selectAssetCategoryClassificationById(@Param("id") Long id);

    int countByCategoryName(@Param("categoryName") String categoryName, @Param("excludeId") Long excludeId);

    int countAssetCategoryByClassificationId(@Param("classificationId") Long classificationId);

    // AssetCategory 相关方法
    int insertAssetCategory(AssetCategory category);

    int updateAssetCategory(AssetCategory category);

    int deleteAssetCategory(@Param("id") Long id);

    List<AssetCategory> selectAssetCategoryList(@Param("classificationId") Long classificationId);

    AssetCategory selectAssetCategoryById(@Param("id") Long id);

    int countBySidScopeIdCategoryNumber(@Param("sid") Long sid, @Param("scopeId") String scopeId,
                                       @Param("categoryNumber") String categoryNumber, @Param("excludeId") Long excludeId);

    // CmdbModelDataFieldRelationMapping 相关方法
    int insertCmdbModelDataFieldRelationMapping(CmdbModelDataFieldRelationMapping mapping);

    int deleteCmdbModelDataFieldRelationMappingByTargetModelCode(@Param("targetModelCode") String targetModelCode);

    List<CmdbModelDataFieldRelationMapping> selectCmdbModelDataFieldRelationMappingByTargetModelCode(@Param("targetModelCode") String targetModelCode);

    int countByTargetModelCodeAndFieldName(@Param("targetModelCode") String targetModelCode,
                                          @Param("targetModelFieldName") String targetModelFieldName,
                                          @Param("excludeId") Long excludeId);

    // AssetCategoryCodingRule 相关方法
    List<AssetCategoryCodingRule> selectAllAssetCategoryCodingRule();

    // AssetCategoryCodingRuleSettingResult 相关方法
    List<AssetCategoryCodingRuleSettingResult> selectAssetCategoryCodingRuleSettingResultByObjId(@Param("objId") Long objId);

    int deleteAssetCategoryCodingRuleSettingResult(@Param("objId") Long objId);

    int insertAssetCategoryCodingRuleSettingResult(AssetCategoryCodingRuleSettingResult result);

}
