package com.digiwin.escloud.aiobasic.edrv2.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class EdrCustomerOrg {
    private Long id;
    private Long eid;
    private Long accountId;
    private String serviceCode;
    private String customerFullName;
    private String name;
    private String state;
    private String processUserName;
    private String voidReason;
    private Integer activeLicenses;
    private Integer totalLicenses;
    private Boolean unlimitedLicenses;
    private Boolean unlimitedExpiration;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expiration;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt;

    private Boolean checkEid = true;
    private Long originId;

    private String customerName;
    private String platform = "Aiops"; // Aiops: Aiops平台, iPass: iPass平台
}
