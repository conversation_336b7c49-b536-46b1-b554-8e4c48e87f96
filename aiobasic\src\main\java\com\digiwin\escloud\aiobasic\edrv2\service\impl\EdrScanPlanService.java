package com.digiwin.escloud.aiobasic.edrv2.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.cronutils.model.Cron;
import com.cronutils.model.CronType;
import com.cronutils.model.definition.CronDefinitionBuilder;
import com.cronutils.model.time.ExecutionTime;
import com.cronutils.parser.CronParser;
import com.digiwin.escloud.aiobasic.edr.model.edr.EdrOrgCollectorProcessRecordSaveDTO;
import com.digiwin.escloud.aiobasic.edr.service.edr.IEdrReportService;
import com.digiwin.escloud.aiobasic.edrv2.dao.EdrAgentMapper;
import com.digiwin.escloud.aiobasic.edrv2.dao.EdrScanPlanMapper;
import com.digiwin.escloud.aiobasic.edrv2.model.*;
import com.digiwin.escloud.aiobasic.edrv2.service.IEdrAgentService;
import com.digiwin.escloud.aiobasic.edrv2.service.IEdrScanPlanService;
import com.digiwin.escloud.aiobasic.report.model.EdrAgentField;
import com.digiwin.escloud.aiobasic.util.BigDataUtil;
import com.digiwin.escloud.aiobasic.util.DsUtil;
import com.digiwin.escloud.aiobasic.util.MessageUtils;
import com.digiwin.escloud.aioitms.model.bigdata.StarRocksEntity;
import com.digiwin.escloud.aioitms.model.bigdata.UploadBigDataContext;
import com.digiwin.escloud.common.controller.ParamCheckHelp;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.digiwin.escloud.aiobasic.edrv2.constant.Constants.*;
import static com.digiwin.escloud.aiobasic.edrv2.constant.Edrv2Const.*;
import static com.digiwin.escloud.aiobasic.util.DsConst.TM;
import static java.lang.Boolean.FALSE;
import static java.lang.Boolean.TRUE;

@Slf4j
@Service
public class EdrScanPlanService implements IEdrScanPlanService, ParamCheckHelp {
    @Value("${aiops.url}")
    private String aiopsUrl;
    @Value("${service.area}")
    private String area;

    @Autowired
    private IEdrReportService edrReportService;
    @Autowired
    private IEdrAgentService edrAgentService;
    @Autowired
    MessageUtils messageUtils;
    @Autowired
    private BigDataUtil bigDataUtil;
    @Autowired
    private EdrScanPlanMapper edrScanPlanMapper;
    @Autowired
    private EdrAgentMapper edrAgentMapper;
    @Autowired
    private DsUtil dsUtil;
    @Autowired
    private TransactionTemplate transactionTemplate;

    @Override
    public BaseResponse saveRecord(OperateLogScanPlanSaveParam operateLogScanPlanSaveParam) {
        // region 參數檢查
        List<String> emptyParam = Stream.of(
                        new AbstractMap.SimpleEntry<>("eid", operateLogScanPlanSaveParam.getEid()),
                        new AbstractMap.SimpleEntry<>("processUserName", operateLogScanPlanSaveParam.getProcessUserName()),
                        new AbstractMap.SimpleEntry<>("processUserId", operateLogScanPlanSaveParam.getProcessUserId()),
                        new AbstractMap.SimpleEntry<>("startTime", operateLogScanPlanSaveParam.getStartTime()),
                        new AbstractMap.SimpleEntry<>("planId", operateLogScanPlanSaveParam.getPlanId()),
                        new AbstractMap.SimpleEntry<>("enable", operateLogScanPlanSaveParam.getEnable()),
                        new AbstractMap.SimpleEntry<>("operation", operateLogScanPlanSaveParam.getOperation()),
                        new AbstractMap.SimpleEntry<>("operateType", operateLogScanPlanSaveParam.getOperateType()),
                        new AbstractMap.SimpleEntry<>("memo", operateLogScanPlanSaveParam.getMemo())
                )
                .filter(entry -> checkParamIsEmpty(entry.getValue(), entry.getKey()).isPresent())
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        if (!emptyParam.isEmpty()) {
            return BaseResponse.dynamicError(ResponseCode.PARAM_IS_EMPTY, emptyParam.stream().collect(Collectors.joining(",")));
        }
        // endregion

        JSONObject operateContent = new JSONObject(); // 操作内容Json
        operateContent.put("eid", operateLogScanPlanSaveParam.getEid());
        operateContent.put("userName", operateLogScanPlanSaveParam.getProcessUserName());
        operateContent.put("operateId", operateLogScanPlanSaveParam.getProcessUserId());
        operateContent.put("startTime", operateLogScanPlanSaveParam.getStartTime().substring(0, 16));
        operateContent.put("planId", operateLogScanPlanSaveParam.getPlanId());
        operateContent.put("enable", operateLogScanPlanSaveParam.getEnable());
        operateContent.put("operation", operateLogScanPlanSaveParam.getOperation());
        operateContent.put("operateType", operateLogScanPlanSaveParam.getOperateType());
        operateContent.put("memo", operateLogScanPlanSaveParam.getMemo());
        //組成JSON字串
        String operateContentString = operateContent.toJSONString();

        StarRocksEntity starRocksEntity = new StarRocksEntity();
        starRocksEntity.setDatabase(bigDataUtil.getSrDbName());
        starRocksEntity.setTable("AiopsOperateLog");
        List<LinkedHashMap<String, Object>> rows = new ArrayList<>();
        LinkedHashMap<String, Object> params = new LinkedHashMap<>();
        params.put("startTime", DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        params.put("eid", operateLogScanPlanSaveParam.getEid());
        params.put("deviceId", null);
        params.put("uploadDataModelCode", "AiopsOperateLog");
        params.put("userSid", null);
        params.put("userName", operateLogScanPlanSaveParam.getProcessUserName());
        params.put("operateId", operateLogScanPlanSaveParam.getProcessUserId());
        params.put("operateType", operateLogScanPlanSaveParam.getOperateType());
        params.put("endTime", null);
        params.put("operateContent", operateContentString);
        params.put("operateResult", null);
        params.put("sid", RequestUtil.getHeaderSid());
        params.put("source_db_id", null);
        rows.add(params);
        starRocksEntity.setRows(rows);
        bigDataUtil.srStreamLoad(starRocksEntity);

        return BaseResponse.ok();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseResponse tasksExec(EdrScanPlanTaskExecActivityParam param) throws Exception {
        // region 參數檢查
        Optional<BaseResponse> opt = checkParamIsEmpty(param.getSstId(), "sstId");
        if (opt.isPresent()) {
            return opt.get();
        }
        // endregion

        // 排程執行紀錄，並取得排程計劃活動Id
        Long sstaId = recordExecActivity(param);

        // 取得排程計劃設備，若saIds為空則查排程下全部
        List<EdrScanPlanTaskAgent> edrAgentList = Optional.ofNullable(edrScanPlanMapper.getScanPlanTaskAgents(param.getSstId(), param.getSaIds()))
                .filter(CollectionUtil::isNotEmpty)
                .orElseThrow(() -> new RuntimeException("get edr agent list error"));

        // 驗證設備
        List<EdrAgentCheckStatus> agentCheckStatusList = Optional.ofNullable(getEdrAgentCheckStatusList(edrAgentList))
                .filter(CollectionUtil::isNotEmpty)
                .orElseThrow(() -> new RuntimeException("get agent check status list error"));

        // 操作紀錄 - 狀態不為可執行設備
        Optional.of(scanFailedRecord(agentCheckStatusList, edrAgentList, sstaId, param))
                .filter(Boolean::booleanValue)
                .orElseThrow(() -> new RuntimeException("get scan failed record error"));


        // 判斷狀態為 In Progress，若ExecuteAction為空，需判斷72小時
        Predicate<EdrAgentCheckStatus> shouldAbortPredicate = agent -> {
            Boolean isInProgress = IN_PROGRESS.equalsIgnoreCase(agent.getCheckStatus());
            return StringUtil.isNotEmpty(param.getExecuteAction()) ? isInProgress :
                    isInProgress && Duration.between(DateUtil.parseToLocalDateTime(agent.getLastSuccessfulScanDate()), DateUtil.getLocalNow()).toHours() > 72;
        };
        Predicate<EdrAgentCheckStatus> isOKPredicate = agent -> OK.equalsIgnoreCase(agent.getCheckStatus());
        Predicate<EdrAgentCheckStatus> isInProgressPredicate = agent -> IN_PROGRESS.equalsIgnoreCase(agent.getCheckStatus());

        // 設備分類
        List<EdrAgentCheckStatus> abortAgentList = agentCheckStatusList.stream().filter(shouldAbortPredicate).collect(Collectors.toList());
        List<EdrAgentCheckStatus> scanOnAgentList = agentCheckStatusList.stream().filter(isOKPredicate).collect(Collectors.toList());
        List<EdrAgentCheckStatus> inProgressAgentList = agentCheckStatusList.stream().filter(isInProgressPredicate).collect(Collectors.toList());

        // 立即執行時，狀態為執行中設備需壓操作紀錄 (手動執行)
        if (StringUtil.isNotEmpty(param.getExecuteAction()) && ON.equalsIgnoreCase(param.getExecuteAction())) {
            scanOnInProgressProcessRecord(sstaId, param, inProgressAgentList, edrAgentList);
        }

        // 非可執行立即中止設備，不需壓紀錄
        // 立即中止時，狀態為進行中設備壓紀錄 (手動執行)
//        if (StringUtil.isNotEmpty(param.getExecuteAction()) && OFF.equalsIgnoreCase(param.getExecuteAction())) {
//            scanOffInProgressProcessRecord(sstaId, param, scanOnAgentList, edrAgentList);
//        }

        // 處理狀態為 "執行中" 且 "不在需中止清單中" 的設備壓紀錄
        if (StringUtil.isEmpty(param.getExecuteAction())) {
            scanOffNotAbortProcessRecord(sstaId, param, inProgressAgentList, abortAgentList, edrAgentList);
        }

        // 執行設備全機掃描並保存掃描紀錄
        if (CollectionUtil.isNotEmpty(scanOnAgentList) && !OFF.equalsIgnoreCase(param.getExecuteAction())) {
            agentScanOn(scanOnAgentList, edrAgentList, sstaId, param);
        }

        // 狀態為掃描中，執行全機掃描中止並保存掃描紀錄
        if (CollectionUtil.isNotEmpty(abortAgentList) && !ON.equalsIgnoreCase(param.getExecuteAction())) {
            agentScanOff(abortAgentList, edrAgentList, sstaId, param);
        }

        return BaseResponse.ok();
    }

    @Override
    public BaseResponse saveScanPlan(EdrScanPlanTaskParam param) {
        // region 參數檢查
        List<String> params = Stream.of(
                        new AbstractMap.SimpleEntry<>("saIds", CollectionUtil.isEmpty(param.getSaIds())),
                        new AbstractMap.SimpleEntry<>("planName", StringUtil.isEmpty(param.getPlanName())),
                        new AbstractMap.SimpleEntry<>("planStatus", Objects.isNull(param.getPlanStatus())),
                        new AbstractMap.SimpleEntry<>("planCron", StringUtil.isEmpty(param.getPlanCron())),
                        new AbstractMap.SimpleEntry<>("saveType", StringUtil.isEmpty(param.getSaveType())))
                .filter(Map.Entry::getValue)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(params)) {
            return BaseResponse.dynamicError(ResponseCode.PARAM_IS_EMPTY, String.join(", ", params));
        }
        // 若有帶planId
        if (!INSERT.equals(param.getSaveType()) && LongUtil.isEmpty(param.getId())) {
            return BaseResponse.dynamicError(ResponseCode.PARAM_IS_EMPTY, param.getPlanName());
        }

        // endregion

        Boolean isSuccess = false;
        Long sstId = null;

        // 調用創建排程計畫
        if (INSERT.equals(param.getSaveType())) {
            sstId = createPlan(param);
            isSuccess = LongUtil.isNotEmpty(sstId);
        }

        // 調用更新排程計畫
        if (UPDATE.equals(param.getSaveType())) {
            isSuccess = updatePlan(param);
        }

        // 調用刪除排程計畫
        if (DELETE.equals(param.getSaveType())) {
            isSuccess = deletePlan(param);
        }

        if (!isSuccess) {
            return BaseResponse.error(ResponseCode.EDR_SCANPLAN_SAVE_ERROR);
        }
        return BaseResponse.ok(sstId);
    }

    private Long createPlan(EdrScanPlanTaskParam param) {
        // 取得設備資訊
        List<EdrAgentField> edrAgentList = edrAgentMapper.getAgentById(param.getSaIds().stream().map(String::valueOf).collect(Collectors.toList()));

        // 取得eid
        Long eid = edrAgentList.stream().map(EdrAgentField::getEid).findFirst().map(LongUtil::objectToLong).orElse(null);
        if (LongUtil.isEmpty(eid)) {
            return null;
        }

        // 新增排程計畫
        Long sstId = SnowFlake.getInstance().newId();
        JSONObject jo = createProcess(sstId, eid, param);
        if (Objects.isNull(jo)) {
            return null;
        }

        Integer code = jo.getIntValue("code");
        if (!Objects.equals(200, code)) {
            log.error(jo.getString("msg"));
            return null;
        }

        // 取得排程id
        Long processId = jo.getJSONObject("data").getLongValue("id");

        // 判斷是否上線排程計畫
        if (param.getPlanStatus()) {
            onlineProcess(processId);
        }

        return transactionTemplate.execute(transactionStatus -> {
            try {
                // 組合 scanplan_tasks
                EdrScanPlanTask edrScanPlanTask = new EdrScanPlanTask();
                edrScanPlanTask.setId(sstId);
                edrScanPlanTask.setDmpTaskId(processId);
                edrScanPlanTask.setEid(eid);
                edrScanPlanTask.setName(param.getPlanName());
                edrScanPlanTask.setStatus(param.getPlanStatus());
                edrScanPlanTask.setCrons(param.getPlanCron());

                // 保存 scanplan_tasks
                Optional.ofNullable(edrScanPlanMapper.saveTask(edrScanPlanTask))
                        .filter(tasksAffectedRow -> tasksAffectedRow > 0)
                        .orElseThrow(() -> new RuntimeException("EDR2.0 SCANPLAN CREATE TASK ERROR"));

                // 新增排程計畫
                List<EdrScanPlanAgent> edrScanPlanAgentList = edrAgentList.stream()
                        .map(edrAgent -> {
                            // 處理 scanplan_agents
                            EdrScanPlanAgent edrScanPlanAgent = new EdrScanPlanAgent();
                            Long ssaId = SnowFlake.getInstance().newId(); // sentinelone_scanplan_agents id
                            edrScanPlanAgent.setId(ssaId);
                            edrScanPlanAgent.setSstId(sstId);
                            edrScanPlanAgent.setSaId(LongUtil.objectToLong(edrAgent.getId()));
                            return edrScanPlanAgent;
                        }).collect(Collectors.toList());

                // 批量寫入 scanplan_agents
                Optional.ofNullable(edrScanPlanMapper.saveAgents(edrScanPlanAgentList))
                        .filter(agentsAffectedRow -> agentsAffectedRow > 0)
                        .orElseThrow(() -> new RuntimeException("EDR2.0 SCANPLAN CREATE AGENTS ERROR"));

            } catch (Exception e) {
                // 發生異常則執行回滾機制
                transactionStatus.setRollbackOnly();
                log.error("SAVE scanplan_agents AND scanplan_tasks ERROR", e);
                return null;
            }

            // 保存無異常，回傳成功
            return sstId;
        });
    }

    private Boolean updatePlan(EdrScanPlanTaskParam param) {
        // 取得排程計畫 Task 資訊
        EdrScanPlanTask edrScanPlanTask = edrScanPlanMapper.getScanPlanTasks(new EdrScanPlanTaskParam(param.getId())).get(0);

        // 執行下線排程計畫
        Long processId = edrScanPlanTask.getDmpTaskId();
        Boolean isOffline = offlineProcess(processId);
        if (!isOffline) {
            return false;
        }

        // 執行編輯排程計畫
        JSONObject jo = updateProcess(param, edrScanPlanTask);
        if (Objects.isNull(jo)) {
            return false;
        }

        Integer code = jo.getIntValue("code");
        if (!Objects.equals(200, code)) {
            log.error(jo.getString("msg"));
            return false;
        }

        // 判斷是否上線排程計畫
        if (param.getPlanStatus()) {
            onlineProcess(processId);
        }

        return transactionTemplate.execute(transactionStatus -> {
            try {
                // 更新 scanplan_tasks
                EdrScanPlanTask edrScanPlanTaskParam = new EdrScanPlanTask();
                edrScanPlanTaskParam.setId(edrScanPlanTask.getId());
                edrScanPlanTaskParam.setDmpTaskId(edrScanPlanTask.getDmpTaskId());
                edrScanPlanTaskParam.setEid(LongUtil.objectToLong(edrScanPlanTask.getEid()));
                edrScanPlanTaskParam.setName(param.getPlanName());
                edrScanPlanTaskParam.setStatus(param.getPlanStatus());
                edrScanPlanTaskParam.setCrons(param.getPlanCron());

                // 保存 scanplan_tasks
                Optional.ofNullable(edrScanPlanMapper.saveTask(edrScanPlanTaskParam))
                        .filter(tasksAffectedRow -> tasksAffectedRow > 0)
                        .orElseThrow(() -> new RuntimeException("EDR2.0 SCANPLAN UPDATE TASK ERROR"));

                // 取得排程計畫 Agent 資訊
                List<EdrScanPlanAgent> edrScanPlanAgentList = edrScanPlanMapper.getScanPlanAgents(param.getId(), param.getSaIds(), null);
                List<Long> existssaIdList = edrScanPlanAgentList.stream().map(EdrScanPlanAgent::getSaId).collect(Collectors.toList());

                // 啟用 isRemove 的 Agent
                List<Long> enableSsaIdList = edrScanPlanAgentList.stream().filter(EdrScanPlanAgent::getIsRemove).map(EdrScanPlanAgent::getSaId).collect(Collectors.toList());
                List<Long> enableSaIdList = param.getSaIds().stream().filter(enableSsaIdList::contains).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(enableSaIdList)) {
                    Optional.ofNullable(edrScanPlanMapper.enableAgents(param.getId(), enableSaIdList))
                            .filter(addAgentAffectedRow -> addAgentAffectedRow > 0)
                            .orElseThrow(() -> new RuntimeException("EDR2.0 SCANPLAN ENABLE AGENT ERROR"));
                }

                // 篩出待新增的 AgentList
                List<EdrScanPlanAgent> addAgentList = param.getSaIds().stream()
                        .filter(saId -> !existssaIdList.contains(saId))
                        .map(saId -> {
                            EdrScanPlanAgent edrScanPlanAgent = new EdrScanPlanAgent();
                            Long ssaId = SnowFlake.getInstance().newId();
                            edrScanPlanAgent.setId(ssaId);
                            edrScanPlanAgent.setSstId(param.getId());
                            edrScanPlanAgent.setSaId(saId);
                            return edrScanPlanAgent;
                        })
                        .collect(Collectors.toList());

                if (CollectionUtil.isNotEmpty(addAgentList)) {
                    Optional.ofNullable(edrScanPlanMapper.saveAgents(addAgentList))
                            .filter(addAgentAffectedRow -> addAgentAffectedRow > 0)
                            .orElseThrow(() -> new RuntimeException("EDR2.0 SCANPLAN SAVE AGENT ERROR"));
                }

                // 篩出待刪除的 Agent
                List<Long> saIdList = edrScanPlanAgentList.stream()
                        .map(EdrScanPlanAgent::getSaId)
                        .filter(saId -> !param.getSaIds().contains(saId))
                        .collect(Collectors.toList());

                if (CollectionUtil.isNotEmpty(saIdList)) {
                    Optional.ofNullable(edrScanPlanMapper.deleteAgentsBysaId(param.getId(), saIdList))
                            .filter(taskAffectedRow -> taskAffectedRow > 0)
                            .orElseThrow(() -> new RuntimeException("EDR2.0 SCANPLAN UPDATE TASK ERROR"));
                }

            } catch (Exception e) {
                // 發生異常則執行回滾機制
                transactionStatus.setRollbackOnly();
                log.error("EDR2.0 SCANPLAN UPDATE TASK ERROR", e);
                return false;
            }

            // 保存無異常，回傳成功
            return true;
        });
    }

    private Boolean deletePlan(EdrScanPlanTaskParam param) {
        // 取得 scanPlanTaskList
        List<EdrScanPlanTask> edrScanPlanTaskList = edrScanPlanMapper.getScanPlanTasks(param);
        if (!CollectionUtil.isNotEmpty(edrScanPlanTaskList)) {
            return false;
        }

        // 取得 processId
        Long processId = edrScanPlanTaskList.get(0).getDmpTaskId();

        // 執行下線排程計畫
        Boolean isOffline = offlineProcess(processId);
        if (!isOffline) {
            return false;
        }

        // 執行刪除排程計畫
        Boolean isDelete = deleteProcess(processId);
        if (!isDelete) {
            return false;
        }

        return transactionTemplate.execute(transactionStatus -> {
            try {
                // 刪除 scanplan_tasks
                Optional.ofNullable(edrScanPlanMapper.deleteTask(param.getId()))
                        .filter(taskAffectedRow -> taskAffectedRow > 0)
                        .orElseThrow(() -> new RuntimeException("EDR2.0 SCANPLAN DELETE TASK ERROR"));

                // 刪除 scanplan_agents
                Optional.ofNullable(edrScanPlanMapper.deleteAgentsBysstId(param.getId()))
                        .filter(agentAffectedRow -> agentAffectedRow > 0)
                        .orElseThrow(() -> new RuntimeException("EDR2.0 SCANPLAN DELETE AGENT ERROR"));

            } catch (Exception e) {
                // 發生異常則執行回滾機制
                transactionStatus.setRollbackOnly();
                log.error("EDR2.0 SCANPLAN DELETE TASK ERROR", e);
                return false;
            }

            // 保存無異常，回傳成功
            return true;
        });
    }

    private JSONObject createProcess(Long sstId, Long eid, EdrScanPlanTaskParam param) {
        Map<String, Object> tmMap = new HashMap<>();
        tmMap.put("id", sstId);
        tmMap.put("eid", eid);
        tmMap.put("name", param.getPlanName());
        tmMap.put("crontab", param.getPlanCron());
        tmMap.put("type", TM);
        tmMap.put("appCode", TM);
        tmMap.put("aiopsUrl", aiopsUrl + "/aiogateway/aiobasic/edr/v2/scanplan/exec");
        return dsUtil.createProcess(tmMap);
    }

    private JSONObject updateProcess(EdrScanPlanTaskParam param, EdrScanPlanTask edrScanPlanTask) {
        Map<String, Object> tmMap = new HashMap<>();
        tmMap.put("id", edrScanPlanTask.getId()); //sstId
        tmMap.put("processId", edrScanPlanTask.getDmpTaskId());
        tmMap.put("eid", edrScanPlanTask.getEid());
        tmMap.put("name", param.getPlanName());
        tmMap.put("crontab", param.getPlanCron());
        tmMap.put("type", TM);
        tmMap.put("appCode", TM);
        tmMap.put("aiopsUrl", aiopsUrl + "/aiogateway/aiobasic/edr/v2/scanplan/exec");
        return dsUtil.updateProcess(tmMap);
    }

    private Boolean deleteProcess(Long processId) {
        JSONObject jo = dsUtil.deleteProcess(StringUtil.toString(processId));
        if (Objects.isNull(jo)) {
            return FALSE;
        }

        Integer code = jo.getIntValue("code");
        if (!Objects.equals(200, code)) {
            log.error(jo.getString("msg"));
            return FALSE;
        }

        return TRUE;
    }

    private Boolean onlineProcess(Long processId) {
        JSONObject jo = dsUtil.onlineProcess(StringUtil.toString(processId));
        if (Objects.isNull(jo)) {
            return FALSE;
        }

        Integer code = jo.getIntValue("code");
        if (!Objects.equals(200, code)) {
            log.error(jo.getString("msg"));
            return FALSE;
        }

        return TRUE;
    }

    private Boolean offlineProcess(Long processId) {
        JSONObject jo = dsUtil.offlineProcess(StringUtil.toString(processId));
        if (Objects.isNull(jo)) {
            return FALSE;
        }

        Integer code = jo.getIntValue("code");
        if (!Objects.equals(200, code)) {
            log.error(jo.getString("msg"));
            return FALSE;
        }

        return TRUE;
    }

    private BaseResponse agentScanOn(List<EdrAgentCheckStatus> scanOnAgentList,
                                     List<EdrScanPlanTaskAgent> edrAgentList,
                                     Long sstaId,
                                     EdrScanPlanTaskExecActivityParam param) throws Exception {
        // 篩選需執行設備
        List<String> okAccountIdList = scanOnAgentList.stream().map(EdrAgentCheckStatus::getAccountId).collect(Collectors.toList());
        List<String> oKAgentIdList = scanOnAgentList.stream().map(EdrAgentCheckStatus::getAgentId).collect(Collectors.toList());
        List<String> oKSiteIdList = scanOnAgentList.stream().map(EdrAgentCheckStatus::getSiteId).collect(Collectors.toList());

        // 取得ssaIdList
        List<Long> ssaIdList = edrAgentList.stream()
                .filter(agent -> oKAgentIdList.contains(agent.getAgentId()))
                .map(EdrScanPlanTaskAgent::getSsaId)
                .collect(Collectors.toList());

        // 執行全機掃描
        BaseResponse scanOnRes = agentScan(okAccountIdList, oKAgentIdList, oKSiteIdList, ON, param.getExecuteReason());

        // 操作紀錄 - 全機掃描成功失敗
        Boolean isSuccess = ResponseCode.SUCCESS.getCode().equalsIgnoreCase(scanOnRes.getCode());
        String scanStatus = isSuccess ? SCANON_SUCCESS : SCANON_FAILED;
        scanRecord(scanOnAgentList, oKAgentIdList, param, scanStatus, isSuccess ? null : MEMO_FAILED);

        // 計劃設備活動紀錄 - 紀錄執行掃描 START
        execRecord(ssaIdList, START, MEMO_START, sstaId);

        // 計劃設備活動紀錄 - 紀錄執行掃描
        if (!isSuccess) {
            execRecord(ssaIdList, FAILED, MEMO_FAILED, sstaId);
        }

        return scanOnRes;
    }

    private BaseResponse agentScanOff(List<EdrAgentCheckStatus> abortAgentList,
                                      List<EdrScanPlanTaskAgent> edrAgentList,
                                      Long sstaId,
                                      EdrScanPlanTaskExecActivityParam param) throws Exception {

        List<String> inProgressAgentIdList = abortAgentList.stream().map(EdrAgentCheckStatus::getAgentId).collect(Collectors.toList());

        // 取得排程任務開始設備
        Set<String> agentIdSet = getStartFromScanPlanAgentIds(edrAgentList);

        // 取得可執行中止即不可執行中止設備
        //可中止
        List<String> abortableAgentIdList = inProgressAgentIdList.stream().filter(agentIdSet::contains).collect(Collectors.toList());
        List<Long> abortableSsaIds = getSsaIds(edrAgentList, abortableAgentIdList);

        //不可中止
        List<String> nonAbortableAgentIdList = inProgressAgentIdList.stream().filter(agentId -> !agentIdSet.contains(agentId)).collect(Collectors.toList());
        List<Long> nonAbortableSsaIds = getSsaIds(edrAgentList, nonAbortableAgentIdList);

        // 不可中止設備壓紀錄
//        scanFailedExecRecord(nonAbortableSsaIds, MEMO_CANNOT_ABORT, sstaId);

        // 執行中止設備
        if (CollectionUtil.isEmpty(abortableAgentIdList)) {
            return BaseResponse.error(new RuntimeException("無需執行中止設備"));
        }

        // 取得對應順序的數據
        // 建立 lookup map
        Map<String, EdrAgentCheckStatus> agentMap = abortAgentList.stream()
                .collect(Collectors.toMap(EdrAgentCheckStatus::getAgentId, Function.identity()));

        // 按照 abortableAgentIdList 的順序組出 siteId / accountId 清單
        List<String> inProgressSiteIdList = abortableAgentIdList.stream()
                .map(agentMap::get)
                .map(EdrAgentCheckStatus::getSiteId)
                .collect(Collectors.toList());
        List<String> inProgressAccountIdList = abortableAgentIdList.stream()
                .map(agentMap::get)
                .map(EdrAgentCheckStatus::getAccountId)
                .collect(Collectors.toList());

        // 執行全機掃描中止
        return executeAbort(sstaId, param, abortAgentList, abortableSsaIds, abortableAgentIdList, inProgressSiteIdList, inProgressAccountIdList);
    }

    private List<Long> getSsaIds(List<EdrScanPlanTaskAgent> edrAgentList, List<String> agentIds) {
        return edrAgentList.stream()
                .filter(agent -> agentIds.contains(agent.getAgentId()))
                .map(EdrScanPlanTaskAgent::getSsaId)
                .collect(Collectors.toList());
    }

    private Set<String> getStartFromScanPlanAgentIds(List<EdrScanPlanTaskAgent> edrAgentList) {
        return edrAgentList.stream()
                .filter(agent -> {
                    EdrScanPlanAgentActivity activity = edrScanPlanMapper.getStartAgentActivity(agent.getSsaId());
                    return Objects.nonNull(activity) && activity.getActivityCount() < 2;
                })
                .map(EdrScanPlanTaskAgent::getAgentId)
                .collect(Collectors.toSet());
    }

    private BaseResponse executeAbort(Long sstaId,
                                      EdrScanPlanTaskExecActivityParam param,
                                      List<EdrAgentCheckStatus> agentCheckStatusList,
                                      List<Long> abortableSsaIds,
                                      List<String> abortableAgentIdList,
                                      List<String> inProgressSiteIdList,
                                      List<String> inProgressAccountIdList) throws Exception {

        BaseResponse scanOffRes = agentScan(inProgressAccountIdList, abortableAgentIdList, inProgressSiteIdList, OFF, param.getExecuteReason());

        // 操作紀錄 - 全機掃描中止成功失敗
        Boolean isSuccess = ResponseCode.SUCCESS.getCode().equalsIgnoreCase(scanOffRes.getCode());
        String scanStatus = isSuccess ? SCANOFF_SUCCESS : SCANOFF_FAILED;
        String defaultDeleteReason = isSuccess ? MEMO_ABORTED : MEMO_FAILED;

        // 人工調用時需紀錄前端傳入deleteReason，否則使用預設值
        String deleteReason = StringUtil.isNotEmpty(param.getExecuteReason()) ? param.getExecuteReason() : defaultDeleteReason;
        scanRecord(agentCheckStatusList, abortableAgentIdList, param, scanStatus, deleteReason);

        // 計劃設備活動紀錄 - 紀錄執行掃描
//        execRecord(abortableSsaIds, START, deleteReason, sstaId);
//        execRecord(abortableSsaIds, isSuccess ? ABORTED : FAILED, deleteReason, sstaId);

        if (!isSuccess) {
            return BaseResponse.error(new RuntimeException("全機掃描中止調用失敗"));
        }

        return scanOffRes;
    }

    private BaseResponse agentScan(List<String> checkStatusAccountIdList, List<String> checkStatusAgentIdList,
                                   List<String> checkStatusSiteIdList, String executeAction, String memo) throws Exception {
        EdrScan edrScan = new EdrScan();
        edrScan.setAccountIds(checkStatusAccountIdList);
        edrScan.setAgentIds(checkStatusAgentIdList);
        edrScan.setSiteIds(checkStatusSiteIdList);
        edrScan.setExecuteAction(executeAction);
        edrScan.setDeleteReason(memo);

        return edrAgentService.scan(edrScan);
    }

    private List<EdrAgentCheckStatus> getEdrAgentCheckStatusList(List<EdrScanPlanTaskAgent> edrAgentList) {
        EdrAgentCheckStatusParam edrAgentCheckStatusParam = new EdrAgentCheckStatusParam();
        List<String> siteIdList = edrAgentList.stream().map(EdrAgentField::getSiteId).collect(Collectors.toList());
        List<String> agentList = edrAgentList.stream().map(EdrAgentField::getAgentId).collect(Collectors.toList());
        edrAgentCheckStatusParam.setSiteIds(siteIdList);
        edrAgentCheckStatusParam.setAgentIds(agentList);
        edrAgentCheckStatusParam.setIncludeScanPlan(true);
        edrAgentCheckStatusParam.setCheckPoint(SCAN_ON);

        return edrAgentService.getEdrAgentCheckStatusList(edrAgentCheckStatusParam);
    }

    private Long recordExecActivity(EdrScanPlanTaskExecActivityParam param) {
        // newId
        Long sstaId = SnowFlake.getInstance().newId();
        param.setId(sstaId);

        // 若自動操作則押系統
        if (StringUtil.isEmpty(param.getUserName())) {
            param.setUserName("系統");
        }

        // 執行寫入，立即中止不押新紀錄
        if (!OFF.equalsIgnoreCase(param.getExecuteAction())) {
            edrScanPlanMapper.saveTasksExecActivity(param);
        }

        return sstaId;
    }

    @Override
    public Boolean execRecord(List<Long> ssaIdList, String operation, String memo, Long sstaId) {
        //如果list為空，回傳true
        if (CollectionUtil.isEmpty(ssaIdList)) {
            return true;
        }

        List<EdrScanPlanAgentActivity> edrScanPlanAgentActivityList = ssaIdList.stream()
                .map(ssaId -> {
                    EdrScanPlanAgentActivity edrScanPlanAgentActivity = new EdrScanPlanAgentActivity();
                    edrScanPlanAgentActivity.setId(SnowFlake.getInstance().newId());
                    edrScanPlanAgentActivity.setSstaId(sstaId);
                    edrScanPlanAgentActivity.setSsaId(ssaId);
                    edrScanPlanAgentActivity.setOperation(operation);
                    edrScanPlanAgentActivity.setMemo(memo);

                    return edrScanPlanAgentActivity;
                }).collect(Collectors.toList());

        return edrScanPlanMapper.saveScanPlanAgentActivity(edrScanPlanAgentActivityList) > 0;
    }

    private Boolean scanFailedRecord(List<EdrAgentCheckStatus> agentCheckStatusList,
                                     List<EdrScanPlanTaskAgent> edrAgentList,
                                     Long sstaId,
                                     EdrScanPlanTaskExecActivityParam param) {
        List<EdrAgentCheckStatus> filterAgentCheckStatusList = agentCheckStatusList.stream()
                .filter(agent -> !OK.equalsIgnoreCase(agent.getCheckStatus()) && !IN_PROGRESS.equalsIgnoreCase(agent.getCheckStatus()))
                .collect(Collectors.toList());

        // 若為空則跳過
        if (CollectionUtil.isEmpty(filterAgentCheckStatusList)) {
            return true;
        }

        // 取得組件狀態不為運行中 ssaIdList
        List<Long> disabledSsaIdList = edrAgentList.stream()
                .filter(agent -> filterAgentCheckStatusList.stream()
                        .anyMatch(agentScanStatus -> !OFFLINE.equalsIgnoreCase(agentScanStatus.getCheckStatus())
                                && agentScanStatus.getAgentId().equals(agent.getAgentId())))
                .map(EdrScanPlanTaskAgent::getSsaId)
                .collect(Collectors.toList());
        // 取得平台連線狀態離線 ssaIdList
        List<Long> offlineSsaIdList = edrAgentList.stream()
                .filter(agent -> filterAgentCheckStatusList.stream()
                        .anyMatch(agentScanStatus -> OFFLINE.equalsIgnoreCase(agentScanStatus.getCheckStatus())
                                && agentScanStatus.getAgentId().equals(agent.getAgentId())))
                .map(EdrScanPlanTaskAgent::getSsaId)
                .collect(Collectors.toList());

        // 設備活動紀錄 - 不可執行
        scanFailedExecRecord(disabledSsaIdList, MEMO_DISABLED, sstaId);
        scanFailedExecRecord(offlineSsaIdList, MEMO_OFFLINE, sstaId);

        return scanFailedProcessRecord(filterAgentCheckStatusList, param, SCANON_FAILED);
    }

    private Boolean scanFailedProcessRecord(List<EdrAgentCheckStatus> filterAgentCheckStatusList,
                                     EdrScanPlanTaskExecActivityParam param,
                                     String operation) {
        // 處理操作紀錄
        List<EdrOrgCollectorProcessRecordSaveDTO> records = filterAgentCheckStatusList.stream()
                .map(agent -> {
                    EdrOrgCollectorProcessRecordSaveDTO edrOrgCollectorProcessRecordSaveDTO = new EdrOrgCollectorProcessRecordSaveDTO();
                    edrOrgCollectorProcessRecordSaveDTO.setEid(LongUtil.objectToLong(agent.getEid()));
                    edrOrgCollectorProcessRecordSaveDTO.setServerId(SERVERID);
                    edrOrgCollectorProcessRecordSaveDTO.setOrgId(LongUtil.objectToLong(agent.getSiteId()));
                    edrOrgCollectorProcessRecordSaveDTO.setCollectorId(LongUtil.objectToLong(agent.getAgentId()));
                    edrOrgCollectorProcessRecordSaveDTO.setUserId(param.getUserId());
                    edrOrgCollectorProcessRecordSaveDTO.setUserName(Optional.ofNullable(param.getUserName()).filter(StringUtil::isNotEmpty).orElse("系統"));
                    edrOrgCollectorProcessRecordSaveDTO.setOperation(operation);
                    edrOrgCollectorProcessRecordSaveDTO.setDeleteReason(generateDeleteReason(agent));
                    return edrOrgCollectorProcessRecordSaveDTO;
                }).collect(Collectors.toList());

        // 批量保存操作紀錄
        return edrReportService.saveProcessRecords(records) > 0;
    }

    private String generateDeleteReason(EdrAgentCheckStatus agent) {
        return agent.getEndPointName() + "_" + messageUtils.noExceptionLogGet(agent.getCheckStatus(), "zh-TW");
    }

    private void scanFailedExecRecord(List<Long> ssaIdList, String memo, Long sstaId) {
        if (CollectionUtil.isEmpty(ssaIdList)) {
            return;
        }
        execRecord(ssaIdList, START, memo, sstaId);
        execRecord(ssaIdList, FAILED, memo, sstaId);
    }

    private Boolean scanRecord(List<EdrAgentCheckStatus> agentCheckStatusList,
                               List<String> checkStatusOKAgentIdList,
                               EdrScanPlanTaskExecActivityParam param,
                               String operation,
                               String deleteReason) {
        List<EdrOrgCollectorProcessRecordSaveDTO> records = agentCheckStatusList.stream()
                .filter(agent -> checkStatusOKAgentIdList.contains(agent.getAgentId()))
                .map(agent -> {
                    EdrOrgCollectorProcessRecordSaveDTO edrOrgCollectorProcessRecordSaveDTO = new EdrOrgCollectorProcessRecordSaveDTO();
                    edrOrgCollectorProcessRecordSaveDTO.setEid(LongUtil.objectToLong(agent.getEid()));
                    edrOrgCollectorProcessRecordSaveDTO.setServerId(SERVERID);
                    edrOrgCollectorProcessRecordSaveDTO.setOrgId(LongUtil.objectToLong(agent.getSiteId()));
                    edrOrgCollectorProcessRecordSaveDTO.setCollectorId(LongUtil.objectToLong(agent.getAgentId()));
                    edrOrgCollectorProcessRecordSaveDTO.setUserId(param.getUserId());
                    edrOrgCollectorProcessRecordSaveDTO.setUserName(StringUtil.isNotEmpty(param.getUserName()) ? param.getUserName() : "系統");
                    edrOrgCollectorProcessRecordSaveDTO.setOperation(operation);
                    edrOrgCollectorProcessRecordSaveDTO.setDeleteReason(deleteReason);
                    return edrOrgCollectorProcessRecordSaveDTO;
                }).collect(Collectors.toList());

        // 若為空則跳過
        if (CollectionUtil.isEmpty(records)) {
            return true;
        }
        return edrReportService.saveProcessRecords(records) > 0;
    }

    @Override
    public BaseResponse getCycle(String eid) {
        //region 參數檢查
        Optional<BaseResponse> optResponse = checkParamIsEmpty(eid, "eid");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }
        //endregion

        List<String> dataList = edrScanPlanMapper.getCycle(eid);
        return BaseResponse.ok(dataList);
    }

    private void scanOnInProgressProcessRecord(Long sstaId ,
                                         EdrScanPlanTaskExecActivityParam param,
                                         List<EdrAgentCheckStatus> inProgressAgentList,
                                         List<EdrScanPlanTaskAgent> edrAgentList) {
        List<Long> inProgressSsaIdList = inProgressAgentList.stream()
                .map(agent -> edrAgentList.stream()
                        .filter(edrAgent -> edrAgent.getAgentId().equals(agent.getAgentId()))
                        .findFirst()
                        .orElse(null))
                .filter(Objects::nonNull)
                .map(EdrScanPlanTaskAgent::getSsaId)
                .collect(Collectors.toList());

        // 計劃設備活動紀錄 - 紀錄執行掃描 & 設備操作紀錄
        if (CollectionUtil.isNotEmpty(inProgressSsaIdList)) {
            // 計劃設備活動紀錄
            scanFailedExecRecord(inProgressSsaIdList, MEMO_IN_PROGRESS, sstaId);

            // 設備操作紀錄
            scanFailedProcessRecord(inProgressAgentList, param, SCANON_FAILED);
        }
    }

    private void scanOffInProgressProcessRecord(Long sstaId ,
                                                EdrScanPlanTaskExecActivityParam param,
                                                List<EdrAgentCheckStatus> scanOnAgentList,
                                                List<EdrScanPlanTaskAgent> edrAgentList) {
        // 若為手動執行需對狀態為進行中設備壓紀錄
        Map<String, EdrScanPlanTaskAgent> agentMap = edrAgentList.stream()
                .collect(Collectors.toMap(EdrScanPlanTaskAgent::getAgentId, Function.identity()));

        List<Long> okSsaIdList = scanOnAgentList.stream()
                .map(agent -> agentMap.get(agent.getAgentId())) // 取得執行中的設備
                .filter(Objects::nonNull)
                .map(EdrScanPlanTaskAgent::getSsaId)
                .collect(Collectors.toList());

        // 計劃設備活動紀錄 - 紀錄執行掃描
        if (CollectionUtil.isNotEmpty(okSsaIdList)) {
            // 計劃設備活動紀錄
            scanFailedExecRecord(okSsaIdList, MEMO_NON_SCAN, sstaId);

            // 設備操作紀錄
            scanFailedProcessRecord(scanOnAgentList, param, SCANOFF_FAILED);
        }
    }

    private void scanOffNotAbortProcessRecord(Long sstaId ,
                                                EdrScanPlanTaskExecActivityParam param,
                                                List<EdrAgentCheckStatus> inProgressAgentList,
                                                List<EdrAgentCheckStatus> abortAgentList,
                                                List<EdrScanPlanTaskAgent> edrAgentList) {
        Set<String> checkStatusAgentIdList = inProgressAgentList.stream()
                .map(EdrAgentCheckStatus::getAgentId)
                .filter(agentId -> !abortAgentList.stream()
                        .map(EdrAgentCheckStatus::getAgentId).collect(Collectors.toSet()).contains(agentId)
                ).collect(Collectors.toSet());

        // 取得執行中的設備
        List<Long> inProgressSsaIdList = edrAgentList.stream()
                .filter(agent -> checkStatusAgentIdList.contains(agent.getAgentId()))
                .map(EdrScanPlanTaskAgent::getSsaId).collect(Collectors.toList());

        // 計劃設備活動紀錄 - 紀錄執行掃描 & 設備操作紀錄
        scanFailedExecRecord(inProgressSsaIdList, MEMO_IN_PROGRESS, sstaId);

        if (CollectionUtil.isNotEmpty(checkStatusAgentIdList)) {
            List<EdrAgentCheckStatus> filterAgentCheckStatusList = inProgressAgentList.stream()
                    .filter(agent -> checkStatusAgentIdList.contains(agent.getAgentId()))
                    .collect(Collectors.toList());
            // 執行中不可中止設備表示是執行全機掃描而非中止，故壓紀錄為SCANON_FAILED
            scanFailedProcessRecord(filterAgentCheckStatusList, param, SCANON_FAILED);
        }
    }

    @Override
    public BaseResponse getScanPlanList(EdrScanPlanListParam edrScanPlanListParam) {
        // region 參數檢查
        Optional<BaseResponse> optResponse = checkParamIsEmpty(edrScanPlanListParam.getEid(), "eid");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }
        // endregion
        List<EdrScanPlanTaskInfo> edrScanPlanTaskInfoList = edrScanPlanMapper.getScanPlanTaskInfo(edrScanPlanListParam);
        //計算下次執行時間
        edrScanPlanTaskInfoList = edrScanPlanTaskInfoList.stream().map(info -> {
            String cronStr = info.getPlanCron();

            if(info.getPlanStatus() == 0 || StringUtil.isEmpty(cronStr)) {
                return info;
            }

            //Cron 7碼與6碼 5碼數據中台不接受
            CronType cronType = cronStr.split(" ").length == 7 ? CronType.QUARTZ : CronType.SPRING;
            CronParser parser = new CronParser(CronDefinitionBuilder.instanceDefinitionFor(cronType));
            Cron cron = parser.parse(cronStr);
            cron.validate(); // 驗證 Cron 是否正確

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime time = LocalDateTime.now();
            ZonedDateTime zonedTime = time.atZone(ZoneId.systemDefault());
            ExecutionTime executionTime = ExecutionTime.forCron(cron);
            Optional<ZonedDateTime> nextRun = executionTime.nextExecution(zonedTime);

            if (nextRun.isPresent()) {
                ZonedDateTime nextRunTime = nextRun.get();
                info.setNextExecuteTime(nextRunTime.format(formatter));
            }
            return info;
        }).collect(Collectors.toList());

        // 添加operationStatus
        edrScanPlanTaskInfoList = insertOperationStatus(edrScanPlanTaskInfoList);
        // 計算未篩選前設備數
        edrScanPlanTaskInfoList = edrScanPlanTaskInfoList.stream()
                .peek(info -> {
                    info.setDeviceCount(info.getEdrScanPlanAgentInfos().size());
                }).collect(Collectors.toList());
        edrScanPlanTaskInfoList = filterEdrScanPlanTaskInfoList(edrScanPlanTaskInfoList, edrScanPlanListParam);

        Map<String, Object> map = new HashMap<>();
        int total = edrScanPlanTaskInfoList.size();
        //分頁
        if(ObjectUtil.isNotNull(edrScanPlanListParam.getPageIndex()) && ObjectUtil.isNotNull(edrScanPlanListParam.getPageSize())) {
            int startIndex = (edrScanPlanListParam.getPageIndex() - 1) * edrScanPlanListParam.getPageSize();
            int endIndex = edrScanPlanListParam.getPageIndex() * edrScanPlanListParam.getPageSize();

            endIndex = Math.min(endIndex, total);
            edrScanPlanTaskInfoList = edrScanPlanTaskInfoList.subList(startIndex, endIndex);
        }
        map.put("total", total);
        map.put("list", edrScanPlanTaskInfoList);

        return BaseResponse.ok(map);
    }
    private List<EdrScanPlanTaskInfo> insertOperationStatus(List<EdrScanPlanTaskInfo> edrScanPlanTaskInfoList) {

        return edrScanPlanTaskInfoList.stream().peek(info -> {
            List<EdrScanPlanAgentInfo> edrScanPlanAgentInfoList = info.getEdrScanPlanAgentInfos();
            if(edrScanPlanAgentInfoList.stream().anyMatch(
                    agentInfo -> IN_PROGRESS.equalsIgnoreCase(agentInfo.getScanStatus()))) {
                info.setOperationStatus(PROGRESSING);
            }
            else if(edrScanPlanAgentInfoList.stream().anyMatch(
                    agentInfo -> FAILED.equalsIgnoreCase(agentInfo.getScanStatus()) || ABORTED.equalsIgnoreCase(agentInfo.getScanStatus()))) {
                info.setOperationStatus(ERROR);
            }
            else if(edrScanPlanAgentInfoList.stream().anyMatch(
                    agentInfo -> COMPLETED.equalsIgnoreCase(agentInfo.getScanStatus()))) {
                info.setOperationStatus(SUCCESS);
            }
            else if(edrScanPlanAgentInfoList.stream().allMatch(
                    agentInfo -> N_A.equalsIgnoreCase(agentInfo.getScanStatus()))) {
                info.setOperationStatus(NONE);
            }
            else {
                info.setOperationStatus(NONE);
            }
        }).collect(Collectors.toList());
    }


    private List<EdrScanPlanTaskInfo> filterEdrScanPlanTaskInfoList(List<EdrScanPlanTaskInfo> edrScanPlanTaskInfoList, EdrScanPlanListParam edrScanPlanListParam) {
        edrScanPlanTaskInfoList = edrScanPlanTaskInfoList.stream().filter(info -> {
            if (ObjectUtil.isNotEmpty(edrScanPlanListParam.getOperationStatus())) {
                return edrScanPlanListParam.getOperationStatus().equalsIgnoreCase(info.getOperationStatus());
            }
            return true;
        }).peek(info -> info.setEdrScanPlanAgentInfos(info.getEdrScanPlanAgentInfos().stream().filter(agent -> {
            if (ObjectUtil.isNotEmpty(edrScanPlanListParam.getScanStatus())) {
                return edrScanPlanListParam.getScanStatus().equalsIgnoreCase(agent.getScanStatus());
            }
            return true;
        // agent為空則正常帶出
        }).filter(agent -> {
            if (ObjectUtil.isNotEmpty(edrScanPlanListParam.getDeviceName())) {
                if(ObjectUtil.isEmpty(agent.getAgentName())) {
                    return false;
                }
                return agent.getAgentName().contains(edrScanPlanListParam.getDeviceName());
            }
            return true;
        }).filter(agent -> {
            if (ObjectUtil.isNotEmpty(edrScanPlanListParam.getDeviceIp())) {
                if(ObjectUtil.isEmpty(agent.getAgentIp())) {
                    return false;
                }
                return agent.getAgentIp().contains(edrScanPlanListParam.getDeviceIp());
            }
            return true;
        }).collect(Collectors.toList()))).collect(Collectors.toList());


        return edrScanPlanTaskInfoList.stream()
                .filter(info -> !info.getEdrScanPlanAgentInfos().isEmpty())
                .collect(Collectors.toList());

    }

    @Override
    public BaseResponse getScanPlanLog(EdrScanPlanLogParam edrScanPlanLogParam) {
        // region 參數檢查
        List<String> params = Stream.of(
                        new AbstractMap.SimpleEntry<>("eid", StringUtil.isEmpty(edrScanPlanLogParam.getEid())),
                        new AbstractMap.SimpleEntry<>("planId", StringUtil.isEmpty(edrScanPlanLogParam.getPlanId())))
                .filter(Map.Entry::getValue)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(params)) {
            return BaseResponse.dynamicError(ResponseCode.PARAM_IS_EMPTY, String.join(", ", params));
        }
        // endregion
        int startIndex = (edrScanPlanLogParam.getPageIndex() - 1) * edrScanPlanLogParam.getPageSize();
        edrScanPlanLogParam.setPageIndex(startIndex);

        List<EdrScanPlanLog> dataList =  edrScanPlanMapper.getScanPlanLog(edrScanPlanLogParam);
        Integer total = edrScanPlanMapper.getScanPlanLogTotal(edrScanPlanLogParam);
        Map<String, Object> map = new HashMap<>();

//        //分頁
//        if(ObjectUtil.isNotNull(edrScanPlanLogParam.getPageIndex()) && ObjectUtil.isNotNull(edrScanPlanLogParam.getPageSize())) {
//            int startIndex = (edrScanPlanLogParam.getPageIndex() - 1) * edrScanPlanLogParam.getPageSize();
//            int endIndex = edrScanPlanLogParam.getPageIndex() * edrScanPlanLogParam.getPageSize();
//
//            endIndex = Math.min(endIndex, total);
//            dataList = dataList.subList(startIndex, endIndex);
//        }
        //做userName遮罩
        dataList = dataList.stream().map(info -> {
            Boolean isMis = edrScanPlanLogParam.getIsMis();
            String name = info.getUserName();
            if(isMis && !"系統".equalsIgnoreCase(name)) {
                name = "TW".equalsIgnoreCase(area) ? "鼎新人員" : "鼎捷人员";
            }
            info.setUserName(name);
            return info;
        }).collect(Collectors.toList());
        map.put("total", total);
        map.put("list", dataList);

        return BaseResponse.ok(map);
    }
}
