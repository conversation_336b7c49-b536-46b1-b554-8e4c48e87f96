package com.digiwin.escloud.aiobasic.edr.service.edr.impl.mail;

import com.digiwin.escloud.aiobasic.edr.annotation.ReportType;
import com.digiwin.escloud.aiobasic.edr.model.base.ReportRecord;
import com.digiwin.escloud.aiobasic.edr.model.edr.CustomerOrgMap;
import com.digiwin.escloud.aiobasic.edr.service.base.IAioReportMailItem;
import com.digiwin.escloud.aiobasic.freemarker.FreemarkerService;
import com.digiwin.escloud.aiobasic.util.FreemarkerTplData;
import com.digiwin.escloud.aiomail.MailSourceType;
import com.digiwin.escloud.common.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;

import static com.digiwin.escloud.aiobasic.util.MessageUtils.ZH_CN_STANDARD;
import static com.digiwin.escloud.aiobasic.util.MessageUtils.ZH_TW_STANDARD;

@Slf4j
@ReportType(15)
@Service
@RefreshScope
public class VersionUpdateAssessmentReport implements IAioReportMailItem {
    @Value("${service.area}")
    String serviceArea;
    @Value("${aiobasic.address}")
    String aiobasicAddress;
    @Value("${mis.root}")
    String misRoot;

    @Autowired
    private FreemarkerService tplService;

    @Override
    public String getReportMailTitle (ReportRecord rm, List<CustomerOrgMap> ofOrgs) {
        if ("CN".equals(serviceArea)) {
            return String.format("【鼎捷数智】智管家版更評估報告_%s_%s(系统发送请勿回复)",
                    DateUtil.getSomeDateFormatString(DateUtil.getLocalToday(), DateUtil.DATE_FORMATTER),
                    rm.getCustomerName());
        }
        return String.format("【鼎新數智】Ai智管家版更評估報告_%s_%s(系統發送請勿回覆)",
                DateUtil.getSomeDateFormatString(DateUtil.getLocalToday(), DateUtil.DATE_FORMATTER),
                rm.getCustomerName());
    }

    @Override
    public String getReportMailContent(ReportRecord rm, List<CustomerOrgMap> ofOrgs) {
        try {
            String description = StringUtils.isEmpty(rm.getDescription()) ? "" : rm.getDescription();
            String reportStartTimeStr = getReportStartTimeString(rm);
            String reportEndTimeStr = getReportEndTimeString(rm);
            String hrefAddress = getHrefAddress(aiobasicAddress, rm);
            String misAddress = getMisAddress(misRoot);
            String downloadAddress = "CN".equalsIgnoreCase(serviceArea) ? "https://es-ygj.digiwincloud.com.cn/productDownload" : "https://es-ygj.digiwincloud.com/productDownload";

            Map<String, Object> map = convertToMap(rm);
            map.put("serviceArea", serviceArea);
            map.put("aiobasicAddress", aiobasicAddress);
            map.put("reportStartTimeStr", reportStartTimeStr);
            map.put("reportEndTimeStr", reportEndTimeStr);
            map.put("description", description);
            map.put("hrefAddress", hrefAddress);
            map.put("misRoot", misRoot);
            map.put("misAddress", misAddress);
            map.put("downloadAddress", downloadAddress);

            FreemarkerTplData tplData = new FreemarkerTplData("CN".equalsIgnoreCase(serviceArea) ? ZH_CN_STANDARD : ZH_TW_STANDARD, map);

            return tplService.getByTemplateName("VersionUpdateAssessmentReportMailContent.ftl", tplData);
        }  catch (Exception ex) {
            log.error("Get VersionUpdateAssessmentReportMail Error:", ex);
            return "";
        }
    }

    @Override
    public MailSourceType getMailSourceType(ReportRecord rm, List<CustomerOrgMap> ofOrgs) {
        return MailSourceType.VersionUpdateAssessmentReport;
    }

    @Override
    public String getMisAddress(String misRoot) {
        return "CN".equals(serviceArea)
                ? "https://aieom.digiwincloud.com.cn/#/report/product-maintenance-report/version-evaluation-report"
                : "https://aieom.digiwincloud.com/#/report/product-maintenance-report/version-evaluation-report";
    }
}
