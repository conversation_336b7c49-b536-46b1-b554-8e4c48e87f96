package com.digiwin.escloud.aiobasic.operatelog.service.impi;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.digiwin.escloud.aiobasic.util.BigDataUtil;
import com.digiwin.escloud.aiobasic.operatelog.model.OperateLogSaveParam;
import com.digiwin.escloud.aiobasic.operatelog.service.IOperateLogService;
import com.digiwin.escloud.aioitms.model.bigdata.StarRocksEntity;
import com.digiwin.escloud.aioitms.model.bigdata.UploadBigDataContext;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.*;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;
import java.util.*;
import java.util.stream.Stream;

import static com.digiwin.escloud.common.util.DateUtil.DATE_FORMATTER;
import static com.digiwin.escloud.common.util.DateUtil.DATE_TIME_FORMATTER;

@Service
public class OperateLogService implements IOperateLogService {
    @Autowired
    private BigDataUtil bigDataUtil;

    @Override
    public BaseResponse saveRecord(OperateLogSaveParam operateLogSaveParam) {
        // region 參數檢查
        List<String> emptyParam = new ArrayList<>();
        if (StringUtils.isEmpty(operateLogSaveParam.getEid())){
            emptyParam.add("eid");
        }
        if (StringUtils.isEmpty(operateLogSaveParam.getProcessUserName())) {
            emptyParam.add("processUserName");
        }
        if (StringUtils.isEmpty(operateLogSaveParam.getProcessUserId())) {
            emptyParam.add("processUserId");
        }
        if (StringUtils.isEmpty(operateLogSaveParam.getOperateType())) {
            emptyParam.add("operateType");
        }
        if (!emptyParam.isEmpty()) {
            return BaseResponse.dynamicError(ResponseCode.PARAM_IS_EMPTY, emptyParam.stream().collect(Collectors.joining(",")));
        }
        // endregion

        // 將參數轉成jsonString
        String operateContentString = GsonUtil.getInstance().toJson(operateLogSaveParam);

        // streamload保存數據
        StarRocksEntity starRocksEntity = new StarRocksEntity();
        starRocksEntity.setDatabase(bigDataUtil.getSrDbName());
        starRocksEntity.setTable("AiopsOperateLog");
        List<LinkedHashMap<String, Object>> rows = new ArrayList<>();
        LinkedHashMap<String, Object> params = new LinkedHashMap<>();
        params.put("startTime", DateUtil.getNowFormatString(DATE_TIME_FORMATTER));
        params.put("eid", operateLogSaveParam.getEid());
        params.put("deviceId", null);
        params.put("uploadDataModelCode", "AiopsOperateLog");
        params.put("userSid", null);
        params.put("userName", operateLogSaveParam.getProcessUserName());
        params.put("operateId", operateLogSaveParam.getProcessUserId());
        params.put("operateType", operateLogSaveParam.getOperateType());
        params.put("endTime", null);
        params.put("operateContent", operateContentString);
        params.put("operateResult", null);
        params.put("sid", RequestUtil.getHeaderSid());
        params.put("source_db_id", null);
        rows.add(params);
        starRocksEntity.setRows(rows);
        bigDataUtil.srStreamLoad(starRocksEntity);

        return BaseResponse.ok();
    }

    @Override
    public BaseResponse getOperateLogList(Map<String, Object> params) {
        // region 參數檢查
        List<String> checkParams = Stream.of(
                new AbstractMap.SimpleEntry<>("isMis", ObjectUtil.isEmpty(params.get("isMis"))),
                new AbstractMap.SimpleEntry<>("area", ObjectUtil.isEmpty(params.get("area")))
        )
                .filter(Map.Entry::getValue)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(checkParams)) {
            return BaseResponse.dynamicError(ResponseCode.PARAM_IS_EMPTY, String.join(", ", checkParams));
        }

        // endregion
        try {
            // 構建查詢 SQL 字串
            StringBuilder queryBuilder = new StringBuilder();
            queryBuilder.append("SELECT eid, deviceId, userName, operateId, startTime , endTime , operateContent, operateType ");
            queryBuilder.append("FROM servicecloud.AiopsOperateLog WHERE 1=1 ");

            String eid = StringUtil.toString(params.get("eid"));
            String operateType = StringUtil.toString(params.get("operateType"));
            Integer page = IntegerUtil.objectToInteger(params.get("page"), 1);
            Integer size = IntegerUtil.objectToInteger(params.get("size"), 10);
            String area = StringUtil.toString(params.get("area"));
            Boolean isMis = Boolean.TRUE.toString().equals(StringUtil.toString(params.get("isMis"))) ? Boolean.TRUE : Boolean.FALSE;

            params.remove("eid");
            params.remove("operateType");
            params.remove("page");
            params.remove("size");
            params.remove("area");
            params.remove("isMis");

            String JSONQuery = getJSONQuery(params);

            // 根據 `eid` 和 `operateType` 添加條件
            if (eid != null && !eid.isEmpty()) {
                queryBuilder.append("AND eid = '").append(eid).append("' ");
            }
            if (operateType != null && !operateType.isEmpty()) {
                queryBuilder.append("AND operateType = '").append(operateType).append("' ");
            }
            if(!JSONQuery.isEmpty()) {
                queryBuilder.append(JSONQuery);
            }

            // 添加排序條件
            queryBuilder.append(" ORDER BY startTime DESC "); // 使用 ASC 升序，或者 DESC 降序

            // 添加分頁條件
            queryBuilder.append("LIMIT ").append((page - 1) * size).append(", ").append(size);

            List<Map<String, Object>> records = bigDataUtil.srQuery(queryBuilder.toString());
            // 查詢總筆數
            StringBuilder countQuery = new StringBuilder();
            countQuery.append("SELECT COUNT(*) AS total ");
            countQuery.append("FROM servicecloud.AiopsOperateLog WHERE 1=1 ");
            if (eid != null && !eid.isEmpty()) {
                countQuery.append("AND eid = '").append(eid).append("' ");
            }
            if (operateType != null && !operateType.isEmpty()) {
                countQuery.append("AND operateType = '").append(operateType).append("' ");
            }
            if(!JSONQuery.isEmpty()) {
                countQuery.append(JSONQuery);
            }

            // 執行總數查詢
            List<Map<String, Object>> countResult = bigDataUtil.srQuery(countQuery.toString());
            int totalCount = 0;
            if (!countResult.isEmpty() && countResult.get(0).get("total") != null) {
                totalCount = Integer.parseInt(countResult.get(0).get("total").toString());
            }

            // 建立結果列表
            List<Map<String, Object>> total = records.stream().map(record -> {
                Map<String, Object> result = new HashMap<>();
                String operateId = (String) record.get("operateId");
                String userName = (String) record.get("userName");

                // 判斷是否包含 @digiwin.com，並根據 area 修改 userName
                if (isMis && operateId.contains("@digiwin.com")) {
                    userName = "TW".equals(area) ? "鼎新人員" : "鼎捷人员";
                }

                // 去除 operateId 與 userName，預防資料被非相關人員查詢
                Map<String, Object> operateContent = GsonUtil.getInstance().fromJson(Objects.toString(record.get("operateContent"), ""), Map.class);
                operateContent.remove("operateId");
                operateContent.remove("userName");

                result.put("eid", record.get("eid"));
                result.put("deviceId", record.get("deviceId"));
                result.put("userName", userName);
                result.put("startTime", record.get("startTime"));
                result.put("endTime", record.get("endTime"));
                result.put("operateContent", operateContent);
                result.put("operateType", record.get("operateType"));
                return result;
            }).collect(Collectors.toList());

            // 返回封裝結果
            Map<String, Object> data = new HashMap<>();
            data.put("total", totalCount); // 总笔数
            data.put("list", total); // 记录列表

            return BaseResponse.ok(data);
        } catch (Exception e) {
            // 捕捉並處理查詢中的任何異常
            return BaseResponse.error(ResponseCode.OPERATION_LOG_EXPORT_ERROR);
        }
    }

    private String getJSONQuery(Map<String, Object> params) {
        if(params.isEmpty()) {
            return "";
        }
        String defaultJSON = "CAST(GET_JSON_OBJECT(operateContent, '$.FIELD') AS VARCHAR)";
        StringBuilder JSONQuery = new StringBuilder();
        if(!ObjectUtil.isEmpty(params.get("startTime")) && !ObjectUtil.isEmpty(params.get("endTime"))) {
            JSONQuery.append(" AND ")
                    .append(defaultJSON.replace("FIELD", "startTime"))
                    .append(" BETWEEN '")
                    .append(params.get("startTime"))
                    .append("' AND '")
                    .append(params.get("endTime")).append("'");
            params.remove("startTime");
            params.remove("endTime");
        }
        if(!ObjectUtil.isEmpty(params.get("operation"))) {
            List<String> operationList = getOperationList(StringUtil.toString(params.get("operation")));
            String result = operationList.stream()
                    .map(str -> "'" + str + "'")
                    .collect(Collectors.joining(","));
            JSONQuery.append(" AND ").append(defaultJSON.replace("FIELD", "operation") + " IN (" + result + ")");
            params.remove("operation");
        }

        if(!ObjectUtil.isEmpty(params)) {
            JSONQuery.append(" AND ").append(
                    params.entrySet().stream()
                            .filter(entry -> !ObjectUtil.isEmpty(entry.getValue()))
                            .map(entry -> defaultJSON.replace("FIELD", entry.getKey()) + " = '" + entry.getValue() + "'")
                            .collect(Collectors.joining(" AND "))
            );
        }
        return JSONQuery.toString();
    }

    private List<String> getOperationList(String operation) {
        List<String> operationList = Arrays.asList("SCANON", "SCANOFF");
        String str = Arrays.stream(operation.split(",")).map(s -> {
            if(operationList.contains(s)) {
                return s + "_SUCCESS," + s + "_FAILED";
            }
            return operation;
        }).collect(Collectors.joining(","));
        return Arrays.asList(str.split(","));
    }

    public void saveSendOperateLog(OperateLogSaveParam operateLogSaveParam, List<String> reciverMailList, String modelVersion, Boolean autoflag) {
        Map<String, Object> operateContent = new HashMap<>(); // 操作内容Json
        operateContent.put("autoflag", autoflag);
        operateContent.put("reciverMail", reciverMailList);
        operateContent.put("modelVersion", modelVersion);

        String operateContentString = GsonUtil.getInstance().toJson(operateContent);

        StringBuilder sb = new StringBuilder();
        sb.append("INSERT INTO servicecloud.AiopsOperateLog(eid, deviceId, userName, operateId, startTime, operateContent, operateType) ");
        sb.append("VALUES('").append(operateLogSaveParam.getEid()).append("', ");
        sb.append("'', ");
        sb.append("'").append(operateLogSaveParam.getProcessUserName()).append("', ");
        sb.append("'").append(operateLogSaveParam.getProcessUserId()).append("', ");
        sb.append("'").append(DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss")).append("', ");
        sb.append("'").append(operateContentString).append("', ");
        sb.append("'").append("aiops_edr_send_mail").append("') ");

        bigDataUtil.srSave(sb.toString());
    }
}
