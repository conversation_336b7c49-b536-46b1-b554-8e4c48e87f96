package com.digiwin.escloud.aiocmdb.backupschedule.controller;

import com.alibaba.fastjson.JSONObject;
import com.digiwin.escloud.aiocmdb.assetmaintenance.model.AssetListGetResponse;
import com.digiwin.escloud.aiocmdb.backupschedule.service.IBackupScheduleService;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.RequestUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

@Api(value = "备份排程", tags = {"BackupSchedule"} )
@Slf4j
@RestController
@RequestMapping("/api/backupschedule")
@CrossOrigin(origins = "*", methods = {RequestMethod.GET, RequestMethod.POST, RequestMethod.DELETE, RequestMethod.PUT})

public class BackupScheduleController {
    @Autowired
    private IBackupScheduleService backupScheduleService;


    @ApiOperation(value = "根据模型获取备份排程列表")
    @PostMapping(value = "/getAssetList")
    public AssetListGetResponse getAssetList(@RequestParam(value="modelCode") String modelCode,
                                             @RequestParam(value="serviceCode") String serviceCode,
                                             @RequestParam(value="eid") long eid,
                                             @RequestParam(value = "deviceStatus", required = false) String deviceStatus,
                                             @RequestParam(value = "filter", required = false) String filter,
                                             @RequestParam(value = "pageNum", required = false, defaultValue = "0") int pageNum,
                                             @RequestParam(value = "pageSize", required = false, defaultValue = "0") int pageSize,
                                             @RequestBody JSONObject jsonObject) {
        AssetListGetResponse res = new AssetListGetResponse();
        try {
            res.setCode(ResponseCode.SUCCESS.toString());
            res.setData(backupScheduleService.getAssetList(modelCode,eid,deviceStatus,filter,pageNum,pageSize,false, jsonObject));
            res.setTotalCount(backupScheduleService.getAssetCount(modelCode,eid,deviceStatus,filter));
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex.getMessage());
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg(ex.getMessage());
        }
        return res;
    }


    @ApiOperation(value = "添加备份排程")
    @PostMapping(value = "/addBackupschedule")
    public BaseResponse addBackupschedule(@RequestBody JSONObject jsonObject) {
        String modelCode = jsonObject.getString("modelCode");
        String serviceCode = jsonObject.getString("serviceCode");
        Long eid = jsonObject.getLong("eid");
        String deviceCatalog = jsonObject.getString("deviceCatalog");
        String sourceDeviceName = jsonObject.getString("sourceDeviceName");
        return backupScheduleService.addBackupschedule(modelCode, RequestUtil.getHeaderSid(), serviceCode, eid,
                deviceCatalog, sourceDeviceName, jsonObject);
    }

    @ApiOperation(value = "导入备份排程")
    @PostMapping( "/batchUpload")
    public BaseResponse batchUpload(@RequestParam("file") MultipartFile file, @RequestParam(value="modelCode") String modelCode,
                                    @RequestParam(value="serviceCode") String serviceCode,@RequestParam(value="eid") long eid, @RequestParam(value="nickName") String nickName){
        BaseResponse res = new BaseResponse();
        try {
            return backupScheduleService.batchUpload(nickName,modelCode, serviceCode, eid,file);
        } catch (Exception ex) {
            log.error(ex.getMessage());
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg(ex.getMessage());
        }
        return res;
    }
    @ApiOperation(value = "删除资产，并将其他资产主动关联该资产的地方一并删除，历史源设备的维护记录不能删除")
    @DeleteMapping(value = "/{deviceId}/deleteAssetAndRelateAsset")
    public BaseResponse deleteAssetAndRelateAsset(@PathVariable(value="deviceId") String deviceId,
                                                  @RequestParam(value="modelCode") String modelCode,
                                                  @RequestParam(value="serviceCode") String serviceCode,
                                                  @RequestParam(value="eid") long eid,
                                                  @RequestParam(value = "modelGroupCode", required = false, defaultValue = "device") String modelGroupCode,
                                                  @RequestParam(value = "deleteRelateAsset", required = true, defaultValue = "true") boolean deleteRelateAsset) {
        BaseResponse res = new BaseResponse();
        try {
            backupScheduleService.deleteAssetAndRelateAsset(modelCode, deviceId, serviceCode, eid, modelGroupCode, deleteRelateAsset);
            res.setCode(ResponseCode.SUCCESS.toString());
        } catch (Exception ex) {
            log.error(ex.getMessage());
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg(ex.getMessage());
        }
        return res;
    }
    @ApiOperation(value = "(批量删除备份排程)删除资产，并将其他资产主动关联该资产的地方一并删除，历史源设备的维护记录不能删除")
    @DeleteMapping(value = "/batchDeleteAssetAndRelateAsset")
    public BaseResponse batchDeleteAssetAndRelateAsset(@RequestBody List<Map<String,String>> list,
                                                       @RequestParam(value="eid") long eid,
                                                       @RequestParam(value = "modelGroupCode", required = false, defaultValue = "device") String modelGroupCode,
                                                       @RequestParam(value = "deleteRelateAsset", required = true, defaultValue = "true") boolean deleteRelateAsset) {
        BaseResponse res = new BaseResponse();
        try {
            res = backupScheduleService.batchDeleteAssetAndRelateAsset(list,eid, modelGroupCode, deleteRelateAsset);
        } catch (Exception ex) {
            log.error(ex.getMessage());
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg(ex.getMessage());
        }
        return res;
    }
    @ApiOperation(value = "获取备份软体列表")
    @GetMapping(value = "/getBackupSoftwarList")
    public BaseResponse getBackupSoftwarList(
                                             @RequestParam(value = "pageNum", required = false, defaultValue = "0") int pageNum,
                                             @RequestParam(value = "pageSize", required = false, defaultValue = "0") int pageSize) {
        BaseResponse res = new BaseResponse();
        try {
            res = backupScheduleService.getBackupScheduleSoftwareList();
        } catch (Exception ex) {
            log.error(ex.getMessage());
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg(ex.getMessage());
        }
        return res;
    }

}
