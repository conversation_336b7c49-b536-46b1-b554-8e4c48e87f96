package com.digiwin.escloud.issueservice.t.issuesubmit.service.impl;

import com.digiwin.escloud.issueservice.dao.IIssueDao;
import com.digiwin.escloud.issueservice.model.*;
import com.digiwin.escloud.issueservice.rest.DmcRest;
import com.digiwin.escloud.issueservice.services.IUserService;
import com.digiwin.escloud.issueservice.statemode.Incomplete;
import com.digiwin.escloud.issueservice.statemode.IssueContext;
import com.digiwin.escloud.issueservice.t.integration.exception.SCMPipelineException;
import com.digiwin.escloud.issueservice.t.integration.fuguan.mq.issue.IssueFGProducer;
import com.digiwin.escloud.issueservice.t.integration.scm.model.SCMCase;
import com.digiwin.escloud.issueservice.t.integration.scm.model.ToT100Flag;
import com.digiwin.escloud.issueservice.t.integration.scm.pipeline.SCMPipeline;
import com.digiwin.escloud.issueservice.t.integration.workday.model.IssueKey;
import com.digiwin.escloud.issueservice.t.integration.workday.mq.issue.IssueWorkDayProducer;
import com.digiwin.escloud.issueservice.t.issue.dao.TIssueMapper;
import com.digiwin.escloud.issueservice.t.issuedetail.dao.IssueProcessMapper;
import com.digiwin.escloud.issueservice.t.issuesubmit.dao.IssueSubmitMapper;
import com.digiwin.escloud.issueservice.t.issuesubmit.service.IssueSubmitService;
import com.digiwin.escloud.issueservice.t.model.cases.IssueAttachment;
import com.digiwin.escloud.issueservice.t.model.cases.*;
import com.digiwin.escloud.issueservice.t.model.cases.constants.CaseProcessType;
import com.digiwin.escloud.issueservice.t.model.cases.constants.CaseStatus;
import com.digiwin.escloud.issueservice.t.model.cases.constants.SubmitTo;
import com.digiwin.escloud.issueservice.t.model.cases.constants.SubmitWay;
import com.digiwin.escloud.issueservice.t.model.cases.dto.CasesFormDTO;
import com.digiwin.escloud.issueservice.t.model.common.BaseResponse;
import com.digiwin.escloud.issueservice.t.model.common.ResponseStatus;
import com.digiwin.escloud.issueservice.t.model.constant.JiaoFuUserType;
import com.digiwin.escloud.issueservice.t.model.customer.constant.CustomerProductStatus;
import com.digiwin.escloud.issueservice.t.model.login.Role;
import com.digiwin.escloud.issueservice.t.model.mail.MailSendSituation;
import com.digiwin.escloud.issueservice.t.service.AttachementService;
import com.digiwin.escloud.issueservice.t.service.IMailService;
import com.digiwin.escloud.issueservice.t.utils.CasesProcessUtils;
import com.digiwin.escloud.issueservice.t.utils.DateUtils;
import com.digiwin.escloud.issueservice.t.utils.StringUtil;
import com.digiwin.escloud.issueservice.utils.DgwFileUtils;
import com.digiwin.escloud.userapi.model.customer.CustomerDetailInfo;
import com.digiwin.escloud.userapi.model.customer.CustomerProjectStatus;
import com.digiwin.escloud.userapi.model.customer.ServiceStaffCond;
import com.digiwin.escloud.userapi.model.user.UserDetailInfo;
import com.digiwin.escloud.userapi.service.UserServiceFeignClient;
import com.google.gson.Gson;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.mongodb.gridfs.GridFSFile;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.gridfs.GridFsTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import static com.digiwin.escloud.issueservice.t.model.mail.MailSendSituation.JIAOFU_ACCEPT_CASE;
import static com.digiwin.escloud.issueservice.t.utils.CasesProcessUtils.EMAIL_SEP;

/**
 * 案件提交接口实现类
 * Created by huly on 2019-7-11
 */
@Service
@Slf4j
public class IssueSubmitServiceImpl implements IssueSubmitService {
    //与数据库字段长度相同
    private static final int CUSTOMER_CC_MAIL_MAX_LENGTH = 300;
    @Autowired
    private CasesProcessUtils casesProcessUtils;
    @Value("${issueAttachAddress}")
    private String issueAttachAddress;
    @Autowired
    private UserServiceFeignClient userServiceFeignClient;

    @Autowired
    private IssueSubmitMapper issuesMapper;

    @Autowired
    private IssueProcessMapper issueProcessMapper;

    @Autowired
    private IMailService mailService;

    @Autowired
    GridFsTemplate gridFsTemplate;

    @Autowired
    private SCMPipeline scmPipeline;

    @Autowired
    private IssueFGProducer fg187Mq;

    @Autowired
    private IssueWorkDayProducer workDayMq;

    @Autowired
    private TIssueMapper tIssueMapper;

    @Autowired
    private DmcRest dmcRest;

    @Autowired
    private AttachementService attachementService;

    @Autowired
    private IUserService userService;
    @Autowired
    private IIssueDao issueDao;
    @Override
    public BaseResponse getIssueClassificationList(String productCode,String filter){
        Map<String,Object> resultMap = new HashMap<>();
        resultMap.put("type","select"); //默認
        if(productCode.equals("100") || productCode.equals("06") || productCode.equals("999") || productCode.equals("164") || productCode.equals("147")){
            List<IssueCatelog> resultList = new ArrayList<>();

            List<IssueClassification> list = issuesMapper.getIssueClassificationListNew(productCode,filter);
            if(!CollectionUtils.isEmpty(list)) {
                Map<String, List<IssueClassification>> map = list.stream().collect(Collectors.groupingBy(IssueClassification::getIssueCatelogCode));

                for (Map.Entry<String, List<IssueClassification>> entry : map.entrySet()) {
                    IssueCatelog issueCatelog = new IssueCatelog();
                    List<IssueClassification> issueClassificationList = entry.getValue();
                    if (!CollectionUtils.isEmpty(issueClassificationList)) {
                        IssueClassification issueClassification = issueClassificationList.get(0);
                        if (!StringUtils.isEmpty(issueClassification.getIssueCatelogCode())) {
                            issueCatelog.setProductCode(issueClassification.getProductCode());
                            issueCatelog.setIssueCatelogCode(issueClassification.getIssueCatelogCode());
                            issueCatelog.setIssueCatelogName(issueClassification.getIssueCatelogName());
                            issueCatelog.setIssueClassificationList(issueClassificationList.stream().filter(o -> o.getIsEnable().equals("Y")).collect(Collectors.toList()));
                            resultList.add(issueCatelog);
                        }
                    }
                }

                if (!CollectionUtils.isEmpty(resultList)) {
                    resultMap.put("type", "tree");
                    resultMap.put("list", resultList.stream()
                            .sorted(Comparator.comparing(IssueCatelog::getIssueCatelogCode))
                            .collect(Collectors.toList()));
                    return BaseResponse.ok(resultMap);
                } else {
                    resultMap.put("type", "select");
                    resultMap.put("list", issuesMapper.getIssueClassificationList(productCode, filter));
                    return BaseResponse.ok(resultMap);
                }
            }else{
                resultMap.put("list", new ArrayList<>());
            }
            return BaseResponse.ok(resultMap);
        }else {
            return BaseResponse.ok(issuesMapper.getIssueClassificationList(productCode,filter));
        }

    }

    @Override
    public BaseResponse getIssueNotDealClassificationList(String productCode,String filter){
        return BaseResponse.ok(issuesMapper.getIssueNotDealClassificationList(productCode,filter));
    }
    @Override
    public BaseResponse getProjectList(String productCode,String serviceCode){
        return BaseResponse.ok(issuesMapper.getProjectList(productCode,serviceCode));
    }
    @Override
    public BaseResponse getContractList(String productCode,String serviceCode){
        return BaseResponse.ok(issuesMapper.getContractList(productCode,serviceCode));
    }
    /**
     * 保存案件主表
     * @param cases
     * @return
     */
    public long saveIssue(Cases cases){
        String currentTime = DateUtils.getCurrentTime();
        cases.setSubmitTime(currentTime);
        if(StringUtils.isEmpty(cases.getSubmitWay())) {
            cases.setSubmitWay(SubmitWay.INNER.getSymbol());
        }
        //SyncStatus为空时赋值，932提报的台湾案件需要同步到crm
        //20250617 T的案件需要同步到crm
//        if (StringUtils.isEmpty(cases.getSyncStatus())){
            cases.setSyncStatus(SyncStatus.UnSync.toString());
//        }
        cases.setIssueType(IssueType.Issue.toString());

        //存案件的主表
        issuesMapper.doSaveIssue(cases);
        if("EASY_TALK".equals(cases.getSubmitWay())){
            cases.setCrmId(IssueCodeRoleStart.ESV.toString() + String.format("%09d", cases.getIssueId()));
        }else {
            cases.setCrmId(CasesProcessUtils.genCrmId(cases.getIssueId()));
        }
        issuesMapper.saveCrmId(cases.getIssueId(), cases.getCrmId());
        return cases.getIssueId();
    }

    /**
     * 保存案件明细
     * @param cases
     */
    public void saveIssueDetail(Cases cases){
        /*if(StringUtils.isEmpty(cases.getIssueClassification())){
            cases.setSyncStatus(SyncStatus.DontNeedSync.toString());
        }else{*/
            cases.setSyncStatus(SyncStatus.EditUnSync.toString());
//        }
        //存案件明细表
        issuesMapper.doSaveIssueDetail(cases);
    }
    /**
     * 保存案件进展
     * @param cases
     * @return
     */
    public int saveIssueProgress(Cases cases){
        CaseHistory caseHistory = new CaseHistory();
        caseHistory.setIssueId(cases.getIssueId());
        caseHistory.setCrmId(cases.getCrmId());
        caseHistory.setCurrentStatus(cases.getCurrentStatus());
        caseHistory.setProcessType(cases.getProcessType());
        caseHistory.setProcessTime(cases.getSubmitTime());
        caseHistory.setSequenceNum(cases.getMaxSequenceNum() == 0? 1: cases.getMaxSequenceNum());
        caseHistory.setProcessor(cases.getUserId());
        //如果是内部员工的话，需要获取工号存到workno
        UserDetailInfo userDetail = userServiceFeignClient.findUserDetailById(cases.getUserId());
        if(!ObjectUtils.isEmpty(userDetail) && !StringUtils.isEmpty(userDetail.getWorkno())){
            caseHistory.setWorkno(userDetail.getWorkno());
        }else {
            caseHistory.setWorkno(null);
        }

        caseHistory.setHandlerId(cases.getServiceId());
        caseHistory.setDescription(cases.getHandlerDetail());
        if(StringUtils.isEmpty(cases.getHandlerDetail())){
            caseHistory.setSyncStatus(SyncStatus.DontNeedSync.toString());
        }else{
            caseHistory.setSyncStatus(SyncStatus.EditUnSync.toString());
        }
        caseHistory.setProcessHours(cases.getWorkHours());

        //存案件进展
        return issuesMapper.doSaveIssueProgress(caseHistory);
    }

    /**
     * 验证参数
     * 如果案件状态字段currentStatus为空，则根据处理人的角色设置值
     * @param cases
     * @return
     */
    private BaseResponse validateParam(Cases cases,String role){
        if(!StringUtils.isEmpty(role)){
            //验证参数
            if(!validateCaseFormByType(cases,role) ) {
                return BaseResponse.error(ResponseStatus.CASE_VERIFY);
            }

            //客户资料验证
            if(role.equals(Role.JIAOFU.getValue())){
                CustomerDetailInfo customerInfo = userServiceFeignClient.getCustomerById(cases.getServiceCode());
                if(ObjectUtils.isEmpty(customerInfo)){
                    return BaseResponse.error(ResponseStatus.CUSTOMER_VERIFY);
                }
                cases.setCustName(customerInfo.getCustomerName());

            }else if(role.equals(Role.CHANZHONG.getValue())){
                if(StringUtils.isEmpty(cases.getServiceCode())){
                    cases.setServiceCode("DIGIWIN");
                }else{
                    CustomerDetailInfo customerInfo = userServiceFeignClient.getCustomerById(cases.getServiceCode());
                    if(ObjectUtils.isEmpty(customerInfo)){
                        return BaseResponse.error(ResponseStatus.CUSTOMER_VERIFY);
                    }
                    cases.setCustName(customerInfo.getCustomerName());
                }

            }
            //提单给交付，还需要验证提单人、反馈人、服务人员
            if(role.equals(Role.JIAOFU.getValue())){
                //验证提单人
                UserDetailInfo submitIdDetail = userServiceFeignClient.findUserDetailById(cases.getSubmitedId());
                if(ObjectUtils.isEmpty(submitIdDetail)){
                    return BaseResponse.error(ResponseStatus.SUBMITOR_VERIFY);
                }

                //验证处理人
                UserDetailInfo processUserDetail = userServiceFeignClient.findUserDetailByMaxAccount(cases.getAccount());
                if(ObjectUtils.isEmpty(processUserDetail)){
                    return BaseResponse.error(ResponseStatus.ACCOUNT_VERIFY);
                }
                if(StringUtils.isEmpty(cases.getCurrentStatus())){
                    if (JiaoFuUserType.JIAOFUFUWU.getValue() == processUserDetail.getJiaofuType()) {
                        cases.setCurrentStatus(CaseStatus.JIAOFU_SERVICE_HANDLING.getStatus());
                    } else {
                        cases.setCurrentStatus(CaseStatus.JIAOFU_CONSULTANT_HANDLING.getStatus());
                    }
                }
                cases.setServiceId(StringUtils.isEmpty(processUserDetail.getUserId()) ? null : processUserDetail.getUserId());
                cases.setServiceDepartment(StringUtils.isEmpty(processUserDetail.getDepartmentCode()) ? null : processUserDetail.getDepartmentCode());
                cases.setMainCharge(StringUtils.isEmpty(processUserDetail.getUserId()) ? null : processUserDetail.getUserId());
            }
            //验证提报人
            /*UserDetailInfo userDetail = userServiceFeignClient.findUserDetailById(cases.getSubmitedId());
            if(ObjectUtils.isEmpty(userDetail)){
                return baseResponse.error(ResponseStatus.SUBMITOR_VERIFY);
            }*/
            //2024-03-01 T服务云拿到下面验证，跟云管家一致，不校验项目状态
            /*if(role.equals(Role.CLIENTY.getValue())){
                //检查提报权限
                try {
                    BaseResponse checkResponse = checkSubmitPrivilege(cases.getServiceCode(), cases.getProductCode());
                    if(checkResponse.getStatus() != ResponseStatus.OK.getCode()){
                        return checkResponse;
                    }
                } catch (RuntimeException e) {
                    return BaseResponse.error(ResponseStatus.EXPECTATION_FAILED, e.getMessage());
                }
            }*/
        }
        return BaseResponse.ok(cases);
    }

    /**
     * 处理案件的其他参数，比如把环境、企业编号、营运据点拼接到问题描述之前，从后往前拼；area等
     *
     * @param cases
     */
    @Deprecated
    private void dealOtherParam(String language, Cases cases){
        //把环境、企业编号、营运据点拼接到问题描述之前，从后往前拼
        cases.setIssueDescription(CasesProcessUtils.getQuestionDescAssembleSiteAndEntAndEnv(language, cases));

        //领域查询（为了发邮件找到对应领域的实施顾问）
        dealArea(cases, cases.getProductCode());
    }

    /**
     * Deprecated since 2020-05-20
     */
    @Deprecated
    @Transactional(propagation = Propagation.REQUIRED, rollbackForClassName = "Exception")
    public BaseResponse submitCasesToGuwen(String language,  Cases cases,String role){
        BaseResponse baseResponse = submitCases(language, cases, role, null);
        if(baseResponse.getStatus() != ResponseStatus.OK.getCode()){
            return baseResponse;
        }
        sendEmail((Cases)baseResponse.getResponse(),cases);
        return baseResponse;
    }

    /**
     * 提交时发送处理人，抄送提单人+反馈人
     */
    private void sendEmail(Cases caseInfo,Cases cases){

        List<UserDetailInfo> mailUsers = new ArrayList<>();
        CasesEmail casesEmail = new CasesEmail();

        BeanUtils.copyProperties(caseInfo, casesEmail);
        casesEmail.setEmergency(cases.isEmergency()?"Y":"N");

        if(StringUtils.isEmpty(casesEmail.getServiceId())){
            log.error("案件处理人为空, issueId:{}", cases.getIssueId());
            log.warn(CasesProcessUtils.constructSendMailSkipedLogMessage("案件处理人", cases.getIssueId(), cases.getMaxSequenceNum()));
            return;
        }
        //发送顾问邮件
        UserDetailInfo processUserDetail = userServiceFeignClient.findUserDetailById(casesEmail.getServiceId());
        Optional.ofNullable(processUserDetail).ifPresent(mailUsers::add);
        //抄送邮件组准备
        HashSet<String> ccMailSet = new HashSet<>();
        if(!StringUtils.isEmpty(casesEmail.getEmail())) {
            //反馈人邮箱
            ccMailSet.add(casesEmail.getEmail());
        }
        UserDetailInfo submitIdDetail = userServiceFeignClient.findUserDetailById(casesEmail.getSubmitedId());
        Optional.ofNullable(submitIdDetail).ifPresent(mailUsers::add);
        if(submitIdDetail != null && !StringUtils.isEmpty(submitIdDetail.getEmail())) {
            ccMailSet.add(submitIdDetail.getEmail());
        }
        log.info("是否抄送给主管:---->{}",new Gson().toJson(processUserDetail));
        //是否抄送主管
        ccMailSet.addAll(casesProcessUtils.getCcManagerEmailList(mailUsers));

        log.info("是否抄送给小组长:---->");
        ccMailSet.addAll(casesProcessUtils.getVirtualTeamLeadersEmail(processUserDetail.getUserId()));
        //是否抄送统筹人
        if ( !StringUtils.isEmpty(caseInfo.getServiceCode()) && !StringUtils.isEmpty(caseInfo.getProductCode())){
            ccMailSet.addAll(casesProcessUtils.getCoordinatorEmailList(caseInfo.getServiceCode(),caseInfo.getProductCode()));
        }
        /*Map<String,List<String>> map = accountListToDetailList(casesEmail.getAccount());
        List<String> emailList = map.get("emailList");
        ccMailSet.addAll(emailList);

        List<String> userIdList = map.get("userIdList");
        try {
            List<String> managerEmailList = userServiceFeignClient.getManagerEmailListByUserId(userIdList);
            ccMailSet.addAll(managerEmailList);
        } catch (Exception e){
            log.info(e.getMessage());
        }*/
        log.info("邮件抄送人:--->{}",new Gson().toJson(ccMailSet));
        if(processUserDetail != null && !StringUtils.isEmpty(processUserDetail.getEmail())) {
            ccMailSet.remove(processUserDetail.getEmail());
            List<String> ccMailList = new ArrayList<>(ccMailSet);
            mailService.SendCasesMail(casesEmail, Collections.singletonList(processUserDetail.getEmail()), ccMailList, JIAOFU_ACCEPT_CASE, processUserDetail.getLanguage());
        } else {
            log.warn(CasesProcessUtils.constructSendMailSkipedLogMessage("案件处理人", cases.getIssueId(), cases.getMaxSequenceNum()));
        }

    }
    private Map<String, List<String>> accountListToDetailList(List<String> accountList) {
        Set<String> userIdList = new HashSet<>();
        Set<String> emailList = new HashSet<>();
        for(String account : accountList){
            List<String> usernameList = new ArrayList<>();
            usernameList.add(account);
            UserDetailInfo user = userServiceFeignClient.findUserDetailByUsername(usernameList);
            if(user != null && user.getEmail() != null && !StringUtils.isEmpty(user.getEmail().replaceAll("\\s*", ""))) {
                userIdList.add(user.getUserId());
                emailList.add(user.getEmail());
            }
        }
        Map<String, List<String>> result = new HashMap<>();
        result.put("userIdList", new ArrayList<>(userIdList));
        result.put("emailList", new ArrayList<>(emailList));
        return result;
    }


    /**
     * Deprecated since 2020-05-20
     */
    @Deprecated
    @Transactional(propagation = Propagation.REQUIRED, rollbackForClassName = "Exception")
    public BaseResponse submitCasesToFuwu(String language, Cases cases,String role){
        BaseResponse baseResponse = submitCases(language, cases, role, null);
        if(baseResponse.getStatus() != ResponseStatus.OK.getCode()){
            return baseResponse;
        }
        sendEmail((Cases)baseResponse.getResponse(),cases);

        return baseResponse;
    }

    /**
     * @param language 语言别，取值:zh-CN, zh-TW
     * @param cases 案件信息
     * @param role 案件提交人员角色
     * @param parentCrmId 父关联crmId，没有传null
     */
    public BaseResponse submitCases(String language, Cases cases,String role, String parentCrmId){
        log.info("submitCases start");
        //1、验证参数
        BaseResponse baseResponse =  validateParam(cases, role);

        if(baseResponse.getStatus() != ResponseStatus.OK.getCode()){
            return baseResponse;
        }

        //2、处理案件的其他参数，比如把环境、企业编号、营运据点拼接到问题描述之前，从后往前拼；area等
        //log.info("dealOtherParam start");
        //dealOtherParam(language, cases);

        log.info("saveIssue start");
        //3、存案件主表
        //新版易聊来的T的案件，立即立案，单头的状态根据状态模式来
        if("EASY_TALK".equals(cases.getSubmitWay()) && !IssueStatus.Closed.toString().equals(cases.getCurrentStatus())){
            IssueContext context = new IssueContext(cases.getProductCode(), cases.getServiceCode());
            new Incomplete().completeIssueInfo(context);
            cases.setCurrentStatus(context.getIssueStatus());
            //如果处理人是本人 直接变成处理中
            if(cases.getServiceId().equals(cases.getUserId())){
                cases.setCurrentStatus(IssueStatus.Processing.toString());
            }
        }
        long issueId = saveIssue(cases);
        log.info("saveIssue end ,issueId={}",issueId);
        //主表插入成功后，才会更新细表及进展表
        if(issueId > 0){
            //4、存案件明细表
            log.info("saveIssueDetail start");
            cases.setReplyMin(0);
            cases.setEndMin(0);
            saveIssueDetail(cases);

            //5、保存案件进展
            log.info("saveIssueProgress start");
            cases.setMaxSequenceNum(1);
            cases.setProcessType(CaseProcessType.OPEN.getProcessType());
            //新版易聊来的T的案件 第一笔单身是处理中
            if("EASY_TALK".equals(cases.getSubmitWay()) && IssueStatus.Closed.toString().equals(cases.getCurrentStatus())){
                IssueContext context = new IssueContext(cases.getProductCode(), cases.getServiceCode());
                new Incomplete().completeIssueInfo(context);
                cases.setCurrentStatus(context.getIssueStatus());
                //如果处理人是本人 直接变成处理中
                if(cases.getServiceId().equals(cases.getUserId())){
                    cases.setCurrentStatus(IssueStatus.Processing.toString());
                }
            }
            saveIssueProgress(cases);
            //如果是开关联子案件，需要存入案件关联关系
            if(!StringUtils.isEmpty(parentCrmId)){
                issuesMapper.insertSubIssue(parentCrmId, cases.getCrmId());
            }

            log.info("submitCases success end");
            return baseResponse.ok(cases);
        }
        log.info("submitCases failed end");
        return baseResponse.error(ResponseStatus.CREATED);
    }

    /**
     * 提报案件至T产中
     * @param issue 案件信息
     * @param handler 案件处理人信息
     * @return 返回提报后最新的案件状态信息
     */
    private Cases toNewCase(Cases issue, UserDetailInfo handler){
        issue.setTurnToT100("Y");
        issue.setProcessType(CaseProcessType.OPEN.getProcessType());
        issue.setCurrentStatus(CaseStatus.PC_HANDLING.getStatus());
        //更新处理人员信息
        issue.setServiceId(handler.getUserId());
        issue.setServiceName(handler.getName());
        issue.setServiceEmail(handler.getEmail());
        issue.setServicePhone(handler.getPhone());
        issue.setServiceDepartment(handler.getDepartmentCode());
        //将产中处理人设置为案件主要负责人
        issue.setMainCharge(handler.getUserId());
        return issue;
    }

    /**
     * 提报案件给顾问
     * Deprecated since 2020-05-20
     **/
    @Deprecated
    @Override
    public BaseResponse submitCaseToGuwen(String language, CasesFormDTO cases)  {
        log.info("submitCasesToGuwen start");
        if(StringUtils.isEmpty(cases.getUserId())){
            return BaseResponse.error(ResponseStatus.EXPECTATION_FAILED, "获取联系方式失败.");
        }

        //新cases储备
        Cases target = new Cases();
        BeanUtils.copyProperties(cases,target);
        target.setCurrentStatus(CaseStatus.JIAOFU_CONSULTANT_HANDLING.getStatus());

        target.setIsPersonalCase("N");
        target.setTurnToT100("N");

        target.setUserContactId(casesProcessUtils.getUserContactId(cases.getUserId(), cases.getUsername(), cases.getEmail(), cases.getPhone()));
        BaseResponse response = submitCasesToGuwen(language,target, Role.JIAOFU.getValue());
        if(response.getStatus() != ResponseStatus.OK.getCode()){
            return response;
        }
        Cases newCases = (Cases)response.getResponse();
        fg187Mq.send(newCases.getIssueId(), newCases.getMaxSequenceNum());
//        workDayMq.produceMsg(new IssueKey(newCases.getIssueId(), newCases.getMaxSequenceNum(),0));
        Runnable runnable = () -> casesProcessUtils.sendToWorkday(newCases.getIssueId(),newCases.getMaxSequenceNum(),0);
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        try {
            executorService.execute(runnable);
        } catch (Exception ex) {
            log.error("sendToWorkday", ex);
        } finally {
            executorService.shutdown();
        }
        log.info("submitCasesToGuwen end");
        return response;
    }

    /**
     * 内部提报案件给服务
     * Deprecated since 2020-05-20
     */
    @Deprecated
    @Override
    public BaseResponse submitCaseToFuwu(String language, CasesFormDTO cases)  {
        log.info("submitCasesToFuwu start");
        if(StringUtils.isEmpty(cases.getUserId())){
            return BaseResponse.error(ResponseStatus.EXPECTATION_FAILED, "获取联系方式失败.");
        }
        //新cases储备
        Cases target = new Cases();
        BeanUtils.copyProperties(cases,target);
        target.setCurrentStatus(CaseStatus.JIAOFU_SERVICE_HANDLING.getStatus());

        target.setIsPersonalCase("N");
        target.setTurnToT100("N");

        target.setUserContactId(casesProcessUtils.getUserContactId(cases.getUserId(), cases.getUsername(), cases.getEmail(), cases.getPhone()));
        BaseResponse response = submitCasesToFuwu(language, target,Role.JIAOFU.getValue());
        if(response.getStatus() != ResponseStatus.OK.getCode()){
            return response;
        }
        Cases newCases = (Cases)response.getResponse();
        fg187Mq.send(newCases.getIssueId(), newCases.getMaxSequenceNum());
//        workDayMq.produceMsg(new IssueKey(newCases.getIssueId(), newCases.getMaxSequenceNum(),0));
        Runnable runnable = () -> casesProcessUtils.sendToWorkday(newCases.getIssueId(),newCases.getMaxSequenceNum(),0);
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        try {
            executorService.execute(runnable);
        } catch (Exception ex) {
            log.error("sendToWorkday", ex);
        } finally {
            executorService.shutdown();
        }
        log.info("submitCasesToFuwu end");
        return response;
    }

    /**
     * 提交案件给交付单位
     * @param language 语言别，取值:zh-CN, zh-TW
     * @param cases 案件提交信息
     * @param parentCrmId 父关联crmId，没有传null
     */
    @Override
    public BaseResponse submitCaseToJiaofu(String language, String userId, CasesFormDTO cases, String parentCrmId) {
        //有些用户提单，cases.getUserId()为空，导致案件打开异常
        if(StringUtils.isEmpty(cases.getUserId())){
            cases.setUserId(userId);
        }
        Cases target = new Cases();
        BeanUtils.copyProperties(cases,target);
        if(!StringUtils.isEmpty(cases.getIssueStatus())){
            target.setCurrentStatus(cases.getIssueStatus());
        }
        if(cases.getContactId() != 0){
            target.setUserContactId(cases.getContactId());
        }else {
            target.setUserContactId(casesProcessUtils.getUserContactId(cases.getUserId(), cases.getUsername(), cases.getEmail(), cases.getPhone()));
        }
        BaseResponse response = toJiaofu(language, target, Role.JIAOFU.getValue(), parentCrmId);
        if(response.getStatus() != ResponseStatus.OK.getCode()){
            return response;
        }
        Cases newCases = (Cases)response.getResponse();
        fg187Mq.send(newCases.getIssueId(), newCases.getMaxSequenceNum());
//        workDayMq.produceMsg(new IssueKey(newCases.getIssueId(), newCases.getMaxSequenceNum(),0));
        Runnable runnable = () -> casesProcessUtils.sendToWorkday(newCases.getIssueId(),newCases.getMaxSequenceNum(),0);
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        try {
            executorService.execute(runnable);
        } catch (Exception ex) {
            log.error("sendToWorkday", ex);
        } finally {
            executorService.shutdown();
        }
        return response;
    }

    /**
     * 提交案件给交付单位
     * @param language 语言别，取值:zh-CN, zh-TW
     * @param target 案件信息
     * @param parentCrmId 父关联crmId，没有传null
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackForClassName = "Exception")
    public BaseResponse toJiaofu(String language, Cases target,String role, String parentCrmId){
        log.info("submitCaseToJiaofu start");
        target.setIsPersonalCase("N");
        target.setTurnToT100("N");

        String issueStatus = target.getIssueStatus();//如果是新版易聊来的案件，立案并结案，那么立案的时候 不需要发邮件
        BaseResponse baseResponse = submitCases(language, target, role, parentCrmId);
        if(baseResponse.getStatus() != ResponseStatus.OK.getCode()){
            return baseResponse;
        }
        if(!IssueStatus.Closed.toString().equals(issueStatus)){
            sendEmail((Cases)baseResponse.getResponse(), target);
        }
        log.info("submitCaseToJiaofu end");
        return baseResponse;
    }

    /**
     * 内部提报案件给产中
     * @param language 语言别，取值:zh-CN, zh-TW
     * @param userId 当前登录用户userId
     * @param param 案件提交信息
     * @param parentCrmId 父关联crmId，没有传null
     */
    @Override
    public BaseResponse submitCaseToT100(String language, String userId, CasesFormDTO param, String parentCrmId){
        //创建案件信息对象
        Cases caseInfo = new Cases();
        BeanUtils.copyProperties(param,caseInfo);
        caseInfo.setIsPersonalCase("N");
        caseInfo.setSubmitTime(DateUtils.getCurrentTime());
        caseInfo.setSubmitWay(SubmitWay.INNER.getSymbol());
        caseInfo.setSyncStatus(SyncStatus.UnSync.toString());
        caseInfo.setIssueType(IssueType.Issue.toString());

        //1、验证参数
        BaseResponse baseResponse =  validateParam(caseInfo, Role.CHANZHONG.getValue());
        if(baseResponse.getStatus() != ResponseStatus.OK.getCode()){
            return baseResponse;
        }
        //获取反馈人信息
        UserDetailInfo applicantUser;
        try {
            applicantUser = userServiceFeignClient.findUserDetailById(param.getUserId());
        } catch (RuntimeException e){
            log.error("调用userservice获取反馈人信息失败.", e);
            return BaseResponse.error(ResponseStatus.EXPECTATION_FAILED, "获取反馈人信息失败.");
        }
        if(Objects.isNull(applicantUser)){
            return BaseResponse.error(ResponseStatus.EXPECTATION_FAILED, "没有获取到问题反馈人信息");
        }
        SCMCase czCaseInfo = new SCMCase();
        try {
            submitToT100(czCaseInfo, caseInfo, applicantUser, parentCrmId);
        } catch (Exception e){
            log.error(e.getMessage(), e);
            if(StringUtils.isEmpty(czCaseInfo.getCzCaseId())) {
                try{
                    scmPipeline.revoke(caseInfo, czCaseInfo.getCzCaseId(), applicantUser.getWorkno());
                } catch (SCMPipelineException ex) {
                    log.error("撤销抛转产中失败, 产中需求单号:"+czCaseInfo.getCzCaseId(), ex);
                }
            }
            rollbackFailed(caseInfo.getIssueId());
            return BaseResponse.error(ResponseStatus.EXPECTATION_FAILED, e.getMessage());
        }
        //将同步187的信息发送至MQ
        fg187Mq.send(caseInfo.getIssueId(), 1);
//        workDayMq.produceMsg(new IssueKey(caseInfo.getIssueId(), 1,0));
        Runnable runnable = () -> casesProcessUtils.sendToWorkday(caseInfo.getIssueId(),1,0);
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        try {
            executorService.execute(runnable);
        } catch (Exception ex) {
            log.error("sendToWorkday", ex);
        } finally {
            executorService.shutdown();
        }
        log.info("submitCasesToT100 end");
        //处理前端要求的权限相关字段
        casesProcessUtils.returnCasesDuringTransfer(userId, language, caseInfo);
        sendEmail(caseInfo,caseInfo);
        return BaseResponse.ok(caseInfo);
    }

    /**
     * 事务回滚失败，删除垃圾数据
     * @param issueId 案件唯一编号
     */
    private void rollbackFailed(long issueId){
        issuesMapper.deleteRollbackFailedIssue(issueId);
        issuesMapper.deleteRollbackFailedIssueCaseDetail(issueId);
        issuesMapper.deleteRollbackFailedIssueProgress(issueId);
    }

    /**
     * 内部提报案件给产中
     * @param czCaseInfo 抛砖产中返回的案件信息对象, 为了获取产中单号
     * @param caseInfo 案件信息
     * @param parentCrmId 父关联crmId，没有传null
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void submitToT100(SCMCase czCaseInfo, Cases caseInfo, UserDetailInfo submitter, String parentCrmId) throws RuntimeException{
        //获取联系人编号
        caseInfo.setUserContactId(casesProcessUtils.getUserContactId(caseInfo.getUserId(), caseInfo.getUsername(), caseInfo.getEmail(), caseInfo.getPhone()));
        //存储案件部分信息，并获取issueId
        issuesMapper.doSaveIssue(caseInfo);
        //生成、设置并保存服务云单号
        caseInfo.setCrmId(CasesProcessUtils.genCrmId(caseInfo.getIssueId()));
        issuesMapper.saveCrmId(caseInfo.getIssueId(), caseInfo.getCrmId());
        //开单至T产中
        SCMCase t100Case = scmPipeline.send(caseInfo, "", ToT100Flag.TO_T100, submitter.getWorkno());
        BeanUtils.copyProperties(t100Case, czCaseInfo);
        //获取产中处理人员的信息
        UserDetailInfo handler;
        try {
            handler = userServiceFeignClient.getUserDetailByWorkNo(t100Case.getHandlerEmpId());
        } catch (RuntimeException e) {
            log.error("调用userservice出错:", e);
            throw new RuntimeException("获取产中处理人员信息出错.", e);
        }
        if(Objects.isNull(handler)) {
            throw new NullPointerException("没有获取到产中处理人员信息.");
        }

        Cases newCase = toNewCase(caseInfo, handler);
        //存案件的主表
        issueProcessMapper.doUpdateIssue(newCase);
        //主表插入成功后，才会插入明细表及处理历史表
        if(newCase.getIssueId() > 0){
            //存案件明细表
            saveIssueDetail(newCase);
            //保存案件进展
            newCase.setMaxSequenceNum(1);
            CaseHistory caseHistory = getCaseProcess(newCase, submitter, t100Case.getCzCaseId());
            issuesMapper.doSaveIssueProgress(caseHistory);
            //如果是开关联子案件，需要存入案件关联关系
            if(!StringUtils.isEmpty(parentCrmId)){
                issuesMapper.insertSubIssue(parentCrmId, newCase.getCrmId());
            }
            //更新summary表
            casesProcessUtils.saveIssueSummaryV3(caseInfo.getUserId(), caseInfo.getIssueId(), IssueProcessType.TurnToProduct.toString(), caseHistory.getProcessTime());
        }
    }

    /**
     * 获取案件处理信息记录
     * @param newIssue 最新案件信息
     * @param user 当前登陆人信息
     * @param t100ReqId T100型管需求单号
     * @return 返回案件处理详情
     * @throws RuntimeException 调用其他微服务获取人员详情出现异常时，抛出的异常类
     */
    private CaseHistory getCaseProcess(Cases newIssue, UserDetailInfo user, String t100ReqId) throws RuntimeException{
        CaseHistory caseHistory = new CaseHistory();
        caseHistory.setIssueId(newIssue.getIssueId());
        caseHistory.setCrmId(newIssue.getCrmId());
        caseHistory.setSequenceNum(newIssue.getMaxSequenceNum() == 0? 1: newIssue.getMaxSequenceNum());
        caseHistory.setProcessType(newIssue.getProcessType());
        caseHistory.setCurrentStatus(newIssue.getCurrentStatus());
        caseHistory.setProcessor(user.getUserId());
        caseHistory.setProcessorName(newIssue.getServiceName());
        caseHistory.setProcessTime(StringUtils.isEmpty(newIssue.getSubmitTime())?DateUtils.getCurrentTime():newIssue.getSubmitTime());
        //如果是内部员工的话，需要获取工号存到workno
        if(!ObjectUtils.isEmpty(user) && !StringUtils.isEmpty(user.getWorkno())){
            caseHistory.setWorkno(user.getWorkno());
        }else {
            caseHistory.setWorkno(null);
        }
        caseHistory.setDescription(newIssue.getHandlerDetail());
        caseHistory.setHandlerId(newIssue.getServiceId());
        if(StringUtils.isEmpty(newIssue.getHandlerDetail())){
            caseHistory.setSyncStatus(SyncStatus.DontNeedSync.toString());
        }else{
            caseHistory.setSyncStatus(SyncStatus.EditUnSync.toString());
        }
        caseHistory.setProcessHours(0.0);
        caseHistory.setT100CaseId(StringUtils.isEmpty(t100ReqId)? null : t100ReqId);
        return caseHistory;
    }

    /**
     * 提交个案
     * @param language 语言别，取值:zh-CN, zh-TW
     * @param cases 案件提交信息
     * @param parentCrmId 父关联crmId，没有传null
     */
    @Deprecated
    @Override
    public BaseResponse submitCaseToPersonal(String language, CasesFormDTO cases, String parentCrmId)  {
        BaseResponse response = submitCustomizedDemand(language, cases, parentCrmId);
        if(response.getStatus() != ResponseStatus.OK.getCode()){
            return response;
        }
        Cases newCases = (Cases)response.getResponse();
        fg187Mq.send(newCases.getIssueId(), newCases.getMaxSequenceNum());
//        workDayMq.produceMsg(new IssueKey(newCases.getIssueId(), newCases.getMaxSequenceNum(),0));
        Runnable runnable = () -> casesProcessUtils.sendToWorkday(newCases.getIssueId(),newCases.getMaxSequenceNum(),0);
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        try {
            executorService.execute(runnable);
        } catch (Exception ex) {
            log.error("sendToWorkday", ex);
        } finally {
            executorService.shutdown();
        }
        return response;
    }

    /**
     * 提交个案
     * @param language 语言别，取值:zh-CN, zh-TW
     * @param param 案件提交信息
     * @param parentCrmId 父关联crmId，没有传null
     */
    @Deprecated
    @Transactional(propagation = Propagation.REQUIRED, rollbackForClassName = "Exception")
    public BaseResponse submitCustomizedDemand(String language, CasesFormDTO param, String parentCrmId){
        log.info("submitCasesToPersonal start");
        //新cases储备
        Cases target = new Cases();
        BeanUtils.copyProperties(param,target);

        target.setCurrentStatus(CaseStatus.PERSONAL_CASE.getStatus());

        target.setIsPersonalCase("Y");
        target.setTurnToT100("N");

        target.setUserContactId(casesProcessUtils.getUserContactId(param.getUserId(), param.getUsername(), param.getEmail(), param.getPhone()));
        BaseResponse response = submitCases(language, target,Role.JIAOFU.getValue(), parentCrmId);

        List<UserDetailInfo> mailUsers = new ArrayList<>();
        CasesEmail casesEmail = new CasesEmail();
        BeanUtils.copyProperties(target,casesEmail);
        casesEmail.setEmergency(target.isEmergency()?"Y":"N");
        //发送个案邮件
        //抄送邮件组准备
        HashSet<String> ccMailSet = new HashSet<>();
        if(!StringUtils.isEmpty(target.getEmail())) {
            ccMailSet.add(target.getEmail());
        }
        UserDetailInfo submitIdDetail = userServiceFeignClient.findUserDetailById(param.getSubmitedId());
        Optional.ofNullable(submitIdDetail).ifPresent(mailUsers::add);
        if(submitIdDetail != null && !StringUtils.isEmpty(submitIdDetail.getEmail())) {
            ccMailSet.add(submitIdDetail.getEmail());
        }
        UserDetailInfo processUserDetail = userServiceFeignClient.findUserDetailByMaxAccount(casesEmail.getAccount());
        Optional.ofNullable(processUserDetail).ifPresent(mailUsers::add);

        ccMailSet.addAll(casesProcessUtils.getCcManagerEmailList(mailUsers));

        if(processUserDetail != null && !StringUtils.isEmpty(processUserDetail.getEmail())) {
            ccMailSet.remove(processUserDetail.getEmail());
            List<String> ccMailList = new ArrayList<>(ccMailSet);
            mailService.SendCasesMail(casesEmail, Collections.singletonList(processUserDetail.getEmail()), ccMailList, MailSendSituation.OPEN_PERSONAL_CASE, processUserDetail.getLanguage());
        } else {
            log.warn(CasesProcessUtils.constructSendMailSkipedLogMessage("案件处理人", target.getIssueId(), target.getMaxSequenceNum()));
        }
        log.info("submitCasesToPersonal end");
        return response;
    }

    /**
     * 客户提报案件
     * @param cases
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    @Override
    public BaseResponse submitCaseByCustomer(String language, String userId, Cases cases) {
        BaseResponse response = submitByCustomer(language, userId, cases);
        if(response.getStatus() != ResponseStatus.OK.getCode()){
            return response;
        }
        Cases newCases = (Cases)response.getResponse();
        fg187Mq.send(newCases.getIssueId(), newCases.getMaxSequenceNum());
//        workDayMq.produceMsg(new IssueKey(newCases.getIssueId(), newCases.getMaxSequenceNum(),cases.getWorkHours()));
        Runnable runnable = () -> casesProcessUtils.sendToWorkday(newCases.getIssueId(),newCases.getMaxSequenceNum(),newCases.getWorkHours());
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        try {
            executorService.execute(runnable);
        } catch (Exception ex) {
            log.error("sendToWorkday", ex);
        } finally {
            executorService.shutdown();
        }
        return response;
    }

    /**
     * 交付补录案件
     * @param cases
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    @Override
    public BaseResponse submitClosedCasesByJiaofu(String language, String userId, Cases cases) {
        BaseResponse response = submitClosedCases(language, userId, cases);
        if(response.getStatus() != ResponseStatus.OK.getCode()){
            return response;
        }
        Cases newCases = (Cases)response.getResponse();
        fg187Mq.send(newCases.getIssueId(), newCases.getMaxSequenceNum());
//        workDayMq.produceMsg(new IssueKey(newCases.getIssueId(), newCases.getMaxSequenceNum(),cases.getWorkHours()));
        Runnable runnable = () -> casesProcessUtils.sendToWorkday(newCases.getIssueId(),newCases.getMaxSequenceNum(),cases.getWorkHours());
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        try {
            executorService.execute(runnable);
        } catch (Exception ex) {
            log.error("sendToWorkday", ex);
        } finally {
            executorService.shutdown();
        }
        return response;
    }

    @Override
    public void submitByCustomerSuccessNotify(String language, String userId, long issueId) throws RuntimeException{
        Cases caseInfo = issueProcessMapper.getCaseByIssueId(issueId);
        CustomerDetailInfo customer = userServiceFeignClient.getCustomerById(caseInfo.getServiceCode());
        //设置客户名称
        caseInfo.setCustName(customer.getCustomerName());
        caseInfo.setCustomerCcMails(casesProcessUtils.getCustomerCcEmails(issueId));
        //6、查询客户家管理员
        casesProcessUtils.searchClientAdmin(caseInfo,userId,caseInfo.getServiceCode());

        //7、 查询客户窗口人员列表
        ServiceStaffCond cond = new ServiceStaffCond();
        cond.setCustomerServiceCode(caseInfo.getServiceCode());
        cond.setProductCode(caseInfo.getProductCode());
        cond.setErpSystemCode(caseInfo.getErpSystemCode());
        //设置领域编号
        dealArea(caseInfo, caseInfo.getProductCode());
        cond.setArea(caseInfo.getArea());

        UserDetailInfo windowUser = userServiceFeignClient.findUserDetailById(caseInfo.getServiceId());
        List<String> windowStaffs = new ArrayList<>();

        System.out.println(">>>>>====getServiceRegion"+caseInfo.getServiceRegion());
        System.out.println(">>>>>====getSubmitWay"+caseInfo.getSubmitWay());
        if (caseInfo.getServiceRegion().equals("TW") &&caseInfo.getSubmitWay().equals(SubmitWay.AZZI932.getSymbol())){
            //需要抄送当前处理人
            System.out.println(">>>>>====抄送当前处理人");
            windowStaffs.add(windowUser.getWorkno());

        }else{
            windowStaffs = userServiceFeignClient.getServiceStaff(cond);
        }

        System.out.println(">>>>>====提报邮件"+"windowStaffs"+windowStaffs);

        sendEmailByCustomer(caseInfo, windowUser, windowStaffs);
    }

    /**
     *
     */
    private void checkCustomerCcMailes(List<String> customerCcmails) throws IllegalArgumentException{
        int length=0;
        int index=0;
        for(String email : customerCcmails){
            //检查邮箱地址是否合法
            if(!StringUtil.checkEmail(email)){
                throw new IllegalArgumentException("不合法的邮箱地址:"+email);
            }
            if(email != null) {
                length += email.length();
            }
            index++;
        }
        if(length > (CUSTOMER_CC_MAIL_MAX_LENGTH - (EMAIL_SEP.length()*index))){
            throw new IllegalArgumentException("抄送邮箱列表超过最大长度"+CUSTOMER_CC_MAIL_MAX_LENGTH);
        }
    }

    /**
     * 用分隔符拼接邮箱地址为一个字符串，以便存到数据库, 如果列表为空，返回null
     */
    private String joinCcEmailAddresses(List<String> emailAddresses){
        if(CollectionUtils.isEmpty(emailAddresses)){
            return null;
        }
        return String.join(EMAIL_SEP, emailAddresses);
    }

    public BaseResponse submitByCustomer(String language, String userId, Cases cases) throws RuntimeException {
        log.info("submitCaseByCustomer start");
        if(!CollectionUtils.isEmpty(cases.getCustomerCcMails())) {
            try {
                checkCustomerCcMailes(cases.getCustomerCcMails());
            } catch (IllegalArgumentException e) {
                return BaseResponse.error(ResponseStatus.WRONG_PARAMETER, e.getMessage());
            }
        }
        //新cases储备
        Cases target = new Cases();
        BeanUtils.copyProperties(cases,target);
        target.setCustomerCcMailString(joinCcEmailAddresses(cases.getCustomerCcMails()));
        target.setUserId(userId);
        target.setSubmitedId(userId);
        //1、验证参数
        BaseResponse baseResponse =  validateParam(target, Role.CLIENTY.getValue());

        if(baseResponse.getStatus() != ResponseStatus.OK.getCode()){
            return baseResponse;
        }

        //2、根据提报案件的人查找用户属于哪个客户家
        CustomerDetailInfo customer = userServiceFeignClient.getCustomerByUserId(userId);
        if(ObjectUtils.isEmpty(customer)){
            return BaseResponse.error(ResponseStatus.CUSTOMER_VERIFY, "请联系服务您的鼎捷人员.");
        }
        //设置客户名称与提报人名称
        target.setCustName(customer.getCustomerName());
        target.setServiceCode(customer.getCustomerServiceCode());

        target.setUserContactId(casesProcessUtils.getUserContactId(userId, cases.getUsername(), cases.getEmail(), cases.getPhone()));

        //6、查询客户家管理员 为了发送邮件获取客户管理员邮箱，逻辑移到客户提交案件发送邮件的接口
        //casesProcessUtils.searchClientAdmin(target,userId,target.getServiceCode());



        //4、处理案件的其他参数，比如把环境、企业编号、营运据点拼接到问题描述之前，从后往前拼；area等
        //dealOtherParam(language, target);

        //领域查询（为了发邮件找到对应领域的实施顾问）
        dealArea(cases, cases.getProductCode());

        log.info("客户提单dealArea"+target.getArea()+"cases"+cases+"target"+target);

        //7、 查询客户窗口人员列表
        ServiceStaffCond cond = new ServiceStaffCond();
        cond.setCustomerServiceCode(customer.getCustomerServiceCode());
        cond.setProductCode(cases.getProductCode());
        cond.setErpSystemCode(target.getErpSystemCode());
        cond.setArea(cases.getArea());

        List<String> windowStaffs = userServiceFeignClient.getServiceStaff(cond);

        if (CollectionUtils.isEmpty(windowStaffs))
        {
            windowStaffs = issueProcessMapper.getDefaultProcess(cond.getProductCode());
            if (CollectionUtils.isEmpty(windowStaffs))
            {
                return BaseResponse.error(ResponseStatus.WINDOW_VERIFY, "请联系服务您的鼎捷人员.");
            }
        }

        //取窗口人员
        String windowWorkno = windowStaffs.get(0);
        UserDetailInfo windowUser;
        try{
            windowUser = userServiceFeignClient.getUserDetailByWorkNo(windowWorkno);
            if(windowUser == null){
                return BaseResponse.error(ResponseStatus.EXPECTATION_FAILED, "没有获取到窗口人员的详细信息, 请联系服务您的鼎捷人员.");
            }
        } catch (RuntimeException e) {
            return BaseResponse.error(ResponseStatus.INTERNAL_SERVER_ERROR, "调用userservice获取问题处理窗口人员信息出错, 请联系管理员.");
        }
        if (windowUser.getJiaofuType() == JiaoFuUserType.JIAOFUFUWU.getValue()) {
            target.setCurrentStatus(CaseStatus.JIAOFU_SERVICE_HANDLING.getStatus());
        }else{
            target.setCurrentStatus(CaseStatus.JIAOFU_CONSULTANT_HANDLING.getStatus());
        }
        target.setServiceId(windowUser.getUserId());
        target.setServiceDepartment(windowUser.getDepartmentCode());
        //取第一窗口人来作为主要负责人
        target.setMainCharge(windowUser.getUserId());

        //8、存案件主表
        long issueId = saveIssue(target);


        //主表插入成功后，才会更新细表及进展
        if(issueId > 0){
            //9、存案件明细表
            target.setIsPersonalCase("N");
            target.setTurnToT100("N");
            saveIssueDetail(target);

            //10、保存案件进展
            target.setMaxSequenceNum(1);
            target.setProcessType(CaseProcessType.OPEN.getProcessType());
            saveIssueProgress(target);

            log.info("submitCaseByCustomer success end ");
            return BaseResponse.ok(target);
        }
        log.info("submitCaseByCustomer failed end ");
        return BaseResponse.error(ResponseStatus.INSERT_FAILD, "请联系管理员.");
    }

    public BaseResponse submitClosedCases(String language, String userId, Cases cases) throws RuntimeException {
        log.info("submitClosedCases start");
        //新cases储备
        Cases target = new Cases();
        BeanUtils.copyProperties(cases,target);
        target.setSubmitedId(userId);
        //1、验证参数
        BaseResponse baseResponse =  validateParam(target, Role.JIAOFU.getValue());

        if(baseResponse.getStatus() != ResponseStatus.OK.getCode()){
            return baseResponse;
        }

        //2、根据客服代号查找用户属于哪个客户家
        CustomerDetailInfo customer = userServiceFeignClient.getCustomerById(cases.getServiceCode());

        if(ObjectUtils.isEmpty(customer)){
            return BaseResponse.error(ResponseStatus.CUSTOMER_VERIFY, "请联系服务您的鼎捷人员.");
        }

        //反馈人不在这家客户已有的联系人中
        if (StringUtils.isEmpty(target.getUserId())){
            //查出默认客户账号的ID
            UserDetailInfo submitIdDetail = userServiceFeignClient.findUserDetailByCustUsername(target.getServiceCode());
            log.info("默认客户账号的ID" + submitIdDetail.getUserId());
            target.setUserId(submitIdDetail.getUserId());
        }

        //设置客户名称与提报人名称
        target.setCustName(customer.getCustomerName());
        target.setServiceCode(customer.getCustomerServiceCode());

        target.setUserContactId(casesProcessUtils.getUserContactId(target.getUserId(), cases.getUsername(), cases.getEmail(), cases.getPhone()));

        //4、处理案件的其他参数，比如把环境、企业编号、营运据点拼接到问题描述之前，从后往前拼；area等
        //dealOtherParam(language, target);

        //案件状态改为结案
        target.setCurrentStatus(CaseStatus.CLOSED.getStatus());
        //8、存案件主表
        long issueId = saveIssue(target);

        //主表插入成功后，才会更新明细表及进展
        if(issueId > 0){
            log.info("主表插入成功" + issueId);
            if (!StringUtils.isEmpty(cases.getHandlerDetail())){
                //更新处理意见
                Cases caseInfo = new Cases();
                caseInfo.setIssueId(issueId);
                caseInfo.setHandlerDetail(cases.getHandlerDetail());
                issueProcessMapper.doUpdateIssue(caseInfo);
            }

            //9、存案件明细表
            target.setTotalWorkHours(cases.getWorkHours());
            target.setIsPersonalCase("N");
            target.setTurnToT100("N");
            saveIssueDetail(target);

            //10、保存案件进展
            UserDetailInfo userDetailInfo = userServiceFeignClient.findUserDetailById(userId);
            String processStatus;
            if (userDetailInfo.getJiaofuType() == JiaoFuUserType.JIAOFUGUWEN.getValue()) {
                processStatus = CaseStatus.JIAOFU_CONSULTANT_HANDLING.getStatus();
            } else {
                processStatus = CaseStatus.JIAOFU_SERVICE_HANDLING.getStatus();
            }

            //a 开立--》交付处理中
            target.setMaxSequenceNum(1);
            target.setProcessType(CaseProcessType.OPEN.getProcessType());
            target.setCurrentStatus(processStatus);
            target.setHandlerDetail(null);
            saveIssueProgress(target);
            log.info("历程表开立插入成功" + target);

            //Cases oldCase = new Cases();
            //BeanUtils.copyProperties(cases, oldCase);
            //b 交付结案
            //问题提报人
            target.setMaxSequenceNum(2);
            UserDetailInfo applicantInfo = userServiceFeignClient.findUserDetailById(target.getUserId());
            target.setProcessType(CaseProcessType.JIAOFU_CLOSED.getProcessType());
            target.setCurrentStatus(CaseStatus.CLIENT_VERIFICATION.getStatus());

            //清空处理工时
            target.setWorkHours(0.0);

            Cases newCases = new Cases();
            BeanUtils.copyProperties(target,newCases);
            //调换交付结案的人与反馈人，便于存历程表
            newCases.setUserId(userId);
            newCases.setServiceId(target.getUserId());
            //设置处理意见
            newCases.setHandlerDetail(cases.getHandlerDetail());

            saveIssueProgress(newCases);
            log.info("交付结案插入成功" + newCases);

            //处理完后当前sequenceNum+1
            target.setMaxSequenceNum(3);
            //抛转187
            fg187Mq.send(target.getIssueId(), target.getMaxSequenceNum());
            workDayMq.produceMsg(new IssueKey(target.getIssueId(), target.getMaxSequenceNum(),target.getWorkHours()));
            //c 反馈人结案
            target.setProcessType(CaseProcessType.APPLICANT_END.getProcessType());
            target.setCurrentStatus(CaseStatus.CLOSED.getStatus());

            if (applicantInfo != null) {
                cases.setUserId(applicantInfo.getUserId());
            }
            target.setServiceId(null);//结案时下一步处理人为空
            target.setServiceDepartment(null);
            saveIssueProgress(target);
            log.info("反馈人结案插入成功" + target);
            //抛转187
            fg187Mq.send(target.getIssueId(), target.getMaxSequenceNum());
            workDayMq.produceMsg(new IssueKey(target.getIssueId(), target.getMaxSequenceNum(),0));
            log.info("submitClosedCases success end ");
            return BaseResponse.ok(target);
        }
        log.info("submitClosedCases failed end ");
        return BaseResponse.error(ResponseStatus.INSERT_FAILD, "请联系管理员.");
    }

    /**
     * 发送邮件：主送窗口人员，抄送窗口人员的所有上级主管
     * @param cases 案件信息
     * @param handler 案件处理人信息
     * @param windowStaffs 窗口人员工号列表
     */
    private void sendEmailByCustomer(Cases cases, UserDetailInfo handler, List<String> windowStaffs){
        List<UserDetailInfo> mailUsers = new ArrayList<>();
        CasesEmail casesEmail = new CasesEmail();
        BeanUtils.copyProperties(cases,casesEmail);
        casesEmail.setEmergency(cases.isEmergency()?"Y":"N");

        //客户提报案件如果客户有上传附件，则根据附件
        try {
            List<String> filenames = attachementService.getFileNameListByIssueId(Long.toString(cases.getIssueId()));
            if(!CollectionUtils.isEmpty(filenames)) {
                casesEmail.setHasAttachement(true);
            }
        } catch (Exception e){
            log.error("获取案件附件列表失败,issueId:{}.", cases.getIssueId(), e);
        }

        if(handler !=null){
            casesEmail.setServiceName(handler.getName());
            casesEmail.setServiceEmail(handler.getEmail());
            casesEmail.setServicePhone(handler.getPhone());
        }
        //抄送邮件组准备
        HashSet<String> ccMailSet = new HashSet<>();
        ccMailSet.addAll(cases.getCustomerCcMails());
        if(!StringUtils.isEmpty(casesEmail.getEmail())){
            //反馈人邮箱
            ccMailSet.add(casesEmail.getEmail());
        }
        UserDetailInfo submitIdDetail = userServiceFeignClient.findUserDetailById(casesEmail.getSubmitedId());
        Optional.ofNullable(submitIdDetail).ifPresent(mailUsers::add);
        if(submitIdDetail != null && !StringUtils.isEmpty(submitIdDetail.getEmail())){
            ccMailSet.add(submitIdDetail.getEmail());
        }

        Optional.ofNullable(handler).ifPresent(mailUsers::add);
        //是否抄送主管
        ccMailSet.addAll(casesProcessUtils.getCcManagerEmailList(mailUsers));

        if (cases.getServiceRegion().equals("TW") && cases.getSubmitWay().equals(SubmitWay.AZZI932.getSymbol())){
            //932反馈的台湾案件抄送给CRM设置的群组，即mars_customerservice中的service_cc_staff_emails
            String ccMails  = userServiceFeignClient.getCCMailsByCust(cases.getServiceCode(),cases.getProductCode());
            System.out.println(">>>>>====ccMails"+ccMails);

            if (!StringUtils.isEmpty(ccMails)){
                //抄送邮件字符串转为数组
                String a []  = ccMails.split(";");
                for(int i=0;i<a.length;i++){
                    ccMailSet.add(a[i]);
                }
                System.out.println(">>>>>====提报邮件TW"+"ccMailSet添加后"+ccMailSet);
            }
        }

        /*Map<String,List<String>> map = accountListToDetailList(casesEmail.getAccount());
        List<String> userIdList = map.get("userIdList");
        try {
            List<String> managerEmailList = userServiceFeignClient.getManagerEmailListByUserId(userIdList);
            if (!CollectionUtils.isEmpty(managerEmailList)) {
                ccMailSet.addAll(managerEmailList);
            }
        } catch (Exception e){
            log.info(e.getMessage());
        }*/
        //是否抄送统筹人
        if ( !StringUtils.isEmpty(cases.getServiceCode()) && !StringUtils.isEmpty(cases.getProductCode())){
            ccMailSet.addAll(casesProcessUtils.getCoordinatorEmailList(cases.getServiceCode(),cases.getProductCode()));
        }
        if(handler != null && !StringUtils.isEmpty(handler.getEmail())) {
            //抄送小组长
            ccMailSet.addAll(casesProcessUtils.getVirtualTeamLeadersEmail(handler.getUserId()));
            List<String> windowStaffsEmail = new ArrayList<>();
            for(String workno : windowStaffs){
                UserDetailInfo windowStaffInfo = userServiceFeignClient.getUserDetailByWorkNo(workno);
                if(windowStaffInfo != null && !StringUtils.isEmpty(windowStaffInfo.getEmail())){
                    windowStaffsEmail.add(windowStaffInfo.getEmail());
                    ccMailSet.remove(windowStaffInfo.getEmail());
                }
            }
            List<String> ccMailList = new ArrayList<>(ccMailSet);
            log.info("发送邮件sendEmailByCustomer");
            log.info("{}--------{}--------{}", new Gson().toJson(windowStaffsEmail), new Gson().toJson(ccMailList), new Gson().toJson(cases));
            mailService.SendCasesMail(casesEmail, windowStaffsEmail, ccMailList, JIAOFU_ACCEPT_CASE, handler.getLanguage());
        } else {
            log.warn(CasesProcessUtils.constructSendMailSkipedLogMessage("案件处理人", cases.getIssueId(), cases.getMaxSequenceNum()));
        }
    }

    /**
     * 上传附件
     * @param file
     * @return
     */
    @Override
    public BaseResponse upload(long issueId,MultipartFile file) throws Exception {
        if (file == null) {
            return BaseResponse.error(ResponseStatus.FILE_IS_NULL);
        }
        String name = file.getOriginalFilename();
        log.info("++++++++++++++++++++++"+name);
        //因为可能会有windows完整路径上传上来，所以将filename做个转换
        String endFilename = name.replaceAll("\\\\", "").replace(":", "").replace("%", "");

        //校验文件扩展名是否符合规范，不可上傳這些副檔名.js、.jar、.bat、.cpl、.scr、.com、.pif、.vbs、.exe、.cmd、.reg、.lnk、.hta
        if(DgwFileUtils.checkFileName(endFilename.substring(endFilename.lastIndexOf(".") + 1))) {
            return BaseResponse.error(ResponseStatus.FILE_TYPE_CHECK_FAIL);
        }
        Cases cases= new Cases();
        cases.setIssueId(issueId);
        List<IssueAttachment> issueAttachments = new ArrayList<>();
        IssueAttachment issueAttachment = new IssueAttachment();
        issueAttachment.setAttachment(file.getBytes());
        //赋值为前端回传附件的MimeType
        issueAttachment.setFileType(file.getContentType());
        issueAttachment.setFileName(endFilename);
        issueAttachments.add(issueAttachment);
        cases.setIssueAttachments(issueAttachments);

        return upload(cases);
    }

    @Override
    public BaseResponse uploadFile(MultipartFile file) {
        String name = file.getOriginalFilename();
        if (StringUtils.isEmpty(name)) {
            name = UUID.randomUUID().toString()+".png";
        }
        log.info("文件名称:"+name);
        //因为可能会有windows完整路径上传上来，所以将filename做个转换
        try {
            String filename = name.replaceAll("\\\\", "").replace(":", "").replace("%", "");
            //校验文件扩展名是否符合规范，不可上傳這些副檔名.js、.jar、.bat、.cpl、.scr、.com、.pif、.vbs、.exe、.cmd、.reg、.lnk、.hta
            if(DgwFileUtils.checkFileName(filename.substring(filename.lastIndexOf(".") + 1))) {
                return BaseResponse.error(ResponseStatus.FILE_TYPE_CHECK_FAIL);
            }
            byte[] bytes = file.getBytes();
            log.info("上传文件大小："+bytes.length);
            String imgUrl = dmcRest.shareUploudFile(filename, bytes);
            return BaseResponse.ok(imgUrl);
        } catch (IOException e) {
            return BaseResponse.error(ResponseStatus.INTERNAL_SERVER_ERROR,e.getMessage());
        }
    }

    /**
     * 刷T生产上的附件
     * @param
     * @return
     */
    @Deprecated
    @Override
    public void uploadTOldFiles() throws Exception {
        int perCount = 400;
        //得到有附件的案件
        List<Cases>  cases = issuesMapper.findTOldFiles();
        if(!CollectionUtils.isEmpty(cases)){
            DBObject metaData = new BasicDBObject();
            InputStream inStream = null;
            HttpURLConnection conn = null;
            for(int j =0;j<cases.size();j++){
                Cases tempcase= cases.get(j);
                long issueId = tempcase.getIssueId();

                // 校验是否存在附加
                List<String> existFileList = gridFsTemplate.find(new Query(Criteria.where("metadata.issueId").is(issueId))).stream().map(x -> x.getFilename()).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(existFileList)) {
                    log.warn("issueId:{}附件已存在:{}",issueId,existFileList.toString());
                    continue;
                }

                String filesPath = tempcase.getIssueClassification();
                if(!StringUtils.isEmpty(filesPath)) {
                    String[] fileArr = filesPath.split("丨:丨:丨");
                    if(fileArr!=null && fileArr.length>0){
                        for (int i =0;i<fileArr.length;i++) {
                            try{
                                String reomteUrl = fileArr[i];
                                URL url = new URL("http://180.167.0.42:8000/"+reomteUrl);
                                //创建连接
                                conn=(HttpURLConnection) url.openConnection();
                                inStream=conn.getInputStream();
                                String fileType = reomteUrl.substring(fileArr[i].lastIndexOf("."));
                                String fileName = reomteUrl.substring(fileArr[i].lastIndexOf("/") + 1, fileArr[i].lastIndexOf("."));
                                fileName = "issue__" + issueId + "__" + System.currentTimeMillis() + "__" + fileName + fileType ;
                                log.info(fileName);
                                metaData.put("issueId", issueId);
                                gridFsTemplate.store(inStream, fileName, fileType, metaData);
                            }catch (Exception e){
                                log.error("附件保存报错:{}",e.getMessage());
                            } finally {
                                try {
                                    inStream.close();
                                    conn.disconnect();
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            }
                        }
                    }
                }
                if ((j+1) % perCount == 0) {
                    System.gc();
                }
            }
        }
    }

    public InputStream getRemoteFileInputStream(String remoteFilePath) {
        InputStream inStream = null;
        HttpURLConnection conn = null;
        try {
            URL url = new URL(remoteFilePath);
            //创建连接
            conn=(HttpURLConnection) url.openConnection();
            inStream=conn.getInputStream();
            conn.disconnect();
        } catch (Exception e) {
            e.printStackTrace();
        }
        finally {
            try {
                if(conn != null){ //huly: 修复漏洞/bug 增加非空判断
                    conn.disconnect();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return inStream;
    }


    public byte[] getRemoteFileByte(String remoteFilePath) {
        InputStream inStream = null;
        byte[] result = null;
        try {
            URL url = new URL(remoteFilePath);
            //创建连接
            HttpURLConnection conn=(HttpURLConnection) url.openConnection();
            inStream=conn.getInputStream();
            int count=conn.getContentLength();//获取远程资源长度
            result=new byte[count];
            int readCount=0;
            while(readCount<count){//循环读取数据
                readCount+=inStream.read(result,readCount,count-readCount);
            }
            conn.disconnect();
            inStream.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        finally {
            try {
                if(inStream != null){ //huly: 修复漏洞/bug 增加非空判断
                    inStream.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
//                return null; //huly: 修复漏洞/bug 注释语句
            }
        }
        return result;
    }

    public BaseResponse upload(Cases cases)  {
        BaseResponse baseResponse = new BaseResponse();
        try {
            StringBuilder sbLog = new StringBuilder();
            if (!CollectionUtils.isEmpty(cases.getIssueAttachments())) {
                StringBuilder sbLog2 = new StringBuilder();
                for (IssueAttachment att : cases.getIssueAttachments()) {
                    //DgwFileUtils.writeToFile(att.getAttachment(),attachmentfilePathPrefix, "issue__" + issue.getIssueId()+ "_" + Integer.toString(att.getSequeceNum()) + att.getFileType());
                    int tryCount = 3;
                    sbLog.setLength(0);
                    GridFSFile gridFSFile = null;
                    Object id = null;
                    long chunkSize = -1;
                    String fileType = att.getFileType();
                    String fileName = "issue__" + cases.getIssueId() + "__" + new Date().getTime() + "__" + att.getFileName();
                    sbLog.append("issueId:");
                    sbLog.append(Long.toString(cases.getIssueId()));
                    sbLog.append(" fileName:");
                    sbLog.append(" do save attachment fileName:");
                    sbLog.append(fileName);
                    sbLog.append(" fileType:");
                    sbLog.append(fileType);
                    do {
                        sbLog2.setLength(0);
                        try {
                            InputStream inputStream = new ByteArrayInputStream(att.getAttachment());
                            DBObject metaData = new BasicDBObject();
                            metaData.put("issueId", cases.getIssueId());
                            gridFSFile = gridFsTemplate.store(inputStream, fileName, fileType, metaData);
                        } catch (Exception ex) {
                            log.error(ex.toString());
                        }
                        if(gridFSFile != null){
                            sbLog2.append(" chunkSize:");
                            sbLog2.append((chunkSize = gridFSFile.getChunkSize()));
                            sbLog2.append(" length:");
                            sbLog2.append(gridFSFile.getLength());
                            sbLog2.append(" fileId:");
                            sbLog2.append((id = gridFSFile.getId()));

                            //存到mysql
                            IssueAttachmentFileV3 issueAttachmentFileV3 = new IssueAttachmentFileV3();
                            issueAttachmentFileV3.setIssueId(cases.getIssueId());
                            issueAttachmentFileV3.setProgressId(0L);
                            issueAttachmentFileV3.setFileId(gridFSFile.getId().toString());
                            issueAttachmentFileV3.setFileName(fileName);
                            issueAttachmentFileV3.setFileType(fileName.substring(fileName.lastIndexOf(".")));
                            issueAttachmentFileV3.setUrl(issueAttachAddress + "/issueservicesyncapi/api/issues/attach/" + issueAttachmentFileV3.getFileId());

                            issueDao.saveAttachmentFile(issueAttachmentFileV3);
                        }
                        sbLog2.append(" tryCount:");
                        sbLog2.append(Integer.toString(tryCount));
                        log.info(sbLog.toString() + sbLog2.toString() + "\n");
                        tryCount--;
                        //gridFsTemplate.findOne(Query.query(Criteria.where("_id").is(fileId))))
                    } while ((gridFSFile == null || id == null || chunkSize == 0) && tryCount > 0);
                    if(tryCount < 0){
                        log.info("Save attachment fail!");
                        return baseResponse.error(ResponseStatus.UPLOAD_FAILD);
                    }
                }
            } else {
                sbLog.append("issueId:");
                sbLog.append(Long.toString(cases.getIssueId()));
                sbLog.append(" not attachment!");
                sbLog.append("\n");
                log.info(sbLog.toString());
            }
        } catch (Exception ex) {
            log.error(ex.toString());
            return baseResponse.error(ResponseStatus.UPLOAD_FAILD);
        }
        return baseResponse.ok();
    }
    /**
     * 领域查询（为了发邮件找到对应领域的实施顾问）
     * @param target
     * @param productCode
     * @return
     */
    private void dealArea(Cases target,String productCode) {
        if (!StringUtils.isEmpty(target.getErpSystemCode())) {

            Module module = issuesMapper.getModuleByModuleCode(productCode, target.getErpSystemCode().toUpperCase());
            if (!ObjectUtils.isEmpty(module)) {
                target.setArea(module.getArea());
                target.setErpSystemCode(module.getErpSystemCode());
            }else{
                target.setArea("");
                target.setErpSystemCode(target.getErpSystemCode());
            }
        }
    }
    /**
     * 根据用户类型对案件的字段校验
     * @param role
     * @param cases
     * @return
     */
    private Boolean validateCaseFormByType(CasesFormDTO cases,String role)  {
        if(Role.JIAOFU.getValue().equals(role)){//内部用户提交案件，需要校验的参数  //huly: 修复漏洞/bug  Role.JIAOFU 改成 Role.JIAOFU.getValue()
            if (!validateCaseForm(cases) || StringUtils.isEmpty(cases.getServiceCode()) || StringUtils.isEmpty(cases.getProductCode())  || CollectionUtils.isEmpty(cases.getAccount())) {
                return false;
            }
            return true;
        }else {   //外部用户提交案件，需要校验的参数
            return validateCaseForm(cases);
        }
    }

    /**
     * 外部用户的案件校验
     * @param cases
     * @return
     */
    private Boolean validateCaseForm(CasesFormDTO cases)  {
        if (ObjectUtils.isEmpty(cases) || StringUtils.isEmpty(cases.getQuestionTitle()) || StringUtils.isEmpty(cases.getProductCode()) || StringUtils.isEmpty(cases.getIssueDescription())) {
            return false;
        }
        return true;
    }

    @Deprecated
    @Override
    public void uploadTRemark() throws Exception {
        List<Cases> issues = issuesMapper.selectAllIssueRemark();
        if(!CollectionUtils.isEmpty(issues)){
            for (int j = 0; j < issues.size(); j++){
                log.info("++++++++++++++++++++++++++++"+issuesMapper.doUpdateRemark(issues.get(j)));
            }
        }
    }

    @Deprecated
    @Override
    public void uploadTOldImage() throws Exception {
        List<Issue> issues = tIssueMapper.selectAllDescription();
        int perCount = 100;
        for (int j = 0; j < issues.size(); j++) {
            Issue issue = issues.get(j);
            log.info("案件ID："+issue.getIssueId());
            String issueDescription = issue.getIssueDescription();
            if (!StringUtils.isEmpty(issueDescription) && issueDescription.contains("tcloud/")) {
                List<String> oldImageUrl = getImageUrl(issueDescription);
                log.info("旧的文件地址："+new Gson().toJson(oldImageUrl));
                List<String> newImageUrl = switchImageUrl(oldImageUrl);
                log.info("新的文件地址："+new Gson().toJson(newImageUrl));
                for (int i = 0; i < oldImageUrl.size(); i++) {
                    String imgUrl = oldImageUrl.get(i);
                    issueDescription = issueDescription.replace(imgUrl,newImageUrl.get(i));
                }
                issue.setIssueDescription(issueDescription);
                tIssueMapper.updateDescription(issue);
            }
            if ((j+1) % perCount == 0) {
                System.gc();
            }
        }

        List<Map<String, Object>> issueProcessList = issueProcessMapper.selectAllDescription();
        for (Map<String, Object> map : issueProcessList) {
            String id = map.get("Id").toString();
            String description = map.get("Description").toString();
            log.info("案件进展ID："+id);
            if (!StringUtils.isEmpty(description) && description.contains("tcloud/")) {
                List<String> oldImageUrl = getImageUrl(description);
                log.info("旧的文件地址："+new Gson().toJson(oldImageUrl));
                List<String> newImageUrl = switchImageUrl(oldImageUrl);
                log.info("新的文件地址："+new Gson().toJson(newImageUrl));
                for (int i = 0; i < oldImageUrl.size(); i++) {
                    String imgUrl = oldImageUrl.get(i);
                    description = description.replace(imgUrl,newImageUrl.get(i));
                }
                map.put("Description",description);
                issueProcessMapper.updateDescription(map);
            }
        }

    }

    public List<String> getImageUrl(String url){
        String[] split = url.split("src=\"");
        List<String> imageUrlList = new ArrayList<>();
        if (split.length > 1) {
            for (int i = 1; i < split.length; i++) {
                String sub = split[i];
                int nextIndex = sub.indexOf("\"");
                // 图片的url
                String imgUrl = sub.substring(0, nextIndex);
                log.info("图片的url"+imgUrl);
                if (!StringUtils.isEmpty(imgUrl) && imgUrl.contains("tcloud")) {
                    if(imgUrl.endsWith("/")){
                        imgUrl = imgUrl.substring(0,imgUrl.length()-1);
                    }
                    imageUrlList.add(imgUrl);
                }
            }
        }
        return imageUrlList;
    }

    /**
     *  转换图片地址
     */
    public List<String> switchImageUrl(List<String> oldImageUrl){
        String urlPrefix = "http://tscloud.digiwin.com/";
        List<String> newImageUrl = new ArrayList<>();
        for (String imageUrl : oldImageUrl) {
            String fileUrl = imageUrl;
            if (!imageUrl.startsWith("http")) {
                imageUrl = urlPrefix + imageUrl;
            }
            byte[] remoteFileByte = getRemoteFileByte(imageUrl);
            if (remoteFileByte != null) {
                int lastIndexOf = imageUrl.lastIndexOf("/");
                String fileName = imageUrl.substring(lastIndexOf + 1);
                fileUrl = dmcRest.shareUploudFile(fileName,remoteFileByte);
            }
            newImageUrl.add(fileUrl);
        }
        return newImageUrl;
    }

    /**
     * 检查是否在年维到期日或试用日期内
     * @param contractExprityDate 合约到期日期，格式'YYYY-mm-dd'
     * @param trialExpired 试用到期日期，'YYYY-mm-dd'
     * @return boolean
     * @throws RuntimeException
     */
    private boolean contractValid(String contractExprityDate, String trialExpired) throws IllegalArgumentException {
        if(StringUtils.isEmpty(contractExprityDate) && StringUtils.isEmpty(trialExpired)){
            throw new IllegalArgumentException("合约到期日期和试用到期日期不能全为空");
        }
        try {
            //合约到期日期不为空, 且合约在有效期, 允许提报
            if(!StringUtils.isEmpty(contractExprityDate) && CasesProcessUtils.checkContractIsValid(contractExprityDate)){
                return true;
            }
        } catch (DateTimeParseException e){
            throw new IllegalArgumentException("合约到期日期格式不正确，期望格式'yyyyMMdd'");
        }
        try {
            //合约日期不满足条件判断试用日期
            return !StringUtils.isEmpty(trialExpired) && CasesProcessUtils.checkTrialIsValid(trialExpired);
        } catch (DateTimeParseException e){
            throw new IllegalArgumentException("试用到期日期格式不正确，期望格式'yyyy-MM-dd'");
        }
    }

    /**
     * 根据客服编号和产品编号判断客户是否有响应产品的问题提报权限
     * @param customerServiceCode 客服编号
     * @param productCode 产品编号
     * @throws RuntimeException
     */
    @Override
    public BaseResponse checkSubmitPrivilege(String customerServiceCode, String productCode) throws RuntimeException{
        CustomerProjectStatus projectStatus;
        try {
            projectStatus = userServiceFeignClient.getCustProjectStatus(productCode, customerServiceCode);
        }catch (RuntimeException e){
            throw new RuntimeException("调用userservice获取客户的项目状态和合约信息失败.", e);
        }
        if(projectStatus == null){
            throw new NullPointerException("没有获取到客户的项目状态和合约信息.");
        }

        if(StringUtils.isEmpty(projectStatus.getProjectStatus())){
            // 项目状态为空允许提报案件
            return BaseResponse.ok(true);
        } else {
            switch (getCustomerProductStatus(projectStatus.getProjectStatus())) {
                case NOT_ONLINE:

                case ONLINE_NOT_DELIVERED:

                case DELIVERED:

                case TSC:
                    //转TSC的客户判断合约到期时间和试用到期时间
                    if(contractValid(projectStatus.getContractExprityDate(), projectStatus.getTrialExpired())){
                        return BaseResponse.ok(true);
                    } else {
                        String prompt_message = "合约到期，请联系鼎捷的业务人员续约.";
                        return BaseResponse.error(ResponseStatus.SUBMIT_CASE_FORBIDDEN, prompt_message);
                    }
                case INVALID:
                    //失效客户禁止提报案件
                    return BaseResponse.error(ResponseStatus.SUBMIT_CASE_FORBIDDEN, "已失效.");
                case SUSPEND:
                    //设置为暂停支持的客户禁止提报案件
                    return BaseResponse.error(ResponseStatus.SUBMIT_CASE_FORBIDDEN, "暂停支持.");
                default:
                    //其他状态允许提报案件
                    return BaseResponse.ok(true);
            }
        }
    }

    /**
     * 根据项目状态编号获取项目状态的枚举常量
     * @param status 数字字符串
     * @return 项目状态枚举常量
     * @throws IllegalArgumentException 项目状态不在定义范围内
     */
    private CustomerProductStatus getCustomerProductStatus(String status) throws IllegalArgumentException{
        for (CustomerProductStatus productStatus : CustomerProductStatus.values()){
            if(productStatus.getCode().equals(status)){
                return productStatus;
            }
        }
        throw new IllegalArgumentException("客户项目状态码非法:不在定义内");
    }

    @Override
    public BaseResponse submitSubIssue(long issueId, String language, String userId, CasesFormDTO params,
                                       SubmitTo operation) {
        Cases parentIssue = issueProcessMapper.getCaseByIssueId(issueId);
        if(parentIssue == null){
            return BaseResponse.error(ResponseStatus.EXPECTATION_FAILED, "没有查询到案件,issueId:"+issueId);
        }
        UserDetailInfo user = userServiceFeignClient.findUserDetailById(parentIssue.getUserId());
        if(!casesProcessUtils.permitSubmitSubIssue(parentIssue.getCrmId(), user)){
            return BaseResponse.error(ResponseStatus.EXPECTATION_FAILED, "不允许提交关联案件");
        }
        switch (operation){
            case T100:
                return submitCaseToT100(language, userId, params, parentIssue.getCrmId());
            case JIAOFU:
                return submitCaseToJiaofu(language, userId, params, parentIssue.getCrmId());
            case PCASE:
                return submitCaseToPersonal(language, params, parentIssue.getCrmId());
            default:
                return BaseResponse.error(ResponseStatus.EXPECTATION_FAILED, "不支持的操作");
        }
    }
}

