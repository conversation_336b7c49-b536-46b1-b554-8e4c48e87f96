package com.digiwin.escloud.aiocmdb.asset.model;

import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 资产类别分类表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Data
@ApiModel(value = "AssetCategoryClassification对象", description = "资产类别分类表")
public class AssetCategoryClassification implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("分类名称")
    private String categoryName;

    @ApiModelProperty("分类名称")
    private String categoryname_CN;

    @ApiModelProperty("分类名称")
    private String categoryname_TW;

    @ApiModelProperty("代号")
    private String code;

    @ApiModelProperty("图标URL")
    private String iconUrl;

    @ApiModelProperty("父ID")
    private Long parentId;

    @ApiModelProperty("分类级别")
    private Integer categoryLevel;

    @ApiModelProperty("是否可以删除")
    private Boolean canDelete;

    @ApiModelProperty("是否可以编辑")
    private Boolean canEdit;

    private LocalDateTime createDate;

    private LocalDateTime updateDate;


}
