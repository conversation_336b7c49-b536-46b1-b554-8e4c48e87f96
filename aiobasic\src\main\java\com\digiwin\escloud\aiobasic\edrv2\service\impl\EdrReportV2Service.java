package com.digiwin.escloud.aiobasic.edrv2.service.impl;

import com.digiwin.escloud.aiobasic.edrv2.dao.EdrApplicationMapper;
import com.digiwin.escloud.aiobasic.edrv2.utils.Edrv2Util;
import com.digiwin.escloud.aiobasic.util.BigDataUtil;
import com.digiwin.escloud.aiobasic.edr.dao.EdrEventMapper;
import com.digiwin.escloud.aiobasic.edr.dao.EdrReportMapper;
import com.digiwin.escloud.aiobasic.edr.model.base.fine.SendStatus;
import com.digiwin.escloud.aiobasic.edrv2.constant.Edrv2Const;
import com.digiwin.escloud.aiobasic.edrv2.dao.EdrAgentMapper;
import com.digiwin.escloud.aiobasic.edrv2.dao.EdrCustomerV2Mapper;
import com.digiwin.escloud.aiobasic.edrv2.model.*;
import com.digiwin.escloud.aiobasic.edrv2.service.IEdrAgentService;
import com.digiwin.escloud.aiobasic.report.model.*;
import com.digiwin.escloud.aiobasic.edrv2.service.IEdrReportV2Service;
import com.digiwin.escloud.aioitms.model.bigdata.StarRocksEntity;
import com.digiwin.escloud.common.controller.ParamCheckHelp;
import com.digiwin.escloud.common.feign.AioItmsFeignClient;
import com.digiwin.escloud.common.feign.AioUserFeignClient;
import com.digiwin.escloud.common.model.JdbcSqlInfo;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import io.reactivex.Observable;
import io.reactivex.schedulers.Schedulers;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.digiwin.escloud.aiobasic.edrv2.constant.Edrv2Const.*;
import static com.digiwin.escloud.common.util.DateUtil.DATE_TIME_FORMATTER;

@Slf4j
@Service
public class EdrReportV2Service implements IEdrReportV2Service, ParamCheckHelp {
    @Value("${digiwin.supplier.defaultsid:241199971893824}")
    String defaultSid;
    @Value("${ENVIRONMENT:dev}")
    String environment;
    @Resource
    AioUserFeignClient aioUserFeignClient;
    @Resource
    AioItmsFeignClient aioItmsFeignClient;
    @Autowired
    IEdrAgentService edrAgentService;
    @Autowired
    EdrCustomerV2Service edrCustomerV2Service;
    @Autowired
    EdrAgentMapper edrAgentMapper;
    @Autowired
    EdrCustomerV2Mapper edrCustomerV2Mapper;
    @Autowired
    EdrEventMapper edrEventMapper;
    @Autowired
    Edrv2Util edrv2Util;
    @Autowired
    BigDataUtil bigDataUtil;
    @Autowired
    private EdrReportMapper edrReportMapper;
    @Autowired
    EdrApplicationMapper edrApplicationMapper;
//    private static final String serverId = "365750770160999"; // EDRv2專用

    @Override
    public BaseResponse saveWhiteList(List<EdrAgentWhiteList> agentWhiteList) {

        if (CollectionUtils.isEmpty(agentWhiteList)) {
            return BaseResponse.error(ResponseCode.EDR_AGENT_WHITELIST_LIST_IS_EMPTY);
        }

        // 保存前先清除白名單
        List<String> siteIdList = agentWhiteList.stream().map(EdrAgentWhiteList::getSiteId).collect(Collectors.toList());
        removeWhiteListByEid(siteIdList);

        // 生成 StarRocksEntity
        StarRocksEntity starRocksEntity = new StarRocksEntity();
        starRocksEntity.setDatabase(bigDataUtil.getSrDbName());
        starRocksEntity.setTable("SentinelOne_Whitelist");

        // 處理數據
        List<LinkedHashMap<String, Object>> rows = MapUtil.convertToListLinkedHashMap(agentWhiteList);
        rows.forEach(whiteList -> {
            whiteList.put("type", whiteList.getOrDefault("whitelist_type", null));
            whiteList.put("collectedTime", DateUtil.getNowFormatString(DATE_TIME_FORMATTER));
            whiteList.put("uploadDataModelCode", "SentinelOne_Whitelist");
            // 轉換日期格式
            whiteList.put("createdAt", stringDateTimeFormat(StringUtil.toString(whiteList.getOrDefault("createdAt", null))));
            whiteList.put("updatedAt", stringDateTimeFormat(StringUtil.toString(whiteList.getOrDefault("updatedAt",null))));
            // 移除 whitelist_type 欄位
            whiteList.remove("whitelist_type");
        });
        starRocksEntity.setRows(rows);

        // 取出欄位名稱
        LinkedHashMap<String, Object> map = rows.stream().findFirst().get();
        String[] fieldArray = map.keySet().stream().toArray(String[]::new);
        starRocksEntity.setFieldNames(fieldArray);

        // 執行保存
        bigDataUtil.srStreamLoad(starRocksEntity);
        return BaseResponse.ok();
    }

    @Override
    public BaseResponse setEventReleaseImmediately(EventReleaseParam eventReleaseParam) {

        //region 参数检查
        Optional<BaseResponse> eventIdOptResponse = checkParamIsEmpty(eventReleaseParam.getEventId(), "eventId");
        Optional<BaseResponse> serverIdOptResponse = checkParamIsEmpty(eventReleaseParam.getServerId(), "serverId");

        if (eventIdOptResponse.isPresent() && serverIdOptResponse.isPresent()) {
            return BaseResponse.error(ResponseCode.EVENTID_AND_SERVERID_IS_EMPTY);
        }
        if (eventIdOptResponse.isPresent()) {
            return BaseResponse.error(ResponseCode.EVENTID_IS_EMPTY);
        }
        if (serverIdOptResponse.isPresent()) {
            return BaseResponse.error(ResponseCode.SERVERID_IS_EMPTY);
        }
        //endregion

        try {
            // 取得事件資料
            List<Map<String, Object>> threatDataList = getThreatData(eventReleaseParam.getEventId(), null, null);
            if (CollectionUtils.isEmpty(threatDataList)) {
                log.error("threatDataList is empty");
                return BaseResponse.error(ResponseCode.EVENT_RELEASE_FAIL);
            }

            Map<String, Object> threatMap = threatDataList.get(0);
            String siteId = StringUtil.toString(threatMap.getOrDefault("siteId", ""));
            String eid = StringUtil.toString(threatMap.getOrDefault("eid", ""));
            String osType = StringUtil.toString(threatMap.getOrDefault("agentOsType", ""));
            String shaCode = StringUtil.toString(threatMap.getOrDefault("shaCode", ""));
            String accountId = edrApplicationMapper.getAccountIdsBySiteId(siteId);
            Boolean isNewSource = ACCOUNTID_NEW.equals(accountId);
            String url = isNewSource ? SENTINELONE_URI_NEW : SENTINELONE_URI;
            String token = isNewSource ? TOKEN_NEW : TOKEN;

            // 取得同shaCode事件
            List<Map<String, Object>> releaseThreatDataList = getThreatData(null, shaCode, siteId);
            if (CollectionUtils.isEmpty(releaseThreatDataList)) {
                log.error("releaseThreatList is empty");
                return BaseResponse.error(ResponseCode.EVENT_RELEASE_FAIL);
            }

            // 調用edr API 做放行，調用成功則回饋放行成功，否則回饋異常
            if (EventRelease(siteId, eventReleaseParam.getReleaseRemark(), osType, shaCode, url, token)) {
                // 將同shaCode事件放行
                eventReleaseParam.setReleaseStatus("release");
                eventReleaseParam.setSid(RequestUtil.getEsHeaderSid(defaultSid));
                Observable.fromIterable(releaseThreatDataList)
                        .subscribeOn(Schedulers.io())
                        .subscribe(item -> {
                                    // 取得eventId
                                    Long eventId = LongUtil.objectToLong(item.get("id"));

                                    // 變更狀態
                                    Boolean isChanged = EventChangeStatus(eventId, "false_positive", url, token);
                                    if (!isChanged) {
                                        throw new RuntimeException("Event Release Failed ThreatId: " + eventId);
                                    }

                                    // 執行解隔離
                                    EventMitigateUnguarantine(eventId, url, token);

                                    // 紀錄已放行
                                    Map<String, Object> releaseMap = new HashMap<>();
                                    releaseMap.put("serverId", eventReleaseParam.getServerId());
                                    releaseMap.put("eventId", eventId);
                                    releaseMap.put("sid", eventReleaseParam.getSid());
                                    releaseMap.put("releaseStatus", "release");
                                    releaseMap.put("releaseRemark", eventReleaseParam.getReleaseRemark());

                                    // 更新事件放行狀態
                                    Long id = edrEventMapper.getIdByEventId(releaseMap);
                                    if (LongUtil.isEmpty(id)) {
                                        id = SnowFlake.getInstance().newId();
                                    }
                                    eventReleaseParam.setId(id);
                                    releaseMap.put("id", id);
                                    edrEventMapper.edrEventReleaseStatusUpdate(releaseMap);

                                }, throwable -> throwable.printStackTrace()
                        );
                // 同步組織白名單
                syncWhiteList(siteId, eid);

                return BaseResponse.ok();
            }
            return BaseResponse.error(ResponseCode.EVENT_RELEASE_FAIL);

        } catch (Exception e) {
            log.error("EDRv2 setEventReleaseImmediately", e);
            return BaseResponse.error(ResponseCode.EVENT_RELEASE_FAIL);
        }
    }

    @Override
    public BaseResponse setEventReleaseCancel(EventReleaseParam eventReleaseParam) {

        //region 参数检查
        Optional<BaseResponse> eventIdOptResponse = checkParamIsEmpty(eventReleaseParam.getEventIdList(), "eventIdList");
        Optional<BaseResponse> serverIdOptResponse = checkParamIsEmpty(eventReleaseParam.getServerId(), "serverId");

        if (eventIdOptResponse.isPresent() && serverIdOptResponse.isPresent()) {
            return BaseResponse.error(ResponseCode.EVENTID_AND_SERVERID_IS_EMPTY);
        }
        if (eventIdOptResponse.isPresent()) {
            return BaseResponse.error(ResponseCode.EVENTID_IS_EMPTY);
        }
        if (serverIdOptResponse.isPresent()) {
            return BaseResponse.error(ResponseCode.SERVERID_IS_EMPTY);
        }
        //endregion

        try {
            // 取得白名單Id
            List<Long> whiteListIdList = getWhiteListIdList(eventReleaseParam.getEventIdList());

            // 取得已放行事件
            List<Map<String, Object>> threatDataList = getThreatData(eventReleaseParam.getEventIdList().get(0), null, null);
            if (CollectionUtils.isEmpty(threatDataList)) {
                log.error("threatDataList is empty");
                return BaseResponse.error(ResponseCode.EDR_RELEASE_CANCEL_FAILED);
            }

            Map<String, Object> threatMap = threatDataList.get(0);
            String siteId = StringUtil.toString(threatMap.getOrDefault("siteId", ""));
            String shaCode = StringUtil.toString(threatMap.getOrDefault("shaCode", ""));
            String accountId = edrApplicationMapper.getAccountIdsBySiteId(siteId);
            Boolean isNewSource = ACCOUNTID_NEW.equals(accountId);
            String url = isNewSource ? SENTINELONE_URI_NEW : SENTINELONE_URI;
            String token = isNewSource ? TOKEN_NEW : TOKEN;

            // 取得同shaCode事件
            List<Map<String, Object>> releaseThreatDataList = getThreatData(null, shaCode, siteId);
            if (CollectionUtils.isEmpty(releaseThreatDataList)) {
                log.error("releaseThreatList is empty");
                return BaseResponse.error(ResponseCode.EDR_RELEASE_CANCEL_FAILED);
            }

            // 調用edr API 做取消放行，調用成功則回饋放行成功，否則回饋異常
            if (EventReleaseCancel(whiteListIdList, url, token)) {
                eventReleaseParam.setReleaseStatus("nonRelease");
                eventReleaseParam.setSid(RequestUtil.getEsHeaderSid(defaultSid));
                Observable.fromIterable(releaseThreatDataList)
                        .subscribeOn(Schedulers.io())
                        .subscribe(item -> {
                                    // 取得eventId
                                    Long eventId = LongUtil.objectToLong(item.get("id"));

                                    // 變更狀態
                                    Boolean isChanged = EventChangeStatus(eventId, "suspicious", url, token);
                                    if (!isChanged) {
                                        throw new RuntimeException("Event Release Cancel Failed! ThreatId: " + eventId);
                                    }

                                    Map<String, Object> releaseMap = new HashMap<>();
                                    releaseMap.put("serverId", eventReleaseParam.getServerId());
                                    releaseMap.put("eventId", eventId);
                                    releaseMap.put("sid", eventReleaseParam.getSid());
                                    releaseMap.put("releaseStatus", "nonRelease");
                                    releaseMap.put("releaseRemark", eventReleaseParam.getReleaseRemark());
                                    Long id = edrEventMapper.getIdByEventId(releaseMap);
                                    if (null == id) {
                                        id = SnowFlake.getInstance().newId();
                                    }
                                    eventReleaseParam.setId(id);
                                    releaseMap.put("id", id);
                                    edrEventMapper.edrEventReleaseStatusUpdate(releaseMap);
                                }, throwable -> throwable.printStackTrace()
                        );

                return BaseResponse.ok(removeWhiteList(whiteListIdList));
            }
            return BaseResponse.error(ResponseCode.EDR_RELEASE_CANCEL_FAILED);
        } catch (Exception e) {
            log.error("EDRv2 setEventReleaseCancel", e);
            return BaseResponse.error(ResponseCode.EDR_RELEASE_CANCEL_FAILED);
        }
    }

    @Override
    public BaseResponse getReport(Long id) {
        // region參數檢查
        Optional<BaseResponse> reportIdOpt = checkParamIsEmpty(id, "id");
        if (reportIdOpt.isPresent()) {
            return BaseResponse.dynamicError(ResponseCode.PARAM_IS_EMPTY, "id");
        }
        // endregion

        EdrReport edrReport = aioItmsFeignClient.edrv2GetReport(StringUtil.toString(id));

        return BaseResponse.ok(edrReport);
    }

    @Async
    public void generateReport(Long reportId, String reportStartDate, String reportEndDate, String generateTime, Long sid, String siteIds, Boolean auto) {
        try {
            List<String> siteIdList = Arrays.asList(siteIds.split(","));

            EdrReport edrReport = new EdrReport();
            edrReport.setGenerateTime(generateTime);

            // customerData 組織資訊
            EdrReportCustomerData customerData = processCustomerData(reportId);
            edrReport.setCustomerData(customerData);
            Long eid = edrReport.getCustomerData().getEid();

            // Authorization 授權資訊
            EdrReportAuthorization edrReportAuthorization = processAuthorization(eid, sid, siteIdList);
            edrReport.setAuthorization(edrReportAuthorization);

            // agentData 設備資訊
            EdrReportAgentData edrReportAgentData = processAgentData(eid, siteIdList);
            edrReport.setAgentData(edrReportAgentData);

            // top5Device
            List<Map<String, Object>> top5Devices = top5DevicesList(eid, reportStartDate, reportEndDate, siteIds);

            // totalAmount 各威脅總數
            List<Map<String, Object>> totalAmount = processTotalAmount(eid, reportStartDate, reportEndDate, siteIds);

            // top10Amount  TOP10威脅數量
            List<Map<String, Object>> top10Amount = processTop10Amount(eid, reportStartDate, reportEndDate, siteIds);

            // EventList
            Map<String, Object> eventList = new HashMap<>();
            eventList.put("totalAmount", totalAmount);
            eventList.put("top5Devices", top5Devices);
            eventList.put("top10Amount", top10Amount);
            edrReport.setEventList(eventList);

            // CaseData 事件數據
            edrReport.setCaseData(processCaseData(eid, sid, reportStartDate, reportEndDate, siteIds));

            // 保存報告
            edrReport.setId(StringUtil.toString(reportId));
            aioItmsFeignClient.edrv2ReportGenerate(edrReport);

            // 更新報告狀態為評估中
            if (!auto){
                edrReportMapper.updateReportRecordSendStatus(reportId, SendStatus.S1Draft.toString());
            }

        } catch (Exception ex) {
            log.error("EDRv2 generateReport", ex);
        }
    }

    private EdrReportCustomerData processCustomerData(Long reportId) {
        try {
            return edrCustomerV2Mapper.getReportCustomerData(reportId);
        } catch (Exception ex) {
            throw new RuntimeException("Generate CustomerData Error", ex);
        }
    }

    private EdrReportAuthorization processAuthorization(Long eid, Long sid, List<String> siteIds) {
        try {
            EdrReportAuthorization edrReportAuthorization = new EdrReportAuthorization();

            // 獲取全部租戶下全部授權安裝數量
            EdrCustomerOrgAuthorizationInfo edrCustomerOrgAllAuthorizationInfo = edrCustomerV2Mapper.selectCustomerOrgAuthorizationInfo(eid);
            Integer allUsedSRV = edrCustomerOrgAllAuthorizationInfo.getUsedSRV();
            Integer allUsedPC = edrCustomerOrgAllAuthorizationInfo.getUsedPC();

            // 獲取組織授權安裝數量
            EdrCustomerOrgAuthorizationInfo edrCustomerOrgAuthorizationInfo = edrCustomerV2Mapper.selectCustomerOrgAuthorizationInfoBySiteIds(siteIds);
            edrReportAuthorization.setUsedSRV(edrCustomerOrgAuthorizationInfo.getUsedSRV());
            edrReportAuthorization.setUsedPC(edrCustomerOrgAuthorizationInfo.getUsedPC());

            // 獲取remainDay
            edrReportAuthorization.setRemainDay(edrCustomerOrgAuthorizationInfo.getRemainDay());

            // 處理授權數量
            BaseResponse<List<Map<String, Object>>> response = aioUserFeignClient.getTenantModuleContractClassDetailByAiopsItemListV2(sid, eid, Arrays.asList("S1EDR"));
            List<Map<String, Object>> dataList = response.getData();

            if (CollectionUtil.isNotEmpty(dataList)) {
                // 獲取授權到期日
                String endDate = StringUtil.toString(dataList.get(0).getOrDefault("endDate", ""));
                edrReportAuthorization.setExpiredDate(endDate);

                List<Map<String, Object>> tenantModuleContractDetailList = (List<Map<String, Object>>) dataList.get(0).getOrDefault("tenantModuleContractDetailList", new ArrayList<>());

                if (CollectionUtil.isNotEmpty(tenantModuleContractDetailList)) {
                    Integer HOSTAvailableCount = tenantModuleContractDetailList.stream()
                            .filter(detail -> "S1EDR_HOST".equals(detail.get("classCode")))
                            .map(detail -> IntegerUtil.objectToInteger(detail.getOrDefault("availableCount", 0)))
                            .findFirst()
                            .orElse(0);
                    edrReportAuthorization.setTotalSRV(HOSTAvailableCount);
                    edrReportAuthorization.setUnUsedSRV(HOSTAvailableCount - allUsedSRV);

                    Integer CLIENTAvailableCount = tenantModuleContractDetailList.stream()
                            .filter(detail -> "S1EDR_CLIENT".equals(detail.get("classCode")))
                            .map(detail -> IntegerUtil.objectToInteger(detail.getOrDefault("availableCount", 0)))
                            .findFirst()
                            .orElse(0);

                    edrReportAuthorization.setTotalPC(CLIENTAvailableCount);
                    edrReportAuthorization.setUnUsedPC(CLIENTAvailableCount - allUsedPC);
                }
            }

            return edrReportAuthorization;
        } catch (Exception ex) {
            throw new RuntimeException("Generate Authorization Error", ex);
        }
    }

    private EdrReportAgentData processAgentData(Long eid, List<String> siteIds) {
        try {
            EdrReportAgentData edrReportAgentData = new EdrReportAgentData();

            // 取得healthy
            Map<String, Object> healthTotalAmount = edrAgentMapper.getHealthTotalAmount(eid, siteIds);

            // 取得infectedMap
            Map<String, Object> infectedMap = new HashMap<>();
            infectedMap.put("title", "Infected");
            infectedMap.put("count", healthTotalAmount.getOrDefault("infectedCount", 0));

            // 取得healthyMap
            Map<String, Object> healthyMap = new HashMap<>();
            healthyMap.put("title", "Healthy");
            healthyMap.put("count", healthTotalAmount.getOrDefault("healthyCount", 0));

            List<Map<String, Object>> totalAmount = new ArrayList<>();
            totalAmount.add(infectedMap);
            totalAmount.add(healthyMap);

            edrReportAgentData.setHealthy(totalAmount);

            // 取得list
            EdrAgentParam edrAgentParam = new EdrAgentParam();
            edrAgentParam.setEidList(Arrays.asList(StringUtil.toString(eid)));
            edrAgentParam.setSiteIds(siteIds);
            Optional<List<EdrAgentField>> optional2 = Optional.ofNullable(edrAgentMapper.getAgent(edrAgentParam));
            if (optional2.isPresent()) {
                List<Map<String, String>> edrAgentFieldList = optional2.get().stream()
                        .map(EdrReportV2Service::convertToMap)
                        .map(agentField -> {
                            formatDateField(agentField, "subscribedOn");
                            formatDateField(agentField, "lastActive");
                            formatDateField(agentField, "agentClosedTime");
                            formatDateField(agentField, "agentPreActiveTime");
                            formatDateField(agentField, "createdAt");
                            formatDateField(agentField, "updatedAt");
                            formatDateField(agentField, "lastRebootDate");

                            return convertMapToStringMap(agentField);
                        })
                        .sorted(Comparator.comparing(agentField -> "Infected".equals(agentField.get("healthState")) ? 0 : 1))
                        .collect(Collectors.toList());
                edrReportAgentData.setList(edrAgentFieldList);
            }

            return edrReportAgentData;
        } catch (Exception ex) {
            throw new RuntimeException("Generate AgentData Error", ex);
        }
    }

    private List<Map<String, Object>> top5DevicesList(Long eid, String reportStartDate, String reportEndDate, String siteIds) {
        try {
            StringBuilder sb = new StringBuilder();
            sb.append("SELECT count(agentComputerName) as deviceCount, agentComputerName as name ");
            sb.append("FROM servicecloud.SentinelOne_Threats ");
            sb.append("WHERE updatedAt BETWEEN CONCAT(NULLIF('").append(reportStartDate).append("', ''), ' 00:00:00') ");
            sb.append("AND CONCAT(NULLIF('").append(reportEndDate).append("', ''), ' 23:59:59') ");
            sb.append("AND eid = ").append(eid).append(" ");
            if (StringUtils.hasText(siteIds)) {
                sb.append("AND siteId IN (").append(siteIds).append(") ");
            }
            sb.append("GROUP BY agentComputerName ");
            sb.append("ORDER BY deviceCount DESC ");
            sb.append("LIMIT 5");

            String sql = sb.toString();

            return bigDataUtil.srQuery(sql);
        } catch (Exception ex) {
            throw new RuntimeException("Generate Top5DevicesList Error", ex);
        }
    }

    private List<Map<String, Object>> processTotalAmount(Long eid, String reportStartDate, String reportEndDate, String siteIds) {
        try {
            StringBuilder sb = new StringBuilder();
            sb.append("SELECT count(classification) as count, classification ");
            sb.append("FROM servicecloud.SentinelOne_Threats ");
            sb.append("WHERE updatedAt BETWEEN CONCAT(NULLIF('").append(reportStartDate).append("', ''), ' 00:00:00') ");
            sb.append("AND CONCAT(NULLIF('").append(reportEndDate).append("', ''), ' 23:59:59') ");
            sb.append("AND eid = ").append(eid).append(" ");
            if (StringUtils.hasText(siteIds)) {
                sb.append("AND siteId IN (").append(siteIds).append(") ");
            }
            sb.append("GROUP BY classification ");
            sb.append("ORDER BY count");

            String sql = sb.toString();
            List<Map<String, Object>> totalAmount = bigDataUtil.srQuery(sql);

            // mapping enumCode
            List<EdrCustomerThreatClassification> classificationMappingList = edrCustomerV2Mapper.getClassificationMappingList();
            totalAmount = totalAmount.stream().peek(data -> classificationMappingList.stream()
                            .filter(mapping -> mapping.getClassification().equals(data.getOrDefault("classification", "")))
                            .findFirst()
                            .ifPresent(mapping -> {
                                data.put("enumCode", mapping.getEnumCode());
                            }))
                    .collect(Collectors.toList());

            // 處理大分類
            List<Map<String, Object>> result = totalAmount.stream()
                    .collect(Collectors.groupingBy(data -> {
                        String enumCode = (String) data.get("enumCode");
                        return enumCode != null && enumCode.contains("_") ? enumCode.split("_")[0] : "";
                    }, Collectors.summingInt(data -> (int) data.get("count"))))
                    .entrySet().stream()
                    .map(entry -> {
                        Map<String, Object> map = new HashMap<>();
                        map.put("classification", entry.getKey());
                        map.put("count", entry.getValue());
                        return map;
                    })
                    .collect(Collectors.toList());

            return result;
        } catch (Exception ex) {
            throw new RuntimeException("Generate TotalAmount Error", ex);
        }
    }

    private List<Map<String, Object>> processTop10Amount(Long eid, String reportStartDate, String reportEndDate, String siteIds) {
        try {
            List<String> titles = Arrays.asList("Ransomware", "Rootkit", "Trojan", "Backdoor", "Exploit",
                    "Worm", "Infostealer", "Downloader", "Spyware", "Virus");

            StringBuilder sb = new StringBuilder();
            sb.append("SELECT count(classification) as count, classification as title ");
            sb.append("FROM servicecloud.SentinelOne_Threats ");
            sb.append("WHERE updatedAt BETWEEN CONCAT(NULLIF('").append(reportStartDate).append("', ''), ' 00:00:00') ");
            sb.append("AND CONCAT(NULLIF('").append(reportEndDate).append("', ''), ' 23:59:59') ");
            sb.append("AND eid = ").append(eid).append(" ");
            if (StringUtils.hasText(siteIds)) {
                sb.append("AND siteId IN (").append(siteIds).append(") ");
            }
            sb.append("AND classification IN ('").append(String.join("','", titles)).append("') ");
            sb.append("GROUP BY classification");

            String sql = sb.toString();
            List<Map<String, Object>> totalAmount = bigDataUtil.srQuery(sql);

            // 處理對應繁簡體 及 添加未查詢到的威脅類型
            List<EdrCustomerThreatClassification> classificationMappingList = edrCustomerV2Mapper.getClassificationMappingList();
            List<Map<String, Object>> finalTotalAmount = totalAmount;
            totalAmount = titles.stream()
                    .map(title -> {
                        // 檢查 totalAmount 是否包含 title
                        Map<String, Object> existingData = finalTotalAmount.stream()
                                .filter(data -> title.equals(data.getOrDefault("title", "")))
                                .findFirst()
                                .orElseGet(() -> {
                                    Map<String, Object> classification = new HashMap<>();
                                    classification.put("title", title);
                                    classification.put("count", 0);
                                    return classification;
                                });

                        // 取得簡繁體參數
                        classificationMappingList.stream()
                                .filter(mapping -> mapping.getClassification().equals(title))
                                .findFirst()
                                .ifPresent(mapping -> {
                                    existingData.put("enumCode", mapping.getEnumCode());
                                    existingData.put("cnFieldName", mapping.getCnFieldName());
                                    existingData.put("twFieldName", mapping.getTwFieldName());
                                });

                        return existingData;
                    })
                    .collect(Collectors.toList());

            return totalAmount;
        } catch (Exception ex) {
            throw new RuntimeException("Generate Top10Amount Error", ex);
        }
    }

    private EdrReportCaseData processCaseData(Long eid, Long sid, String reportStartDate, String reportEndDate, String siteIds) {
        try {
            EdrReportCaseData edrReportCaseData = new EdrReportCaseData();
            edrReportCaseData.setReportStartDate(reportStartDate);
            edrReportCaseData.setReportEndDate(reportEndDate);

            // 事件查詢參數
            EdrCustomerThreatsParam edrCustomerThreatsParam = new EdrCustomerThreatsParam();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

            LocalDate localDate = LocalDate.parse(reportStartDate, formatter);
            LocalDateTime localDateTime = localDate.atStartOfDay();
            Date startDate = Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());

            localDate = LocalDate.parse(reportEndDate, formatter);
            localDateTime = localDate.atTime(LocalTime.MAX);
            Date endDate = Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());

            edrCustomerThreatsParam.setThreatTimeFrom(startDate);
            edrCustomerThreatsParam.setThreatTimeTo(endDate);
            edrCustomerThreatsParam.setEid(eid);
            edrCustomerThreatsParam.setSid(StringUtil.toString(sid));
            edrCustomerThreatsParam.setSiteIds(siteIds);

            // threatId組合
            List<String> threatIds = edrReportMapper.getEdrv2EventIds();
            edrCustomerThreatsParam.setThreatId(String.join(",", threatIds));

            // 異步處理事件
            List<Map<String, Object>> threatList = edrCustomerV2Service.getThreats(edrCustomerThreatsParam, edrCustomerThreatsParam.getArea()); // 預設mapping繁中 ClassificationName
            List<Map<String, Object>> resultList = Observable.fromIterable(threatList)
                    .subscribeOn(Schedulers.io())
                    .flatMap(threat -> {
                        EdrCustomerThreatsParam edrCustomerThreatsDetailParam = new EdrCustomerThreatsParam();
                        edrCustomerThreatsDetailParam.setThreatId(String.join(",", threatIds));
                        edrCustomerThreatsDetailParam.setThreatName(StringUtil.toString(threat.getOrDefault("threatName", "")));
                        edrCustomerThreatsDetailParam.setThreatTimeFrom(startDate);
                        edrCustomerThreatsDetailParam.setThreatTimeTo(endDate);
                        edrCustomerThreatsDetailParam.setEid(eid);
                        edrCustomerThreatsDetailParam.setCollectionId(StringUtil.toString(threat.getOrDefault("collectionId", "")));
                        edrCustomerThreatsDetailParam.setSiteIds(siteIds);

                        // 處理子事件，使用Observable包裝子事件的處理邏輯
                        return Observable.fromIterable(edrCustomerV2Service.getThreatsDetail(edrCustomerThreatsDetailParam, edrCustomerThreatsParam.getArea()))
                                .map(threatDetail -> {
                                    threatDetail.put("id", StringUtil.toString(threatDetail.getOrDefault("id", "")));
                                    threatDetail.put("collectionId", StringUtil.toString(threat.getOrDefault("collectionId", "")));
                                    threatDetail.put("originalThreatName", threatDetail.getOrDefault("threatName", ""));
                                    threatDetail.put("threatName", threat.getOrDefault("threatName", ""));
                                    threatDetail.put("threatCount", threat.getOrDefault("threatCount", ""));
                                    threatDetail.put("classification", threat.getOrDefault("classification", ""));
                                    threatDetail.put("recentTime", threat.getOrDefault("recentTime", ""));
                                    threatDetail.put("processDescription", threat.getOrDefault("processDescription", ""));
                                    return threatDetail;
                                });
                    })
                    .toList()
                    .blockingGet();

            edrReportCaseData.setList(resultList);
            edrReportCaseData.setTotal(resultList.size());

            return edrReportCaseData;
        } catch (Exception ex) {
            throw new RuntimeException("Generate CaseData Error", ex);
        }
    }

    private void formatDateField(Map<String, Object> agentField, String fieldName) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        if (agentField.containsKey(fieldName)) {
            Date dateValue = (Date) agentField.get(fieldName);
            String formattedDate = Objects.nonNull(dateValue) ? dateFormat.format(dateValue) : null;
            agentField.put(fieldName, formattedDate);
        }
    }

    private Boolean EventRelease(String siteId, String description, String osType, String value, String url, String token) {

        // 建立Body數據
        Map<String, Object> requestBody = new HashMap<>();
        Map<String, String> dataMap = new HashMap<>();
        dataMap.put("type", "white_hash");
        dataMap.put("description", description);
        dataMap.put("osType", osType);
        dataMap.put("value", value);
        dataMap.put("targetScope", "site");

        Map<String, Object> filterMap = new HashMap<>();
        filterMap.put("siteIds", String.valueOf(siteId));

        requestBody.put("data", dataMap);
        requestBody.put("filter", filterMap);

        // 發送請求
        Map<String, Object> res = edrv2Util.sendRequest(url + THREAT_RELEASE_URI, token, HttpMethod.POST, requestBody);

        if (Objects.isNull(res)) {
            return false;
        }
        Object object = res.getOrDefault("error", null);

        return Objects.isNull(object);
    }

    private Boolean EventMitigateUnguarantine(Long ids, String url, String token) {

        // 建立Body數據
        Map<String, Object> requestBody = new HashMap<>();
        Map<String, Object> filterMap = new HashMap<>();
        List<Long> idList = new ArrayList<>();
        idList.add(ids);
        filterMap.put("ids", idList);

        requestBody.put("filter", filterMap);

        // 發送請求
        Map<String, Object> res = edrv2Util.sendRequest(url + THREAT_MITIGATE_UNGUARANTINE_URI, token, HttpMethod.POST, requestBody);

        if (Objects.isNull(res)) {
            return false;
        }
        Object object = res.getOrDefault("error", null);
        if (Objects.nonNull(object)){
            log.error("eventMitigateUnguarantine response error", object);
            return false;
        }

        return !res.getOrDefault("affected", 0).equals(0);
    }

    private Boolean EventReleaseCancel(List<Long> ids, String url, String token) {

        // 建立Body數據
        Map<String, Object> requestBody = new HashMap<>();
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("ids", ids);

        requestBody.put("data", dataMap);

        // 發送請求
        Map<String, Object> res = edrv2Util.sendRequest(url + THREAT_RELEASE_CANCEL_URI, token, HttpMethod.DELETE, requestBody);

        if (Objects.isNull(res)) {
            return false;
        }
        Object object = res.getOrDefault("error", null);
        if (Objects.nonNull(object)){
            log.error("eventReleaseCancel response error", object);
            return false;
        }

        return !res.getOrDefault("affected", 0).equals(0);
    }

    private Boolean EventChangeStatus(Long id, String analystVerdict, String url, String token) {

        // 建立Body數據
        Map<String, Object> requestBody = new HashMap<>();
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("analystVerdict", analystVerdict);

        Map<String, Object> filterMap = new HashMap<>();
        filterMap.put("ids", Collections.singletonList(id));

        requestBody.put("data", dataMap);
        requestBody.put("filter", filterMap);

        // 發送請求
        Map<String, Object> res = edrv2Util.sendRequest(url + THREAT_CHANGE_STATUS_URI, token, HttpMethod.POST, requestBody);

        if (Objects.isNull(res)) {
            return false;
        }
        Object object = res.getOrDefault("error", null);
        if (Objects.nonNull(object)){
            log.error("eventChangeStatus response error", object);
            return false;
        }

        return !res.getOrDefault("affected", 0).equals(0);
    }

    private List<Long> getWhiteListIdList(List<Long> eventIdList) {

        String idListString = eventIdList.stream()
                .map(String::valueOf)
                .collect(Collectors.joining(","));

        StringBuilder sb = new StringBuilder();
        sb.append("SELECT distinct sw.id FROM servicecloud.SentinelOne_Whitelist sw ");
        sb.append("LEFT JOIN servicecloud.SentinelOne_Threats st ON st.shaCode=sw.value OR st.filePath=sw.value ");
        sb.append("WHERE st.id in (").append(idListString).append(")");

        List<Map<String, Object>> dataList = bigDataUtil.srQuery(sb.toString());

        return dataList.stream()
                .map(data -> LongUtil.objectToLong(data.getOrDefault("id", null)))
                .collect(Collectors.toList());
    }

    private List<Map<String, Object>> getThreatData(Long eventId, String shaCode, String siteId) {

        StringBuilder sb = new StringBuilder();
        sb.append("SELECT id, eid, siteId, shaCode, agentOsType FROM servicecloud.SentinelOne_Threats ");
        sb.append("WHERE 1=1 ");
        if (LongUtil.isNotEmpty(eventId)) {
            sb.append("AND id = '").append(eventId).append("' ");
        }
        if (StringUtils.hasText(shaCode)) {
            sb.append(" AND shaCode = '").append(shaCode).append("' ");
        }
        if (StringUtils.hasText(siteId)) {
            sb.append(" AND siteId = '").append(siteId).append("' ");
        }

        return bigDataUtil.srQuery(sb.toString());
    }

    private void syncWhiteList(String siteId, String eid) {

        // 建立Body數據
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("siteId", siteId);
        requestBody.put("eid", eid);
        requestBody.put("env", environment);

        // 發送請求
        edrv2Util.sendRequest(SYNC_WHITELIST_URI, null, HttpMethod.POST, requestBody);
    }

    private Map<String, Object> getThreatSiteIdAndEid(String siteIds) {
        StringBuilder sb = new StringBuilder();
        sb.append("SELECT siteId, eid FROM servicecloud.SentinelOne_Threats ");
        sb.append("WHERE siteId in ('").append(String.join("','", siteIds)).append("')");

        List<Map<String, Object>> dataList = bigDataUtil.srQuery(sb.toString());

        return dataList.stream().findFirst().orElse(new HashMap<>());
    }

    private Boolean removeWhiteList(List<Long> whiteListIdList) {
        String idListString = whiteListIdList.stream()
                .map(String::valueOf)
                .collect(Collectors.joining(","));

        StringBuilder sb = new StringBuilder();
        sb.append("DELETE FROM servicecloud.SentinelOne_Whitelist ");
        sb.append("WHERE id IN (").append(idListString).append(")");

        JdbcSqlInfo sqlInfo = new JdbcSqlInfo("starrocks", sb.toString());
        return bigDataUtil.jdbcExecute(sqlInfo);
    }

    private Boolean removeWhiteListByEid(List<String> siteIdList) {
        StringBuilder sb = new StringBuilder();
        sb.append("DELETE FROM servicecloud.SentinelOne_Whitelist ");
        sb.append("WHERE siteId in ('").append(String.join("','", siteIdList)).append("')");

        JdbcSqlInfo sqlInfo = new JdbcSqlInfo("starrocks", sb.toString());
        return bigDataUtil.jdbcExecute(sqlInfo);
    }

    private static <T> Map<String, Object> convertToMap(T data) {
        Map<String, Object> map = new HashMap<>();
        try {
            Field[] fields = data.getClass().getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                map.put(field.getName(), field.get(data));
            }
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        return map;
    }

    private static <T> LinkedHashMap<String, Object> convertToLinkedHashMap(T data) {
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        try {
            Field[] fields = data.getClass().getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                map.put(field.getName(), field.get(data));
            }
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        return map;
    }

    private static <T> Map<String, String> convertMapToStringMap(Map<String, Object> data) {
        Map<String, String> resultMap = new HashMap<>();

        for (Map.Entry<String, Object> entry : data.entrySet()) {
            Object value = entry.getValue();
            resultMap.put(entry.getKey(), value != null ? String.valueOf(value) : null);
        }

        return resultMap;
    }

    private String stringDateTimeFormat(String date) {
        if (StringUtils.isEmpty(date)) {
            return null;
        }
        OffsetDateTime offsetDateTime = OffsetDateTime.parse(date);
        ZonedDateTime localZonedDateTime = offsetDateTime.atZoneSameInstant(ZoneId.systemDefault());
        return localZonedDateTime.format(DateUtil.DATE_TIME_FORMATTER);
    }
}
