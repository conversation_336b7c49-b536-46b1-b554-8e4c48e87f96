package com.digiwin.escloud.issueservice.dao.Impl;

import com.digiwin.escloud.common.util.GsonUtil;
import com.digiwin.escloud.issueservice.dao.IIssueDao;
import com.digiwin.escloud.issueservice.model.*;
import com.digiwin.escloud.issueservice.services.IKbService;
import com.digiwin.escloud.issueservice.utils.HttpUtil;
import com.digiwin.escloud.issueservice.utils.MathsUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

// import com.alibaba.nacos.client.naming.utils.CollectionUtils;

/**
 * Created by Administrator on 2018-01-03.
 */
@Service
@Slf4j
public class IssueDao implements IIssueDao {
    @Value("${digiwin.issue.connectarea}")
    private String connectArea;
    @Autowired
    private SqlSession sqlSession;
    @Autowired
    private IKbService kbService;

    @Override
    public long InsertIssue(Issue issue) {
        sqlSession.insert("escloud.issuemapper.insertIssue", issue);
        return issue.getIssueId();
    }

    @Override
    public int updateProcessor(Issue issue) {
        return sqlSession.update("escloud.issuemapper.updateProcessor", issue);
    }
    @Override
    public int UpdateIssueStatus(long issueId, String issueStatus, String oldIssueStatus, String department, String userId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueStatus", issueStatus);
        map.put("oldIssueStatus", oldIssueStatus);
        map.put("issueId", issueId);
        map.put("department", department);
        map.put("userId", userId);
        return sqlSession.update("escloud.issuemapper.updateIssueStatus", map);
    }

    @Override
    public int updateIssueStatus(long issueId, String issueStatus, String syncStatus) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueStatus", issueStatus);
        map.put("syncStatus", syncStatus);
        map.put("issueId", issueId);
        return sqlSession.update("escloud.issuemapper.updateIssueStatusForAutoUpdateSystem", map);
    }
    @Override
    public int UpdateIssueStatusByStaff(long issueId, String issueStatus, String oldIssueStatus, String department, String userId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueStatus", issueStatus);
        map.put("oldIssueStatus", oldIssueStatus);
        map.put("issueId", issueId);
        map.put("department", department);
        map.put("userId", userId);
        return sqlSession.update("escloud.issuemapper.updateIssueStatusByStaff", map);
    }
    @Override
    public String SelectIssueStatus(long issueId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueId", issueId);
        return sqlSession.selectOne("escloud.issuemapper.selectIssueStatus", map);
    }

    @Override
    public String getCrmIdByIssueId(long issueId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueId", issueId);
        return sqlSession.selectOne("escloud.issuemapper.getCrmIdByIssueId", map);
    }
    @Override
    public int UpdateIssue(long issueId, String crmId, String serviceId, String department, String issueStatus, long userContactId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueId", issueId);
        map.put("crmId", crmId);
        map.put("serviceId", serviceId);
        map.put("department", department);
        map.put("issueStatus", issueStatus);
        map.put("userContactId", userContactId);
        return sqlSession.update("escloud.issuemapper.updateIssue", map);
    }

    @Override
    public int UpdateSyncStatusByIssueId(long issueId, String syncStatus) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueId", issueId);
        map.put("syncStatus", syncStatus);
        return sqlSession.update("escloud.issuemapper.updateSyncStatusByIssueId", map);
    }
    @Override
    public int UpdateIssueAdditionalExplanationSync(long issueId,long progressId, String syncStatus) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueId", issueId);
        map.put("progressId", progressId);
        map.put("syncStatus", syncStatus);
        return sqlSession.update("escloud.issuemapper.UpdateIssueAdditionalExplanationSync", map);
    }
    @Override
    public int UpdateProcessSyncStatusByIssueId(long issueId, String syncStatus) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueId", issueId);
        map.put("syncStatus", syncStatus);
        return sqlSession.update("escloud.issuemapper.UpdateProcessSyncStatusByIssueId", map);
    }
    @Override
    public int InsertIssueProgress(long issueId, String crmId, IssueProgress issueProgress) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("IssueId", issueId);
        map.put("CrmId", crmId);
        map.put("SequenceNum", issueProgress.getSequeceNum()+1);
        map.put("ProcessType", issueProgress.getProcessType());
        map.put("Processor", issueProgress.getProcessor());
        map.put("Description", issueProgress.getDescription());
        map.put("ProcessTime", issueProgress.getProcessTime());
        map.put("ReplyType", issueProgress.getReplyType());
        map.put("ProcessHours", issueProgress.getProcessHours());
        map.put("SyncStatus", issueProgress.getSyncStatus());

        //云管家打通T服务云，需要存下面3个字段
        map.put("workno", issueProgress.getWorkno());
        map.put("handlerId", issueProgress.getHandlerId());
        map.put("CurrentStatus", issueProgress.getCurrentStatus());
        return sqlSession.insert("escloud.issuemapper.insertIssueProgress", map);
    }
    @Override
    public List<Issue> SelectIssueList(String userId, String productCode, String status, String issueType, int start, int end, String department, String queryUserId, String newReply, String myself, String from, String serviceCode, Boolean userCollect) {
        Map<String, Object> map = new HashMap<String, Object>();
        /*map.put("productCode", productCode);*/ //多产品线
        if(StringUtils.isNotBlank(productCode)){
            //productCode01 存T产品线，因T产品线的案件 客服提交案件不对客户公开，productCode02存非T产品线
            map.put("productCode01", Arrays.asList(productCode.split(",")).stream().filter(k->k.equals("100") || k.equals("06") || k.equals("999") || k.equals("164") || k.equals("147")).collect(Collectors.toList()));
            map.put("productCode02", Arrays.asList(productCode.split(",")).stream().filter(k->!k.equals("100") && !k.equals("06") && !k.equals("999") && !k.equals("164") && !k.equals("147")).collect(Collectors.toList()));
        }else {
            map.put("productCode01",new ArrayList<>());
            map.put("productCode02",new ArrayList<>());
            map.put("productCode", productCode);
        }

        map.put("issuestatus", status);
        map.put("issueType", issueType);
        map.put("start", start);
        map.put("end", end);
        map.put("processType", IssueProcessType.Synchro.toString());
        if(department == null || department.isEmpty()){
            map.put("department", "");
            map.put("userId", userId);
        }else{
            map.put("department", department);
            map.put("userId", "");
        }
        map.put("newReply", StringUtils.isEmpty(newReply) ? "" : newReply);
        map.put("myself", StringUtils.isEmpty(myself) ? "" : myself);
        map.put("from", StringUtils.isEmpty(from) ? "" : from);
        map.put("submiterId", StringUtils.isEmpty(userId) ? "" : userId);
        map.put("serviceCode", StringUtils.isEmpty(serviceCode) ? "" : serviceCode);
        map.put("userCollect", Boolean.TRUE.equals(userCollect) ? "1" : "");
        return sqlSession.selectList("escloud.issuemapper.selectIssueList", map);
    }


    @Override
    public List<Issue> getIssuesForMis(int pageIndex, int size, String userId, String department, String issueStatus, String issueType, String startTime, String endTime, String machineRegion, String serviceCode, String productCode, String erpSystemCode, String esSearch) {
        log.info("userId="+userId);
        log.info("department="+department);
        log.info("serviceCode="+serviceCode);
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("start", (pageIndex - 1) * size);
        map.put("end", size);
        map.put("issueStatus", issueStatus);
        map.put("issueType", issueType);
        map.put("startTime", startTime);
        map.put("endTime", endTime);
        map.put("connectArea", connectArea);
        map.put("serviceRegion", machineRegion);
        map.put("serviceCode", serviceCode);
        map.put("productCode", productCode);
        map.put("erpSystemCode", erpSystemCode);
        map.put("esSearch",esSearch);
        if(department == null || department.isEmpty()){
            map.put("department", "");
            map.put("userId", userId);
        }else{
            map.put("department", department);
            map.put("userId", "");
        }
        return sqlSession.selectList("escloud.issuemapper.getIssuesForMis", map);
    }

    @Override
    public List<Issue> SelectIssueListByStaff(String serviceId, String status, int start, int end, String deptId, String queryUserId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("userId", serviceId);
        map.put("issuestatus", status);
        map.put("start", start);
        map.put("end", end);
        map.put("department", deptId);
        if (deptId.equals(""))
            map.put("queryUserId", "");
        else
            map.put("queryUserId", queryUserId);
        return sqlSession.selectList("escloud.issuemapper.selectIssueListByStaff", map);
    }

    @Override
    public List<Issue> SelectIssueListByServiceCode(String serviceCode, String productCode, String status, int start, int end) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("userId", serviceCode);
        map.put("productCode", productCode);
        map.put("issuestatus", status);
        map.put("start", start);
        map.put("end", end);
        map.put("processType", IssueProcessType.Synchro.toString());
        return sqlSession.selectList("escloud.issuemapper.selectIssueListByServiceCode", map);
    }

    @Override
    public Issue SelectIssueByIssueId(String issueId,String from, String userId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueId", issueId);
        map.put("from", from);
        map.put("userId", userId);
        map.put("processType", IssueProcessType.Synchro.toString());
        return sqlSession.selectOne("escloud.issuemapper.selectIssueByIssueId", map);
    }

    @Override
    public Issue SelectIssueByIssueIdForMis(String issueId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueId", issueId);
        map.put("processType", IssueProcessType.Synchro.toString());
        return sqlSession.selectOne("escloud.issuemapper.SelectIssueByIssueIdForMis", map);
    }
    @Override
    public List<IssueProgress> SelectIssueProgress(String issueId,String userId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueId", issueId);
        map.put("userId", userId);
        return sqlSession.selectList("escloud.issuemapper.selectIssueProgressList", map);
    }
    @Override
    public List<IssueAdditionalExplanation> SelectIssueAdditionalExplanation(String issueId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueId", issueId);
        return sqlSession.selectList("escloud.issuemapper.selectIssueAdditionalExplanationList", map);
    }
    public IssueCasedetail SelectIssueCasedetail(String issueId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueId", issueId);
        return sqlSession.selectOne("escloud.issuemapper.selectIssueCasedetailList", map);
    }
    @Override
    public IssueKbshare SelectIssueKbshare(String issueId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueId", issueId);
        return sqlSession.selectOne("escloud.issuemapper.selectIssueKbShareList", map);
    }
    @Override
    public IssueCount SelectIssueCountByUserId(String userId, String productCode, String department, String queryUserId, String issueType, String serviceRegion, String condition_TW, String condition_check_TW, String agent,String newReply,String myself,String from, String serviceCode) {
        Map<String, Object> map = new HashMap<String, Object>();
        if(department == null || department.isEmpty()){
            map.put("department", "");
            map.put("userId", userId);
        }else{
            map.put("department", department);
            map.put("userId", userId);
        }
        /*map.put("productCode", productCode);*/
        if(StringUtils.isNotBlank(productCode)){
            //productCode01 存T产品线，因T产品线的案件 客服提交案件不对客户公开，productCode02存非T产品线
            map.put("productCode01", Arrays.asList(productCode.split(",")).stream().filter(k->k.equals("100") || k.equals("06") || k.equals("999") || k.equals("164") || k.equals("147")).collect(Collectors.toList()));
            map.put("productCode02", Arrays.asList(productCode.split(",")).stream().filter(k->!k.equals("100") && !k.equals("06") && !k.equals("999") && !k.equals("164") && !k.equals("147")).collect(Collectors.toList()));
        }else {
            map.put("productCode01",new ArrayList<>());
            map.put("productCode02",new ArrayList<>());
            map.put("productCode", productCode);
        }
        map.put("issueType", issueType);
        map.put("serviceRegion",serviceRegion !=null ? serviceRegion :"");
        map.put("connectArea", connectArea);
        map.put("condition_TW", condition_TW);
        map.put("condition_check_TW", condition_check_TW);
        map.put("agent", StringUtils.isEmpty(agent) ? "" : agent);
        map.put("newReply", StringUtils.isEmpty(newReply) ? "" : newReply);
        map.put("myself", StringUtils.isEmpty(myself) ? "" : myself);
        map.put("from", StringUtils.isEmpty(from) ? "" : from);
        map.put("submiterId", StringUtils.isEmpty(userId) ? "" : userId);
        map.put("serviceCode", StringUtils.isEmpty(serviceCode) ? "" : serviceCode);
        return sqlSession.selectOne("escloud.issuemapper.selectIssueCount", map);
    }

    @Override
    public IssueCount SelectIssueCountByUserIdAndDescription(String userId, String productCode, String department, String queryUserId, String issueType,SearchByItem[] searchByItems, String serviceRegion, String condition_TW, String condition_check_TW,String newReply,String myself,String from, String serviceCode) {
        Map<String, Object> map = new HashMap<String, Object>();
        if(department == null || department.isEmpty()){
            map.put("department", "");
            map.put("userId", userId);
        }else{
            map.put("department", department);
            map.put("userId", userId);
        }
        /*map.put("productCode", productCode);*/
        if(StringUtils.isNotBlank(productCode)){
            //productCode01 存T产品线，因T产品线的案件 客服提交案件不对客户公开，productCode02存非T产品线
            map.put("productCode01", Arrays.asList(productCode.split(",")).stream().filter(k->k.equals("100") || k.equals("06") || k.equals("999") || k.equals("164") || k.equals("147")).collect(Collectors.toList()));
            map.put("productCode02", Arrays.asList(productCode.split(",")).stream().filter(k->!k.equals("100") && !k.equals("06") && !k.equals("999") && !k.equals("164") && !k.equals("147")).collect(Collectors.toList()));
        }else {
            map.put("productCode01",new ArrayList<>());
            map.put("productCode02",new ArrayList<>());
            map.put("productCode", productCode);
        }
        map.put("issueType", issueType);
        map.put("searchByItems", searchByItems);
        map.put("searchByItemsLength", searchByItems != null ? searchByItems.length : 0);
        map.put("serviceRegion",serviceRegion !=null ? serviceRegion :"");
        map.put("connectArea", connectArea);
        map.put("condition_TW", condition_TW);
        map.put("condition_check_TW", condition_check_TW);
        map.put("newReply", StringUtils.isEmpty(newReply) ? "" : newReply);
        map.put("myself", StringUtils.isEmpty(myself) ? "" : myself);
        map.put("from", StringUtils.isEmpty(from) ? "" : from);
        map.put("submiterId", StringUtils.isEmpty(userId) ? "" : userId);
        map.put("serviceCode", StringUtils.isEmpty(serviceCode) ? "" : serviceCode);
        return sqlSession.selectOne("escloud.issuemapper.selectIssueCountbydescription", map);
    }

    @Override
    public UserContact SelectIssueSubmiterMail(long issueId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueId", issueId);
        return sqlSession.selectOne("escloud.issuemapper.selectIssuerMail", map);
    }

    @Override
    public List<Map<String, String>> getWebIssueClassification(String serviceRegion, String customerServiceCode, String productCode) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("serviceRegion", serviceRegion);
        map.put("customerServiceCode", customerServiceCode);
        map.put("productCode", productCode);
        return sqlSession.selectList("escloud.issuemapper.getWebIssueClassification", map);
    }

    @Override
    public  List<ServiceProductCount> getServiceProductTarget(String serviceRegion, String staffId,String refreshDate)
    {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("serviceRegion", serviceRegion);
        map.put("staffId", staffId);
        map.put("refreshDate", refreshDate);
        return sqlSession.selectList("escloud.issuemapper.getServiceProductCount", map);
    }

    @Override
    public int PutUpdateRequestIssueStatus(String queueId, String processor) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("queueId", queueId);
        int res = sqlSession.update("escloud.issuemapper.putUpdateRequestIssueStatus", map);
        if (res > 0) {
            Map<String, Object> insertmap = new HashMap<String, Object>();
            insertmap.put("queueId", queueId);
            insertmap.put("processor", processor);
            sqlSession.update("escloud.issuemapper.insertUpdateRequestIssueCloseProgress", insertmap);
        }
        return res;
    }


    @Override
    public int UpdateIssueCrmId(long issueId, String crmId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueId", issueId);
        map.put("crmId", crmId);
        return sqlSession.selectOne("escloud.issuemapper.UpdateIssueCrmId", map);
    }

    @Override
    public int UpdateIssueTime(long issueId, String planDownloadTime, String planUpdateTime) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("planDownloadTime", planDownloadTime);
        map.put("planUpdateTime", planUpdateTime);
        map.put("issueId", issueId);
        return sqlSession.update("escloud.issuemapper.updateIssueTime", map);

    }
@Override
    public List<Issue> SelectUpdateIssueListByStaff(String serviceId, String status,String issueType, int start, int end, String deptId, String queryUserId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("userId", serviceId);
        map.put("issuestatus", status);
        map.put("start", start);
        map.put("issueType", issueType);
        map.put("end", end);
        map.put("department", deptId);
        if (deptId.equals(""))
            map.put("queryUserId", "");
        else
            map.put("queryUserId", queryUserId);
        return sqlSession.selectList("escloud.issuemapper.selectIssueListByStaff", map);
    }

    @Override
    public int getNewIssuesInfoCountByUserAndStatus(String issueStatusParam,String userId,String deptId
            ,String issueType,String queryUserId,String startTime,String endTime, String machineRegion, String additionalExplanationReadType,String serviceContact,String projectUpdate,String serialNumberUpdate,String custLevelParam, String serviceCode, String productCodeParam, String employeeId, String crmId,String supportId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("userId", userId);
        map.put("department", deptId);
        StringBuffer issueStatus = new StringBuffer();
        if(!StringUtils.isEmpty(issueStatusParam)){
            String[] issueStatusArr = issueStatusParam.split(",");
            if(!ArrayUtils.isEmpty(issueStatusArr)){
                for(String issueStatusTemp :issueStatusArr){
                    if(!StringUtils.isEmpty(issueStatusTemp)){
                        if(issueStatusTemp.contains("N")){
                            issueStatus.append("'I', 'C', 'N','2','3','4','O','VN','11','12','22'").append(",");
                        }else  if(issueStatusTemp.contains("Y")){
                            issueStatus.append("'Y','7','10'").append(",");
                        }else {
                            issueStatus.append("'").append(issueStatusTemp).append("',");
                        }
                    }
                }
            }
        }
        if(!StringUtils.isEmpty(issueStatus)){
            map.put("issueStatus", issueStatus.substring(0,issueStatus.length()-1));
        }
        map.put("issueType", issueType);
        if (deptId.equals(""))
            map.put("queryUserId", "");
        else
            map.put("queryUserId", queryUserId);
        map.put("startTime", startTime);
        map.put("endTime", endTime);
        map.put("connectArea", connectArea);
        map.put("serviceRegion", machineRegion);
        map.put("additionalExplanationReadType",additionalExplanationReadType);
        map.put("serviceContact",serviceContact);
        map.put("projectUpdate",projectUpdate);
        map.put("serialNumberUpdate",serialNumberUpdate);
//        StringBuffer custLevel = new StringBuffer();
        if(!StringUtils.isEmpty(custLevelParam)){
            String[] custLevelArr = custLevelParam.replaceAll(" ", "").split(",");

            String custLevel = Arrays.stream(custLevelArr)
                    .collect(Collectors.joining( "','", "'", "'"));

            if(custLevelParam.contains("NULL")){
                map.put("custLevelContainNull", "NULL");
            } else if(custLevelParam.contains("None")){
                // 台灣區 None 無合約
                map.put("custLevelContainNull", "None");
            }
            if(!StringUtils.isEmpty(custLevel)){
                map.put("custLevel", custLevel.substring(0,custLevel.length()-1));
            }
        }

        map.put("serviceCode", serviceCode);
        if(StringUtils.isNotBlank(productCodeParam)){
            String[] productCodeArr = productCodeParam.replaceAll(" ", "").split(",");
            map.put("productCode",
                    Arrays.stream(productCodeArr).collect(Collectors.joining( "','", "'", "'")));
        }
        map.put("crmId", crmId);
        map.put("serviceId", employeeId);
        map.put("supportId", supportId);

        return sqlSession.selectOne("escloud.issuemapper.selectNewIssueCountByWorknoAndStatus", map);
    }

    @Override
    public int getIssuesInfoCountV2(String issueStatusParam,String userId,String deptId
            ,String issueType,String queryUserId,String startTime,String endTime, String machineRegion, String custLevelParam, String contractState, String serviceCode, String productCodeParam, List<ProductInfo> productList,String employeeId,String crmId,  String additionalExplanationReadType,String issueDescription) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("userId", userId);
        map.put("department", deptId);
        StringBuffer issueStatus = new StringBuffer();
        if(!StringUtils.isEmpty(issueStatusParam)){
            String[] issueStatusArr = issueStatusParam.split(",");
            if(issueStatusArr != null && issueStatusArr.length>0){
                for(int i =0;i<issueStatusArr.length;i++){
                    String issueStatusTemp = issueStatusArr[i];
                    if(!StringUtils.isEmpty(issueStatusTemp)){
                        if(issueStatusTemp.contains("N")){
                            issueStatus.append("'I', 'C', 'N','2','3','4','O','VN','11','12','22'").append(",");
                        }else  if(issueStatusTemp.contains("Y")){
                            issueStatus.append("'Y','7','10'").append(",");
                        }else {
                            issueStatus.append("'").append(issueStatusTemp).append("',");
                        }
                    }
                }
            }
        }
        if(!StringUtils.isEmpty(issueStatus)){
            map.put("issueStatus", issueStatus.substring(0,issueStatus.length()-1));
        }
        map.put("issueType", issueType);
        map.put("queryUserId", employeeId);
        map.put("startTime", startTime);
        map.put("endTime", endTime);
        map.put("connectArea", connectArea);
        map.put("serviceRegion", machineRegion);
        StringBuffer custLevel = new StringBuffer();
        if(!StringUtils.isEmpty(custLevelParam)){
            String[] custLevelArr = custLevelParam.split(",");
            if(custLevelArr != null && custLevelArr.length>0){
                for(int i =0;i<custLevelArr.length;i++){
                    String custLevelTemp = custLevelArr[i];
                    if(!StringUtils.isEmpty(custLevelTemp)){
                         custLevel.append("'").append(custLevelTemp).append("',");
                    }
                }
            }

            if(custLevelParam.contains("NULL")){
                map.put("custLevelContainNull", "NULL");
            }
            if(!StringUtils.isEmpty(custLevel)){
                map.put("custLevel", custLevel.substring(0,custLevel.length()-1));
            }
        }

        map.put("contractState", contractState);
        map.put("serviceCode", serviceCode);
        StringBuffer productCode = new StringBuffer();
        if(!StringUtils.isEmpty(productCodeParam)){
            String[] productCodeArr = productCodeParam.split(",");
            if(productCodeArr != null && productCodeArr.length>0){
                for(int i =0;i<productCodeArr.length;i++){
                    String productCodeTemp = productCodeArr[i];
                    if(!StringUtils.isEmpty(productCodeTemp)){
                        productCode.append("'").append(productCodeTemp).append("',");
                    }
                }
            }
            if(!StringUtils.isEmpty(productCode)){
                map.put("productCode", productCode.substring(0,productCode.length()-1));
            }
        }
        if(deptId == null || deptId.isEmpty()){
            map.put("department", "");
            map.put("userId", userId);
        }else{
            map.put("department", deptId);
            map.put("userId", "");
        }
        map.put("productList", productList);
        map.put("crmId", crmId);
        map.put("additionalExplanationReadType",additionalExplanationReadType);
        map.put("issueDescription",issueDescription);
        return sqlSession.selectOne("escloud.issuemapper.getIssuesInfoCountV2", map);
    }

    @Override
    public int getIssuesInfoCountForCustomerServiceManager(String issueStatusParam,String userId,String deptId
            ,String issueType,String queryUserId,String startTime,String endTime, String machineRegion, String custLevelParam, String serviceCode, String productCodeParam, List<ProductInfo> productList,String employeeId,String crmId,  String additionalExplanationReadType,List<IssueProductCodeClassificationData> issueProductCodeClassificationList) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("userId", userId);
        map.put("department", deptId);
        StringBuffer issueStatus = new StringBuffer();
        if(!StringUtils.isEmpty(issueStatusParam)){
            String[] issueStatusArr = issueStatusParam.split(",");
            if(issueStatusArr != null && issueStatusArr.length>0){
                for(int i =0;i<issueStatusArr.length;i++){
                    String issueStatusTemp = issueStatusArr[i];
                    if(!StringUtils.isEmpty(issueStatusTemp)){
                        if(issueStatusTemp.contains("N")){
                            issueStatus.append("'I', 'C', 'N','2','3','4','O','VN','11','12','22'").append(",");
                        }else  if(issueStatusTemp.contains("Y")){
                            issueStatus.append("'Y','7','10'").append(",");
                        }else {
                            issueStatus.append("'").append(issueStatusTemp).append("',");
                        }
                    }
                }
            }
        }
        if(!StringUtils.isEmpty(issueStatus)){
            map.put("issueStatus", issueStatus.substring(0,issueStatus.length()-1));
        }
        map.put("issueType", issueType);
        map.put("queryUserId", employeeId);
        map.put("startTime", startTime);
        map.put("endTime", endTime);
        map.put("connectArea", connectArea);
        map.put("serviceRegion", machineRegion);
        StringBuffer custLevel = new StringBuffer();
        if(!StringUtils.isEmpty(custLevelParam)){
            String[] custLevelArr = custLevelParam.split(",");
            if(custLevelArr != null && custLevelArr.length>0){
                for(int i =0;i<custLevelArr.length;i++){
                    String custLevelTemp = custLevelArr[i];
                    if(!StringUtils.isEmpty(custLevelTemp)){
                        custLevel.append("'").append(custLevelTemp).append("',");
                    }
                }
            }

            if(custLevelParam.contains("NULL")){
                map.put("custLevelContainNull", "NULL");
            }
            if(!StringUtils.isEmpty(custLevel)){
                map.put("custLevel", custLevel.substring(0,custLevel.length()-1));
            }
        }

        map.put("serviceCode", serviceCode);
        StringBuffer productCode = new StringBuffer();
        if(!StringUtils.isEmpty(productCodeParam)){
            String[] productCodeArr = productCodeParam.split(",");
            if(productCodeArr != null && productCodeArr.length>0){
                for(int i =0;i<productCodeArr.length;i++){
                    String productCodeTemp = productCodeArr[i];
                    if(!StringUtils.isEmpty(productCodeTemp)){
                        productCode.append("'").append(productCodeTemp).append("',");
                    }
                }
            }
            if(!StringUtils.isEmpty(productCode)){
                map.put("productCode", productCode.substring(0,productCode.length()-1));
            }
        }

        map.put("productList", productList);
        map.put("crmId", crmId);
        map.put("additionalExplanationReadType",additionalExplanationReadType);
        map.put("productCodeClassificationList", issueProductCodeClassificationList);
        return sqlSession.selectOne("escloud.issuemapper.selectNewIssueCountForCustomerServiceManager", map);
    }

    @Override
    public List<IssueDetailInfo> getIssuesInfoListByUserAndStatusForCustomerServiceManager(String issueStatusParam, int pageIndex, int size, String userId, String deptId
            , String issueType, String queryUserId, String startTime, String endTime, String machineRegion, String custLevelParam, String serviceCode, String productCodeParam, List<ProductInfo> productList, String employeeId, String crmId, String additionalExplanationReadType,List<IssueProductCodeClassificationData> issueProductCodeClassificationList) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("userId", userId);
        map.put("department", deptId);
        StringBuffer issueStatus = new StringBuffer();
        if(!StringUtils.isEmpty(issueStatusParam)){
            String[] issueStatusArr = issueStatusParam.split(",");
            if(issueStatusArr != null && issueStatusArr.length>0){
                for(int i =0;i<issueStatusArr.length;i++){
                    String issueStatusTemp = issueStatusArr[i];
                    if(!StringUtils.isEmpty(issueStatusTemp)){
                        if(issueStatusTemp.contains("N")){
                            issueStatus.append("'I', 'C', 'N','2','3','4','O','VN','11','12','22'").append(",");
                        }else  if(issueStatusTemp.contains("Y")){
                            issueStatus.append("'Y','7','10'").append(",");
                        }else {
                            issueStatus.append("'").append(issueStatusTemp).append("',");
                        }
                    }
                }
            }
        }
        if(!StringUtils.isEmpty(issueStatus)){
            map.put("issueStatus", issueStatus.substring(0,issueStatus.length()-1));
        }

        map.put("issueType", issueType);
        map.put("queryUserId", employeeId);
        map.put("startTime", startTime);
        map.put("endTime", endTime);
        map.put("start", pageIndex < 1 ? 0 : (pageIndex - 1) * size);
        map.put("size", size);
        map.put("connectArea", connectArea);
        map.put("serviceRegion", machineRegion);
        StringBuffer custLevel = new StringBuffer();
        if(!StringUtils.isEmpty(custLevelParam)){
            String[] custLevelArr = custLevelParam.split(",");
            if(custLevelArr != null && custLevelArr.length>0){
                for(int i =0;i<custLevelArr.length;i++){
                    String custLevelTemp = custLevelArr[i];
                    if(!StringUtils.isEmpty(custLevelTemp)){
                        custLevel.append("'").append(custLevelTemp).append("',");
                    }
                }
            }

            if(custLevelParam.contains("NULL")){
                map.put("custLevelContainNull", "NULL");
            }
            if(!StringUtils.isEmpty(custLevel)){
                map.put("custLevel", custLevel.substring(0,custLevel.length()-1));
            }
        }

        map.put("serviceCode", serviceCode);
        StringBuffer productCode = new StringBuffer();
        if(!StringUtils.isEmpty(productCodeParam)){
            String[] productCodeArr = productCodeParam.split(",");
            if(productCodeArr != null && productCodeArr.length>0){
                for(int i =0;i<productCodeArr.length;i++){
                    String productCodeTemp = productCodeArr[i];
                    if(!StringUtils.isEmpty(productCodeTemp)){
                        productCode.append("'").append(productCodeTemp).append("',");
                    }
                }
            }
            if(!StringUtils.isEmpty(productCode)){
                map.put("productCode", productCode.substring(0,productCode.length()-1));
            }
        }

        map.put("productList", productList);
        map.put("crmId", crmId);
        map.put("additionalExplanationReadType",additionalExplanationReadType);
        map.put("productCodeClassificationList", issueProductCodeClassificationList);
        return sqlSession.selectList("escloud.issuemapper.selectNewIssueListForCustomerServiceManager", map);
    }




    @Override
    public int getNewIssuesInfoCountByKewei(String issueStatus,String issueType,String additionalExplanationReadType, String productCode) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueStatus", issueStatus);
        map.put("issueType", issueType);
        map.put("connectArea", connectArea);
        map.put("additionalExplanationReadType",additionalExplanationReadType);
        map.put("productCode",productCode);
        return sqlSession.selectOne("escloud.issuemapper.selectNewIssueCountByKewei", map);
    }

    @Override
    public List<IssueDetailInfo> getNewIssuesInfoListByUserAndStatus(String issueStatusParam,int pageIndex, int size,String userId,String deptId
            ,String issueType,String queryUserId,String startTime,String endTime, String machineRegion, String additionalExplanationReadType, String serviceContact, String projectUpdate, String serialNumberUpdate,String custLevelParam, String serviceCode, String productCodeParam, String employeeId, String crmId,String supportId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("userId", userId);
        map.put("department", deptId);
        StringBuffer issueStatus = new StringBuffer();
        if(!StringUtils.isEmpty(issueStatusParam)){
            String[] issueStatusArr = issueStatusParam.split(",");
            if(!ArrayUtils.isEmpty(issueStatusArr)){
                for(String issueStatusTemp:issueStatusArr) {
                    if(!StringUtils.isEmpty(issueStatusTemp)){
                        if(issueStatusTemp.contains("N")){
                            issueStatus.append("'I', 'C', 'N','2','3','4','O','VN','11','12','22'").append(",");
                        }else  if(issueStatusTemp.contains("Y")){
                            issueStatus.append("'Y','7','10'").append(",");
                        }else {
                            issueStatus.append("'").append(issueStatusTemp).append("',");
                        }
                    }
                }
            }
        }
        if(!StringUtils.isEmpty(issueStatus)){
            map.put("issueStatus", issueStatus.substring(0,issueStatus.length()-1));
        }
        map.put("issueType", issueType);
        if (deptId.equals(""))
            map.put("queryUserId", "");
        else
            map.put("queryUserId", queryUserId);
        map.put("startTime", startTime);
        map.put("endTime", endTime);
        map.put("start", pageIndex < 1 ? 0 : (pageIndex - 1) * size);
        map.put("size", size);
        map.put("connectArea", connectArea);
        map.put("serviceRegion", machineRegion);
        map.put("additionalExplanationReadType",additionalExplanationReadType);
        map.put("serviceContact",serviceContact);
        map.put("projectUpdate",projectUpdate);
        map.put("serialNumberUpdate",serialNumberUpdate);
        StringBuffer custLevel = new StringBuffer();
        if(!StringUtils.isEmpty(custLevelParam)){
            String[] custLevelArr = custLevelParam.split(",");
            if(!ArrayUtils.isEmpty(custLevelArr)){
                for(String custLevelTemp :custLevelArr){
                    if(!StringUtils.isEmpty(custLevelTemp)){
                        custLevel.append("'").append(custLevelTemp).append("',");
                    }
                }
            }
            if(custLevelParam.contains("NULL")){
                map.put("custLevelContainNull", "NULL");
            } else if(custLevelParam.contains("None")){
                // 台灣區 None 無合約
                map.put("custLevelContainNull", "None");
            }
            if(!StringUtils.isEmpty(custLevel)){
                map.put("custLevel", custLevel.substring(0,custLevel.length()-1));
            }
        }
        map.put("serviceCode", serviceCode);
        StringBuffer productCode = new StringBuffer();
        if(!StringUtils.isEmpty(productCodeParam)){
            String[] productCodeArr = productCodeParam.split(",");
            if(!ArrayUtils.isEmpty(productCodeArr)){
                for(String productCodeTemp:productCodeArr){
                    if(!StringUtils.isEmpty(productCodeTemp)){
                        productCode.append("'").append(productCodeTemp).append("',");
                    }
                }
            }
            if(!StringUtils.isEmpty(productCode)){
                map.put("productCode", productCode.substring(0,productCode.length()-1));
            }
        }
        map.put("crmId", crmId);
        map.put("serviceId", employeeId);
        map.put("supportId", supportId);

        return sqlSession.selectList("escloud.issuemapper.selectNewIssueListByWorknoAndStatus", map);
    }

    @Override
    public List<IssueDetailInfo> getAllIssueDetails(String issueStatusParam, int pageIndex, int size, String userId, String deptId
            , String issueType, String queryUserId, String startTime, String endTime, String machineRegion, String custLevelParam, String contractState, String serviceCode, String productCodeParam, List<ProductInfo> productList, String employeeId, String crmId, String additionalExplanationReadType, String issueDescription) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("userId", userId);
        map.put("department", deptId);
        StringBuffer issueStatus = new StringBuffer();
        if(!StringUtils.isEmpty(issueStatusParam)){
            String[] issueStatusArr = issueStatusParam.split(",");
            if(!ArrayUtils.isEmpty(issueStatusArr)){
                for (String issueStatusTemp : issueStatusArr) {
                    if (!StringUtils.isEmpty(issueStatusTemp)) {
                        if (issueStatusTemp.contains("N")) {
                            issueStatus.append("'I', 'C', 'N','2','3','4','O','VN','11','12','22'").append(",");
                        } else if (issueStatusTemp.contains("Y")) {
                            issueStatus.append("'Y','7','10'").append(",");
                        } else {
                            issueStatus.append("'").append(issueStatusTemp).append("',");
                        }
                    }
                }
            }
        }
        if(!StringUtils.isEmpty(issueStatus)){
            map.put("issueStatus", issueStatus.substring(0,issueStatus.length()-1));
        }

        map.put("issueType", issueType);
        map.put("queryUserId", employeeId);
        map.put("startTime", startTime);
        map.put("endTime", endTime);
        map.put("start", pageIndex < 1 ? 0 : (pageIndex - 1) * size);
        map.put("size", size);
        map.put("connectArea", connectArea);
        map.put("serviceRegion", machineRegion);
        StringBuffer custLevel = new StringBuffer();
        if(!StringUtils.isEmpty(custLevelParam)){
            String[] custLevelArr = custLevelParam.split(",");
            if(!ArrayUtils.isEmpty(custLevelArr)){
                for (String custLevelTemp : custLevelArr) {
                    if (!StringUtils.isEmpty(custLevelTemp)) {
                        custLevel.append("'").append(custLevelTemp).append("',");
                    }
                }
            }

            if(custLevelParam.contains("NULL")){
                map.put("custLevelContainNull", "NULL");
            }
            if(!StringUtils.isEmpty(custLevel)){
                map.put("custLevel", custLevel.substring(0,custLevel.length()-1));
            }
        }

        map.put("contractState", contractState);
        map.put("serviceCode", serviceCode);
        StringBuffer productCode = new StringBuffer();
        if(!StringUtils.isEmpty(productCodeParam)){
            String[] productCodeArr = productCodeParam.split(",");
            if(!ArrayUtils.isEmpty(productCodeArr)){
                for (String productCodeTemp : productCodeArr) {
                    if (!StringUtils.isEmpty(productCodeTemp)) {
                        productCode.append("'").append(productCodeTemp).append("',");
                    }
                }
            }
            if(!StringUtils.isEmpty(productCode)){
                map.put("productCode", productCode.substring(0,productCode.length()-1));
            }
        }
        if(deptId == null || deptId.isEmpty()){
            map.put("department", "");
            map.put("userId", userId);
        }else{
            map.put("department", deptId);
            map.put("userId", "");
        }
        map.put("productList", productList);
        map.put("crmId", crmId);
        map.put("additionalExplanationReadType",additionalExplanationReadType);
        map.put("issueDescription",issueDescription);
        return sqlSession.selectList("escloud.issuemapper.getAllIssueDetails", map);
    }
    @Override
    public IssueProgress SelectIssueProgressById(long id) {
        return sqlSession.selectOne("escloud.issuemapper.selectIssueProgressById",id);
    }

    @Override
    public int UpdateIssueProgressById(IssueProgress issueProgress) {
        return sqlSession.update("escloud.issuemapper.UpdateIssueProgressById",issueProgress);
    }

    @Override
    public int UpdateIssueProgress(IssueProgress issueProgress) {
        return sqlSession.update("escloud.issuemapper.UpdateIssueProgress",issueProgress);
    }
    @Override
    public int UpdateIssueProductVersion(Issue issue){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueId", issue.getIssueId());
        return sqlSession.update("escloud.issuemapper.UpdateIssueProductVersion",map);
    }

    @Override
    public int getMaxOrder(long  issueId){
        return sqlSession.selectOne("escloud.issuemapper.getMaxOrder",issueId);
    }
    @Override
    public int getProcessMaxSeqNum(long  issueId){
        return sqlSession.selectOne("escloud.issuemapper.getProcessMaxSeqNum",issueId);
    }
    @Override
    public List<Issue> SelectIssueListbydescription(String userId, String productCode, String status, String issueType, int start, int end, String department, String queryUserId, SearchByItem[] searchByItems, OrderByItem[] orderByItems, String serviceRegion, String waitingEvaluatedIssue_TW, String condition_TW, String condition_check_TW,String agent, String newReply, String myself, String from,String serviceCode, Boolean userCollect) {
        Map<String, Object> map = new HashMap<String, Object>();
        /*map.put("productCode", productCode);*/
        if(StringUtils.isNotBlank(productCode)){
            //productCode01 存T产品线，因T产品线的案件 客服提交案件不对客户公开，productCode02存非T产品线
            map.put("productCode01", Arrays.asList(productCode.split(",")).stream().filter(k->k.equals("100") || k.equals("06") || k.equals("999") || k.equals("164") || k.equals("147")).collect(Collectors.toList()));
            map.put("productCode02", Arrays.asList(productCode.split(",")).stream().filter(k->!k.equals("100") && !k.equals("06") && !k.equals("999") && !k.equals("164") && !k.equals("147")).collect(Collectors.toList()));
        }else {
            map.put("productCode01",new ArrayList<>());
            map.put("productCode02",new ArrayList<>());
            map.put("productCode", productCode);
        }
        map.put("issuestatus", status);
        map.put("issueType", issueType);
        map.put("start", start);
        map.put("end", end);
        map.put("processType", IssueProcessType.Synchro.toString());
        if(department == null || department.isEmpty()){
            map.put("department", "");
            map.put("userId", userId);
        }else{
            map.put("department", department);
            map.put("userId", "");
        }
        map.put("searchByItems", searchByItems);
        map.put("searchByItemsLength", searchByItems != null ? searchByItems.length : 0);
        map.put("orderByItems", orderByItems);
        map.put("orderByItemsLength", orderByItems != null ? orderByItems.length : 0);
        map.put("serviceRegion",serviceRegion !=null ? serviceRegion :"");
        map.put("connectArea", connectArea);
        map.put("waitingEvaluatedIssue_TW", waitingEvaluatedIssue_TW == null || waitingEvaluatedIssue_TW.isEmpty() ?"":waitingEvaluatedIssue_TW);
        map.put("condition_TW", StringUtils.isEmpty(condition_TW) ? "" : condition_TW);
        map.put("condition_check_TW", StringUtils.isEmpty(condition_check_TW) ? "" : condition_check_TW);
        map.put("agent", StringUtils.isEmpty(agent) ? "" : agent);
        map.put("newReply", StringUtils.isEmpty(newReply) ? "" : newReply);
        map.put("myself", StringUtils.isEmpty(myself) ? "" : myself);
        map.put("from", StringUtils.isEmpty(from) ? "" : from);
        map.put("submiterId", StringUtils.isEmpty(userId) ? "" : userId);
        map.put("serviceCode", StringUtils.isEmpty(serviceCode) ? "" : serviceCode);
        map.put("userCollect", Boolean.TRUE.equals(userCollect) ? "1" : "");
        return sqlSession.selectList("escloud.issuemapper.selectIssueListbydescription", map);
    }

    @Override
    public List<String> SelectEmails(long id) {
        return sqlSession.selectList("escloud.issuemapper.SelectEmail",id);
    }

    @Override
    public List<String> SelectEmailsBySelfUpdateSystem(long id) {
        return sqlSession.selectList("escloud.issuemapper.SelectEmailsBySelfUpdateSystem",id);
    }

    @Override
    public String GetIssueFollowUpLastTime(long issueId){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueId", issueId);
        map.put("processType", IssueProcessType.FollowUp.toString());
        return sqlSession.selectOne("escloud.issuemapper.SelectFollowUpLastTime",map);
    }

    @Override
    public List<IssueDetailInfo> getWebIssuesInfoList(String issueStatus,String productCode,String codeOrName,int pageIndex, int size,
                                                      String issueType,String startTime,String endTime) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueStatus", issueStatus);
        map.put("productCode", productCode);
        map.put("codeOrName", codeOrName);
        map.put("issueType", issueType);
        map.put("connectArea", connectArea);
        map.put("startTime", startTime);
        map.put("endTime", endTime);
        map.put("start", pageIndex < 1 ? 0 : (pageIndex - 1) * size);
        map.put("size", size);
        return sqlSession.selectList("escloud.issuemapper.selectWebIssuesInfoList", map);
    }

    @Override
    public int getWebIssuesCount(String issueStatus,String productCode,String codeOrName,String issueType,String startTime,String endTime) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueStatus", issueStatus);
        map.put("productCode", productCode);
        map.put("codeOrName", codeOrName);
        map.put("issueType", issueType);
        map.put("startTime", startTime);
        map.put("endTime", endTime);
        return sqlSession.selectOne("escloud.issuemapper.selectWebIssuesCount", map);
    }

    @Override
    public List<String> getCustomerIssueServiceRegionInfo(String customerServiceCode){
        return sqlSession.selectList("escloud.issuemapper.SelectCustomerIssueServiceRegion",customerServiceCode);
    }

    @Override
    public List<String> getMachineRegionByStaffId(String staffId){
        // 20200313 找MachineRegion可能會造成前端(易聊-案件管理)無對應的文字，所以改找ServiceRegion
        //return sqlSession.selectList("escloud.issuemapper.SelectIssueMachineRegionByStaffId",staffId);
        return sqlSession.selectList("escloud.issuemapper.SelectIssueServiceRegionByStaffId",staffId);
    }
    @Override
    public int UpdateAdditionalExplanationReadType(String issueId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueId", issueId);
        return sqlSession.update("escloud.issuemapper.UpdateIssueAdditionalExplanationReadType",map);
    }
    @Override
    public int UpdateIssueDescription(IssueDetailInfo issue) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueId", issue.getIssueId());
        map.put("issueDescription", issue.getIssuedescription());
        map.put("ServiceId", issue.getServiceId());
        return sqlSession.update("escloud.issuemapper.UpdateIssueDescription",map);
    }
    @Override
    public int updateIssueSummary(String issueId,String type,String processTime) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueId", issueId);
        map.put("type", type);
        map.put("processTime", processTime);
        return sqlSession.update("escloud.issuemapper.UpdateIssueSummary",map);
    }
    @Override
    public int insertIssueSummary(String issueId,String type,String processTime) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueId", issueId);
        map.put("type", type);
        map.put("processTime", processTime);
        return sqlSession.update("escloud.issuemapper.InsertIssueSummary",map);
    }
    @Override
    public int updateIssueSummaryForAgreeClose(String issueId,String processTime) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueId", issueId);
        map.put("processTime", processTime);
        return sqlSession.update("escloud.issuemapper.UpdateIssueSummaryForAgreeClose",map);
    }
    @Override
    public int insertIssueSummaryForAgreeClose(String issueId,String processTime) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueId", issueId);
        map.put("processTime", processTime);
        return sqlSession.update("escloud.issuemapper.InsertIssueSummaryForAgreeClose",map);
    }
    @Override
    public IssueSummary getIssueSummary(String issueId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueId", issueId);
        return sqlSession.selectOne("escloud.issuemapper.getIssueSummary",map);
    }
    @Override
    public List<IssueDetailInfo> getIssuesDetailInfoByWorkNo(String workNo, String issueStatus) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("workNo", workNo);
        map.put("issueStatus", issueStatus);
        return sqlSession.selectList("escloud.issuemapper.getIssuesDetailInfoByWorkNo",map);
    }

    @Override
    public List<IssueDetailInfo> getIssuesDetailInfoByWorkNoFromNewIssue(String deptCode, String workNo,String color, String issueStatus ) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("deptCode", deptCode);
        map.put("workNo", workNo);
        map.put("color", color);
        map.put("issueStatus", issueStatus);
        return sqlSession.selectList("escloud.issuemapper.getIssuesDetailInfoByWorkNoFromNewIssue",map);
    }
	
    @Override
    public int SelectSubmitedIssueCount(String serviceCode,String productCode,String contractStartDate,String contractExprityDate){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("serviceCode", serviceCode);
        map.put("productCode", productCode);
        map.put("contractStartDate", contractStartDate);
        map.put("contractExprityDate", contractExprityDate);
        //不論甚麼方式所建立的案件(雲管家-提案、CRM立案、客服易聊-立案...)都算
        //20201014 增加限制 由客服後台立案(ITMS_Service)與客服易聊-立案(SIM...)的都不算在立案限制次數中
        return sqlSession.selectOne("escloud.issuemapper.selectTrialSubmitedIssueCount",map);
	}

    @Override
    public boolean updateIssueService(String issueId, String serviceId, String syncStatus, String department,String productCode) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueId", issueId);
        map.put("serviceId", serviceId);
        map.put("syncStatus", syncStatus);
        map.put("department", department);
        map.put("productCode", productCode);

        return sqlSession.update("escloud.issuemapper.updateIssueService",map)>0;
    }

    @Override
    public boolean cancelCloseIssue(String issueId, String syncStatus) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueId", issueId);
        map.put("syncStatus", syncStatus);
        return sqlSession.update("escloud.issuemapper.cancelCloseIssue",map)>0;
    }

    @Override
    public boolean updateCurrentStatus(long progressId, String status,String issueSyncStatus) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("progressId", progressId);
        map.put("status", status);
        map.put("syncStatus", issueSyncStatus);
        return sqlSession.update("escloud.issuemapper.updateCurrentStatus",map)>0;
    }
    @Override
    public Integer checkIssueIsSyncCrm(String serviceRegion, String productCode,String issueStatus) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("serviceRegion", serviceRegion);
        map.put("productCode", productCode);
        map.put("issueStatus", issueStatus);
        return sqlSession.selectOne("escloud.issuemapper.checkIssueIsSyncCrm",map);
    }

    @Override
    public String getIssueSyncStatus(String issueId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueId", issueId);
        return sqlSession.selectOne("escloud.issuemapper.getIssueSyncStatus",map);
    }

    @Override
    public String getIssueStatus(String crmId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("crmId", crmId);
        return sqlSession.selectOne("escloud.issuemapper.getIssueStatus",map);
    }

    @Override
    public String getUserIdbyMail(String mail,String serviceCode) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("mail", mail);
        map.put("serviceCode", serviceCode);
        return sqlSession.selectOne("escloud.issuemapper.getUserIdbyMail",map);
    }

    @Override
    public String getDefaultPhonebyServiceCode(String serviceCode) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("serviceCode", serviceCode);
        return sqlSession.selectOne("escloud.issuemapper.getDefaultPhonebyServiceCode",map);
    }

    @Override
    public String getMailbyPhoneFromMars_userpersonalinfo(String serviceCode, String phone, String extension , String mobilePhone) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("serviceCode", serviceCode);
        map.put("phone", phone);
        map.put("extension", extension);
        map.put("mobilePhone", mobilePhone);
        return sqlSession.selectOne("escloud.issuemapper.getMailbyPhoneFromMars_userpersonalinfo",map);
    }

    @Override
    public String getMailbyPhone(String serviceCode, String phone, String extension , String mobilePhone) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("serviceCode", serviceCode);
        map.put("phone", phone);
        map.put("extension", extension);
        map.put("mobilePhone", mobilePhone);
        String mailRegex = "^([a-z0-9A-Z\\u4e00-\\u9fa5]+[-|_#+!~$%&//.]?)+\\@[A-Za-z0-9]([\\.\\_\\-]?[A-Za-z0-9])+\\.+([A-Za-z])+$";
        map.put("regexp", mailRegex);
        return sqlSession.selectOne("escloud.issuemapper.getMailbyPhone",map);
    }

    @Override
    public int batchInsertWarningId(long issueId, String crmId, List<String> warningIdCollection) {
        warningIdCollection = Optional.ofNullable(warningIdCollection).orElse(new ArrayList<>());
        List<Map<String, Object>> listMap = warningIdCollection.stream()
                .filter(warningId -> StringUtils.isNotBlank(warningId))
                .map(warningId -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("warningId", warningId);
                    map.put("issueId", issueId);
                    map.put("crmId", crmId);
                    return map;
                })
                .collect(Collectors.toList());
        if (warningIdCollection.isEmpty()) {
            return 0;
        } else {
            return sqlSession.insert("escloud.issuemapper.batchInsertWarningId", listMap);
        }
    }
    @Override
    public int batchInsertSourceMap(long issueId, String crmId, List<IssueSourceMap> issueSourceMapList) {
        //新增跟這個 ISSUE 有關的项目任务
        issueSourceMapList = Optional.ofNullable(issueSourceMapList).orElse(new ArrayList<>());
        if (issueSourceMapList.isEmpty()) {
            return 0;
        } else {
            Map<String,Object> map = new HashMap<>();
            map.put("issueId",issueId);
            map.put("issueCode",crmId);
            map.put("issueSourceMapList",issueSourceMapList);
            return sqlSession.insert("escloud.issuemapper.batchInsertSourceMap", map);
        }
    }

    @Override
    public int updateWarningIdGroup(long issueId, String crmId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueId", issueId);
        map.put("crmId", crmId);
        return sqlSession.update("escloud.issuemapper.updateWarningIdGroup",map);
    }

    @Override
    public int updateIssueSourceMap(long issueId, String crmId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueId", issueId);
        map.put("crmId", crmId);
        return sqlSession.update("escloud.issuemapper.updateIssueSourceMap",map);
    }

    @Override
    public List<String> getWarningIdByIssueId(String issueId) {
        Map<String, Object> map = new HashMap<>();

        if (StringUtils.isBlank(issueId)) {
            throw new RuntimeException("getWarningIdByIssueId() error: issueId is empty.");
        }
        map.put("crmId", "");
        map.put("issueId", issueId);
        return sqlSession.selectList("escloud.issuemapper.getWarningIds",map);
    }

    @Override
    public List<String> getWarningIdByCrmId(String crmId) {
        Map<String, Object> map = new HashMap<>();

        if (StringUtils.isBlank(crmId)) {
            throw new RuntimeException("getWarningIdByIssueId() error: crmId is empty.");
        }
        map.put("crmId", crmId);
        map.put("issueId", "");
        return sqlSession.selectList("escloud.issuemapper.getWarningIds",map);
    }

    @Override
    public List<Issue> getIssueSourceMapList(Map<String, Object> map){
        return sqlSession.selectList("escloud.issuemapper.getIssueSourceMapList",map);
    }
    @Override
    public long getIssueSourceMapCount(Map<String, Object> map){
        return sqlSession.selectOne("escloud.issuemapper.getIssueSourceMapCount",map);
    }
    @Override
    public IssueSum getIssueSum(Map<String,Object> map){
        return sqlSession.selectOne("escloud.issuemapper.getIssueSum",map);
    }

    @Override
    public int deleteProgress(long id){
        Map<String, Object> map = new HashMap<>();
        map.put("id", id);
        return sqlSession.delete("escloud.issuemapper.deleteProgress",map);
    }
    @Override
    public StaffUserInfo getDefaultStaffUserInfo(String productCode) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("productCode", productCode);
        return sqlSession.selectOne("escloud.issuemapperv3.getDefaultStaffUserInfo",map);
    }
    @Override
    public void saveAttachmentFile(IssueAttachmentFileV3 issueAttachmentFileV3){
        sqlSession.insert("escloud.issuemapperv3.saveAttachmentFile",issueAttachmentFileV3);
    }
    @Override
    public int updateSyncStatus4Test(String issueId) {
        return sqlSession.update("escloud.issuemapper.updateSyncStatus4Test", issueId);
    }
    @Override
    public int updateCaseDetailSyncStatus(long issueId) {
        return sqlSession.update("escloud.issuemapper.updateCaseDetailSyncStatus", issueId);
    }
    @Override
    public boolean checkCrmCustomer(String serviceCode, String productCode){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("serviceCode", serviceCode);
        map.put("productCode", productCode);
        Boolean result = sqlSession.selectOne("escloud.issuemapper.checkCrmCustomer", map);
        return result != null ? result : false ;
    }

    @Override
    public List<Issue> SelectIssueListbyServiceCodeAndSearchKey(String serviceCode, int start, int end, String searchKey, String serviceRegion) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("serviceCode", serviceCode);
        map.put("start", start);
        map.put("end", end);
        map.put("searchKey",searchKey);
        map.put("serviceRegion", serviceRegion);

        return sqlSession.selectList("escloud.issuemapper.selectIssueListbyServiceCodeAndSearchKey", map);
    }

    @Override
    public IssueCount SelectIssueCountByServiceCodeAndSearchKey(String serviceCode, String searchKey, String serviceRegion) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("serviceCode", serviceCode);
        map.put("searchKey",searchKey);
        map.put("serviceRegion", serviceRegion);

        return sqlSession.selectOne("escloud.issuemapper.selectIssueCountByServiceCodeAndSearchKey", map);
    }

    /**
     * 资管寫入單身處理描述
     *
     * @param
     * @return
     */
    @Override
    public String InsertCrmCaseProcess(String crmId, String submitDate, String processTime, String workNo, IssueProgress issueProgress, Boolean closed) {
        Map<String, Object> params = new HashMap<>();
        params.put("TYPE", "I");
        params.put("CREATOR", "EsCloud");
        params.put("BR001", crmId);
        params.put("BR002", submitDate);
        params.put("BR004", "Q");
        params.put("BR005", issueProgress.getDescription());
        params.put("BR006", issueProgress.getProcessHours());
        if (!closed) {
            params.put("BR008", "1");
        } else {
            params.put("BR008", "9");
        }
        params.put("BR009", processTime);
        params.put("BR010", "01");
        params.put("BR011", workNo);
        params.put("BR012", workNo);
        params.put("BR014", "與客服聯繫");
        params.put("appkey", crmId);
        params.put("appsecret", getAppSecret(crmId));
        try {
            String res = HttpUtil.doPost("http://misws.digiwin.com/CRM_WebApi/api/CaseCALBRInsert", params);
            if (StringUtils.isNotBlank(res)) {
                Map<String, String> MapList = GsonUtil.getInstance().fromJson(res, Map.class);
                if ("0".equals(MapList.get("status")) && Objects.nonNull(MapList.get("Message"))) {
                    return MapList.get("Message");
                }
                System.out.println("======新增资管案件单身Fail==========");
                System.out.println(res);
                return "";
            }
        } catch (Exception e) {
            System.out.println("======新增资管案件单身Exception==========");
            e.printStackTrace();
        }
        return "";
    }

    /**
     * 资管更新单头
     *
     * @param
     * @return
     */
    @Override
    public Boolean updateCrmCase(String crmId, String closeDate, String workNo, String deptNo) {
        Map<String, Object> params = new HashMap<>();
        params.put("TYPE", "U");
        params.put("CREATOR", "EsCloud");
        params.put("USR_GROUP", "ES");
        params.put("FLAG", readCrmCaseFlag(crmId) + 3);
        params.put("BQ001", crmId);
        params.put("BQ024", "Y");
        params.put("BQ026", closeDate);
        params.put("BQ027", workNo);
        params.put("BQ042", deptNo);
        try {
            String res = HttpUtil.doPost("http://misws.digiwin.com/CRM_WebApi/api/Case", params);
            if (StringUtils.isNotEmpty(res)) {
                String result1 = GsonUtil.getInstance().fromJson(res, String.class);
                Map<String, String> MapList = GsonUtil.getInstance().fromJson(result1, Map.class);
                if ("0".equals(MapList.get("status"))) {
                    return true;
                }
                System.out.println("======更新资管案件单头Fail==========");
                System.out.println(result1);
                return false;
            }

        } catch (Exception e) {
            System.out.println("======更新资管案件单头Exception==========");
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 读取资管案件单头
     *
     * @param
     * @return
     */
    @Override
    public int readCrmCaseFlag(String crmId) {
        Map<String, Object> params = new HashMap<>();
        params.put("BQ001", crmId);
        params.put("appkey", crmId);
        params.put("appsecret", getAppSecret(crmId));
        try {
            String res = HttpUtil.doPost("http://misws.digiwin.com/CRM_WebApi/api/CaseCALBQRead", params);
            if (StringUtils.isNotEmpty(res) && !"null".equals(res)) {
                Map<String, String> MapList = GsonUtil.getInstance().fromJson(res, Map.class);
                if ("0".equals(MapList.get("status")) && MapList.get("FLAG") != null) {
                    String flag = String.valueOf(MapList.get("FLAG"));
                    return Double.valueOf(flag).intValue();
                }
                return 0;
            }
        } catch (Exception e) {
            System.out.println("======查询资管案件单头Exception==========");
            e.printStackTrace();
        }
        return 0;
    }
    /**
     * 读取资管案件单头
     * @param
     * @return
     */
    @Override
    public String readCrmCaseStatus(String crmId) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("BQ001", crmId);
        params.put("appkey", crmId);
        params.put("appsecret", getAppSecret(crmId));
        try {
            String res = HttpUtil.doPost("http://misws.digiwin.com/CRM_WebApi/api/CaseCALBQRead", params);
            if (StringUtils.isNotEmpty(res) && !"null".equals(res)) {
                HashMap<String, String> MapList = GsonUtil.getInstance().fromJson(res, HashMap.class);
                if ("0".equals(MapList.get("status")) && MapList.get("BQ024") != null) {
                    return String.valueOf(MapList.get("BQ024"));
                }
                return "";
            }
        } catch (Exception e) {
            System.out.println("======查询资管案件单头Exception==========");
            e.printStackTrace();
        }
        return "";
    }
    @Override
    public int UpdateProcessSeq(long issueId,long progressId, String crm_BR002,String crm_BR003) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueId", issueId);
        map.put("Id", progressId);
        map.put("crm_BR002", crm_BR002);
        map.put("crm_BR003", crm_BR003);
        return sqlSession.update("escloud.issuemapper.UpdateProcessSeq", map);
    }
    private String getAppSecret(String crmId){
        //appsecret = MD5( 案件代號 + 年月日) 例如: B22003001979+20200706
        LocalDateTime createDate = LocalDateTime.now(ZoneId.of("Asia/Shanghai"));
        String todayDateStr = String.format("%d" + "" + "%02d" + "" + "%02d", createDate.getYear(), createDate.getMonthValue(), createDate.getDayOfMonth());
        String appsecret = MathsUtils.string2MD5(crmId + todayDateStr);
        return appsecret;
    }
    /**
     * es分词
     * @param
     * @return
     */
    private List<String> analyze(String esSearch){
        try{
            Map map = kbService.analyze(esSearch);
            if(map != null && map.get("code") != null && "0".equals(map.get("code"))){
                return (List<String>) map.get("data") ;
            }
        } catch (Exception e) {
            log.error(e.toString());
        }
        return new ArrayList<>();
    }
    @Override
    public long InsertIssueProgressForCC(IssueProgress issueProgress) {
        sqlSession.insert("escloud.issuemapper.insertIssueProgressForCC", issueProgress);
        return issueProgress.getId();
    }
    @Override
    public int InsertIssueKbshare(long issueId, String crmId, IssueKbshare issueKbshare) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("IssueId", issueId);
        map.put("CrmId", crmId);
        map.put("ProductCode", issueKbshare.getProductCode());
        map.put("SearchText", issueKbshare.getSearchText());
        map.put("Kbid", issueKbshare.getKbid());
        map.put("ShareContent", issueKbshare.getShareContent());
        map.put("ShareUrl", issueKbshare.getShareUrl());
        map.put("SubmitTime", issueKbshare.getSubmitTime());
        map.put("FinishSearchChatFile", issueKbshare.getFinishSearchChatFile());
        map.put("ChatFileContent", issueKbshare.getChatFileContent());
        map.put("ChatFileSearchText", issueKbshare.getChatFileSearchText());
        map.put("ChatFileErrorInfo", issueKbshare.getChatFileErrorInfo());
        map.put("InvalidChatFileAnswer", issueKbshare.isInvalidChatFileAnswer());
        map.put("ChatfileKnowledgeList", issueKbshare.getChatfileKnowledgeList());
        map.put("invalidChatFileKnowledgeNo", issueKbshare.getInvalidChatFileKnowledgeNo());
        map.put("aiSource", issueKbshare.getAiSource());
        return sqlSession.insert("escloud.issuemapper.insertIssueKbshare", map);
    }

    // 利用issueId找出是對應的EDREventId
    @Override
    public List<String> getEdrEventIdByIssueId(long issueId) {
        Map<String, Object> map = new HashMap<>();
        map.put("issueId", issueId);
        return sqlSession.selectList("escloud.issuemapper.getEdrEventIdByIssueId",map);
    }

    // 利用crmId找出是對應的EDREventId
    @Override
    public List<String> getEdrEventIdByCrmId(String crmId) {
        Map<String, Object> map = new HashMap<>();
        map.put("crmId", crmId);
        return sqlSession.selectList("escloud.issuemapper.getEdrEventIdByCrmId",map);
    }

    // 找出客代對應的ServerId
    @Override
    public String getServerId(String aioDBName, String serviceCode,long sid) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("aioDBName", aioDBName);
        map.put("serviceCode", serviceCode);
        map.put("sid", sid);
        return sqlSession.selectOne("escloud.issuemapper.getServerId",map);
    }

    // 找出EventId對應的id
    @Override
    public Long getIdByEventId(String aioDBName, String serverId, String eventId, long sid){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("aioDBName", aioDBName);
        map.put("serverId", serverId);
        map.put("eventId", eventId);
        map.put("sid", sid);
        return sqlSession.selectOne("escloud.issuemapper.getIdByEventId",map);
    }
    // 添加事件之案件狀態
    @Override
    public int insertEventIssueStatus(Map<String, Object> map){
        return sqlSession.insert("escloud.issuemapper.insertEventIssueStatus",map);
    }

    @Override
    public int insertEventDetailIssueStatus(Map<String, Object> map){
        return sqlSession.insert("escloud.issuemapper.insertEventDetailIssueStatus",map);
    }

    @Override
    public int updateEventDetailIssueStatus(Map<String, Object> map){
        return sqlSession.insert("escloud.issuemapper.updateEventDetailIssueStatus",map);
    }
    @Override
    public List<Issue> issueFullContentSearch(String msgContent,String userId, String serviceCode, String productCode, String localId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("msgContent", msgContent);
        map.put("userId", userId);
        map.put("serviceCode", serviceCode);
        map.put("productCode", productCode);
        map.put("localId", localId);
        return sqlSession.selectList("escloud.issuemapper.issueFullContentSearch", map);
    }

    @Override
    public String getCustomerCodeByServiceCode(String serviceCode) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("serviceCode", serviceCode);
        return sqlSession.selectOne("escloud.issuemapper.getCustomerCodeByServiceCode", map);
    }
    @Override
    public int checkAgent(String serviceCode, String productCode){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("serviceCode", serviceCode);
        map.put("productCode", productCode);
        return sqlSession.selectOne("escloud.issuemapper.checkAgent", map);
    }

    @Override
    public AgentIssueSum selectAgentIssueSumarry(String serviceCode, String productCode){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("serviceCode", serviceCode);
        map.put("productCode", productCode);
        return sqlSession.selectOne("escloud.issuemapper.selectAgentIssueSumarry", map);
    }


    @Override
    public Boolean isSendMailByTypeId(int typeId){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("typeId", typeId);
        return sqlSession.selectOne("escloud.issuemapper.isSendMailByTypeId", map);
    }

    @Override
    public int selectAgentNotifyEmail(String productCode,String serviceCode,String contractExprityDate){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("productCode", productCode);
        map.put("serviceCode", serviceCode);
        map.put("contractExprityDate", contractExprityDate);
        return sqlSession.selectOne("escloud.issuemapper.selectAgentNotifyEmail", map);
    }

    @Override
    public int saveAgentNotifyEmail(String productCode,String serviceCode,String contractExprityDate){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("productCode", productCode);
        map.put("serviceCode", serviceCode);
        map.put("contractExprityDate", contractExprityDate);
        return sqlSession.update("escloud.issuemapper.saveAgentNotifyEmail", map);
    }

    @Override
    public int updateIssueProgressSyncStatus(Long issueId, String syncStatus){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueId", issueId);
        map.put("syncStatus", syncStatus);
        return sqlSession.update("escloud.issuemapper.updateIssueProgressSyncStatus", map);
    }

    @Override
    public  CustomerServiceInfo getIssueCustomerInfo(long issueId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueId", issueId);
        return sqlSession.selectOne("escloud.issuemapper.getIssueCustomerInfo", map);
    }

    @Override
    public List<CustomerServiceInfo> getCustomerServices(String customerServiceCode) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("customerServiceCode", customerServiceCode);
        return sqlSession.selectList("escloud.issuemapper.getCustomerServices", map);
    }
    @Override

    public boolean getIsSearchByChatFile(String switchName){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("switchName", switchName);
        Boolean result = sqlSession.selectOne("escloud.issuemapper.getIsSearchByChatFile", map);
        return result != null ? result : false ;
    }

    @Override
    public   Long getAcceptIssueProcessId(long issueId, String crmId){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueId", issueId);
        map.put("crmId", crmId);
        return sqlSession.selectOne("escloud.issuemapper.getAcceptIssueProcessId", map);
    }

    @Override
    public int updateAcceptIssueProgress(String crmId,long issueProgressId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueProgressId", issueProgressId);
        map.put("CrmId", crmId);
        map.put("ProcessType", IssueProcessType.Process);
        map.put("Processor", "Vescloud");
        map.put("ReplyType", "A");
        map.put("SyncStatus", "P");
        map.put("workno", "Vescloud");

        return sqlSession.insert("escloud.issuemapper.updateAcceptIssueProgress", map);
    }

    @Override
    public List<EventIssue> getEventIssues(String eventId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("sourceId", eventId+"_");
        map.put("sourceType", IssueSourceType.EDREvent.toString());

        return sqlSession.selectList("escloud.issuemapper.getEventIssues", map);
    }

    @Override
    public int getEdrEventIssue(Map<String,Object> map) {
        return sqlSession.selectOne("escloud.issuemapper.getEdrEventIssue", map);
    }

    @Override
    public int saveAuthorizedSerail(Map<String,Object> map) {
        return sqlSession.insert("escloud.issuemapper.saveAuthorizedSerail", map);
    }

    @Override
    public int updateAuthorizedSerail(Map<String,Object> map) {
        return sqlSession.insert("escloud.issuemapper.updateAuthorizedSerail", map);
    }

    @Override
    public int getCountByCrmId(String crmId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("crmId", crmId);
        return sqlSession.selectOne("escloud.issuemapper.getCountByCrmId", map);
    }

    @Override
    public List<IssueUnresolvedCount> selectUnresolvedIssueList(Map<String, Object> map) {
        return sqlSession.selectList("escloud.issuemapper.selectUnresolvedIssueList", map);
    }

    @Override
    public List<IssueCountStatistic> selectIssueStatisticList(Map<String, Object> map) {
        return sqlSession.selectList("escloud.issuemapper.selectIssueStatisticList", map);
    }

    @Override
    public List<IssueProductCodeCount> selectIssueProductCodeStatisticList(Map<String, Object> map) {
        return sqlSession.selectList("escloud.issuemapper.selectIssueProductCodeStatisticList", map);
    }

    @Override
    public  boolean checkIssueHasChatFileSearch(String serviceRegion, String productCode){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("serviceRegion", serviceRegion);
        map.put("productCode", productCode);
        int result = sqlSession.selectOne("escloud.issuemapper.selectIssueHasChatFileSearch", map);//checkCrmCustomer
        return result>0 ?true : false ;
    }

    @Override
    public int updateUserId(IssueChangeSubmiterHistory issueChangeSubmiterHistory){
        Map<String, Object> map = new HashMap<>();
        map.put("userId", issueChangeSubmiterHistory.getAfterSubmiter());
        map.put("issueId", issueChangeSubmiterHistory.getIssueId());
        return sqlSession.update("escloud.issuemapper.updateUserId",map);
    }
    @Override
    public int insertIssueChangeSubmiterHistory(IssueChangeSubmiterHistory issueChangeSubmiterHistory){
        return sqlSession.insert("escloud.issuemapper.insertIssueChangeSubmiterHistory",issueChangeSubmiterHistory);
    }
    @Override
    public String selectIssueById(Long issueId){
        Map<String, Object> map = new HashMap<>();
        map.put("issueId", issueId);
        return sqlSession.selectOne("escloud.issuemapper.selectIssueById",map);
    }
    @Override
    public List<IssueChangeSubmiterHistoryUserpersonalinfo> selectIssueChangeSubmiterHistory(Long issueId){
        return sqlSession.selectList("escloud.issuemapper.selectIssueChangeSubmiterHistory",issueId);
    }
    @Override
    public List<UserPersonalInfo> selectUserPersonalInfoByUserId(String userId){
        Map<String, Object> map = new HashMap<>();
        map.put("customerServiceCode", userId);
        return sqlSession.selectList("escloud.issuemapper.selectUserPersonalInfoByUserId",map);
    }
}
