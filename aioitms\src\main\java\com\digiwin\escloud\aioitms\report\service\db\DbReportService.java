package com.digiwin.escloud.aioitms.report.service.db;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.digiwin.escloud.aioitms.bigdata.BigDataUtil;
import com.digiwin.escloud.aioitms.bigdata.model.QueryWrapper;
import com.digiwin.escloud.aioitms.dataexamination.dao.DataExaminationReportRecordMapper;
import com.digiwin.escloud.aioitms.dataexamination.dao.DataExaminationReportTemplateMapper;
import com.digiwin.escloud.aioitms.dataexamination.model.DataExaminationReportRecordQueryParam;
import com.digiwin.escloud.aioitms.dataexamination.model.DataExaminationReportTemplate;
import com.digiwin.escloud.aioitms.es.service.EsService;
import com.digiwin.escloud.aioitms.model.bigdata.StarRocksEntity;
import com.digiwin.escloud.aioitms.model.report.ReportDTO;
import com.digiwin.escloud.aioitms.report.dao.DbReportMapper;
import com.digiwin.escloud.aioitms.report.model.OperateLogSaveParam;
import com.digiwin.escloud.aioitms.report.model.ReportStatus;
import com.digiwin.escloud.aioitms.report.model.ScoreMap;
import com.digiwin.escloud.aioitms.report.model.UpdateEsParam;
import com.digiwin.escloud.aioitms.report.model.base.DbReport;
import com.digiwin.escloud.aioitms.report.model.db.*;
import com.digiwin.escloud.aioitms.report.service.AiopsUserService;
import com.digiwin.escloud.aioitms.report.service.ChatGptEvalType;
import com.digiwin.escloud.aioitms.report.service.impl.ReportTemplateService;
import com.digiwin.escloud.common.feign.UserV2FeignClient;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.*;
import com.digiwin.escloud.userv2.model.Customer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.fetch.subphase.FetchSourceContext;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.elasticsearch.core.query.FetchSourceFilter;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.Query;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 2022-06-15 10:36
 * @Description
 */
@Slf4j
@Service
public class DbReportService {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    @Resource
    private DbReportMapper dbReportMapper;
    @Resource
    private DataExaminationReportRecordMapper dataExaminationReportRecordMapper;
    @Resource
    private DbDataFactory dbDataFactory;
    @Resource
    private UserV2FeignClient userV2FeignClient;
    @Resource
    private AsyncService asyncService;
    @Resource
    private AiopsUserService aiopsUserService;
    @Resource
    private ReportReferenceValueService reportRefValueService;
    @Resource
    private ReportTemplateService reportTemplateService;
    @Resource
    private DataExaminationReportTemplateMapper dataExaminationReportTemplateMapper;
    @Resource
    private BigDataUtil bigDataUtil;
    @Value("${report.smart.health.menuId:662714260828736}")
    private String reportSmartHealthMenuId;
    @Value("${aio.service.area}")
    String area;
    @Value("${mis.root}")
    private String misRoot;

    @Resource
    private EsService esService;

    public PageInfo getDbReports(DbQueryParam dbQueryParam) {
        int pageNum = dbQueryParam.getPageNum();
        int pageSize = dbQueryParam.getPageSize();
        // 如果是顾问角色，需要查询eidList
        dbQueryParam.setEidList(aiopsUserService.getEidList());
        dbQueryParam.setArea(area);
        dbQueryParam.setIsMis(isMis());
        PageHelper.startPage(pageNum, pageSize);
        List<DbReportRecord> list = dbReportMapper.getDbReports(dbQueryParam);
        PageInfo<DbReportRecord> pageInfo = new PageInfo<>(list);
        return pageInfo;
    }
    private Boolean isMis() {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        // 預防非內部人員看到內部人員姓名
        if (Objects.isNull(requestAttributes)) {
            return true;
        }

        String referer = requestAttributes.getRequest().getHeader("Referer");
        // 預防非內部人員看到內部人員姓名
        if (StringUtils.isEmpty(referer)) {
            return true;
        }

        String normalizedReferer = referer.replaceAll("/$", "");
        String normalizedMisRoot = misRoot.replaceAll("/$", "");
        log.info("referer: {}", normalizedReferer);
        log.info("misRoot: {}", normalizedMisRoot);

        return normalizedReferer.equals(normalizedMisRoot);
    }

    /**
     * 清除存储在 Redis 中的报告参考值缓存。
     * 此方法用于确保报告参考值是最新的，通过移除缓存中的任何陈旧或过期的值。
     * 当报告参考值的底层数据发生变化，且需要刷新缓存以反映最新值时，这一操作尤为有用。
     */
    public void clearReportRefValueCache() {
        reportRefValueService.clearRedisCache();
    }

    public long generateReport(DbReportRecord dbReportRecord) {
//        if (CollectionUtils.isEmpty(dbReportRecord.getDeviceIdList()) &&
//                StringUtils.isBlank(dbReportRecord.getProductCode())) {
//            //1.7只实现ORACLE
//            if (!dbReportRecord.getDbType().equals(DbType.ORACLE.toString())) {
//                return 0;
//            }
//        }
        if (dbReportRecord.getId() <= 0) {
            dbReportRecord.setId(SnowFlake.getInstance().newId());
        }
        try {
            ResponseBase res = userV2FeignClient.getTenantBasicDetail(dbReportRecord.getServiceCode());
            if (ResponseCode.SUCCESS.toString().equals(res.getCode())) {
                Optional.ofNullable(res.getData()).ifPresent(o -> {
                    Customer customer = JSON.parseObject(JSON.toJSONString(o), Customer.class);
                    dbReportRecord.setCustomerFullName(customer.getCustomerFullNameCH());
                });
            }
        } catch (Exception e) {
            log.error("userV2FeignClient.getTenantBasicDetail", e);
        }
        dbReportRecord.setReportGenerateTime(LocalDateTime.now());
        dbReportRecord.setReportStatus(ReportStatus.GENERATING.getIndex());

        dbReportMapper.saveDbReportRecord(dbReportRecord);
        asyncService.saveDbReportData(dbReportRecord);
        return dbReportRecord.getId();
    }

    public Object getDbReportData(String id, String dbType, String productCode) {
        return dbDataFactory.getDbReportData(id, getCurrentType(dbType, productCode));
    }

    public List<Object> getDbReportData(Query query, String dbType, String productCode) {
        return dbDataFactory.getDbReportData(query, getCurrentType(dbType, productCode));
    }

    private String getCurrentType(String dbType, String productCode) {
        if (StringUtils.isBlank(productCode)) {
            return dbType;
        }
        String reportType = productCode + "_" + dbType;
        if (dbDataFactory.checkReportIsExist(reportType)) {
            return reportType;
        } else {
            // 如果產線報告不存在, 則都是查詢 defaut 的
            return "9999_" + dbType;
        }
    }

    public <T extends DbReport> T saveDbReportData(Object obj, String dbType, String productCode) {
        Class<T> dbReportClass = dbDataFactory.getDbReportClass(getCurrentType(dbType, productCode));
        T t = JSON.parseObject(JSON.toJSONString(obj), dbReportClass);
        return dbDataFactory.saveDbReportData(t, dbType, false);
    }

    public void saveDbReportItems(UpdateEsParam esParam) {
        dbDataFactory.saveDbReportItems(esParam.getId(), esParam.getUpdateFields(),
                getCurrentType(esParam.getDbType(), esParam.getProductCode()));
    }

    public void saveDbReportItem(UpdateEsParam esParam) {
        dbDataFactory.saveDbReportItem(esParam.getId(), esParam.getFieldName(), esParam.getValue(),
                getCurrentType(esParam.getDbType(), esParam.getProductCode()), esParam.isUseScript()
                ,esParam.getReportReferenceScoreType());
    }

    public int getDbReportStatus(long id) {
        return dbReportMapper.getDbReportStatus(id);
    }

    public boolean sendDbReport(long id, SendDbReportParam param) {
        LocalDateTime time = LocalDateTime.now();
        if(param.getIsAppointment()){
            time = null;
        }
        return dbReportMapper.updateDbReportStatus(id, ReportStatus.SENT.getIndex(), param.getEdrReportRecordId(), param.getReportSender(), time) > 0;
    }

    public boolean deleteDbReport(String id, String dbType, String productCode) {
        DbReportRecord dbReportRecord = dbReportMapper.selectDbReportById(Long.valueOf(id));
        if (Optional.ofNullable(dbReportRecord).isPresent()) {
            productCode = dbReportRecord.getProductCode();
        }
        dbDataFactory.deleteDbReport(id, getCurrentType(dbType, productCode));
        return dbReportMapper.deleteById(Long.parseLong(id)) > 0;
    }

    @Async("asyncPoolTaskExecutor")
    public void readDbReport(LogDbRecord logDbRecord) {
        logDbRecord.setId(SnowFlake.getInstance().newId());
        logDbRecord.setReadTime(LocalDateTime.now());
        dbReportMapper.readDbReport(logDbRecord);
    }

    public PageInfo getReadLogs(long id, int pageNum, int pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<LogDbRecord> list = dbReportMapper.getReadLogs(id);
        PageInfo<LogDbRecord> pageInfo = new PageInfo<>(list);
        return pageInfo;
    }

    public void refreshConclusion(UpdateEsParam updateEsParam) {
        updateEsParam.setFieldName("resSuggestion");
        //先获取报告模板结构
        SqlServerDbReport sqlServerDbReport = (SqlServerDbReport) getDbReportData(updateEsParam.getId(),
                updateEsParam.getDbType(), updateEsParam.getProductCode());

        Optional<String> questionOpt = ChatGptEvalType.SqlServerReportConclusion.getQuestion(reportTemplateService, sqlServerDbReport);
        if (questionOpt.isPresent()) {
            updateEsParam.setValue(questionOpt.get());
        }
        saveDbReportItem(updateEsParam);
    }

    public ResponseBase<Map<String, List<ReportDTO>>> getDbReportList(Long eid) {
        // 如果是顾问角色，需要查询eidList

        DbQueryParam param = new DbQueryParam();
        param.setEid(eid);
        param.setReportType(3);
        param.setReportStatus(new int[]{2, 3});

        List<DbReportRecord> reportList = dbReportMapper.getDbReports(param);
        List<ReportDTO> seList = new ArrayList<>();
        Map<String, List<DbReportRecord>> productMap = reportList.stream()
                .filter(r -> "MSSQL".equals(r.getDbType()))
                .collect(Collectors.groupingBy(i -> i.getDbType() + "^" + i.getProductCode()));
        Map<String, List<ReportDTO>> retMap = new HashMap<>();

        // 构建查询条件
        Map<Long, Map<String, Object>> reportIdMap = new HashMap<>();

        StopWatch one = new StopWatch();
        one.start();
        for (Map.Entry<String, List<DbReportRecord>> entry : productMap.entrySet()) {
            String[] dbTypeProductArray = entry.getKey().split("\\^");
            List<Long> sqlServerIdList = entry.getValue().stream().map(DbReportRecord::getId)
                    .collect(Collectors.toList());
            NativeSearchQuery query = new NativeSearchQueryBuilder()
                    .withQuery(QueryBuilders.termsQuery("id", sqlServerIdList))
                    .withSourceFilter(new FetchSourceFilter(new String[]{"scoreMap", "totalScore"}, new String[]{}))
                    .build();
            List<Object> dbReportData = getDbReportData(query, dbTypeProductArray[0], dbTypeProductArray[1]);
            Map<Long, Map<String, Object>> memoMap = dbReportData
                    .stream()
                    .filter(Objects::nonNull)
                    .map(i ->
                            OBJECT_MAPPER.<Map<String, Object>>convertValue(i, TypeFactory.defaultInstance().constructMapType(Map.class, String.class, Object.class)))
                    .collect(Collectors.toMap(i -> LongUtil.objectToLong(i.get("id")), Function.identity()));
            reportIdMap.putAll(memoMap);
        }
        one.stop();
        log.info("[getDbReportList] one : {} s", one.getTotalTimeSeconds());

        for (DbReportRecord reportRecord : reportList) {
            ReportDTO se = new ReportDTO();
            if ("MSSQL".equals(reportRecord.getDbType())) {
                Map<String, Object> map = reportIdMap.getOrDefault(reportRecord.getId(), new HashMap<>());
                Object scoreMapObj = map.get("scoreMap");
                if (Objects.nonNull(scoreMapObj)) {
                    String scoreMapJson = JSONObject.toJSONString(scoreMapObj);
                    if (JSONObject.isValid(scoreMapJson) && !"{}".equals(scoreMapJson)) {
                        BigDecimal score = dealScore(scoreMapJson);
                        if (Objects.nonNull(map.get("totalScore")) && StringUtils.isNotBlank(map.get("totalScore").toString())) {
                            se.setTotalScore(IntegerUtil.objectToInteger(map.get("totalScore")));
                        } else {
                            se.setTotalScore(score.intValue());
                        }
                    }
                }
            }
            se.setReportDate(reportRecord.getReportDate());
            se.setCreateTime(reportRecord.getCreateTime());
            se.setReportId(reportRecord.getId());
            seList.add(se);
        }

        DataExaminationReportRecordQueryParam dParam = new DataExaminationReportRecordQueryParam();
        dParam.setEid(eid);
        DataExaminationReportRecordQueryParam.OrderByParam ob = new DataExaminationReportRecordQueryParam.OrderByParam();
        ob.setCol("reportDate");
        ob.setDire("desc");
        dParam.setOrderByParam(Lists.newArrayList(ob));
        DataExaminationReportTemplate template = dataExaminationReportTemplateMapper.selectReportMenuByMenuId(reportSmartHealthMenuId);

        if (Objects.nonNull(template)) {
            dParam.setReportTemplateId(template.getId());
            dParam.setReportStatusList(Lists.newArrayList(1, 2));
            List<ReportDTO> shList = dataExaminationReportRecordMapper.selectReportRecordByCondition(dParam)
                    .stream()
                    .map(derr -> {
                        ReportDTO sh = new ReportDTO();
                        sh.setReportDate(DateUtil.parseToLocalDate(derr.getReportDate()));
                        sh.setCreateTime(DateUtil.parseToLocalDateTime(derr.getCreateDate()));
                        sh.setReportId(LongUtil.objectToLong(derr.getId()));
                        return sh;
                    }).collect(Collectors.toList());
            retMap.put("SMART_HEALTH", shList);
        }
        retMap.put("SMART_SECURITY", seList);
        return ResponseBase.okT(retMap);
    }

    public BigDecimal dealScore(String json) {
        Gson gson = new GsonBuilder().setLenient().create();

        // 使用泛型类型信息解析 JSON 字符串
        Type type = new TypeToken<Map<String, ScoreMap.ItemGroupScore>>() {
        }.getType();
        Map<String, ScoreMap.ItemGroupScore> scoreMap = gson.fromJson(json, type);

        // 创建 ScoreMap 对象并设置 scoreMap
        ScoreMap scoreMapObject = new ScoreMap();
        scoreMapObject.setScoreMap(scoreMap);
        BigDecimal reduce = calculateTotalScores(scoreMapObject.getScoreMap())
                .values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
        return reduce.setScale(0, RoundingMode.HALF_UP);
    }

    public static Map<String, BigDecimal> calculateTotalScores(Map<String, ScoreMap.ItemGroupScore> scoreMap) {
        Map<String, BigDecimal> totalScores = new HashMap<>();

        for (Map.Entry<String, ScoreMap.ItemGroupScore> entry : scoreMap.entrySet()) {
            BigDecimal totalScore = BigDecimal.ZERO;
            for (ScoreMap.ItemScore itemScore : entry.getValue().getRsiList()) {
                totalScore = totalScore.add(itemScore.getItemScore());
            }
            totalScore = totalScore.multiply(entry.getValue().getItemGroupScore());
            totalScores.put(entry.getKey(), totalScore.setScale(2, RoundingMode.HALF_UP)); // 设置保留两位小数
        }

        return totalScores;
    }

    public BaseResponse operatelog(OperateLogSaveParam operateLogSaveParam) {
        List<String> operateTypeList = operateLogSaveParam.getOperateType();
        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        sb.append("get_json_string(parse_json(operateContent), '$.eid') as eid, ");
        sb.append("get_json_string(parse_json(operateContent), '$.reportId') as reportId, ");
        sb.append("get_json_string(parse_json(operateContent), '$.processUserName') as userName, ");
        sb.append("get_json_string(parse_json(operateContent), '$.processUserId') as operateId, ");
        sb.append("get_json_string(parse_json(operateContent), '$.startTime') as startTime, ");
        sb.append("get_json_string(parse_json(operateContent), '$.operateType') as operateType ");
        sb.append("from servicecloud.AiopsOperateLog");
        sb.append(" where 1=1");
        if (StringUtils.isNotEmpty(operateLogSaveParam.getEid())) {
            sb.append(" and get_json_string(parse_json(operateContent), '$.eid') = '").append(operateLogSaveParam.getEid()).append("'");
        }
        if (StringUtils.isNotEmpty(operateLogSaveParam.getReportId())) {
            sb.append(" and get_json_string(parse_json(operateContent), '$.reportId') = '").append(operateLogSaveParam.getReportId()).append("'");
        }
        if (!CollectionUtils.isEmpty(operateTypeList)) {
            String types = operateTypeList.stream()
                    .map(type -> String.format("'%s'", type))
                    .collect(Collectors.joining(","));
            sb.append(" and get_json_string(parse_json(operateContent), '$.operateType') in (").append(types).append(")");
        }

        List<Map<String, Object>> list = bigDataUtil.srQuery(StringUtil.toString(sb));
        Integer total = list.size();

        Map<String, Object> map = new HashMap<>();
        map.put("list", list);
        map.put("total", total);

        return BaseResponse.ok(map);
    }
}
