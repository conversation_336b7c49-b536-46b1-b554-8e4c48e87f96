package com.digiwin.escloud.aiouser.service.impl;

import com.digiwin.escloud.aiouser.dao.ITenantNotifyDao;
import com.digiwin.escloud.aiouser.dao.IUserDao;
import com.digiwin.escloud.aiouser.model.common.BizException;
import com.digiwin.escloud.aiouser.model.tenantNotice.*;
import com.digiwin.escloud.aiouser.model.user.User;
import com.digiwin.escloud.aiouser.service.ITenantNotifyService;
import com.digiwin.escloud.common.controller.ParamCheckHelp;
import com.digiwin.escloud.common.feign.AioItmsFeignClient;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.BooleanUtil;
import com.digiwin.escloud.common.util.LongUtil;
import com.digiwin.escloud.common.util.RequestUtil;
import com.digiwin.escloud.common.util.SnowFlake;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Date 2021/6/21 14:21
 * @Created yanggld
 * @Description
 */
@Service
@Slf4j
public class TenantNotifyService implements ITenantNotifyService, ParamCheckHelp {

    @Autowired
    private ITenantNotifyDao tenantNoticeDao;

    @Autowired
    private IUserDao userDao;

    @Autowired
    private AioItmsFeignClient aioItmsFeignClient;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private RestTemplate restTemplate;

    @Value("${wechat.token.url:https://es-api.digiwincloud.com.cn/imconsume/weixin/manage/access/token/get}")
    private String wechatTokenUrl;

    @Value("${wechat.subscribe.url:https://api.weixin.qq.com/cgi-bin/user/info}")
    private String wechatSubscribeUrl;

    @Value("${warning.default.notify.Level:FATAL,ERROR,WARNING,INFO}")
    private String warningDefaultNotifyLevel;

    @Override
    public List<TenantNotifyGroupListRespDTO> list(Map<String, Object> params) {
        return tenantNoticeDao.list(params);
    }

    @Override
    public TenantNotifyGroupRespDTO findByTenantNoticeGroupId(Long id) {
        TenantNotifyGroup tenantNotifyGroup = findById(id);
        if (tenantNotifyGroup == null) {
            return null;
        }
        List<UserNotifyContact> userNotifyContactList = findUserNoticeContactByNoticeGroupId(id);
        String token = getToken();
        //添加是否关注公众号
        userNotifyContactList.forEach(item -> {
            item.setSubscribe(getWechatSubscribe(token, item.getWechat()));
            item.setBandingWeChat(StringUtils.isNotEmpty(item.getWechat()));
        });
        TenantNotifyGroupRespDTO tenantNotifyGroupRespDTO = new TenantNotifyGroupRespDTO();
        tenantNotifyGroupRespDTO.setId(tenantNotifyGroup.getId());
        tenantNotifyGroupRespDTO.setEid(tenantNotifyGroup.getEid());
        tenantNotifyGroupRespDTO.setName(tenantNotifyGroup.getName());
        tenantNotifyGroupRespDTO.setRemark(tenantNotifyGroup.getRemark());
        tenantNotifyGroupRespDTO.setUserNotifyContactList(userNotifyContactList);
        return tenantNotifyGroupRespDTO;
    }

    private String getWechatSubscribe(String token, String openId) {
        if (StringUtils.isEmpty(openId)) {
            return null;
        }
        String url = wechatSubscribeUrl + "?access_token=" + token + "&openid=" + openId + "&lang=zh_CN";
        HashMap map = restTemplate.getForObject(url, HashMap.class);
        String subscribe = map.getOrDefault("subscribe", "").toString();
        return subscribe;
    }


    private String getToken() {
        HashMap map = restTemplate.getForObject(wechatTokenUrl, HashMap.class);
        String accessToken = map.getOrDefault("accessToken", "").toString();
        return accessToken;
    }

    @Override
    public TenantNotifyGroup findById(Long id) {
        return tenantNoticeDao.findById(id);
    }

    @Override
    public List<UserNotifyContact> findUserNoticeContactByNoticeGroupId(Long id) {
        return tenantNoticeDao.findByTenantNoticeGroupId(id);
    }

    @Override
    @Transactional
    public void save(TenantNotifyGroupRespDTO dto) {
        TenantNotifyGroup tenantNotifyGroup = new TenantNotifyGroup();
        Long tenantNoticeGroupId = dto.getId();
        tenantNotifyGroup.setId(dto.getId());
        tenantNotifyGroup.setName(dto.getName());
        tenantNotifyGroup.setRemark(dto.getRemark());
        tenantNotifyGroup.setEid(dto.getEid());
        List<UserNotifyContact> userNotifyContactList = dto.getUserNotifyContactList();
        for (UserNotifyContact userNotifyContact : userNotifyContactList) {
            userNotifyContact.setEid(dto.getEid());
        }
        List<Long> userNoticeContactId=new ArrayList<>();
        // 新增
        if (tenantNoticeGroupId == null || tenantNoticeGroupId == 0) {
            // 新增分组
            tenantNoticeGroupId = insertTenantNoticeGroup(tenantNotifyGroup);
            // 修改
        } else {
            // 修改分组
            updateTenantNoticeGroup(tenantNotifyGroup);
            // 删除通知人和映射关系
            deleteUserNoticeContactAndMapping(tenantNoticeGroupId);
        }
        //去重通知人
        List<UserNotifyContact> uniqueContacts = userNotifyContactList.stream()
                .filter(o -> StringUtils.isNotEmpty(o.getUserId()))
                .collect(Collectors.toMap(
                        UserNotifyContact::getUserId,   // Key改为仅userId
                        Function.identity(),
                        (oldVal, newVal) -> oldVal.getId() > newVal.getId() ? oldVal : newVal
                ))
                .values()
                .stream()
                .collect(Collectors.toList());
        List<UserNotifyContact> emptyUserIdContacts = userNotifyContactList.stream()
                .filter(o -> StringUtils.isEmpty(o.getUserId()))
                .collect(Collectors.toList());
        List<Long> finalContactIds = new ArrayList<>();
        //只新增空userId的联系方式，保证不重复新增同一个userId的联系方式
        if (!CollectionUtils.isEmpty(emptyUserIdContacts)) {
            userNoticeContactId = insertUserNoticeContact(emptyUserIdContacts);
        }
        if (!CollectionUtils.isEmpty(userNoticeContactId)) {
            finalContactIds.addAll(userNoticeContactId);
        }
        if (!CollectionUtils.isEmpty(uniqueContacts)) {
            List<Long> uniqueContactIds = uniqueContacts.stream().map(o -> o.getId()).collect(Collectors.toList());
            finalContactIds.addAll(uniqueContactIds);
        }
        if (!CollectionUtils.isEmpty(finalContactIds)) {
            insertTenantNoticeGroupUserMapping(tenantNoticeGroupId, finalContactIds);
        }
    }

    @Override
    public void delete(Long tenantNoticeGroupId) {
        BaseResponse baseResponse = aioItmsFeignClient.checkDeviceWarningNotifyExist(tenantNoticeGroupId, "");
        if ("0".equals(baseResponse.getCode())) {
            if (baseResponse.getData() != null && !baseResponse.getData().equals(true)) {
                tenantNoticeDao.deleteAll(tenantNoticeGroupId);
                return;
            }
        }
        throw new BizException("该群组已经被使用无法删除",5100);
    }

    @Override
    @Transactional
    public void setDefault(Long tenantNoticeGroupId) {
        TenantNotifyGroup thisGroup = tenantNoticeDao.findById(tenantNoticeGroupId);
        if (thisGroup == null) {
            return;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("pageNum", 1);
        params.put("pageSize", 1);
        params.put("sid", RequestUtil.getHeaderSid());
        params.put("eid", thisGroup.getEid());
        params.put("dft", 1);
        List<TenantNotifyGroupListRespDTO> list = list(params);
        if (!CollectionUtils.isEmpty(list)) {
            for (TenantNotifyGroup model : list) {
                TenantNotifyGroup tenantNotifyGroup = new TenantNotifyGroup();
                tenantNotifyGroup.setId(model.getId());
                tenantNotifyGroup.setDft(false);
                tenantNoticeDao.updateTenantNoticeGroup(tenantNotifyGroup);
            }
        }
        TenantNotifyGroup tenantNotifyGroup = new TenantNotifyGroup();
        tenantNotifyGroup.setId(tenantNoticeGroupId);
        tenantNotifyGroup.setDft(true);
        tenantNoticeDao.updateTenantNoticeGroup(tenantNotifyGroup);
    }

    @Override
    public List<UserNotifyContact> getUserNotifyContact(List<Long> tngIdList) {
        if (CollectionUtils.isEmpty(tngIdList)) {
            return new ArrayList<>();
        }
        List<UserNotifyContact> userNotifyContactList = tenantNoticeDao.getUserNotifyContact(tngIdList);

        // 检查列表是否为空
        if (CollectionUtils.isEmpty(userNotifyContactList)) {
            return new ArrayList<>();
        }
        userNotifyContactList.stream()
                .filter(contact -> StringUtils.isBlank(contact.getReceiveLevels()))
                .forEach(contact -> contact.setReceiveLevels(warningDefaultNotifyLevel));
        return userNotifyContactList;
    }

    @Override
    public BaseResponse getUserNoticeContactByServiceCode(String serviceCodeOrEid, Boolean isDefault) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(serviceCodeOrEid, "serviceCodeOrEid");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        Map<String, Object> map = new HashMap<>(2);
        map.put("serviceCodeOrEid", serviceCodeOrEid);
        map.put("isDefault", isDefault);
        //0:停用；1:启用，理应需获取启用的
        map.put("status", 1);
        List<TenantNotifyGroupRespDTO> tenantNotifyGroupRespDTOList = tenantNoticeDao.selectUserNoticeContactByMap(map);
        
        // 检查列表是否为空
        if (CollectionUtils.isEmpty(tenantNotifyGroupRespDTOList)) {
            return BaseResponse.ok(tenantNotifyGroupRespDTOList);
        }
        
        // 获取预警级别
        Long eid = tenantNotifyGroupRespDTOList.stream()
                .findFirst()
                .map(TenantNotifyGroupRespDTO::getEid)
                .orElse(null);
                
        if (eid == null) {
            return BaseResponse.ok(tenantNotifyGroupRespDTOList);
        }
        
        BaseResponse<List<String>> notifyLevelResponse = aioItmsFeignClient.getWarningNotifyLevel(eid);
        List<String> notifyLevelList = notifyLevelResponse.getData(); // 预警级别
        
        // 如果预警级别列表为空，则不需要处理
        if (CollectionUtils.isEmpty(notifyLevelList)) {
            return BaseResponse.ok(tenantNotifyGroupRespDTOList);
        }

        String notifyLevelStr = String.join(",", notifyLevelList);
        tenantNotifyGroupRespDTOList.forEach(group -> {
            if (group.getUserNotifyContactList() != null) {
                group.getUserNotifyContactList().stream()
                    .filter(contact -> StringUtils.isBlank(contact.getReceiveLevels()))
                    .forEach(contact -> contact.setReceiveLevels(notifyLevelStr));
            }
        });
        return BaseResponse.ok(tenantNotifyGroupRespDTOList);
    }

    @Override
    public List<UserNotifyContact> getUserNoticeContactByMail(String eid, List<String> mailList) {
        if (CollectionUtils.isEmpty(mailList)) {
            return new ArrayList<>();
        }
        List<UserNotifyContact> userNoticeContactList = tenantNoticeDao.getUserNoticeContactByMail(eid, mailList);

        // 检查列表是否为空
        if (CollectionUtils.isEmpty(userNoticeContactList)) {
            return new ArrayList<>();
        }

        // 如果都取不到则不发送
        if (userNoticeContactList.stream().allMatch(contact ->
                (StringUtils.isBlank(contact.getEmail()) && StringUtils.isBlank(contact.getTelephone())))) {
            return new ArrayList<>();
        }

        userNoticeContactList.stream()
                .filter(contact -> StringUtils.isBlank(contact.getReceiveLevels()))
                .forEach(contact -> contact.setReceiveLevels(warningDefaultNotifyLevel));
        return userNoticeContactList;
    }

    @Override
    public int updateTenantNotifyGroup(TenantNotifyGroup tenantNotifyGroup) {
        return tenantNoticeDao.updateTenantNoticeGroup(tenantNotifyGroup);
    }

    @Override
    public List<NotifyModule> getNotifyModule() {
        return tenantNoticeDao.getNotifyModule();
    }

    @Override
    public List<NotifyReceiveLevel> getNotifyReceiveLevel(Long nmId) {
        return tenantNoticeDao.getNotifyReceiveLevel(nmId);
    }

    @Override
    public List<NotifyWay> getNotifyWay(Long nmId) {
        return tenantNoticeDao.getNotifyWay(nmId);
    }

    @Override
    public ResponseBase saveNotifyModule(UserNotifyModuleSettingReq req) {
        //保存user_notify_module_setting
        long userSid = req.getAuthUserSid();
        String userId = req.getAuthUserId();
        Long eid = req.getEid();
        //保存更新预警通知模块设定
        UserNotifyModuleSetting setting = new UserNotifyModuleSetting();
        setting.setId(SnowFlake.getInstance().newId());
        setting.setUserSid(userSid);
        setting.setNmId(req.getNmId());
        setting.setEnabled(req.isEnabled());
        setting.setCreateTime(LocalDateTime.now());
        setting.setUpdateTime(LocalDateTime.now());

//        特殊开启，这个参数前端不会使用，是其他地方批量调用使用，目的是覆盖enabled得值
        if (BooleanUtil.objectToBoolean(req.getSpecialEnabled())) {
            setting.setEnabled(req.getSpecialEnabled());
        }

        //更新user表
        UserNotifyContact userNotifyContact = new UserNotifyContact();
        userNotifyContact.setId(SnowFlake.getInstance().newId());
        userNotifyContact.setSid(RequestUtil.getHeaderSid());
        userNotifyContact.setEid(eid);
        userNotifyContact.setUserId(userId);
        User user = tenantNoticeDao.getUserByUserId(req.getAuthUserId());
        userNotifyContact.setName(user.getName());
        userNotifyContact.setEmail(req.getEmail());
        userNotifyContact.setTelephone(req.getTelephone());

        int count = tenantNoticeDao.selectUserNoticeContactByUqKey(userNotifyContact);

        //保存更新user_notify_setting_result
        UserNotifySettingResult settingResult = new UserNotifySettingResult();
        settingResult.setId(SnowFlake.getInstance().newId());
        settingResult.setUserSid(userSid);
        settingResult.setUserId(userId);
        settingResult.setNmId(req.getNmId());
        settingResult.setNrlIds(req.getNrlIds());
        settingResult.setNwIds(req.getNwIds());
        settingResult.setCreateTime(LocalDateTime.now());
        settingResult.setUpdateTime(LocalDateTime.now());

        Boolean execute = transactionTemplate.execute((transactionStatus) -> {
            try {
                //保存更新预警通知模块设定
                tenantNoticeDao.insertOrUpdateUserNotifyModuleSetting(setting);

                if (count <= 0) {
                    tenantNoticeDao.insertUserNoticeContact(Arrays.asList(userNotifyContact));
                } else {
                    tenantNoticeDao.updateUserNotifyContact(userNotifyContact);
                }
                //保存更新user_notify_setting_result
                tenantNoticeDao.insertOrUpdateUserNotifySettingResult(settingResult);
            } catch (Exception e) {
                log.error("insert component exception, error:{}", e);
                transactionStatus.setRollbackOnly();
                return false;
            }
            return true;
        });
        if (!execute) {
            return ResponseBase.error(ResponseCode.INTERNAL_ERROR.getCode(), "save notify module error");
        }
        return ResponseBase.ok();
    }

    @Override
    public ResponseBase getNotifyModuleSetting(Long nmId, String authUserId, Long eid) {
        long sid = RequestUtil.getHeaderSid();
        //调整获取用户最新一笔数据
        return ResponseBase.ok(tenantNoticeDao.getSettingResultRes(authUserId, nmId));
    }

    @Override
    public ResponseBase getGroupNotifier(Long eid, Long id) {
        //获取租户下所有用户
        List<NotifyContactResp> userList = Optional.ofNullable(tenantNoticeDao.getUserByEid(eid))
                .orElseGet(Collections::emptyList)
                .stream()
                .peek(user -> user.setBandingWeChat(StringUtils.isNotEmpty(user.getWechat())))
                .collect(Collectors.toList());
        return ResponseBase.ok(userList);
    }

    @Override
    public ResponseBase getUserWechatSubscribe(String userId) {
        User user = Optional.ofNullable(tenantNoticeDao.getUserByUserId(userId)).orElse(new User());
        String wechat = user.getWechat();
        if (StringUtils.isEmpty(wechat)) {
            return ResponseBase.ok(0);
        }
        String token = getToken();
        return ResponseBase.ok(getWechatSubscribe(token, wechat));
    }

    @Override
    public ResponseBase fixNotifySetting(List<Long> eidList) {
        //需要根据租户获取所有设备-》所有收集项-》所有预警项目设置预警级别 --通知方式
        long nmId = 1;
        Map<String, Long> notifyWayMap = getNotifyWay(nmId).stream()
                .collect(Collectors.toMap(NotifyWay::getWayCode, NotifyWay::getId));
        Map<String, Long> notifyReceiveLevelMap = getNotifyReceiveLevel(nmId).stream()
                .collect(Collectors.toMap(NotifyReceiveLevel::getReceiveLevelCode, NotifyReceiveLevel::getId));

        //获取user表当前租户下所有用户必须关联了user_notify_contact表没关联上的数据不更新
        List<FixNotifyUser> userList = tenantNoticeDao.selectNotifyContactByEid(eidList);
        if (CollectionUtils.isEmpty(userList)) {
            return ResponseBase.ok();
        }
        Map<Long, List<FixNotifyUser>> userMap = userList.stream().collect(Collectors.groupingBy(FixNotifyUser::getEid));
        userMap.forEach(
                (eid, users) -> {
                    //获取当前租户的预警级别
                    BaseResponse<List<String>> notifyWayResponse = aioItmsFeignClient.getWarningNotifyWayByEid(eid);
                    BaseResponse<List<String>> notifyLevelResponse = aioItmsFeignClient.getWarningNotifyLevel(eid);
                    //获取通知方式
                    List<String> notifyWayList = notifyWayResponse.getData();//通知方式
                    StringJoiner notifyWayStr = new StringJoiner(",");
                    notifyWayList.forEach(item -> {
                        notifyWayStr.add(notifyWayMap.get(item).toString());
                    });
                    //获取预警级别
                    List<String> notifyLevelList = notifyLevelResponse.getData();//预警级别
                    StringJoiner notifyLevelStr = new StringJoiner(",");
                    notifyLevelList.forEach(item -> {
                        notifyLevelStr.add(notifyReceiveLevelMap.get(item).toString());
                    });
                    //获取当前租户的预警通知方式
                    List<UserNotifyModuleSetting> settingList = new ArrayList<>();
                    List<UserNotifySettingResult> resultList = new ArrayList<>();
                    users.forEach(item -> {
                        long sid = item.getSid();
                        //目前只有预警项通知设定
                        String userId = item.getId();
                        UserNotifyModuleSetting setting = new UserNotifyModuleSetting();
                        setting.setId(SnowFlake.getInstance().newId());
                        setting.setEnabled(true);//需要判断不为空的情况下都是开启状态
                        setting.setUserSid(sid);
                        setting.setNmId(nmId);
                        setting.setCreateTime(LocalDateTime.now());
                        setting.setUpdateTime(LocalDateTime.now());
                        settingList.add(setting);
//                        tenantNoticeDao.insertOrUpdateUserNotifyModuleSetting(setting);

                        UserNotifySettingResult settingResult = new UserNotifySettingResult();
                        settingResult.setId(SnowFlake.getInstance().newId());
                        settingResult.setUserSid(sid);
                        settingResult.setUserId(userId);
                        settingResult.setNmId(nmId);
                        settingResult.setNrlIds(notifyLevelStr.toString());
                        settingResult.setNwIds(notifyWayStr.toString());
                        settingResult.setCreateTime(LocalDateTime.now());
                        settingResult.setUpdateTime(LocalDateTime.now());
                        resultList.add(settingResult);
//                        tenantNoticeDao.insertOrUpdateUserNotifySettingResult(settingResult);
                    });
                    if (!CollectionUtils.isEmpty(settingList)) {
                        tenantNoticeDao.batchInsertOrUpdateUserNotifyModuleSetting(settingList);
                    }
                    if (!CollectionUtils.isEmpty(resultList)) {
                        tenantNoticeDao.batchInsertOrUpdateUserNotifySettingResult(resultList);
                    }

                }
        );

        return null;
    }

    private void insertTenantNoticeGroupUserMapping(Long tenantNoticeGroupId, List<Long> userNoticeContactId) {
        int flag = tenantNoticeDao.insertTenantNoticeGroupUserMapping(tenantNoticeGroupId, userNoticeContactId);
        if (flag <= 0) {
            throw new RuntimeException("保存租户群组失败");
        }
    }

    private void deleteUserNoticeContactAndMapping(Long tenantNoticeGroupId) {
        tenantNoticeDao.deleteUserNoticeContactAndMapping(tenantNoticeGroupId);
    }


    private List<Long> insertUserNoticeContact(List<UserNotifyContact> userNotifyContactList) {
        List<String> emails = userNotifyContactList.stream().map(UserNotifyContact::getEmail).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        List<Long> ids = new ArrayList<>(userNotifyContactList.size());
        userNotifyContactList = userNotifyContactList.stream().map(userNotifyContact -> {
            Long id = userNotifyContact.getId();
            if (LongUtil.isEmpty(id)) {
                long userNoticeContactId = SnowFlake.getInstance().newId();
                userNotifyContact.setId(userNoticeContactId);
            }
            userNotifyContact.setSid(RequestUtil.getHeaderSid());
            ids.add(userNotifyContact.getId());
            return userNotifyContact;
        }).collect(Collectors.toList());
        int flag = tenantNoticeDao.insertUserNoticeContact(userNotifyContactList);
        return ids;
    }

    private Long insertTenantNoticeGroup(TenantNotifyGroup tenantNotifyGroup) throws BizException {
        long tenantNoticeGroupId = SnowFlake.getInstance().newId();
        tenantNotifyGroup.setSid(RequestUtil.getHeaderSid());
        tenantNotifyGroup.setId(tenantNoticeGroupId);
        tenantNotifyGroup.setStatus(1);
        // 第一条数据为默认群组
        Map<String, Object> params = new HashMap<>();
        params.put("pageNum", 1);
        params.put("pageSize", 1);
        params.put("sid", RequestUtil.getHeaderSid());
        params.put("eid", tenantNotifyGroup.getEid());
        List<TenantNotifyGroupListRespDTO> list = list(params);
        if (CollectionUtils.isEmpty(list)) {
            tenantNotifyGroup.setDft(true);
        } else {
            tenantNotifyGroup.setDft(false);
        }
        try {
            int flag = tenantNoticeDao.insertTenantNoticeGroup(tenantNotifyGroup);
            if (flag > 0) {
                return tenantNoticeGroupId;
            } else {
                throw new BizException("保存租户群组失败");
            }
        }catch (DuplicateKeyException ex){
           ex.printStackTrace();
            throw new BizException("DuplicateKeyException",5000);
        }
    }

    private void updateTenantNoticeGroup(TenantNotifyGroup tenantNotifyGroup) throws RuntimeException {
        int flag = tenantNoticeDao.updateTenantNoticeGroup(tenantNotifyGroup);
        if (flag == 0) {
            throw new RuntimeException("修改租户群组失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseBase cleanupDuplicateUserNotifyContact() {
        // 1. 查找重复的记录ID（需要删除的）
        List<Long> duplicateIds = tenantNoticeDao.findDuplicateUserNotifyContactIds();

        if (CollectionUtils.isEmpty(duplicateIds)) {
            return ResponseBase.ok("没有发现重复数据");
        }

        log.info("发现 {} 条重复的user_notify_contact记录需要清理", duplicateIds.size());

        // 2. 查找需要更新的映射关系
        List<Map<String, Object>> updateInfoList = tenantNoticeDao.findMappingUpdateInfo(duplicateIds);

        // 3. 更新tenant_notify_group_user_mapping表
        int updatedMappings = 0;
        if (!CollectionUtils.isEmpty(updateInfoList)) {
            updatedMappings = tenantNoticeDao.batchUpdateTenantNotifyGroupUserMapping(updateInfoList);
            log.info("更新了 {} 条tenant_notify_group_user_mapping记录", updatedMappings);
        }

        // 4. 删除重复的user_notify_contact记录
        int deletedRecords = tenantNoticeDao.batchDeleteDuplicateUserNotifyContact(duplicateIds);
        log.info("删除了 {} 条重复的user_notify_contact记录", deletedRecords);

        Map<String, Object> result = new HashMap<>();
        result.put("deletedUserNotifyContactRecords", deletedRecords);
        result.put("updatedMappingRecords", updatedMappings);
        result.put("duplicateIds", duplicateIds);

        return ResponseBase.ok(result);
    }
}
