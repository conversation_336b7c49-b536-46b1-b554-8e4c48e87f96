package com.digiwin.escloud.issueservice.services.Impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.digiwin.escloud.aioitms.model.bigdata.UploadBigDataContext;
import com.digiwin.escloud.common.util.DateUtil;
import com.digiwin.escloud.common.util.SnowFlake;
import com.digiwin.escloud.integration.api.iam.req.user.IamAuthoredUser;
import com.digiwin.escloud.integration.common.WatchRxError;
import com.digiwin.escloud.integration.service.iam.AuthorizationService;
import com.digiwin.escloud.issueservice.cache.KmoCache;
import com.digiwin.escloud.issueservice.dao.IIssueSyncDao;
import com.digiwin.escloud.issueservice.model.ChatFileConfig;
import com.digiwin.escloud.issueservice.services.IKcfService;
import com.digiwin.escloud.issueservice.utils.BigDataUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class KcfService  implements IKcfService {
    @Value("${kcf.file.address}")
    private String kcfFileAddress;
    @Value("${kcf.url.address}")
    private String kcfUrlAddress;
    @Value("${kcf.userId}")
    private String kcfUserId;
    @Value("${kcf.serviceCode}")
    private String kcfServiceCode;
    @Value("${esc.integration.appToken}")
    private String appToken;
    @Value("${esc.integration.chatfile.apiKey}")
    private String apiKey;
    @Value("${digiwin.issue.connectarea}")
    private String connectArea;
    @Value("${digiwin.issueservice.defaultSid:241199971893824}")
    Long defaultSid;
    @Autowired
    private AuthorizationService authorizationService;
    @Autowired
    private BigDataUtil bigDataUtil;
    @Autowired
    IIssueSyncDao issueSyncDao;
    @Autowired
    KmoCache kmoCache;

    public IamAuthoredUser getIamAuthoredUser(String userId, String serviceCode){
        WatchRxError watchRxError = new WatchRxError();
        IamAuthoredUser iamAuthoredUser = new IamAuthoredUser();
        authorizationService.getIamAuthoredUser(userId, serviceCode).subscribe(o -> {
            BeanUtils.copyProperties(o, iamAuthoredUser);
        }, watchRxError);
        Optional.ofNullable(watchRxError.getErrorContent()).ifPresent((e) -> {
            log.error("getIamUserInfo error esUserId:" + userId + " customerServiceCode:" + serviceCode, e);
            throw e;
        });
        return iamAuthoredUser;
    }

    @Override
    public String getAuthToken(){
        try{
            IamAuthoredUser iamAuthoredUser = getIamAuthoredUser(kcfUserId, kcfServiceCode);
            if(iamAuthoredUser != null)
            {
                return iamAuthoredUser.getToken();
            }

        }catch (Exception ex){
            log.error("获取iamtoken失败:{}",ex.toString());
        }
        return "";
    }

    @Override
    public JSONObject searchCharFile(String productCode,String content, String serviceRegion){
        String mapAsString ="";
        String url="";
        String startSearchTime="";
        String respondTime="";
        //headMap用于记sr里面的日志
        Map<String,Object> headMap = new HashMap<>();
        Map<String, Object> map = new HashMap<>();
        String chatfileResponse = "";
        try {
            String token = kmoCache.getChatFileAuth();
            if(StringUtils.isEmpty(token)) {
                Map<String, Object> mapList = new HashMap<String, Object>();
                mapList.put("chatAPIError", "chatAPI Error. token error.");
                return JSON.parseObject(JSON.toJSONString(mapList), JSONObject.class);
            }

            // 取資料表中的資料
            ChatFileConfig chatFileConfig = issueSyncDao.getCharFileConfig(productCode,serviceRegion);
            if(chatFileConfig == null){
                log.error("案件搜索charFile，無產品線 {} 配置資訊或未將issueChatFileSearch設定為true",productCode);
                Map<String, Object> mapList =new HashMap<String, Object>();
                mapList.put("chatAPIError","chatAPI Error. not found product");
                return JSON.parseObject(JSON.toJSONString(mapList),JSONObject.class);
                //return new JSONObject();
            }
            HttpHeaders headers = new HttpHeaders();

            headers.add("Accept-Language", "CN".equals(connectArea) ? "zh-CN" : "zh-TW");
            headMap.put("Accept-Language","CN".equals(connectArea) ? "zh-CN" : "zh-TW");
            headers.add("token", token);
            headMap.put("token",token);
            headers.add("digi-middleware-auth-app", appToken);
            headMap.put("digi-middleware-auth-app",appToken);
            headers.add("digi-kai-api-key", apiKey);
            headMap.put("digi-kai-api-key",apiKey);
            headers.add("content-type", "application/json");
            headMap.put("content-type","application/json");


            map.put("language",getLanguageName("CN".equals(connectArea) ? "zh-CN" : "zh-TW"));
            map.put("folderNo","");
            map.put("question",content);
            map.put("historyFlag",false);
            map.put("historyList",new ArrayList<>() );
            map.put("fileNo","");
            map.put("rootDirectory",chatFileConfig.getRootDirectory());
            map.put("oneDirectory", chatFileConfig.getOne_directory());
            map.put("twoDirectory",chatFileConfig.getTwo_directory());
            map.put("threeDirectory",chatFileConfig.getThree_directory());
            mapAsString = map.keySet().stream()
                    .map(key -> key + ":" + map.get(key))
                    .collect(Collectors.joining(", ", "{", "}"));

            HttpEntity  requestEntity = new HttpEntity<>(map, headers);
            url=kcfUrlAddress+"restful/standard/kcf/isv/api/v2/qa/chatfile/for/sc";

            log.info("案件搜索charFile，url , {}",url);
            log.info("案件搜索charFile，map , {}",mapAsString);

            SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
            factory.setConnectTimeout(45*1000);
            factory.setReadTimeout(45*1000);

            RestTemplate restTemplate= new RestTemplate(factory);

            LocalDateTime now = DateUtil.getLocalNow();
            startSearchTime=DateUtil.getSomeDateFormatString(now, DateUtil.DATE_TIME_FORMATTER);

            ResponseEntity<String> response = restTemplate.exchange( url, HttpMethod.POST, requestEntity, String.class);

            LocalDateTime endnow = DateUtil.getLocalNow();
            respondTime=DateUtil.getSomeDateFormatString(endnow, DateUtil.DATE_TIME_FORMATTER);
            chatfileResponse = response.getBody();
            //记录搜索日志到sr表chatfileCallLog
            saveSRLog(defaultSid, content, productCode, serviceRegion, url, startSearchTime, respondTime, map, headMap, chatfileResponse,"issueInternal");
            if (response.getStatusCode().value() == HttpStatus.SC_OK) {
                String body = response.getBody();
                //{"duration":3608,"statusDescription":"OK","response":{"message":null,"success":true,"data":{"answer":"","knowledge_no":[""],"status":"success"}},"profile":{"tenantName":"","tenantSid":,"tenantId":"ServiceCloudManager","userSid":,"userName":"","userId":""},"uuid":"","status":200}
                JSONObject json = JSON.parseObject(body, JSONObject.class);
                JSONObject bodyResponse = null;
                try {
                    bodyResponse = JSON.parseObject(json.get("response").toString(), JSONObject.class);
                    if (bodyResponse != null) {
                        JSONObject data = null;
                        try {
                            data = JSON.parseObject(bodyResponse.get("data").toString(), JSONObject.class);
                            if (data != null) {
                                log.info("案件搜索charFile，搜索成功, {}", body);
                                Map<String, Object> mapList = new HashMap<String, Object>();
                                mapList.put("answer", data.getOrDefault("answer", null)); // 其返回會有string
                                mapList.put("knowledge_no", data.getOrDefault("knowledge_no", null)); // 其返回的結構為[]或null
                                mapList.put("file_info", data.getOrDefault("file_info", "[]"));// 其返回的結構為[]或null
                                mapList.put("status", data.getOrDefault("status", null)); // 其返回會有string   failed、success <--目前不作此判別
                                mapList.put("data", bodyResponse.getOrDefault("data", null));
                                return JSON.parseObject(JSON.toJSONString(mapList), JSONObject.class);
                            } else {
                                // 表示回饋的結構中沒有data，則當作查詢有問題，直接返回原本的結構，使判別說為查不到資料
                                log.info("案件搜索charFile，API返回無data結構, {}", body);
                                Map<String, Object> mapList = new HashMap<String, Object>();
                                mapList.put("error", body +"  startSearchTime:"+ startSearchTime +" respondTime:"+respondTime+"  Url: " + url + "   body map: " + mapAsString);
                                return JSON.parseObject(JSON.toJSONString(mapList), JSONObject.class);
                            }
                        } catch (Exception e) {
                            log.info("案件搜索charFile，API返回data不為JSON 格式, {}", body);
                            Map<String, Object> mapList = new HashMap<String, Object>();
                            mapList.put("error", body + "  startSearchTime:"+ startSearchTime +" respondTime:"+respondTime+"  Url: " + url + "   body map: " + mapAsString);
                            return JSON.parseObject(JSON.toJSONString(mapList), JSONObject.class);
                        }
                    } else {
                        log.info("案件搜索charFile，API返回無response結構, {}", body);
                        Map<String, Object> mapList = new HashMap<String, Object>();
                        mapList.put("error", body + "  startSearchTime:"+ startSearchTime +" respondTime:"+respondTime+"  Url: " + url + "   body map: " + mapAsString);
                        return JSON.parseObject(JSON.toJSONString(mapList), JSONObject.class);
                    }
                } catch (Exception e) {
                    log.info("案件搜索charFile，API返回response不為JSON 格式, {}", body);
                    Map<String, Object> mapList = new HashMap<String, Object>();
                    mapList.put("error", body +"  startSearchTime:"+ startSearchTime +" respondTime:"+respondTime+ "  Url: " + url + "   body map: " + mapAsString);
                    return JSON.parseObject(JSON.toJSONString(mapList), JSONObject.class);
                }
            } else {
                log.info("案件搜索charFile，搜索失败, {}",response.getStatusCode().value());
                Map<String, Object> mapList =new HashMap<String, Object>();
                mapList.put("chatAPIError","Client error, unable to receive the returned message. code:");
                return JSON.parseObject(JSON.toJSONString(mapList),JSONObject.class);
            }
        }
        catch (HttpStatusCodeException hcee) {
            // 有異常code
            log.error("Exception {}", hcee.getStatusCode().value() + "    Message: " + hcee.getMessage());
            Map<String, Object> mapList = new HashMap<String, Object>();
            if(StringUtils.isEmpty(respondTime)) {
                LocalDateTime endnow = DateUtil.getLocalNow();
                respondTime = DateUtil.getSomeDateFormatString(endnow, DateUtil.DATE_TIME_FORMATTER);
            }
            mapList.put("chatAPIError", "Client error, unable to receive the returned message. code:");
            //记录搜索日志到sr表chatfileCallLog
            saveSRLog(defaultSid, content, productCode, serviceRegion, url, startSearchTime, respondTime, map, headMap, hcee.getMessage(),"issueInternal");
            return JSON.parseObject(JSON.toJSONString(mapList), JSONObject.class);
        }
        catch (Exception e1) {
            log.error("案件搜索charFile，搜索失败，{}", e1.getCause().getMessage() + "   Message: " + e1.getMessage());
            Map<String, Object> mapList = new HashMap<String, Object>();
            if(StringUtils.isEmpty(respondTime)) {
                LocalDateTime endnow = DateUtil.getLocalNow();
                respondTime = DateUtil.getSomeDateFormatString(endnow, DateUtil.DATE_TIME_FORMATTER);
            }
            if ("connect timed out".equals(e1.getCause().getMessage()) || "Read timed out".equals(e1.getCause().getMessage())) {
                //逾時
                mapList.put("chatAPIError", "Timeout expired with no response.");
            } else {
                // 網域錯誤、斷網...
                mapList.put("chatAPIFail", "Client error, unable to receive the returned message. code:");
            }
            //记录搜索日志到sr表chatfileCallLog
            saveSRLog(defaultSid, content, productCode, serviceRegion, url, startSearchTime, respondTime, map, headMap, e1.getMessage(),"issueInternal");
            return JSON.parseObject(JSON.toJSONString(mapList), JSONObject.class);
            // log.error("案件搜索charFile，搜索失败，{}",e1.getMessage());
        }
        //return new JSONObject();
    }

    private String getLanguageName(String lang){
        switch (lang) {
            case "zh-TW":
                return "繁體中文";
            case "zh-CN":
                return "简体中文";
            case "en-US":
                return "English";
            case "vi-VN":
                return "Tiếng Việt";
            case "th-TH":
                return  "แบบไทย";
            case "ms-MY":
                return "Melayu";
            case "ko-KR": //韓文
                return "한국인";
            case "ja-JP": //日語
                return "日本語";
            default:
                return "繁體中文";
        }
    }

    /**
     * 记录搜索日志到sr表chatfileCallLog
     * @param sid
     * @param searchContent
     * @param productCode
     * @param serviceRegion
     * @param url
     * @param startSearchTime
     * @param respondTime
     * @param mapAsString
     * @param headMap
     * @param body
     */
    /*private void saveSRLog(long sid, String searchContent, String productCode, String serviceRegion, String url, String startSearchTime, String respondTime, String mapAsString, Map<String,Object> headMap, String body,String source) {
        try
        {
            StringBuffer sb = new StringBuffer();
            sb.append("insert into servicecloud.chatfileCallLog(id,startSearchTime,responseTime,sid,source,productCode,serviceRegion,searchContent," +
                    "resultContent,callUrl,callType,APIparam,APIbody)");
            sb.append(" values ") ;
            sb.append("(");
            sb.append(SnowFlake.getInstance().newId()).append(",");
            sb.append("\"").append(startSearchTime).append("\",");
            sb.append("\"").append(respondTime).append("\",");
            sb.append(sid).append(",");
            sb.append("\"").append(source).append("\",");
            sb.append("\"").append(productCode).append("\",");
            sb.append("\"").append(serviceRegion).append("\",");
            sb.append("\"").append(searchContent).append("\",");
            sb.append(JSON.toJSONString(body)).append(",");
            sb.append("\"").append(url).append("\",");
            sb.append("\"").append(HttpMethod.POST).append("\",");

            HashMap<String,Object> srParam = new HashMap<>();
            srParam.put("head",headMap);
            sb.append(JSON.toJSONString(JSON.toJSONString(srParam))).append(",");

            sb.append(JSON.toJSONString(mapAsString));
            sb.append("),");

            bigDataUtil.srSave(sb.substring(0,sb.length()-1));
        }
        catch (Exception e){
            log.error("saveSRLog:chatfileCallLog:" + e.toString());
        }
    }*/

    /**
     *
     * 优化sr存储
     */
    private void saveSRLog(long sid, String searchContent, String productCode, String serviceRegion, String url, String startSearchTime, String respondTime,
                           Map<String,Object> bodyMap, Map<String,Object> headMap, String resultContent,String source) {
        List<Map<String, Object>> edrOperateLogList = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        map.put("id", SnowFlake.getInstance().newId());
        map.put("startSearchTime", startSearchTime);
        map.put("respondTime", respondTime);
        map.put("sid", sid);
        map.put("source", source);
        map.put("aiSource", "chatfile");
        map.put("productCode", productCode);
        map.put("serviceRegion", serviceRegion);
        map.put("searchContent", searchContent);
        map.put("resultContent", resultContent);
        map.put("callUrl", url);
        map.put("callType", HttpMethod.POST);
        HashMap<String,Object> srParam = new HashMap<>();
        srParam.put("head",headMap);
        map.put("APIparam", srParam);
        map.put("APIbody", bodyMap);

        UploadBigDataContext uploadBigDataContext = new UploadBigDataContext(null, "", null, null, "chatfileCallLog", false, map);
        edrOperateLogList.addAll(bigDataUtil.createParentUploadStruct(uploadBigDataContext));

        bigDataUtil.simulateLocalUploadData(edrOperateLogList);
    }
}
