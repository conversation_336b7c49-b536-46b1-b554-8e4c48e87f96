package com.digiwin.escloud.aioitms.bcp.service;

import com.digiwin.escloud.aioitms.bcp.model.BcpRequestParam;
import com.digiwin.escloud.aioitms.bcp.model.BcpSurveyRecordSave;
import com.digiwin.escloud.aioitms.bcp.model.BcpSurveyRecord;
import com.digiwin.escloud.common.response.BaseResponse;

public interface IBcpService {
    BaseResponse getData(String version, String lang);
    BaseResponse getResultBySurveyId(String surveyId, String lang);
    BaseResponse getResultList(BcpRequestParam param);
    BaseResponse saveCustomerData (BcpSurveyRecord param);
    BaseResponse saveRecord(BcpSurveyRecordSave bcpSurveyRecordSave);
}
