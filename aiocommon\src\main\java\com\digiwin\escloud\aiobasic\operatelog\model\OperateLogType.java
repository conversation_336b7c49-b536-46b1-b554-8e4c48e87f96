package com.digiwin.escloud.aiobasic.operatelog.model;

public enum OperateLogType {
    Generate("aiops_report_type_4_generate", "生成新報告"),
    Send2MIS("aiops_report_type_4_send2mis", "發送報告"),
    SendMail("aiops_report_type_4_sendmail", "發送郵件"),
    Delete("aiops_report_type_4_delete", "刪除")
    ;

    private String code;
    private String msg;

    private OperateLogType(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
