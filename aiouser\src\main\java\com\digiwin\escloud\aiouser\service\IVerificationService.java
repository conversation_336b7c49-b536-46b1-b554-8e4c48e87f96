package com.digiwin.escloud.aiouser.service;

import com.digiwin.escloud.integration.api.emc.res.common.BaseResultRes;
import com.digiwin.escloud.integration.api.emc.res.common.EmcResultRes;

public interface IVerificationService {
    BaseResultRes getVerificationCodeByEmail(String lang, String account, String scene);
    BaseResultRes getVerificationCodeByPhone(String lang, String phone, String scene);
    EmcResultRes checkVerificationCode(String account, String scene, String code);
}
