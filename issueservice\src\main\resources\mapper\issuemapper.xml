<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="escloud.issuemapper">
    <resultMap id="issueDetailmap" type="com.digiwin.escloud.issueservice.model.IssueDetailInfo">
        <result property="issueId" column="issueId"/>
        <association property="issueCasedetail" column="issueId" select="escloud.issuedetailmapper.selectIssuedetail"/>
    </resultMap>
    <resultMap id="issuemap" type="com.digiwin.escloud.issueservice.model.Issue">
    </resultMap>
    <resultMap id="issueselectmap" type="com.digiwin.escloud.issueservice.model.Issue">
        <result property="issueId" column="issueId"/>
        <result property="crmId" column="crmId"/>
        <result property="serviceCode" column="serviceCode"/>
        <result property="productCode" column="productCode"/>
        <result property="productCategory" column="productCategory"/>
        <result property="productVersion" column="productVersion"/>
        <result property="userId" column="userId"/>
        <result property="submitName" column="submitName"/>
        <result property="submitUserName" column="submitUserName"/>
        <result property="submitedId" column="submited_id"/>
        <result property="ccUser" column="ccUser"/>
        <result property="userContactId" column="userContactId"/>
        <result property="issueDescription" column="issueDescription"/>
        <result property="question_title" column="question_title"/>
        <result property="issueStack" column="issueStack"/>
        <result property="submitWay" column="submitWay"/>
        <result property="issueStatus" column="issueStatus"/>
        <result property="submitTime" column="submitTime"/>
        <result property="serviceStaff" column="serviceStaff"/>
        <result property="supportStaff" column="SupportStaff"/>
        <result property="serviceId" column="ServiceId"/>
        <result property="serviceDepartment" column="ServiceDepartment"/>
        <result property="supportDepartment" column="SupportDepartment"/>
        <result property="department" column="department"/>
        <result property="requestPatchId" column="requestPatchId"/>
        <result property="programCode" column="programCode"/>
        <result property="programVersion" column="programVersion"/>
        <result property="issueType" column="issueType"/>
        <result property="syncStatus" column="syncStatus"/>
        <result property="customerName" column="CustomerName"/>
        <result property="programName" column="programName"/>
        <result property="planDownloadTime" column="planDownloadTime"/>
        <result property="planUpdateTime" column="planUpdateTime"/>
        <result property="serviceRegion" column="serviceRegion"/>
        <result property="machineRegion" column="machineRegion"/>
        <result property="site" column="site"/>
        <result property="environment" column="environment"/>
        <result property="ent" column="ent"/>
        <result property="isDirectClosedShow" column="isDirectClosedShow"/>
        <result property="isWaitingEvaluatedIssue" column="isWaitingEvaluatedIssue"/>
        <result property="contractExprityDate" column="contractExprityDate"/>
        <result property="serviceContact" column="serviceContact"/>
        <result property="serviceContactRed" column="serviceContactRed"/>
        <result property="projectUpdate" column="projectUpdate"/>
        <result property="projectUpdateRed" column="projectUpdateRed"/>
        <result property="serialNumberUpdate" column="serialNumberUpdate"/>
        <result property="serialNumberUpdateRed" column="serialNumberUpdateRed"/>
        <result property="userCollect" column="userCollect"/>
        <association property="userContact" columnPrefix="userContact_" resultMap="userContactselectmap"/>
        <collection property="issueSourceMapList" column="{issueId=issueId}" javaType="ArrayList"
                    ofType="com.digiwin.escloud.issueservice.model.IssueSourceMap" select="selectNewIssueSourceMap"/>
    </resultMap>

    <resultMap id="issueChangeSubmiterHistory" type="com.digiwin.escloud.issueservice.model.IssueChangeSubmiterHistory">
        <result property="id" column="id"/>
        <result property="issueId" column="issueId"/>
        <result property="operatorId" column="operatorId"/>
        <result property="beforeSubmiter" column="beforeSubmiter"/>
        <result property="afterSubmiter" column="afterSubmiter"/>
        <result property="changeReson" column="changeReson"/>
        <result property="changeTime" column="changeTime"/>
    </resultMap>

    <resultMap id="issueChangeSubmiterHistoryUserpersonalinfo" type="com.digiwin.escloud.issueservice.model.IssueChangeSubmiterHistoryUserpersonalinfo">
        <result property="id" column="id"/>
        <result property="issueId" column="issueId"/>
        <result property="operatorId" column="operatorId"/>
        <result property="operatorName" column="operatorName"/>
        <result property="beforeSubmiter" column="beforeSubmiter"/>
        <result property="beforeSubmiterName" column="beforeSubmiterName"/>
        <result property="afterSubmiter" column="afterSubmiter"/>
        <result property="afterSubmiterName" column="afterSubmiterName"/>
        <result property="changeReson" column="changeReson"/>
        <result property="changeTime" column="changeTime"/>
    </resultMap>

    <resultMap id="issuefullcontentsearchmap" type="com.digiwin.escloud.issueservice.model.Issue">
        <result property="issueId" column="issueId"/>
        <result property="crmId" column="crmId"/>
        <result property="serviceCode" column="serviceCode"/>
        <result property="productCode" column="productCode"/>
        <result property="productCategory" column="productCategory"/>
        <result property="productVersion" column="productVersion"/>
        <result property="userId" column="userId"/>
        <result property="submitName" column="submitName"/>
        <result property="submitUserName" column="submitUserName"/>
        <result property="ccUser" column="ccUser"/>
        <result property="userContactId" column="userContactId"/>
        <result property="issueDescription" column="issueDescription"/>
        <result property="issueStack" column="issueStack"/>
        <result property="submitWay" column="submitWay"/>
        <result property="issueStatus" column="issueStatus"/>
        <result property="submitTime" column="submitTime"/>
        <result property="serviceStaff" column="serviceStaff"/>
        <result property="serviceId" column="ServiceId"/>
        <result property="serviceDepartment" column="ServiceDepartment"/>
        <result property="department" column="department"/>
        <result property="requestPatchId" column="requestPatchId"/>
        <result property="programCode" column="programCode"/>
        <result property="programVersion" column="programVersion"/>
        <result property="issueType" column="issueType"/>
        <result property="syncStatus" column="syncStatus"/>
        <result property="customerName" column="CustomerName"/>
        <result property="programName" column="programName"/>
        <result property="planDownloadTime" column="planDownloadTime"/>
        <result property="planUpdateTime" column="planUpdateTime"/>
        <result property="serviceRegion" column="serviceRegion"/>
        <result property="machineRegion" column="machineRegion"/>
        <result property="site" column="site"/>
        <result property="environment" column="environment"/>
        <result property="ent" column="ent"/>
        <result property="isDirectClosedShow" column="isDirectClosedShow"/>
        <result property="isWaitingEvaluatedIssue" column="isWaitingEvaluatedIssue"/>
        <result property="contractExprityDate" column="contractExprityDate"/>
        <association property="userContact" columnPrefix="userContact_" resultMap="userContactselectmap"/>
        <collection property="issueSourceMapList" column="{issueId=issueId}" javaType="ArrayList"
                    ofType="com.digiwin.escloud.issueservice.model.IssueSourceMap" select="selectAthenaIssueSourceMap"/>
    </resultMap>

    <resultMap id="userContactselectmap" type="com.digiwin.escloud.issueservice.model.UserContact">
        <result property="id" column="id"/>
        <result property="userId" column="userId"/>
        <result property="name" column="name"/>
        <result property="email" column="email"/>
        <result property="phone01" column="phone01"/>
        <result property="phone02" column="phone02"/>
        <result property="phone03" column="phone03"/>
        <result property="qq" column="qq"/>
    </resultMap>

    <resultMap id="issueprogressmap" type="com.digiwin.escloud.issueservice.model.IssueProgress">
    </resultMap>

    <resultMap id="issueadditionalexplanationmap" type="com.digiwin.escloud.issueservice.model.IssueAdditionalExplanation">
    </resultMap>

    <resultMap id="issuecasedetailmap" type="com.digiwin.escloud.issueservice.model.IssueCasedetail">
    </resultMap>

    <resultMap id="issuekbsharemap" type="com.digiwin.escloud.issueservice.model.IssueKbshare">
    </resultMap>

    <resultMap id="issuecountmap" type="com.digiwin.escloud.issueservice.model.IssueCount">
    </resultMap>

    <resultMap id="serviceproductcountmap" type="com.digiwin.escloud.issueservice.model.ServiceProductCount">
    </resultMap>

    <resultMap id="userContactmap" type="com.digiwin.escloud.issueservice.model.UserContact">
    </resultMap>
    <sql id="authCondition">
        <choose>
            <when test="department != ''">
                and issue.${deptId} = #{department}
            </when>
            <otherwise>
                <if test="userId != ''">
                    and issue.${Id} = #{userId}
                </if>
            </otherwise>
        </choose>
    </sql>

    <sql id="staffAuthCondition">
        and ${selecttable}.ServiceId = #{userId} and ${selecttable}.ServiceDepartment = #{department}
    </sql>

    <insert id="insertIssue" useGeneratedKeys="true" parameterType="com.digiwin.escloud.issueservice.model.Issue"
            keyProperty="issueId" keyColumn="IssueId">
        INSERT INTO issue (ServiceCode,ProductCode,ProductVersion,UserId,submited_id,CcUser,
                            question_title,IssueDescription,IssueStack,SubmitWay,IssueStatus,SubmitTime,
                            RequestPatchId, ProgramCode, ProgramVersion, IssueType, SyncStatus,
                            planDownloadTime, planUpdateTime, site, environment,ent
                            <if test="machineRegion !=null and machineRegion!=''">
                                ,machineRegion
                            </if>
                            <if test="serviceRegion !=null and serviceRegion!=''">
                                ,serviceRegion
                            </if>
                            <if test="issueClassificationNo !=null and issueClassificationNo!=''">
                                ,issueClassificationNo
                            </if>
                            <if test="warningId !=null and warningId!=''">
                                ,seqId
                            </if>
                            ,issueCloseInform,username,email,phone,emergency)
        VALUES (#{serviceCode}, #{productCode}, #{productVersion}, #{userId},#{userId}, #{ccUser},
                 SUBSTRING(#{issueDescription},1,255),#{issueDescription}, #{issueStack}, #{submitWay}, #{issueStatus}, #{submitTime},
                 #{requestPatchId}, #{programCode}, #{programVersion}, #{issueType}, #{syncStatus},
                 #{planDownloadTime}, #{planUpdateTime}, #{site}, #{environment},#{ent}
                <if test="machineRegion !=null and machineRegion!=''">
                    ,#{machineRegion}
                </if>
                <if test="serviceRegion !=null and serviceRegion!=''">
                    ,#{serviceRegion}
                </if>
                <if test="issueClassificationNo !=null and issueClassificationNo!=''">
                    ,#{issueClassificationNo}
                </if>
                <if test="warningId !=null and warningId!=''">
                    ,#{warningId}
                </if>
                ,#{issueCloseInform},#{userContact.name},#{userContact.email},
                <choose>
                    <when  test="userContact.phone02 !=null and userContact.phone02!=''">
                        CONCAT(#{userContact.phone01},'#', #{userContact.phone02})
                    </when>
                    <otherwise>
                        #{userContact.phone01}
                    </otherwise>
                </choose>
                ,#{emergency})
    </insert>

    <update id="updateIssue">
        UPDATE issue
        SET CrmId = #{crmId}, IssueStatus = #{issueStatus}, ServiceId = #{serviceId}, ServiceDepartment = #{department}, UserContactId = #{userContactId}, main_charge = #{serviceId}
        WHERE IssueId = #{issueId}
    </update>

    <update id="updateSyncStatusByIssueId">
        UPDATE issue
        SET SyncStatus = #{syncStatus}
        WHERE IssueId = #{issueId}
    </update>
    <update id="UpdateProcessSyncStatusByIssueId">
        UPDATE issue_progress
        SET SyncStatus = #{syncStatus}
        WHERE IssueId = #{issueId} and ProcessType='Submit'
    </update>
    <update id="updateIssueStatus">
        UPDATE issue
        SET IssueStatus = #{issueStatus}
        WHERE IssueId = #{issueId}
        <choose>
            <when test='oldIssueStatus == "Y"||oldIssueStatus == "7"||oldIssueStatus == "10" || oldIssueStatus == "A"'>
                AND IssueStatus in ('Y','7','10','A')
            </when>
            <otherwise>
                AND IssueStatus = #{oldIssueStatus}
            </otherwise>
        </choose>
        <include refid="authCondition">
            <property name="Id" value="UserId"/>
            <property name="deptId" value="ServiceCode"/>
        </include>
    </update>

    <update id="updateProcessor">
        UPDATE issue
        SET ServiceId = #{serviceId},ServiceDepartment = #{department}
        WHERE IssueId = #{issueId}
    </update>
    <update id="updateIssueStatusByStaff">
        UPDATE issue
        SET IssueStatus = #{issueStatus}
        WHERE IssueId = #{issueId}
        <if test='oldIssueStatus == "N"'>
           AND IssueStatus IN ('2','3','4','11','12','22','N','O','VN','Q','R')
        </if>
        <if test='oldIssueStatus != "N"'>
            and IssueStatus = #{oldIssueStatus}
        </if>
        <!--<include refid="authCondition">-->
            <!--<property name="Id" value="ServiceId"/>-->
            <!--<property name="deptId" value="ServiceDepartment"/>-->
        <!--</include>-->
    </update>

    <update id="updateIssueStatusForAutoUpdateSystem">
        UPDATE issue
        SET IssueStatus = #{issueStatus},SyncStatus = #{syncStatus}
        WHERE IssueId = #{issueId}
    </update>
    <select id="selectIssueStatus" resultType="String">
        select
             case when issue.IssueStatus IN ('2','3','4','11','12','22','O','VN','Q','R') then 'N'
                  when issue.IssueStatus IN ('7','10','A') then 'Y'
                  else issue.IssueStatus END  IssueStatus
        from issue
        WHERE IssueId = #{issueId}
    </select>
    <select id="getCrmIdByIssueId" resultType="String">
        select crmId
        from issue
        WHERE IssueId = #{issueId}
    </select>

    <insert id="insertIssueProgress">
        INSERT INTO issue_progress (IssueId,CrmId,SequenceNum,ProcessType,Processor,Description,ProcessTime,ReplyType,ProcessHours,SyncStatus,CurrentStatus,workno,handlerId)
        VALUES (#{IssueId}, #{CrmId}, #{SequenceNum}, #{ProcessType}, #{Processor},#{Description},  #{ProcessTime},#{ReplyType},
        <if test="(Description =='Accept Issue' or Description =='问题受理中' or Description =='Resolved Issue' or Description =='问题已解决'  )">
            0,
        </if>
        <if test="Description !='Accept Issue' and Description !='问题受理中' and Description !='Resolved Issue' and Description !='问题已解决'">
            #{ProcessHours},
        </if>
    #{SyncStatus},#{CurrentStatus},#{workno},#{handlerId})
</insert>

<!--<insert id="insertIssueProgress">-->
    <!--INSERT INTO issue_progress (IssueId,CrmId,SequenceNum,ProcessType,Processor,Description,ProcessTime)-->
    <!--VALUES (#{IssueId}, #{CrmId}, #{SequenceNum}, #{ProcessType}, #{Processor},#{Description}, #{ProcessTime})-->
    <!--</insert>-->

    <select id="selectIssueList" resultMap="issueselectmap">
        SELECT issue.IssueId,ifnull(issue.CrmId, '') as CrmId,issue.ServiceCode,issue.ProductCode,issue.ProductVersion,
        issue.UserId,issue.CcUser,issue.UserContactId,issue.IssueDescription,issue.IssueStack,issue.SubmitWay,
        issue.IssueStatus,issue.SubmitTime, ifnull(d.fullname, e.ServiceStaff) as ServiceStaff,
        f.`Name` as userContact_name, f.email as userContact_email, f.phone01 as userContact_phone01,
        f.phone02 as userContact_phone02, f.qq as userContact_qq, issue.UserContactId as userContact_id, f.userId as
        userContact_userId,
        issue.RequestPatchId, issue.ProgramCode,issue.ProgramVersion, issue.IssueType, issue.SyncStatus,
        issue.planDownloadTime, issue.planUpdateTime
                ,g.CustomerName,h.ProgramName as programName, product.ProductCategory as productCategory
        ,j.name as submitName,case when issue.newReply = 'serviceNewReply' then true else false end newReply,
        case when iucUserId is not null then 1 else 0 end userCollect
        FROM (SELECT issue.*,iuc.userId iucUserId FROM issue
        LEFT JOIN issue_casedetail cd ON cd.IssueId = issue.IssueId
        LEFT JOIN mars_user mu ON mu.ID = issue.userId
        LEFT JOIN issue_usercollection iuc ON iuc.issueId = issue.IssueId and iuc.userId=#{submiterId}
        <if test='issuestatus == "T"'>
            LEFT JOIN mars_servicesetting s ON s.productCode = issue.productCode AND s.serviceCode = issue.serviceCode
        </if>
        WHERE issue.IssueStatus!='V'
        <!--<if test="productCode != ''">
            AND issue.ProductCode = #{productCode}
        </if>-->
        <choose>
            <when test="productCode01.size > 0 and productCode02.size > 0">
                and (
                <foreach collection="productCode01" item="item"
                         open=" (((mu.UserType !=  '2' and issue.userId is not null and issue.userId != '') or (issue.userId is null or issue.userId = '') ) and issue.productCode in ( "
                         separator=", " close=")">
                    #{item}
                </foreach>
                ) or
                <foreach collection="productCode02" item="item" open=" issue.ProductCode in ( " separator=", "
                         close=")">
                    #{item}
                </foreach>
                )
            </when>
            <when test="productCode02.size == 0 and productCode01.size > 0 ">
                and
                <foreach collection="productCode01" item="item"
                         open=" ((mu.UserType !=  '2' and issue.userId is not null and issue.userId != '') or (issue.userId is null or issue.userId = '') ) and issue.productCode in ( "
                         separator=", " close=")">
                    #{item}
                </foreach>
            </when>
            <when test="productCode01.size == 0 and productCode02.size > 0 ">
                and
                <foreach collection="productCode02" item="item" open=" issue.ProductCode in ( " separator=", "
                         close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND (   (issue.ProductCode IN ('100','06','999','164','147')
                AND ( (mu.UserType !=  '2' and issue.userId is not null and issue.userId != '') or (issue.userId is null or issue.userId = '') )
                )
                OR issue.ProductCode not IN ('100','06','999','164','147'))
            </otherwise>
        </choose>
        <if test='issuestatus == "T"'>
            AND issue.IssueStatus ='T'
            AND FIND_IN_SET(#{userId},s.confirmIds)
        </if>
        <!--<if test='issuestatus == "N"'>
            AND  issue.IssueStatus in ('I', 'C', 'N','2','3','4','11','12','22','O','Q','R')
        </if>-->
        <!--兼容web云管家 和 云管家 反馈人验证中 11, R-->
        <choose>
            <when test='issuestatus == "N"'>
                <choose>
                    <when test='from == "web"'>
                        AND  issue.IssueStatus in ('I', 'C', 'N','2','3','4','12','O','VN','Q')
                    </when>
                    <otherwise>
                        AND  issue.IssueStatus in ('I', 'C', 'N','2','3','4','11','12','22','O','VN','Q','R')
                    </otherwise>
                </choose>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='issuestatus == "Y"'>
            AND issue.IssueStatus in ('Y','7','10','A')
        </if>
        <if test='issuestatus == "R"'>
            AND issue.IssueStatus in ('11','R')
        </if>
        <if test='issuestatus == "22"'>
            AND issue.IssueStatus = '22'
        </if>
        <if test='issuestatus!="N" and issuestatus!="Y" and issuestatus!="R" and issuestatus!="22" and issuestatus !=null and issuestatus != ""'>
            AND #{issuestatus} in (issue.IssueStatus, '')
        </if>
        <if test="issueType != ''">
            AND issue.IssueType = #{issueType}
        </if>
        <include refid="authCondition">
            <property name="Id" value="UserId"/>
            <property name="deptId" value="ServiceCode"/>
        </include>
        <if test="newReply  !=null and newReply != ''">
            AND issue.newReply = #{newReply}
        </if>
        <choose>
            <when test="myself == '1'.toString()">
                AND issue.submited_id = #{submiterId}
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test="serviceCode  !=null and serviceCode != ''">
            AND issue.ServiceCode = #{serviceCode}
        </if>
        <if test="userCollect  !=null and userCollect != ''">
            AND iuc.userId = #{submiterId}
        </if>
        AND NOT EXISTS (
        SELECT kh.KH001 FROM calkh kh
        WHERE kh.KH002 = cd.IssueClassification AND kh.KH001 = issue.ProductCode)
        ORDER BY issue.SubmitTime DESC
        LIMIT #{start} , #{end}
        ) issue
        LEFT JOIN mars_userpersonalinfo b on b.userid = issue.ServiceId
        LEFT JOIN mars_customerservicesatff d on d.workno = b.workno
        LEFT JOIN mars_customerservice e on e.CustomerServiceCode = issue.ServiceCode and (ifnull(issue.ProductCode,'')
        = '' or e.ProductCode = issue.ProductCode)
        LEFT JOIN user_contacts f on f.Id = issue.UserContactId
        LEFT JOIN mars_customer g on issue.ServiceCode = g.CustomerServiceCode
        LEFT JOIN erppatchs h on issue.RequestPatchId = h.PatchId
        LEFT JOIN mars_product product on issue.ProductCode = product.ProductCode
        LEFT JOIN mars_userpersonalinfo j on j.userid = issue.UserId
        GROUP BY issue.IssueId
        ORDER BY issue.SubmitTime DESC
    </select>

    <select id="getIssuesForMis" resultMap="issueselectmap">
        select issue.IssueId as issueId,issue.UserId as userId,issue.CrmId as crmId,
        case when issue.IssueStatus IN ('Y','7','10','A','P') then 'Y' when issue.IssueStatus not in ('Y','7','10','A','P','V') then 'N' ELSE 'N' END issueStatus,
        DATE_FORMAT(issue.submitTime,'%Y-%m-%d %H:%i:%S') as submitTime,
        issue.IssueDescription as issuedescription ,issue.ProductCode as productCode,
        issue.UserContactId,issue.ServiceId as ServiceId,issue.emergency,issue.ServiceCode customerServiceCode,
        b.name serviceName,f.Name as contactname
        from issue as issue
        <if test="erpSystemCode  !=null and erpSystemCode  !=''">
            left join issue_casedetail detail on issue.IssueId = detail.IssueId
        </if>
        LEFT JOIN mars_userpersonalinfo b on b.userid = issue.ServiceId
        LEFT JOIN user_contacts as f on f.Id = issue.UserContactId
        where issue.productCode in ('15','163','147') and issue.IssueStatus != 'V'
        <if test="startTime != ''">
            AND issue.submittime >= #{startTime}
        </if>
        <if test="endTime != ''">
            AND issue.submittime &lt;= #{endTime}
        </if>

        <if test="issueStatus  == 'N'.toString() ">
            AND  issue.IssueStatus not in ('Y','7','10','A','P','V')
        </if>
        <if test="issueStatus  == 'Y'.toString() ">
            AND issue.IssueStatus in ('Y','7','10','A','P')
        </if>
        <!--<if test="userId != ''">
            AND issue.submited_id = #{userId}
        </if>-->
        <!--<if test="employeeId != ''">
            AND issue.ServiceId = #{employeeId}
        </if>-->
        <if test="issueType != ''">
            AND issue.IssueType = #{issueType}
        </if>
        <if test="serviceRegion  !=null and serviceRegion  !=''">
            AND issue.serviceRegion = #{serviceRegion}
        </if>
        <if test="productCode  !=null and productCode != ''">
            AND issue.ProductCode = #{productCode}
        </if>
        <if test="erpSystemCode  !=null and erpSystemCode  !=''">
            AND detail.ErpSystemCode = #{erpSystemCode}
        </if>
        <if test="esSearch != null and esSearch != ''">
            AND ( issue.crmId like "%"#{esSearch}"%" or issue.IssueDescription like "%"#{esSearch}"%" )
        </if>
        <if test="serviceCode  !=null and serviceCode != ''">
            AND issue.ServiceCode = #{serviceCode}
        </if>
        <include refid="authCondition">
            <property name="Id" value="UserId"/>
            <property name="deptId" value="ServiceCode"/>
        </include>
        ORDER BY if( issue.IssueStatus IN ('Y','7','10','A'), 'Y' , 'N' ) ASC, issue.submitTime DESC
        <if test="end>0">
            LIMIT #{start} , #{end}
        </if>
    </select>

    <select id="selectIssueListbydescription" resultMap="issueselectmap">
        SELECT  issue.IssueId,ifnull(issue.CrmId, '') as CrmId,issue.ServiceCode,issue.ProductCode,issue.ProductVersion,
        issue.UserId,issue.UserContactId,issue.IssueDescription,issue.question_title,issue.IssueStack,issue.SubmitWay,
        <choose>
            <when test='from == "web"'>
                case when issue.IssueStatus IN ('N','2','3','4','12','8','O','VN','Q') then 'N'
                when issue.IssueStatus IN ('Y','7','10','A') then 'Y'
                when issue.IssueStatus IN ('11','R') then 'R'
                else issue.IssueStatus END  IssueStatus,
            </when>
            <otherwise>
                case when issue.IssueStatus IN ('N','2','3','4','11','12','22','8','O','VN','Q','R') then 'N'
                when issue.IssueStatus IN ('Y','7','10','A') then 'Y'
                else issue.IssueStatus END  IssueStatus,
            </otherwise>
        </choose>
        issue.SubmitTime,
        case when issue.IssueStatus ='T' then confirm.confirmIds ELSE issue.ServiceId end  ServiceId,
        case when issue.IssueStatus ='T' then confirm.confirmName ELSE issue.ServiceStaff end  ServiceStaff,
        f.`Name` as userContact_name, f.email as userContact_email, f.phone01 as userContact_phone01,
        f.phone02 as userContact_phone02, f.qq as userContact_qq, issue.UserContactId as userContact_id, f.userId as
        userContact_userId,
        issue.RequestPatchId, issue.ProgramCode,issue.ProgramVersion, issue.IssueType, issue.SyncStatus,
        issue.planDownloadTime, issue.planUpdateTime
        ,g.CustomerName,h.ProgramName as programName, issue.ProductCategory as productCategory, issue.serviceRegion as serviceRegion, issue.machineRegion as machineRegion
        ,issue.submitName
        ,case when count(issue.caseid)>0 then true else false end isWaitingEvaluatedIssue,
        case when issue.newReply = 'serviceNewReply' then true else false end newReply,
        case when iucUserId is not null then 1 else 0 end userCollect
        <choose>
            <when test="serviceRegion == 'TW' or serviceRegion == ''">
                <choose>
                    <when test="condition_TW == 'servicecontact'">
                        ,issue.serviceContact,issue.serviceContactRed
                    </when>
                    <when test="condition_TW == 'projectupdate'">
                        ,issue.projectUpdate,issue.projectUpdateRed
                    </when>
                    <when test="condition_TW == 'serialnumberupdate'">
                        ,issue.serialNumberUpdate,issue.serialNumberUpdateRed
                    </when>
                    <otherwise>
                        ,case when isc1.issueId != '' then 1 else 0 end serviceContact
                        ,case when isc1.serviceContactRed > 0 then 1 else 0 end serviceContactRed
                        ,case when isusf1.issueId != '' then 1 else 0 end projectUpdate
                        ,case when isusf1.projectUpdateRed > 0 then 1 else 0 end projectUpdateRed
                        ,case when isusf2.issueId != '' then 1 else 0 end serialNumberUpdate
                        ,case when isusf2.serialNumberUpdateRed > 0 then 1 else 0 end serialNumberUpdateRed
                    </otherwise>
                </choose>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        FROM (
            SELECT issue.*, ifnull(b.`name`, e.ServiceStaff) AS ServiceStaff, product.ProductCategory, satisfysr.caseid, j.name AS submitName,iuc.userId iucUserId
            <choose>
                <when test="serviceRegion == 'TW' or serviceRegion == ''">
                    <choose>
                        <when test="condition_TW == 'servicecontact'">
                            ,case when isc1.issueId != '' then 1 else 0 end serviceContact,case when isc1.serviceContactRed > 0 then 1 else 0 end serviceContactRed
                        </when>
                        <when test="condition_TW == 'projectupdate'">
                            ,case when isusf1.issueId != '' then 1 else 0 end projectUpdate,case when isusf1.projectUpdateRed > 0 then 1 else 0 end projectUpdateRed
                        </when>
                        <when test="condition_TW == 'serialnumberupdate'">
                            ,case when isusf2.issueId != '' then 1 else 0 end serialNumberUpdate,case when isusf2.serialNumberUpdateRed > 0 then 1 else 0 end serialNumberUpdateRed
                        </when>
                        <otherwise>
                        </otherwise>
                    </choose>
                </when>
                <otherwise>
                </otherwise>
            </choose>
            FROM issue
            LEFT JOIN issue_casedetail cd ON cd.IssueId = issue.IssueId
            LEFT JOIN mars_userpersonalinfo b ON IFNULL(issue.ServiceId, '') <![CDATA[<>]]> '' AND b.userid = issue.ServiceId
            LEFT JOIN mars_customerservice e ON IFNULL(issue.ServiceId, '') = '' AND e.CustomerServiceCode = issue.ServiceCode
            AND ( ifnull(issue.ProductCode, '') = ''
            OR e.ProductCode = issue.ProductCode)
            LEFT JOIN mars_product product ON issue.ProductCode = product.ProductCode
            LEFT JOIN mars_userpersonalinfo j ON j.userid = issue.UserId
            LEFT JOIN mars_user mu ON mu.ID = issue.userId
            LEFT JOIN issue_usercollection iuc ON iuc.issueId = issue.IssueId and iuc.userId=#{submiterId}
            <if test="waitingEvaluatedIssue_TW == ''">
                LEFT JOIN satisfysr on satisfysr.caseid = issue.CrmId and satisfysr.cusid = issue.ServiceCode and issue.IssueStatus IN ( 'Y','7','10','A')
                and (SELECT count(*) from kuqige where satisfysr.caseid = kuqige.CaseID and satisfysr.cusid = kuqige.CusID)=0
                <!-- 近三天，不含週六,週日-->
                and satisfysr.createtime >= date_sub(current_date ,interval 3-2*floor((dayofweek(current_date))/4)+(floor(((dayofweek(current_date)-8)/6))+2) day)
            </if>
            <if test="waitingEvaluatedIssue_TW != ''">
                INNER JOIN satisfysr on satisfysr.caseid = issue.CrmId and satisfysr.cusid = issue.ServiceCode and issue.IssueStatus IN ( 'Y','7','10','A')
                and (SELECT count(*) from kuqige where satisfysr.caseid = kuqige.CaseID and satisfysr.cusid = kuqige.CusID)=0
                <!-- 近三天，不含週六,週日-->
                and satisfysr.createtime >= date_sub(current_date ,interval 3-2*floor((dayofweek(current_date))/4)+(floor(((dayofweek(current_date)-8)/6))+2) day)
            </if>
            <choose>
                <when test="serviceRegion == 'TW' or serviceRegion == ''">
                    <choose>
                        <when test="condition_TW == 'servicecontact'.toString()">
                            <choose>
                                <when test="condition_check_TW == '1'.toString()">
                                    inner join (select isc.issueId,1 serviceContactRed
                                                from issue_service_contact isc
                                                where isc.readTime IS NULL
                                                group by isc.issueId) isc1 on isc1.issueId = issue.IssueId
                                </when>
                                <otherwise>
                                    inner join ( select isc.issueId,SUM(case when isc.readTime IS NULL then 1 ELSE 0 END) serviceContactRed
                                                from issue_service_contact isc
                                                group by isc.issueId) isc1 on isc1.issueId = issue.IssueId
                                </otherwise>
                            </choose>
                        </when>
                        <when test="condition_TW == 'projectupdate'.toString()">
                            <choose>
                                <when test="condition_check_TW == '1'.toString()">
                                    inner join (select isusf.issueId,1 projectUpdateRed
                                    from issue_self_update_system_file isusf
                                    where isusf.updateType = 'UPDATESERVICE' and isusf.invalidStatus != 1 and (isusf.operationTime IS NULL or isusf.operationTime = '')
                                    group by isusf.issueId) isusf1 on isusf1.issueId = issue.IssueId and issue.IssueStatus not IN ('Y','7','10','A','P','V')
                                </when>
                                <otherwise>
                                    inner join (select isusf.issueId,SUM(case when isusf.operationTime IS NULL OR isusf.operationTime = '' then 1 ELSE 0 END) projectUpdateRed
                                    from issue_self_update_system_file isusf
                                    where isusf.updateType = 'UPDATESERVICE' and isusf.invalidStatus != 1
                                    group by isusf.issueId) isusf1 on isusf1.issueId = issue.IssueId and issue.IssueStatus not IN ('Y','7','10','A','P','V')
                                </otherwise>
                            </choose>
                        </when>
                        <when test="condition_TW == 'serialnumberupdate'.toString()">
                            inner join (select isusf.issueId,SUM(case when isusf.operationTime IS NULL OR isusf.operationTime = '' then 1 ELSE 0 END) serialNumberUpdateRed
                            from issue_self_update_system_file isusf
                            where isusf.updateType in ('AUTHINSTALL', 'SFT_AUTHINSTALL', 'SFT_RE_AUTHINSTALL') and (isusf.operationStatus is null or isusf.operationStatus = '' or isusf.operationStatus != 'S')
                            group by isusf.issueId) isusf2 on isusf2.issueId = issue.IssueId and issue.IssueStatus not IN ('Y','7','10','A','P','V')
                        </when>
                    </choose>
                </when>
            </choose>
            WHERE issue.IssueStatus!='V'
            <choose>
                <when test="productCode01.size > 0 and productCode02.size > 0">
                    and (
                    <foreach collection="productCode01" item="item"
                             open=" (((mu.UserType !=  '2' and issue.userId is not null and issue.userId != '') or (issue.userId is null or issue.userId = '') ) and issue.productCode in ( "
                             separator=", " close=")">
                        #{item}
                    </foreach>
                    ) or
                    <foreach collection="productCode02" item="item" open=" issue.ProductCode in ( " separator=", "
                             close=")">
                        #{item}
                    </foreach>
                    )
                </when>
                <when test="productCode02.size == 0 and productCode01.size > 0 ">
                    and
                    <foreach collection="productCode01" item="item"
                             open=" ((mu.UserType !=  '2' and issue.userId is not null and issue.userId != '') or (issue.userId is null or issue.userId = '') ) and issue.productCode in ( "
                             separator=", " close=")">
                        #{item}
                    </foreach>
                </when>
                <when test="productCode01.size == 0 and productCode02.size > 0 ">
                    and
                    <foreach collection="productCode02" item="item" open=" issue.ProductCode in ( " separator=", "
                             close=")">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    AND (   (issue.ProductCode IN ('100','06','999','164','147')
                    AND ( (mu.UserType !=  '2' and issue.userId is not null and issue.userId != '') or (issue.userId is null or issue.userId = '') )
                    )
                    OR issue.ProductCode not IN ('100','06','999','164','147'))
                </otherwise>
            </choose>
        <if test="codeList != null">
            <foreach collection="codeList" item="item" open=" AND code IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
            <choose>
                <when test='issuestatus == "N"'>
                    <choose>
                        <when test='from == "web"'>
                            AND  issue.IssueStatus in ('I', 'C', 'N','2','3','4','12','O','VN','Q')
                        </when>
                        <otherwise>
                            AND  issue.IssueStatus in ('I', 'C', 'N','2','3','4','11','12','22','O','VN','Q','R')
                        </otherwise>
                    </choose>
                </when>
                <otherwise>
                </otherwise>
            </choose>
            <if test='issuestatus == "Y"'>
                AND issue.IssueStatus in ('Y','7','10','A')
            </if>
            <if test='issuestatus == "R"'>
                AND issue.IssueStatus in ('11','R')
            </if>
            <if test='issuestatus == "22"'>
                AND issue.IssueStatus = '22'
            </if>
            <if test='issuestatus!="N" and issuestatus!="Y" and issuestatus!="R" and issuestatus!="22" and issuestatus !=null and issuestatus != ""'>
                AND #{issuestatus} in (issue.IssueStatus, '')
            </if>
            <if test="issueType != ''">
                AND issue.IssueType = #{issueType}
            </if>
            <if test="serviceRegion !=''">
                AND issue.serviceRegion =#{serviceRegion}
            </if>
            <if test="searchByItemsLength > 0">
                <foreach collection="searchByItems" item="item" separator=" ">
                    <if test='item.SearchType=="LIKE"'>
                        AND ${item.columnsName} like CONCAT('%', #{item.Description},'%')
                    </if>
                    <if test='item.SearchType=="BETWEEN"'>
                        AND date(${item.columnsName})  ${item.SearchType}  ${item.Description}
                    </if>
                    <if test='item.SearchType=="&gt;" or item.SearchType=="&gt;=" or item.SearchType=="&lt;" or item.SearchType=="&lt;="'>
                        AND  date(${item.columnsName})  ${item.SearchType}   date(#{item.Description})
                    </if>
                    <if test='item.SearchType=="="'>
                        AND DATE_FORMAT(${item.columnsName},'%Y-%m-%d')  ${item.SearchType}   date(#{item.Description})
                    </if>
                </foreach>
            </if>
            <choose>
                <when test="agent == '1'.toString()">
                    and cd.IssueClassification in (select d.issueclassificationCode
                                from agent_issueclassification d
                                where d.productCode = issue.productCode)
                </when>
                <when test="agent == '0'.toString()">
                    and (cd.IssueClassification not in (select d.issueclassificationCode
                    from agent_issueclassification d
                    where d.productCode = issue.productCode) OR (cd.IssueClassification IS NULL OR cd.IssueClassification = ''))
                </when>
                <otherwise>
                </otherwise>
            </choose>
            <if test="newReply  !=null and newReply != ''">
                AND issue.newReply = #{newReply}
            </if>
            <if test="serviceCode  !=null and serviceCode != ''">
                AND issue.ServiceCode = #{serviceCode}
            </if>
            <choose>
                <when test="myself == '1'.toString()">
                    AND issue.submited_id = #{submiterId}
                </when>
                <otherwise>
                </otherwise>
            </choose>
            <if test="userCollect  !=null and userCollect != ''">
                AND iuc.userId = #{submiterId}
            </if>
            <include refid="authCondition">
                <property name="Id" value="UserId"/>
                <property name="deptId" value="ServiceCode"/>
            </include>
            AND NOT EXISTS (
            SELECT kh.KH001 FROM calkh kh
            WHERE kh.KH002 = cd.IssueClassification AND kh.KH001 = issue.ProductCode)
            ORDER BY
            <if test="orderByItemsLength == 0">
                issue.SubmitTime DESC
            </if>
            <if test="orderByItemsLength > 0">
                <foreach collection="orderByItems" item="item" separator=" ,">
                    ${item.columnName}
                    <if test="item.isAsc">
                        ASC
                    </if>
                    <if test="!item.isAsc">
                        DESC
                    </if>
                </foreach>
            </if>
            LIMIT #{start} , #{end}
        ) issue
        LEFT JOIN user_contacts f on f.Id = issue.UserContactId
        LEFT JOIN mars_customer g on issue.ServiceCode = g.CustomerServiceCode
        LEFT JOIN erppatchs h on issue.RequestPatchId = h.PatchId
        LEFT JOIN mars_userpersonalinfo j on j.userid = issue.UserId
        LEFT JOIN (
            SELECT s.serviceCode,s.productCode,s.confirmIds,GROUP_CONCAT(info.NAME) confirmName
            FROM mars_servicesetting s
            JOIN mars_userpersonalinfo info ON FIND_IN_SET(info.userid,s.confirmIds )
            WHERE s.confirm='1'
            GROUP BY s.serviceCode,s.productCode
        ) confirm ON confirm.serviceCode= issue.ServiceCode AND confirm.productCode=issue.ProductCode
        <if test="(serviceRegion == 'TW' or serviceRegion == '') and condition_TW != 'servicecontact'.toString()
                  and condition_TW != 'projectupdate'.toString() and condition_TW != 'serialnumberupdate'.toString()">
        LEFT JOIN (select isc.issueId,SUM(case when isc.readTime IS NULL then 1 ELSE 0 END) serviceContactRed
            from issue_service_contact isc
            group by isc.issueId) isc1 on isc1.issueId = issue.IssueId
        LEFT JOIN (select isusf.issueId,SUM(case when isusf.operationTime IS NULL OR isusf.operationTime = '' then 1 ELSE 0 END) projectUpdateRed
            from issue_self_update_system_file isusf
            where isusf.updateType = 'UPDATESERVICE' and isusf.invalidStatus != 1
            group by isusf.issueId) isusf1 on isusf1.issueId = issue.IssueId
        LEFT JOIN (select isusf.issueId,SUM(case when isusf.operationTime IS NULL OR isusf.operationTime = '' then 1 ELSE 0 END) serialNumberUpdateRed
            from issue_self_update_system_file isusf
            where isusf.updateType in ('AUTHINSTALL', 'SFT_AUTHINSTALL', 'SFT_RE_AUTHINSTALL') and isusf.invalidStatus != 1
            group by isusf.issueId) isusf2 on isusf2.issueId = issue.IssueId
        </if>
        GROUP BY issue.IssueId
        ORDER BY
        <if test="orderByItemsLength == 0">
            issue.SubmitTime DESC
        </if>
        <if test="orderByItemsLength > 0">
            <foreach collection="orderByItems" item="item" separator=" ,">
                ${item.columnName}
                <if test="item.isAsc">
                    ASC
                </if>
                <if test="!item.isAsc">
                    DESC
                </if>
            </foreach>
        </if>
    </select>
    <!--<choose>-->
        <!--<when test="issuestatus = 'N'.toString() "> AND  issue.IssueStatus in ('I', 'C', 'N') </when>-->
        <!--<when test="issuestatus != '' and issuestatus != 'N'.toString() "> AND  issue.IssueStatus = #{issuestatus} </when>-->
    <!--</choose>-->
    <!--<if test="issuestatus != ''">-->
        <!--AND  issue.IssueStatus = #{issuestatus}-->
    <!--</if>-->
    <select id="selectIssueListByStaff" resultMap="issueselectmap">
        SELECT issue.IssueId,ifnull(issue.CrmId, '') as CrmId,issue.ServiceCode,issue.ProductCode,issue.ProductVersion,
        issue.UserId,issue.CcUser,issue.UserContactId,issue.IssueDescription,issue.IssueStack,issue.SubmitWay,
        issue.IssueStatus,issue.SubmitTime, ifnull(d.fullname, e.ServiceStaff) as ServiceStaff,
        f.`Name` as userContact_name, f.email as userContact_email, f.phone01 as userContact_phone01,
        f.phone02 as userContact_phone02, f.qq as userContact_qq, issue.UserContactId as userContact_id, f.userId as
        userContact_userId,
        issue.RequestPatchId, issue.ProgramCode,issue.ProgramVersion, issue.IssueType, issue.SyncStatus,
        issue.planDownloadTime, issue.planUpdateTime
                ,g.CustomerName,h.ProgramName
        ,j.name as submitName
        FROM issue issue
        LEFT JOIN mars_userpersonalinfo b on b.userid = issue.ServiceId
        LEFT JOIN mars_customerservicesatff d on d.workno = b.workno
        LEFT JOIN mars_customerservice e on e.CustomerServiceCode = issue.ServiceCode and (ifnull(issue.ProductCode,'') = '' or e.ProductCode = issue.ProductCode)
        LEFT JOIN user_contacts f on f.Id = issue.UserContactId
        LEFT JOIN mars_customer g on issue.ServiceCode = g.CustomerServiceCode
        LEFT JOIN erppatchs h on issue.RequestPatchId = h.PatchId
        LEFT JOIN mars_userpersonalinfo j on j.userid = issue.UserId
        WHERE 1=1
        <if test="queryUserId != ''">
            AND issue.ServiceId = #{queryUserId}
        </if>
        <if test="issuestatus != ''">
            AND issue.IssueStatus = #{issuestatus}
        </if>
        <if test="issueType != ''">
            AND issue.IssueType = #{issueType}
        </if>
        <include refid="authCondition">
            <property name="Id" value="ServiceId"/>
            <property name="deptId" value="ServiceDepartment"/>
        </include>
        GROUP BY issue.IssueId
        ORDER BY issue.SubmitTime DESC
        LIMIT #{start} , #{end}
    </select>

    <select id="selectIssueListByServiceCode" resultMap="issueselectmap">
         SELECT a.IssueId,ifnull(a.CrmId, '') as CrmId,a.ServiceCode,a.ProductCode,a.ProductVersion,
                a.UserId,a.CcUser,a.UserContactId,a.IssueDescription,a.IssueStack,a.SubmitWay,
                a.IssueStatus,a.SubmitTime, ifnull(d.fullname, e.ServiceStaff) as ServiceStaff,
                f.`Name` as userContact_name, f.email as userContact_email, f.phone01 as userContact_phone01,
                f.phone02 as userContact_phone02, f.qq as userContact_qq, a.UserContactId as userContact_id, f.userId as userContact_userId,
                a.RequestPatchId, a.ProgramCode,a.ProgramVersion, a.IssueType, a.SyncStatus,
                a.planDownloadTime, a.planUpdateTime
                ,j.name as submitName
        FROM issue a
        LEFT JOIN mars_userpersonalinfo b on b.userid = a.ServiceId
        LEFT JOIN mars_customerservicesatff d on d.workno = b.workno
        LEFT JOIN mars_customerservice e on e.CustomerServiceCode = a.ServiceCode and (ifnull(a.ProductCode,'') = '' or e.ProductCode = a.ProductCode)
        LEFT JOIN user_contacts f on f.Id = a.UserContactId
        LEFT JOIN mars_userpersonalinfo j on j.userid = issue.UserId
        WHERE a.ServiceCode = #{serviceCode} AND a.ProductCode = #{productCode} AND #{issuestatus} IN (a.IssueStatus , '')
        ORDER BY a.SubmitTime DESC
        LIMIT #{start} , #{end}
    </select>

    <select id="selectIssueProgressList" resultMap="issueprogressmap">
        SELECT a.Id,IssueId,CrmId,SequenceNum as SequeceNum,
            case when a.ProcessType IN ( '1','16','5','9','19','18','17','13','21','15','14','23','27','6') AND a.currentstatus !='7' then 'Process'
                 when a.ProcessType  IN ('25','26','6') and a.currentstatus !='11'  then 'Close'
                 ELSE ProcessType END ProcessType,
                 Processor,b.name as ProcessorName,ReplyType,ProcessHours,Description,date_format(ProcessTime, '%Y-%m-%d %H:%i:%s') as ProcessTime,
                 '' as ServerName , '' as DownloadPath,
                 case when a.processType IN ('Process','TurnToProduct','TurnToISV','FollowProcess') AND a.Processor = #{userId} then 1 else 0 end isCanDelete
        FROM issue_progress a
        left join mars_userpersonalinfo b on b.userid=a.Processor

        left JOIN (SELECT UserType FROM mars_user WHERE Id = #{userId}) temp ON 1=1
        WHERE a.IssueId = #{issueId} AND if(temp.UserType ='2' , 1=1, a.isOpenToSubmit = 1)
        UNION
        SELECT '' as Id,ip.IssueId,ip.CrmId,ip.SequenceNum as SequeceNum,a.ProcessType,'' as Processor,'' as ProcessorName,'' as ReplyType , '' as ProcessHours, '' as Description,
               date_format(a.ProcessTime, '%Y-%m-%d %H:%i:%s') as ProcessTime,
               a.ServerName , a.DownloadPath,
                 case when ip.processType IN ('Process','TurnToProduct','TurnToISV','FollowProcess') AND ip.Processor = #{userId} then 1 else 0 end isCanDelete
        FROM erppatch_updatequeue_progress a
        LEFT JOIN issue_progress ip on  a.QueueId = ip.Description and ip.ProcessType ='Confirm'
        WHERE ip.IssueId = #{issueId}
        order by ProcessTime DESC, SequeceNum DESC
    </select>

    <select id="selectIssueAdditionalExplanationList" resultMap="issueadditionalexplanationmap">
        SELECT Id as id,IssueId as issueId,CrmId as crmId,AdditionalExplanation as description ,date_format(SubmitTime, '%Y-%m-%d %H:%i:%s') as submitTime,IsRead as isRead
        FROM issue_contactlist
        WHERE IssueId = #{issueId}
        order by SubmitTime DESC
    </select>

    <select id="selectIssueCasedetailList" resultMap="issuecasedetailmap">
        SELECT a.IssueId as issueId,b.IssueClassification as issueClassification,b.erpSystemCode as erpSystemCode,b.programCode as ProgramCode,b.programVersion as programVersion
        ,b.SyncStatus as syncStatus,'' as additionalExplanation,b.issue_level issueLevel,b.coefficientId,b.chatfileHelp
        FROM issue  a
        left join issue_caseDetail b on a.IssueId=b.IssueId
        where a.IssueId=#{issueId}
    </select>
    <select id="selectIssueKbShareList" resultMap="issuekbsharemap">
        SELECT IssueId, CrmId, ProductCode, SearchText, Kbid, date_format(SubmitTime, '%Y-%m-%d %H:%i:%s') as SubmitTime, ShareContent, ShareUrl, FinishSearchChatFile, ChatFileContent, ChatFileSearchText, invalidChatFileKnowledgeNo, SyncStatus, SyncTime, ChatFileErrorInfo, InvalidChatFileAnswer, ChatfileKnowledgeList, ChatfileHelp
        FROM issue_kbshare
        WHERE IssueId = #{issueId}
        order by SubmitTime DESC
        limit 1
    </select>
    <select id="selectIssueByIssueId" resultMap="issueselectmap">
        SELECT a.IssueId,ifnull(a.CrmId, '') as CrmId,a.ServiceCode, IF(customer.CustomerName IS NULL OR TRIM(customer.CustomerName) = '', serae.AE002, customer.CustomerName) as CustomerName,
                a.ProductCode,a.ProductVersion,mp.ProductCategory productCategory,
                a.UserId,ifnull(h.name,'') submitName,ifnull(muser.username,'') submitUserName,  a.CcUser,a.UserContactId,a.IssueDescription,a.IssueStack,a.SubmitWay,
                <choose>
                    <when test='from == "web"'>
                        case when a.IssueStatus IN ( 'N','2','3','4','12','O','VN','Q') then 'N'
                        when a.IssueStatus  IN ('Y','7','10','A') then 'Y'
                        when a.IssueStatus  IN ('11','R') then 'R'
                        ELSE IssueStatus END IssueStatus,
                    </when>
                    <otherwise>
                        case when a.IssueStatus IN ( 'N','2','3','4','11','12','22','O','VN','Q','R') then 'N'
                        when a.IssueStatus  IN ('Y','7','10','A') then 'Y'
                        ELSE IssueStatus END IssueStatus,
                    </otherwise>
                </choose>
                a.SubmitTime, ifnull(d.fullname, e.ServiceStaff) as ServiceStaff,a.ServiceDepartment,
                mcs.fullname as SupportStaff, mcs.departmentcode as SupportDepartment,
                f.`Name` as userContact_name, f.email as userContact_email, f.phone01 as userContact_phone01,
                f.phone02 as userContact_phone02, f.phone03 as userContact_phone03, f.qq as userContact_qq, a.UserContactId as userContact_id, f.userId as userContact_userId,
                a.RequestPatchId, a.ProgramCode,a.ProgramVersion, a.IssueType, a.SyncStatus,ifnull(a.ServiceId, '') as ServiceId,
                a.planDownloadTime, a.planUpdateTime, a.serviceRegion as serviceRegion,a.machineRegion as machineRegion,a.site as site,a.environment as environment,
                case when a.IssueStatus IN ('C','N','2','3','4') then 1
                     ELSE 0 END isDirectClosedShow,case when iuc.issueId IS NOT NULL then 1 ELSE 0 END userCollect
        FROM issue a
        LEFT JOIN mars_userpersonalinfo b on b.userid = a.ServiceId
        LEFT JOIN mars_customerservicesatff d on d.workno = b.workno

        left join mars_userpersonalinfo h on h.userid = a.UserId
        left join mars_user muser on muser.id = a.UserId
        LEFT JOIN mars_customerservice e on e.CustomerServiceCode = a.ServiceCode and (ifnull(a.ProductCode,'') = '' or e.ProductCode = a.ProductCode)
        left join mars_customer customer on customer.CustomerServiceCode = a.ServiceCode
        LEFT JOIN serae  on a.ServiceCode = serae.AE001

        LEFT JOIN user_contacts f on f.Id = a.UserContactId
        LEFT JOIN mars_product mp ON mp.ProductCode = a.ProductCode

        LEFT JOIN mars_userpersonalinfo mu on mu.userid = a.SupportId
        LEFT JOIN mars_customerservicesatff mcs on mcs.workno = mu.workno

        LEFT JOIN issue_usercollection iuc on iuc.issueId = #{issueId} AND iuc.userId = #{userId}

        WHERE a.IssueId = #{issueId}
        LIMIT 0,1
    </select>

    <select id="SelectIssueByIssueIdForMis" resultMap="issueselectmap">
        SELECT a.IssueId,ifnull(a.CrmId, '') as CrmId,a.ServiceCode, IF(customer.CustomerName IS NULL OR TRIM(customer.CustomerName) = '', serae.AE002, customer.CustomerName) as CustomerName,
                a.ProductCode,a.ProductVersion,mp.ProductCategory productCategory,
                a.UserId,ifnull(h.name,'') submitName,ifnull(muser.username,'') submitUserName, a.CcUser,a.UserContactId,a.IssueDescription,a.IssueStack,a.SubmitWay,
                case when a.IssueStatus IN ('Y','7','10','A','P') then 'Y'
                     ELSE 'N' END IssueStatus,
                a.SubmitTime, ifnull(d.fullname, e.ServiceStaff) as ServiceStaff,e.ContractExprityDate contractExprityDate,
                f.`Name` as userContact_name, f.email as userContact_email, f.phone01 as userContact_phone01,
                f.phone02 as userContact_phone02, f.qq as userContact_qq, a.UserContactId as userContact_id, f.userId as userContact_userId,
                a.RequestPatchId, a.ProgramCode,a.ProgramVersion, a.IssueType, a.SyncStatus,ifnull(a.ServiceId, '') as ServiceId,
                a.planDownloadTime, a.planUpdateTime, a.serviceRegion as serviceRegion,a.machineRegion as machineRegion,a.site as site,a.environment as environment,
                case when a.IssueStatus IN ('C','N','2','3','4') then 1
                     ELSE 0 END isDirectClosedShow
        FROM issue a
        LEFT JOIN mars_userpersonalinfo b on b.userid = a.ServiceId
        LEFT JOIN mars_customerservicesatff d on d.workno = b.workno

        left join mars_userpersonalinfo h on h.userid = a.UserId
        left join mars_user muser on muser.id = a.UserId
        LEFT JOIN mars_customerservice e on e.CustomerServiceCode = a.ServiceCode and (ifnull(a.ProductCode,'') = '' or e.ProductCode = a.ProductCode)
        left join mars_customer customer on customer.CustomerServiceCode = a.ServiceCode
        LEFT JOIN serae  on a.ServiceCode = serae.AE001

        LEFT JOIN user_contacts f on f.Id = a.UserContactId
        LEFT JOIN mars_product mp ON mp.ProductCode = a.ProductCode
        WHERE a.IssueId = #{issueId}
        LIMIT 0,1
    </select>
    <select id="selectIssueCount" resultMap="issuecountmap">
        SELECT sum(issuesCount) as issuesCount ,
        sum(waitingIssuesCount) as waitingIssuesCount,
        sum(waitingSubmiterCheckCount) as waitingSubmiterCheckCount,
        sum(approvingIssuesCount) as approvingIssuesCount,
        sum(returnedIssuesCount) as returnedIssuesCount,
        sum(processingIssuesCount) as processingIssuesCount,
        sum(closedIssuesCount) as closedIssuesCount,
        sum(evaluatedIssuesCount) as evaluatedIssuesCount,
        sum(invalidIssuesCount) as invalidIssuesCount,
        sum(waitingEvaluatedIssuesCount) as waitingEvaluatedIssuesCount,
        sum(newReplyCount) as newReplyCount,
        sum(reSubmitIssuesCount) as reSubmitIssuesCount,
        sum(userCollectCount) as userCollectCount
        <choose>
            <when test="serviceRegion == 'TW' or serviceRegion == ''">
                ,sum(servicecontactcount) servicecontactcount,sum(projectupdatecount) projectupdatecount,sum(serialnumberupdatecount) serialnumberupdatecount
            </when>
            <otherwise>
            </otherwise>
        </choose>
        FROM (
        SELECT count(*) as issuesCount ,
        sum(case when issue.IssueStatus = 'C' or issue.IssueStatus = 'I' then 1 else 0 end) as waitingIssuesCount,
        sum(case when issue.IssueStatus = '11' or issue.IssueStatus = 'R' then 1 else 0 end) as waitingSubmiterCheckCount,
        sum(case when issue.IssueStatus = 'T' then 1 else 0 end) as approvingIssuesCount,
        sum(case when issue.IssueStatus = 'D' then 1 else 0 end) as returnedIssuesCount,
        <choose>
            <when test='from == "web"'>
                sum(case when issue.IssueStatus IN ( 'N','2','3','4','12','8','O','VN','Q') then 1 else 0 end) as processingIssuesCount,
            </when>
            <otherwise>
                sum(case when issue.IssueStatus IN ( 'N','2','3','4','11','12','22','8','O','VN','Q','R') then 1 else 0 end) as processingIssuesCount,
            </otherwise>
        </choose>
        sum(case when issue.IssueStatus IN ( 'Y','7','10','A') then 1 else 0 end) as closedIssuesCount,
        sum(case when issue.IssueStatus = 'P' then 1 else 0 end) as evaluatedIssuesCount,
        sum(case when issue.IssueStatus = 'F' then 1 else 0 end) as invalidIssuesCount,
        0 as waitingEvaluatedIssuesCount,
        sum(case when issue.newReply = 'serviceNewReply' then 1 else 0 end) as newReplyCount,
        sum(case when issue.IssueStatus = '22' then 1 else 0 end) as reSubmitIssuesCount,
        sum(case when iuc.userId = #{submiterId} then 1 else 0 end) as userCollectCount
        <choose>
            <when test="serviceRegion == 'TW' or serviceRegion == ''">
                ,count(isc1.issueId) servicecontactcount,count(isusf1.issueId) projectupdatecount,count(isusf2.issueId) serialnumberupdatecount
            </when>
            <otherwise>
            </otherwise>
        </choose>
        from issue
        LEFT JOIN issue_casedetail cd on issue.IssueId = cd.IssueId
        LEFT OUTER JOIN calkh kh on kh.KH001 = issue.ProductCode and kh.KH002 = cd.IssueClassification
        LEFT JOIN mars_user mu ON mu.ID = issue.userId
        LEFT JOIN issue_usercollection iuc on iuc.IssueId = issue.IssueId and iuc.userId = #{submiterId}
        <choose>
            <when test="serviceRegion == 'TW' or serviceRegion == ''">
                        left join (select isc.issueId
                        from issue_service_contact isc
                        where 1=1
                        <choose>
                            <when test="condition_TW == 'servicecontact'.toString()">
                                <choose>
                                    <when test="condition_check_TW == '1'.toString()">
                                        and (isc.readTime IS NULL)
                                    </when>
                                    <otherwise>

                                    </otherwise>
                                </choose>
                            </when>
                            <otherwise>
                                and (isc.readTime IS NULL)
                            </otherwise>
                        </choose>
                        group by isc.issueId) isc1 on isc1.issueId = issue.IssueId

                        left join (select isusf.issueId
                        from issue_self_update_system_file isusf
                        where isusf.updateType = 'UPDATESERVICE' and isusf.invalidStatus != 1
                        <choose>
                            <when test="condition_TW == 'projectupdate'.toString()">
                                <choose>
                                    <when test="condition_check_TW == '1'.toString()">
                                        and (isusf.operationTime IS NULL or isusf.operationTime = '')
                                    </when>
                                    <otherwise>

                                    </otherwise>
                                </choose>
                            </when>
                            <otherwise>
                                and (isusf.operationTime IS NULL or isusf.operationTime = '')
                            </otherwise>
                        </choose>
                        group by isusf.issueId) isusf1 on isusf1.issueId = issue.IssueId and issue.IssueStatus not IN ('Y','7','10','A','P','V')

                        left join (select isusf.issueId
                        from issue_self_update_system_file isusf
                        where isusf.updateType in ('AUTHINSTALL', 'SFT_AUTHINSTALL', 'SFT_RE_AUTHINSTALL') and (isusf.operationStatus is null or isusf.operationStatus = '' or isusf.operationStatus != 'S')
                        group by isusf.issueId) isusf2 on isusf2.issueId = issue.IssueId and issue.IssueStatus not IN ('Y','7','10','A','P','V')
                    <!--</otherwise>
                </choose>-->
            </when>
            <otherwise>
            </otherwise>
        </choose>
        where 1 = 1
        AND issue.IssueStatus!='V'
        <!--2022-04-13 增加下面的判断是因为T的客户在云管家能看到顾问提的单子 不能让客户看到-->
        <!--<if test= "productCode == '100'.toString()  or productCode == '06'.toString()  or productCode == '999'.toString() or productCode == '164'.toString() or productCode == '147'.toString() ">
            and ( (mu.UserType != '2' and issue .userId is not null and issue.userId != '') or (issue.userId is null or
            issue.
            userId = '') )
        </if>
        <if test="productCode != ''">
            AND issue.ProductCode = #{productCode}
        </if>-->
        <!--2022-04-13 增加下面的判断是因为T的客户在云管家能看到顾问提的单子 不能让客户看到-->
        <!--<if test="productCode == ''">
            AND ( (issue. ProductCode IN ('100','06','999','164','147')
            AND ( (mu.UserType != '2' and issue.userId is not null and issue.userId != '') or (issue.userId is null or
            issue.
            userId = '') )
            )
            OR issue.ProductCode not IN ('100','06','999','164','147'))
        </if>-->
        <choose>
            <when test="productCode01.size > 0 and productCode02.size > 0">
                and (
                <foreach collection="productCode01" item="item"
                         open=" (((mu.UserType !=  '2' and issue.userId is not null and issue.userId != '') or (issue.userId is null or issue.userId = '') ) and issue.productCode in ( "
                         separator=", " close=")">
                    #{item}
                </foreach>
                ) or
                <foreach collection="productCode02" item="item" open=" issue.ProductCode in ( " separator=", "
                         close=")">
                    #{item}
                </foreach>
                )
            </when>
            <when test="productCode02.size == 0 and productCode01.size > 0 ">
                and
                <foreach collection="productCode01" item="item"
                         open=" ((mu.UserType !=  '2' and issue.userId is not null and issue.userId != '') or (issue.userId is null or issue.userId = '') ) and issue.productCode in ( "
                         separator=", " close=")">
                    #{item}
                </foreach>
            </when>
            <when test="productCode01.size == 0 and productCode02.size > 0 ">
                and
                <foreach collection="productCode02" item="item" open=" issue.ProductCode in ( " separator=", "
                         close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND (   (issue.ProductCode IN ('100','06','999','164','147')
                AND ( (mu.UserType !=  '2' and issue.userId is not null and issue.userId != '') or (issue.userId is null or issue.userId = '') )
                )
                OR issue.ProductCode not IN ('100','06','999','164','147'))
            </otherwise>
        </choose>
        <if test="issueType != ''">
            AND issue.IssueType = #{issueType}
        </if>
        <if test="serviceRegion !=''">
            AND issue.serviceRegion = #{serviceRegion}
        </if>
        <choose>
            <when test="agent == '1'.toString()">
                and cd.IssueClassification in (select d.issueclassificationCode
                from agent_issueclassification d
                where d.productCode = issue.productCode)
            </when>
            <when test="agent == '0'.toString()">
                and (cd.IssueClassification not in (select d.issueclassificationCode
                from agent_issueclassification d
                where d.productCode = issue.productCode) OR (cd.IssueClassification IS NULL OR cd.IssueClassification = ''))
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test="newReply  !=null and newReply != ''">
            AND issue.newReply = #{newReply}
        </if>
        <if test="serviceCode  !=null and serviceCode != ''">
            AND issue.ServiceCode = #{serviceCode}
        </if>
        <choose>
            <when test="myself == '1'.toString()">
                AND issue.submited_id = #{submiterId}
            </when>
            <otherwise>
            </otherwise>
        </choose>
        AND kh.KH001 IS null AND kh.KH002 IS null
        <include refid="authCondition">
            <property name="Id" value="UserId"/>
            <property name="deptId" value="ServiceCode"/>
        </include>

        union

        SELECT 0 as issuesCount ,
        0 as waitingIssuesCount,
        0 as waitingSubmiterCheckCount,
        0 as approvingIssuesCount,
        0 as returnedIssuesCount,
        0 as processingIssuesCount,
        0 as closedIssuesCount,
        0 as evaluatedIssuesCount,
        0 as invalidIssuesCount,
        count(satisfysr.caseid) as waitingEvaluatedIssuesCount,
        0 newReplyCount,
        0 reSubmitIssuesCount,
        0 userCollectCount
        <choose>
            <when test="serviceRegion == 'TW' or serviceRegion == ''">
                ,0 servicecontactcount,0 projectupdatecount,0 serialnumberupdatecount
            </when>
            <otherwise>
            </otherwise>
        </choose>
        from issue
        LEFT JOIN issue_casedetail cd on issue.IssueId = cd.IssueId
        LEFT OUTER JOIN calkh kh on kh.KH001 = issue.ProductCode and kh.KH002 = cd.IssueClassification
        LEFT JOIN mars_user mu ON mu.ID = issue.userId
        LEFT JOIN satisfysr on satisfysr.caseid = issue.CrmId and satisfysr.cusid = issue.ServiceCode and issue.
        IssueStatus IN ( 'Y','7','10','A')
        and (SELECT count(*) from kuqige where satisfysr.caseid = kuqige.CaseID and satisfysr.cusid = kuqige.CusID)=0
        <!-- 近三天，不含週六,週日-->
        and satisfysr.createtime >= date_sub(current_date ,interval 3-2*
        floor((dayofweek(current_date))/4)+(floor(((dayofweek(current_date)-8)/6))+2) day)
        where 1 = 1
        AND issue.IssueStatus!='V'
        <!--2022-04-13 增加下面的判断是因为T的客户在云管家能看到顾问提的单子 不能让客户看到-->
        <!--<if test= "productCode == '100'.toString()  or productCode == '06'.toString()  or productCode == '999'.toString() or productCode == '164'.toString() or productCode == '147'.toString() ">
            and ( (mu.UserType != '2' and issue.userId is not null and issue.userId != '') or (issue.userId is null or
            issue.userId = '') )
        </if>
        <if test="productCode != ''">
            AND issue.ProductCode = #{productCode}
        </if>-->
        <!--2022-04-13 增加下面的判断是因为T的客户在云管家能看到顾问提的单子 不能让客户看到-->
        <!--<if test="productCode == ''">
            AND ( ( issue.ProductCode IN ('100','06','999','164','147')
            AND ( (mu.UserType != '2' and issue.userId is not null and issue.userId != '') or (issue.userId is null or
            issue.userId = '') )
            )
            OR issue.ProductCode not IN ('100','06','999','164','147'))
        </if>-->
        <choose>
            <when test="productCode01.size > 0 and productCode02.size > 0">
                and (
                <foreach collection="productCode01" item="item"
                         open=" (((mu.UserType !=  '2' and issue.userId is not null and issue.userId != '') or (issue.userId is null or issue.userId = '') ) and issue.productCode in ( "
                         separator=", " close=")">
                    #{item}
                </foreach>
                ) or
                <foreach collection="productCode02" item="item" open=" issue.ProductCode in ( " separator=", "
                         close=")">
                    #{item}
                </foreach>
                )
            </when>
            <when test="productCode02.size == 0 and productCode01.size > 0 ">
                and
                <foreach collection="productCode01" item="item"
                         open=" ((mu.UserType !=  '2' and issue.userId is not null and issue.userId != '') or (issue.userId is null or issue.userId = '') ) and issue.productCode in ( "
                         separator=", " close=")">
                    #{item}
                </foreach>
            </when>
            <when test="productCode01.size == 0 and productCode02.size > 0 ">
                and
                <foreach collection="productCode02" item="item" open=" issue.ProductCode in ( " separator=", "
                         close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND (   (issue.ProductCode IN ('100','06','999','164','147')
                AND ( (mu.UserType !=  '2' and issue.userId is not null and issue.userId != '') or (issue.userId is null or issue.userId = '') )
                )
                OR issue.ProductCode not IN ('100','06','999','164','147'))
            </otherwise>
        </choose>
        <if test="issueType != ''">
            AND issue.IssueType = #{issueType}
        </if>
        <if test="serviceRegion !=''">
            AND issue.serviceRegion = #{serviceRegion}
        </if>
        <if test="serviceCode  !=null and serviceCode != ''">
            AND issue.ServiceCode = #{serviceCode}
        </if>
        AND kh.KH001 IS null AND kh.KH002 IS null
        and issue.userId = #{userId}
      ) A
    </select>

    <select id="selectIssueCountbydescription" resultMap="issuecountmap">
        SELECT sum(issuesCount) as issuesCount ,
        sum(waitingIssuesCount) as waitingIssuesCount,
        sum(waitingSubmiterCheckCount) as waitingSubmiterCheckCount,
        sum(approvingIssuesCount) as approvingIssuesCount,
        sum(returnedIssuesCount) as returnedIssuesCount,
        sum(processingIssuesCount) as  processingIssuesCount,
        sum(closedIssuesCount) as closedIssuesCount,
        sum(evaluatedIssuesCount) as evaluatedIssuesCount,
        sum(invalidIssuesCount) as invalidIssuesCount,
        sum(waitingEvaluatedIssuesCount) as waitingEvaluatedIssuesCount,
        sum(userCollectCount) userCollectCount
        <choose>
            <when test="serviceRegion == 'TW' or serviceRegion == ''">
                ,sum(servicecontactcount) servicecontactcount,sum(projectupdatecount) projectupdatecount,sum(serialnumberupdatecount) serialnumberupdatecount
            </when>
            <otherwise>
            </otherwise>
        </choose>
        ,sum(newReplyCount) as newReplyCount,sum(reSubmitIssuesCount) as reSubmitIssuesCount
        FROM (
        SELECT count(*) as issuesCount ,
        sum(case when issue.IssueStatus = 'C' or issue.IssueStatus = 'I' then 1 else 0 end) as waitingIssuesCount,
        sum(case when issue.IssueStatus = '11' or issue.IssueStatus = 'R' then 1 else 0 end) as waitingSubmiterCheckCount,
        sum(case when issue.IssueStatus = 'T' then 1 else 0 end) as approvingIssuesCount,
        sum(case when issue.IssueStatus = 'D' then 1 else 0 end) as returnedIssuesCount,
        <choose>
            <when test='from == "web"'>
                sum(case when issue.IssueStatus IN ( 'N','2','3','4','12','8','O','VN','Q') then 1 else 0 end) as processingIssuesCount,
            </when>
            <otherwise>
                sum(case when issue.IssueStatus IN ( 'N','2','3','4','11','12','22','8','O','VN','Q','R') then 1 else 0 end) as processingIssuesCount,
            </otherwise>
        </choose>
        sum(case when issue.IssueStatus IN ( 'Y','7','10','A') then 1 else 0 end) as closedIssuesCount,
        sum(case when issue.IssueStatus = 'P' then 1 else 0 end) as evaluatedIssuesCount,
        sum(case when issue.IssueStatus = 'F' then 1 else 0 end) as invalidIssuesCount,
        0 as waitingEvaluatedIssuesCount,
        sum(case when issue.newReply = 'serviceNewReply' then 1 else 0 end) as newReplyCount,
        sum(case when issue.IssueStatus = '22' then 1 else 0 end) as reSubmitIssuesCount,
        sum(case when iuc.userId = #{submiterId} then 1 else 0 end) as userCollectCount
        <choose>
            <when test="serviceRegion == 'TW' or serviceRegion == ''">
                ,count(isc1.issueId) servicecontactcount,count(isusf1.issueId) projectupdatecount,count(isusf2.issueId) serialnumberupdatecount
            </when>
            <otherwise>
            </otherwise>
        </choose>
        from issue
        LEFT JOIN issue_casedetail cd on issue.IssueId = cd.IssueId
        LEFT OUTER JOIN calkh kh on kh.KH001 = issue.ProductCode and kh.KH002 = cd.IssueClassification
        LEFT JOIN mars_user mu ON mu.ID = issue.userId
        LEFT JOIN issue_usercollection iuc on iuc.IssueId = issue.IssueId and iuc.userId = #{submiterId}
        <choose>
            <when test="serviceRegion == 'TW' or serviceRegion == ''">
                        left join (select isc.issueId
                        from issue_service_contact isc
                        where 1=1
                        <choose>
                            <when test="condition_TW == 'servicecontact'.toString()">
                                <choose>
                                    <when test="condition_check_TW == '1'.toString()">
                                        and (isc.readTime IS NULL)
                                    </when>
                                    <otherwise>

                                    </otherwise>
                                </choose>
                            </when>
                            <otherwise>
                                and (isc.readTime IS NULL)
                            </otherwise>
                        </choose>
                        group by isc.issueId) isc1 on isc1.issueId = issue.IssueId

                        left join (select isusf.issueId
                        from issue_self_update_system_file isusf
                        where isusf.updateType = 'UPDATESERVICE' and isusf.invalidStatus != 1
                        <choose>
                            <when test="condition_TW == 'projectupdate'.toString()">
                                <choose>
                                    <when test="condition_check_TW == '1'.toString()">
                                        and (isusf.operationTime IS NULL or isusf.operationTime = '')
                                    </when>
                                    <otherwise>

                                    </otherwise>
                                </choose>
                            </when>
                            <otherwise>
                                and (isusf.operationTime IS NULL or isusf.operationTime = '')
                            </otherwise>
                        </choose>
                        group by isusf.issueId) isusf1 on isusf1.issueId = issue.IssueId and issue.IssueStatus not IN ('Y','7','10','A','P','V')

                        left join (select isusf.issueId
                        from issue_self_update_system_file isusf
                        where isusf.updateType in ('AUTHINSTALL', 'SFT_AUTHINSTALL', 'SFT_RE_AUTHINSTALL') and (isusf.operationStatus is null or isusf.operationStatus = '' or isusf.operationStatus != 'S')
                        group by isusf.issueId) isusf2 on isusf2.issueId = issue.IssueId and issue.IssueStatus not IN ('Y','7','10','A','P','V')
                    <!--</otherwise>
                </choose>-->
            </when>
            <otherwise>
            </otherwise>
        </choose>
        where 1 = 1
        AND issue.IssueStatus!='V'
        <!--2022-04-13 增加下面的判断是因为T的客户在云管家能看到顾问提的单子 不能让客户看到-->
        <!--<if test="productCode == '100'.toString()  or productCode == '06'.toString()  or productCode == '999'.toString() or productCode == '164'.toString() or productCode == '147'.toString() ">
            and ( (mu.UserType != '2' and issue.userId is not null and issue.userId != '') or (issue.userId is null or
            issue.userId = '') )
        </if>
        <if test="productCode != ''">
            AND issue.ProductCode = #{productCode}
        </if>-->
        <!--2022-04-13 增加下面的判断是因为T的客户在云管家能看到顾问提的单子 不能让客户看到-->
        <!--<if test="productCode == ''">
            AND ( (issue.ProductCode IN ('100','06','999','164','147')
            AND ( (mu.UserType != '2' and issue.userId is not null and issue.userId != '') or (issue.userId is null or
            issue.userId = '') )
            )
            OR issue.ProductCode not IN ('100','06','999','164','147'))
        </if>-->
        <choose>
            <when test="productCode01.size > 0 and productCode02.size > 0">
                and (
                <foreach collection="productCode01" item="item"
                         open=" (((mu.UserType !=  '2' and issue.userId is not null and issue.userId != '') or (issue.userId is null or issue.userId = '') ) and issue.productCode in ( "
                         separator=", " close=")">
                    #{item}
                </foreach>
                ) or
                <foreach collection="productCode02" item="item" open=" issue.ProductCode in ( " separator=", "
                         close=")">
                    #{item}
                </foreach>
                )
            </when>
            <when test="productCode02.size == 0 and productCode01.size > 0 ">
                and
                <foreach collection="productCode01" item="item"
                         open=" ((mu.UserType !=  '2' and issue.userId is not null and issue.userId != '') or (issue.userId is null or issue.userId = '') ) and issue.productCode in ( "
                         separator=", " close=")">
                    #{item}
                </foreach>
            </when>
            <when test="productCode01.size == 0 and productCode02.size > 0 ">
                and
                <foreach collection="productCode02" item="item" open=" issue.ProductCode in ( " separator=", "
                         close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND (   (issue.ProductCode IN ('100','06','999','164','147')
                AND ( (mu.UserType !=  '2' and issue.userId is not null and issue.userId != '') or (issue.userId is null or issue.userId = '') )
                )
                OR issue.ProductCode not IN ('100','06','999','164','147'))
            </otherwise>
        </choose>
        <if test="issueType != ''">
            AND issue.IssueType = #{issueType}
        </if>
        <if test="serviceRegion !=''">
            AND issue.serviceRegion=#{serviceRegion}
        </if>
        <if test="newReply  !=null and newReply != ''">
            AND issue.newReply = #{newReply}
        </if>
        <choose>
            <when test="myself == '1'.toString()">
                AND issue.submited_id = #{submiterId}
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test="serviceCode  !=null and serviceCode != ''">
            AND issue.ServiceCode = #{serviceCode}
        </if>
        <if test="searchByItemsLength > 0">
            <foreach collection="searchByItems" item="item" separator=" ">
                <if test='item.SearchType=="LIKE"'>
                    AND ${item.columnsName} like CONCAT('%', #{item.Description},'%')
                </if>
                <if test='item.SearchType=="BETWEEN"'>
                    AND date(${item.columnsName})  ${item.SearchType}  ${item.Description}
                </if>
                <if test='item.SearchType=="&gt;" or item.SearchType=="&gt;=" or item.SearchType=="&lt;" or item.SearchType=="&lt;="'>
                    AND ${item.columnsName} ${item.SearchType} #{item.Description}
                </if>
                <if test='item.SearchType=="="'>
                    AND DATE_FORMAT(${item.columnsName},'%Y-%m-%d') ${item.SearchType} date(#{item.Description})
                </if>
            </foreach>
        </if>
        AND kh.KH001 IS null AND kh.KH002 IS null
        <include refid="authCondition">
            <property name="Id" value="UserId"/>
            <property name="deptId" value="ServiceCode"/>
        </include>

        UNION

        SELECT 0 as issuesCount ,
        0 as waitingIssuesCount,
        0 as waitingSubmiterCheckCount,
        0 as approvingIssuesCount,
        0 as returnedIssuesCount,
        0 as processingIssuesCount,
        0 as closedIssuesCount,
        0 as evaluatedIssuesCount,
        0 as invalidIssuesCount,
        count(satisfysr.caseid) as waitingEvaluatedIssuesCount
        <choose>
            <when test="serviceRegion == 'TW' or serviceRegion == ''">
                ,0 servicecontactcount,0 projectupdatecount,0 serialnumberupdatecount
            </when>
            <otherwise>
            </otherwise>
        </choose>
        ,0 as newReplyCount,0 as reSubmitIssuesCount,
        0 as userCollectCount
        from issue
        LEFT JOIN issue_casedetail cd on issue.IssueId = cd.IssueId
        LEFT OUTER JOIN calkh kh on kh.KH001 = issue.ProductCode and kh.KH002 = cd.IssueClassification
        LEFT JOIN mars_user mu ON mu.ID = issue.userId
        LEFT JOIN satisfysr on satisfysr.caseid = issue.CrmId and satisfysr.cusid = issue.ServiceCode and
        issue.IssueStatus IN ( 'Y','7','10','A')
        and (SELECT count(*) from kuqige where satisfysr.caseid = kuqige.CaseID and satisfysr.cusid = kuqige.CusID)=0
        <!-- 近三天，不含週六,週日-->
        and satisfysr.createtime >= date_sub(current_date ,interval
        3-2*floor((dayofweek(current_date))/4)+(floor(((dayofweek(current_date)-8)/6))+2) day)
        where 1 = 1
        AND issue.IssueStatus!='V'
        <!--2022-04-13 增加下面的判断是因为T的客户在云管家能看到顾问提的单子 不能让客户看到-->
        <!--<if test="productCode == '100'.toString()  or productCode == '06'.toString()  or productCode == '999'.toString() or productCode == '164'.toString() or productCode == '147'.toString() ">
            and ( (mu.UserType != '2' and issue.userId is not null and issue.userId != '') or (issue.userId is null or
            issue.userId = '') )
        </if>
        <if test="productCode != ''">
            AND issue.ProductCode = #{productCode}
        </if>-->
        <!--2022-04-13 增加下面的判断是因为T的客户在云管家能看到顾问提的单子 不能让客户看到-->
        <!--<if test="productCode == ''">
            AND ( (issue.ProductCode IN ('100','06','999','164','147')
            AND ( (mu.UserType != '2' and issue.userId is not null and issue.userId != '') or (issue.userId is null or
            issue.userId = '') )
            )
            OR issue.ProductCode not IN ('100','06','999','164','147'))
        </if>-->
        <choose>
            <when test="productCode01.size > 0 and productCode02.size > 0">
                and (
                <foreach collection="productCode01" item="item"
                         open=" (((mu.UserType !=  '2' and issue.userId is not null and issue.userId != '') or (issue.userId is null or issue.userId = '') ) and issue.productCode in ( "
                         separator=", " close=")">
                    #{item}
                </foreach>
                ) or
                <foreach collection="productCode02" item="item" open=" issue.ProductCode in ( " separator=", "
                         close=")">
                    #{item}
                </foreach>
                )
            </when>
            <when test="productCode02.size == 0 and productCode01.size > 0 ">
                and
                <foreach collection="productCode01" item="item"
                         open=" ((mu.UserType !=  '2' and issue.userId is not null and issue.userId != '') or (issue.userId is null or issue.userId = '') ) and issue.productCode in ( "
                         separator=", " close=")">
                    #{item}
                </foreach>
            </when>
            <when test="productCode01.size == 0 and productCode02.size > 0 ">
                and
                <foreach collection="productCode02" item="item" open=" issue.ProductCode in ( " separator=", "
                         close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND (   (issue.ProductCode IN ('100','06','999','164','147')
                AND ( (mu.UserType !=  '2' and issue.userId is not null and issue.userId != '') or (issue.userId is null or issue.userId = '') )
                )
                OR issue.ProductCode not IN ('100','06','999','164','147'))
            </otherwise>
        </choose>
        <if test="issueType != ''">
            AND issue.IssueType = #{issueType}
        </if>
        <if test="serviceRegion !=''">
            AND issue.serviceRegion=#{serviceRegion}
        </if>
        <if test="serviceCode  !=null and serviceCode != ''">
            AND issue.ServiceCode = #{serviceCode}
        </if>
        <if test="searchByItemsLength > 0">
            <foreach collection="searchByItems" item="item" separator=" ">
                <if test='item.SearchType=="LIKE"'>
                    AND ${item.columnsName} like CONCAT('%', #{item.Description},'%')
                </if>
                <if test='item.SearchType=="BETWEEN"'>
                    AND date(${item.columnsName}) ${item.SearchType} ${item.Description}
                </if>
                <if test='item.SearchType=="&gt;" or item.SearchType=="&gt;=" or item.SearchType=="&lt;" or item.SearchType=="&lt;="'>
                    AND ${item.columnsName} ${item.SearchType} #{item.Description}
                </if>
                <if test='item.SearchType=="="'>
                    AND DATE_FORMAT(${item.columnsName},'%Y-%m-%d') ${item.SearchType} date(#{item.Description})
                </if>
            </foreach>
        </if>
        AND kh.KH001 IS null AND kh.KH002 IS null
        and issue.userId = #{userId}
      ) A

    </select>

    <select id="selectIssuerMail" resultMap="userContactmap">
        SELECT b.name, b.email, b.phone01, b.phone02, b.qq, b.isDefault
        FROM issue a
        LEFT JOIN USER_CONTA b ON a.UserContactId = b.userContactId
        where a.IssueId = #{issueId}
    </select>

    <select id="selectUpdateRequestIssue" resultMap="issueselectmap">
        select issue.IssueId
        INNER JOIN issue_progress ip on issue.IssueId = ip.IssueId and ip.ProcessType ='Confirm'
        where issue.IssueType = 'U' AND ip.Description = #{queueId}
    </select>

    <update id="putUpdateRequestIssueStatus">
        UPDATE issue issue
        INNER JOIN issue_progress ip on issue.IssueId = ip.IssueId and ip.ProcessType ='Confirm'
        SET issue.IssueStatus = 'Y'
        where issue.IssueType = 'U' AND ip.Description = #{queueId}
    </update>

    <insert id="insertUpdateRequestIssueCloseProgress">
        INSERT INTO issue_progress (IssueId,CrmId,SequenceNum,ProcessType,Processor,Description,ProcessTime)
        SELECT DISTINCT issue.IssueId, issue.CrmId, 10, 'Process', #{processor}, '下载完成', DATE_FORMAT(NOW(),'%Y-%m-%d %H:%i:%S')
        from issue issue
        INNER JOIN issue_progress ip on issue.IssueId = ip.IssueId and ip.ProcessType ='Confirm'
        where issue.IssueType = 'U' AND ip.Description = #{queueId} and issue.IssueStatus = 'Y'
    </insert>

    <update id="UpdateIssueCrmId">
        UPDATE issue
        SET CrmId = #{crmId}
        where IssueId = #{issueId}
    </update>

    <update id="updateIssueTime">
        UPDATE issue
        SET planDownloadTime = #{planDownloadTime},planUpdateTime = #{planUpdateTime}
        WHERE IssueId = #{issueId}
    </update>

    <select id="selectNewIssueCountByWorknoAndStatus" resultType="int">
        select count(*) from issue as issue
        JOIN mars_userpersonalinfo b on b.userid = issue.ServiceId
        <if test='connectArea=="TW"'>
            left JOIN mars_userpersonalinfo mus on mus.userid = issue.SupportId
            LEFT JOIN mars_customerservicesatff mcs on mcs.workno = mus.workno
        </if>
        <if test='additionalExplanationReadType=="0"'>
          left join (SELECT IssueId,min(IsRead) as IsRead from issue_contactlist group by IssueId)  c on  c.IssueId=issue.IssueId
        </if>
        <if test='serviceContact=="1"'>
            inner join (select isc.issueId
            from issue_service_contact isc
            group by isc.issueId) isc1 on isc1.issueId = issue.IssueId
        </if>
        <if test='projectUpdate=="1"'>
            inner join (select isusf.issueId
            from issue_self_update_system_file isusf
            where isusf.updateType = 'UPDATESERVICE' and isusf.invalidStatus != 1
            group by isusf.issueId) isusf1 on isusf1.issueId = issue.IssueId
        </if>
        <if test='serialNumberUpdate=="1"'>
            inner join (select isusf.issueId
            from issue_self_update_system_file isusf
            where isusf.updateType in ('AUTHINSTALL', 'SFT_AUTHINSTALL', 'SFT_RE_AUTHINSTALL')
            group by isusf.issueId) isusf2 on isusf2.issueId = issue.IssueId
        </if>
        where 1=1
        <if test="startTime != ''">
            AND issue.submittime &gt;= #{startTime}
        </if>
        <if test="endTime != ''">
            AND issue.submittime &lt;= #{endTime}
        </if>
        <!--<if test='issueStatus == "N"'>
            <if test='connectArea=="TW"'>
                AND issue.IssueStatus in ('I', 'C', 'N','2','3','4','11','12','22')
            </if>
            <if test='connectArea!="TW"'>
                AND issue.IssueStatus = #{issueStatus}
            </if>
        </if>
        <if test='issueStatus == "Y"'>
            AND issue.IssueStatus in ('Y','7','10')
        </if>
        <if test='issueStatus!="N" and issueStatus!="Y" and issueStatus !=null and issueStatus != "" '>
            AND #{issueStatus} in (issue.IssueStatus, '')
        </if>-->
        <if test='issueStatus !=null and issueStatus != ""'>
            AND issue.IssueStatus in (${issueStatus})
        </if>
        <if test="queryUserId != ''">
            AND issue.ServiceId = #{queryUserId}
        </if>
        <if test="issueType != ''">
            AND issue.IssueType = #{issueType}
        </if>
        <if test="serviceRegion !=null and serviceRegion !=''">
            AND issue.serviceRegion=#{serviceRegion}
        </if>
        <if test='additionalExplanationReadType=="0"'>
            AND c.IsRead=0
        </if>
        <if test="crmId !=null and crmId !=''">
            AND issue.CrmId = #{crmId}
        </if>
        <if test='connectArea=="TW"'>
            <if test="productCode  !=null and productCode != ''">
                AND issue.ProductCode in (${productCode})
            </if>
            <if test="serviceCode  !=null and serviceCode != ''">
                AND issue.ServiceCode = #{serviceCode}
            </if>
            <if test="custLevel  !=null and custLevel != ''">
                AND ( customerservice.cust_level in (${custLevel})
                <if test="custLevelContainNull  !=null and custLevelContainNull != '' and (custLevelContainNull =='NULL'.toString() or custLevelContainNull =='None'.toString())">
                    OR (customerservice.cust_level is null or customerservice.cust_level = '')
                </if>
                )
            </if>
            <choose>
                <when test="department != null and department != '' ">
                    and (issue.ServiceDepartment = #{department} or mcs.departmentCode = #{department})
                    <if  test="serviceId !=null and serviceId != '' and supportId != null and supportId != ''">
                        and (issue.ServiceId = #{serviceId} or  issue.SupportId = #{supportId})
                    </if>
                    <if  test="serviceId !=null and serviceId != '' and (supportId == null or supportId == '')">
                        and issue.ServiceId = #{serviceId}
                    </if>
                    <if  test="(serviceId == null or serviceId == '') and supportId != null and supportId != '' ">
                        and issue.SupportId = #{supportId}
                    </if>
                </when>
                <when test="(department ==null or department == '') and ((serviceId !=null and serviceId != '') or (supportId != null and supportId != ''))">
                    <if  test="serviceId !=null and serviceId != '' and supportId != null and supportId != ''">
                        and (issue.ServiceId = #{serviceId} or  issue.SupportId = #{supportId})
                    </if>
                    <if  test="serviceId !=null and serviceId != '' and (supportId == null or supportId == '')">
                        and issue.ServiceId = #{serviceId}
                    </if>
                    <if  test="(serviceId == null or serviceId == '') and supportId != null and supportId != ''">
                        and issue.SupportId = #{supportId}
                    </if>
                </when>
                <otherwise>
                    <if test="userId !=null and userId != ''">
                        and (issue.ServiceId = #{userId} or  issue.SupportId = #{userId})
                    </if>
                </otherwise>
            </choose>
        </if>
        <if test='connectArea!="TW"'>
            <include refid="authCondition">
                <property name="Id" value="ServiceId"/>
                <property name="deptId" value="ServiceDepartment"/>
            </include>
        </if>
    </select>

    <select id="selectNewIssueCountByKewei" resultType="int">
        select count(*) from issue as issue
        JOIN mars_userpersonalinfo b on b.userid = issue.ServiceId
        <if test='additionalExplanationReadType=="0"'>
            left join (SELECT IssueId,min(IsRead) as IsRead from issue_contactlist group by IssueId)  c on  c.IssueId=issue.IssueId
        </if>
        where 1=1 AND issue.ProductCode=#{productCode}
        <if test='issueStatus == "N"'>
            <if test='connectArea=="TW"'>
                AND issue.IssueStatus in ('I', 'C', 'N','2','3','4','11','12','22','O','VN','Q','R')
            </if>
            <if test='connectArea!="TW"'>
                AND issue.IssueStatus = #{issueStatus}
            </if>
        </if>
        <if test='issueStatus == "Y"'>
            AND issue.IssueStatus in ('Y','7','10','A')
        </if>
        <if test='issueStatus!="N" and issueStatus!="Y" and issueStatus !=null and issueStatus != "" '>
            AND #{issueStatus} in (issue.IssueStatus, '')
        </if>
        <if test="issueType != ''">
            AND issue.IssueType = #{issueType}
        </if>
        <if test='additionalExplanationReadType=="0"'>
            AND c.IsRead=0
        </if>
    </select>

    <select id="selectNewIssueListByWorknoAndStatus" resultMap="issueDetailmap">
        select p.*,f.Name as contactname,g.notename as contactNote,h.Name as submitName,h.noteName as submitNote,
        IF(f.phone01 = '' OR f.phone01 IS NULL,f.phone02,f.phone01) as telephone,f.phone03 as cellphone,f.email as mail, f.qq as qq ,
        customer.CustomerName as customerName, customer. CustomerServiceCode as  customerServiceCode
        from
        (select  issue.IssueId as issueId,issue.UserId as userId,issue.CcUser as ccUser,
        issue.CrmId as crmId,
        case when issue.IssueStatus IN ('2','3','4','11','12','22','O','VN','Q','R') then 'N' when issue.IssueStatus IN ('7','10','A') then 'Y' ELSE issue.IssueStatus END state,
        issue.submittime as creatdatetime,
        issue.IssueDescription as issuedescription ,issue.ProductCode as productCode,issue.ProductVersion as productVersion,
        issue.ServiceCode as ServiceCode,issue.UserContactId as UserContactId,issue.machineRegion as MachineRegion, issue.serviceRegion as ServiceRegion,
        issue.site as site,issue.environment as environment,issue.SyncStatus as syncStatus, customerservice.cust_level as custlevel, customerservice.ContractExprityDate as contractExprityDate
        <if test='connectArea=="TW"'>
          ,case c.IsRead when 0 THEN false else true end as isRead
        </if>
        <if test='connectArea!="TW"'>
            ,true as isRead
        </if>
        ,issue.ServiceId as ServiceId
        from issue as issue
        JOIN mars_userpersonalinfo b on b.userid = issue.ServiceId
        <if test='connectArea=="TW"'>
            left JOIN mars_userpersonalinfo mus on mus.userid = issue.SupportId
            LEFT JOIN mars_customerservicesatff mcs on mcs.workno = mus.workno
        </if>
        LEFT join (SELECT IssueId,min(IsRead) as IsRead from issue_contactlist group by IssueId)  c on  c.IssueId=issue.IssueId
        LEFT JOIN mars_customerservice customerservice on issue.ServiceCode = customerservice.CustomerServiceCode and issue.ProductCode = customerservice.ProductCode
        <if test='serviceContact=="1"'>
            inner join (select isc.issueId
            from issue_service_contact isc
            group by isc.issueId) isc1 on isc1.issueId = issue.IssueId
        </if>
        <if test='projectUpdate=="1"'>
            inner join (select isusf.issueId
            from issue_self_update_system_file isusf
            where isusf.updateType = 'UPDATESERVICE' and isusf.invalidStatus != 1
            group by isusf.issueId) isusf1 on isusf1.issueId = issue.IssueId
        </if>
        <if test='serialNumberUpdate=="1"'>
            inner join (select isusf.issueId
            from issue_self_update_system_file isusf
            where isusf.updateType in ('AUTHINSTALL', 'SFT_AUTHINSTALL', 'SFT_RE_AUTHINSTALL')
            group by isusf.issueId) isusf2 on isusf2.issueId = issue.IssueId
        </if>
        where 1=1
        <if test="startTime != ''">
            AND issue.submittime &gt;= #{startTime}
        </if>
        <if test="endTime != ''">
            AND issue.submittime &lt;= #{endTime}
        </if>
        <!--<if test='issueStatus == "N"'>
            <if test='connectArea=="TW"'>
                AND issue.IssueStatus in ('I', 'C', 'N','2','3','4','11','12','22')
            </if>
            <if test='connectArea!="TW"'>
                AND issue.IssueStatus = #{issueStatus}
            </if>
        </if>
        <if test='issueStatus == "Y"'>
            AND issue.IssueStatus in ('Y','7','10')
        </if>
        <if test='issueStatus!="N" and issueStatus!="Y" and issueStatus !=null and issueStatus != ""'>
            AND #{issueStatus} in (issue.IssueStatus, '')
        </if>-->
        <if test='issueStatus !=null and issueStatus != ""'>
            AND issue.IssueStatus in (${issueStatus})
        </if>
        <if test="queryUserId != ''">
            AND issue.ServiceId = #{queryUserId}
        </if>
        <if test="issueType != ''">
            AND issue.IssueType = #{issueType}
        </if>
        <if test="serviceRegion  !=null and serviceRegion  !=''">
            AND issue.serviceRegion = #{serviceRegion}
        </if>
        <if test='additionalExplanationReadType=="0"'>
            AND c.IsRead=0
        </if>
        <if test="crmId !=null and crmId  !=''">
            AND issue.CrmId = #{crmId}
        </if>
        <if test='connectArea=="TW"'>
            <if test="productCode !=null and productCode != ''">
                AND issue.ProductCode in (${productCode})
            </if>
            <if test="serviceCode !=null and serviceCode != ''">
                AND issue.ServiceCode = #{serviceCode}
            </if>
            <if test="custLevel !=null and custLevel != ''">
                AND ( customerservice.cust_level in (${custLevel})
                <if test="custLevelContainNull !=null and custLevelContainNull != '' and (custLevelContainNull =='NULL'.toString() or custLevelContainNull =='None'.toString())">
                    OR (customerservice.cust_level is null or customerservice.cust_level = '')
                </if>
                )
            </if>
            <choose>
                <when test="department !=null and department != '' ">
                    and (issue.ServiceDepartment = #{department} or mcs.departmentCode = #{department})
                    <if  test="serviceId != null and  serviceId != '' and supportId != null and supportId != ''">
                        and (issue.ServiceId = #{serviceId} or  issue.SupportId = #{supportId})
                    </if>
                    <if  test="serviceId != null and serviceId != '' and (supportId == null or supportId == '')">
                        and issue.ServiceId = #{serviceId}
                    </if>
                    <if  test="(serviceId == null or serviceId == '') and supportId != null and supportId != '' ">
                        and issue.SupportId = #{supportId}
                    </if>
                </when>
                <when test="(department == null or department == '') and ((serviceId != null and  serviceId != '') or (supportId != null and supportId != ''))">
                    <if  test="serviceId != null and  serviceId != '' and supportId != null and supportId != ''">
                        and (issue.ServiceId = #{serviceId} or  issue.SupportId = #{supportId})
                    </if>
                    <if  test="serviceId != null and serviceId != '' and (supportId == null or supportId == '')">
                        and issue.ServiceId = #{serviceId}
                    </if>
                    <if  test="(serviceId  == null or serviceId == '') and supportId != null and supportId != '' ">
                        and issue.SupportId = #{supportId}
                    </if>
                </when>
                <otherwise>
                    <if test="userId !=null and userId != ''">
                        and (issue.ServiceId = #{userId} or  issue.SupportId = #{userId})
                    </if>
                </otherwise>
            </choose>
        </if>
        <if test='connectArea!="TW"'>
            <include refid="authCondition">
                <property name="Id" value="ServiceId"/>
                <property name="deptId" value="ServiceDepartment"/>
            </include>
        </if>
        ORDER by creatdatetime DESC limit #{start},#{size}) p
        left join mars_user as u on p.userId = u.Id
        left join mars_customer as customer on  customer.CustomerServiceCode = p.ServiceCode
        LEFT JOIN user_contacts as f on f.Id = p.UserContactId
        LEFT JOIN mars_userpersonalinfo as g  on f.UserId = g.UserId
        LEFT JOIN mars_userpersonalinfo as h on p.UserId = h.UserId
    </select>
    <select id="getIssuesInfoCountV2" resultType="int">
        select count(*)
        from issue as issue
        JOIN mars_userpersonalinfo b on b.userid = issue.ServiceId
        LEFT join (SELECT IssueId,min(IsRead) as IsRead from issue_contactlist group by IssueId)  c on  c.IssueId=issue.IssueId
        LEFT JOIN mars_customerservice customerservice on issue.ServiceCode = customerservice.CustomerServiceCode and issue.ProductCode = customerservice.ProductCode
        left join mars_customer as customer on  customer.CustomerServiceCode = issue.ServiceCode
        where 1=1
        <if test="startTime != ''">
            AND issue.submittime &gt;= #{startTime}
        </if>
        <if test="endTime != ''">
            AND issue.submittime &lt;= #{endTime}
        </if>
       <!-- <if test='issueStatus == "N"'>
            <if test='connectArea=="TW"'>
                AND issue.IssueStatus in ('I', 'C', 'N','2','3','4','11','12','22')
            </if>
            <if test='connectArea!="TW"'>
                /*AND issue.IssueStatus = #{issueStatus}*/
                AND issue.IssueStatus in ('I', 'C', 'N')
            </if>
        </if>
        <if test='issueStatus == "Y"'>
            AND issue.IssueStatus in ('Y','7','10')
        </if>
        <if test='issueStatus!="N" and issueStatus!="Y" and issueStatus !=null and issueStatus != ""'>
            AND #{issueStatus} in (issue.IssueStatus, '')
        </if>-->
        <if test='issueStatus !=null and issueStatus != ""'>
            AND issue.IssueStatus in (${issueStatus})
        </if>
        <if test="queryUserId != ''">
            AND issue.ServiceId = #{queryUserId}
        </if>
        <if test="issueType != ''">
            AND issue.IssueType = #{issueType}
        </if>
        <if test="serviceRegion  !=null and serviceRegion  !=''">
            AND issue.serviceRegion = #{serviceRegion}
        </if>
        <if test="productCode  !=null and productCode != ''">
            AND issue.ProductCode in (${productCode})
        </if>
        <if test="productList != null and productList.size > 0">
            AND issue.ProductCode IN
            <foreach collection="productList" index="index" item="item" open="(" separator="," close=")">
                #{item.productCode}
            </foreach>
        </if>
        <if test="serviceCode  !=null and serviceCode != ''">
            AND (customer.CustomerName like "%"#{serviceCode}"%" or customer.CustomerServiceCode like "%"#{serviceCode}"%" )
        </if>
        <if test="custLevel  !=null and custLevel != ''">
            AND ( customerservice.cust_level in (${custLevel})
            <if test="custLevelContainNull  !=null and custLevelContainNull != '' and custLevelContainNull =='NULL'.toString()">
                OR (customerservice.cust_level is null or customerservice.cust_level = '')
            </if>
            )
        </if>
        <if test="contractState  !=null and contractState != ''">
            AND TRIM(customerservice.contractState) = #{contractState}
        </if>
        <include refid="authCondition">
            <property name="Id" value="ServiceId"/>
            <property name="deptId" value="ServiceDepartment"/>
        </include>
        <!--<choose>
            <when test="custLevel  !=null and custLevel  !='' and custLevel !='NULL'.toString()">
                AND customerservice.cust_level in (${custLevel})
            </when>
            <when test="custLevel =='NULL'.toString()">
                AND (customerservice.cust_level is null or customerservice.cust_level = '')
            </when>
            <otherwise>

            </otherwise>
        </choose>-->
        <if test="crmId  !=null and crmId  !=''">
            AND issue.CrmId = #{crmId}
        </if>
        <if test='additionalExplanationReadType=="0"'>
            AND c.IsRead=0
        </if>
        <if test="issueDescription  !=null and issueDescription  !=''">
            AND issue.issueDescription like "%"#{issueDescription}"%"
        </if>
    </select>
    <select id="getAllIssueDetails" resultMap="issueDetailmap">
        select p.*,f.Name as contactname,g.notename as contactNote,h.Name as submitName,h.noteName as submitNote,
        IF(f.phone01 = '' OR f.phone01 IS NULL,f.phone02,f.phone01) as telephone,f.email as mail, f.qq as qq ,
        customer.CustomerName as customerName, customer.CustomerServiceCode as  customerServiceCode  from
        (select distinct issue.IssueId as issueId,issue.UserId as userId,issue.CcUser as ccUser,
        issue.CrmId as crmId,
        case when issue.IssueStatus IN ('2','3','4','11','12','22','O','VN','Q','R') then 'N' when issue.IssueStatus IN ('7','10','A') then 'Y' ELSE issue.IssueStatus END state,
        issue.submittime as creatdatetime,
        issue.IssueDescription as issuedescription ,issue.ProductCode as productCode,issue.ProductVersion as productVersion,
        issue.ServiceCode as ServiceCode,issue.UserContactId as UserContactId,issue.machineRegion as MachineRegion, issue.serviceRegion as ServiceRegion,
        issue.site as site,issue.environment as environment,issue.SyncStatus as syncStatus, customerservice.cust_level as custlevel,customerservice.ContractExprityDate as contractExprityDate,customerservice.ContractState contractState
        <if test='connectArea=="TW"'>
            ,case c.IsRead when 0 THEN false else true end as isRead
        </if>
        <if test='connectArea!="TW"'>
            ,true as isRead
        </if>
        ,issue.ServiceId as ServiceId,b.name serviceName
        from issue as issue
        JOIN mars_userpersonalinfo b on b.userid = issue.ServiceId
        LEFT join (SELECT IssueId,min(IsRead) as IsRead from issue_contactlist group by IssueId)  c on  c.IssueId=issue.IssueId
        LEFT JOIN mars_customerservice customerservice on issue.ServiceCode = customerservice.CustomerServiceCode and issue.ProductCode = customerservice.ProductCode
        where 1=1
        <if test="startTime != ''">
            AND issue.submittime &gt;= #{startTime}
        </if>
        <if test="endTime != ''">
            AND issue.submittime &lt;= #{endTime}
        </if>
        <!--<if test='issueStatus == "N"'>
            <if test='connectArea=="TW"'>
                AND issue.IssueStatus in ('I', 'C', 'N','2','3','4','11','12','22')
            </if>
            <if test='connectArea!="TW"'>
                /*AND issue.IssueStatus = #{issueStatus}*/
                AND issue.IssueStatus in ('I', 'C', 'N')
            </if>
        </if>
        <if test='issueStatus == "Y"'>
            AND issue.IssueStatus in ('Y','7','10')
        </if>
        <if test='issueStatus!="N" and issueStatus!="Y" and issueStatus !=null and issueStatus != ""'>
            AND #{issueStatus} in (issue.IssueStatus, '')
        </if>-->

        <if test='issueStatus !=null and issueStatus != ""'>
            AND issue.IssueStatus in (${issueStatus})
        </if>
        <if test="queryUserId != ''">
            AND issue.ServiceId = #{queryUserId}
        </if>
        <if test="issueType != ''">
            AND issue.IssueType = #{issueType}
        </if>
        <if test="serviceRegion  !=null and serviceRegion  !=''">
            AND issue.serviceRegion = #{serviceRegion}
        </if>
        <if test="productCode  !=null and productCode != ''">
            AND issue.ProductCode in (${productCode})
        </if>
        <if test="productList != null and productList.size > 0">
            AND issue.ProductCode IN
            <foreach collection="productList" index="index" item="item" open="(" separator="," close=")">
                #{item.productCode}
            </foreach>
        </if>

        <if test="custLevel  !=null and custLevel != ''">
            AND ( customerservice.cust_level in (${custLevel})
            <if test="custLevelContainNull  !=null and custLevelContainNull != '' and custLevelContainNull =='NULL'.toString()">
                OR (customerservice.cust_level is null or customerservice.cust_level = '')
            </if>
            )
        </if>
        <if test="contractState  !=null and contractState != ''">
            AND TRIM(customerservice.contractState) = #{contractState}
        </if>
        <include refid="authCondition">
            <property name="Id" value="ServiceId"/>
            <property name="deptId" value="ServiceDepartment"/>
        </include>
        <!--<choose>
            <when test="custLevel  !=null and custLevel  !='' and custLevel !='NULL'.toString()">
                AND customerservice.cust_level in ($custLevel})
            </when>
            <when test="custLevel =='NULL'.toString()">
                AND (customerservice.cust_level is null or customerservice.cust_level = '')
            </when>
            <otherwise>

            </otherwise>
        </choose>-->
        <if test="crmId  !=null and crmId  !=''">
            AND issue.CrmId = #{crmId}
        </if>
        <if test='additionalExplanationReadType=="0"'>
            AND c.IsRead=0
        </if>
        <if test="issueDescription  !=null and issueDescription  !=''">
            AND issue.issueDescription like "%"#{issueDescription}"%"
        </if>
        ORDER by creatdatetime DESC limit #{start},#{size}) p
        left join mars_user as u on p.userId = u.Id
        left join mars_customer as customer on  customer.CustomerServiceCode = p.ServiceCode
        LEFT JOIN user_contacts as f on f.Id = p.UserContactId
        LEFT JOIN mars_userpersonalinfo as g  on f.UserId = g.UserId
        LEFT JOIN mars_userpersonalinfo as h on p.UserId = h.UserId
        where 1=1
        <if test="serviceCode  !=null and serviceCode != ''">
            AND (customer.CustomerName like "%"#{serviceCode}"%" or customer.CustomerServiceCode like "%"#{serviceCode}"%" )
        </if>
    </select>

    <select id="selectNewIssueCountForCustomerServiceManager" resultType="int">
        select count(*)
        from issue as issue
        JOIN mars_userpersonalinfo b on b.userid = issue.ServiceId
        LEFT join (SELECT IssueId,min(IsRead) as IsRead from issue_contactlist group by IssueId)  c on  c.IssueId=issue.IssueId
        LEFT JOIN mars_customerservice customerservice on issue.ServiceCode = customerservice.CustomerServiceCode and issue.ProductCode = customerservice.ProductCode
        <if test="productCodeClassificationList != null and productCodeClassificationList.size > 0">
            LEFT JOIN  issue_casedetail on issue.IssueId = issue_casedetail.IssueId
        </if>
        where 1=1
        <if test="startTime != ''">
            AND issue.submittime &gt;= #{startTime}
        </if>
        <if test="endTime != ''">
            AND issue.submittime &lt;= #{endTime}
        </if>
        <!-- <if test='issueStatus == "N"'>
             <if test='connectArea=="TW"'>
                 AND issue.IssueStatus in ('I', 'C', 'N','2','3','4','11','12','22')
             </if>
             <if test='connectArea!="TW"'>
                 /*AND issue.IssueStatus = #{issueStatus}*/
                 AND issue.IssueStatus in ('I', 'C', 'N')
             </if>
         </if>
         <if test='issueStatus == "Y"'>
             AND issue.IssueStatus in ('Y','7','10')
         </if>
         <if test='issueStatus!="N" and issueStatus!="Y" and issueStatus !=null and issueStatus != ""'>
             AND #{issueStatus} in (issue.IssueStatus, '')
         </if>-->
        <if test='issueStatus !=null and issueStatus != ""'>
            AND issue.IssueStatus in (${issueStatus})
        </if>
        <if test="queryUserId != ''">
            AND issue.ServiceId = #{queryUserId}
        </if>
        <if test="issueType != ''">
            AND issue.IssueType = #{issueType}
        </if>
        <if test="serviceRegion  !=null and serviceRegion  !=''">
            AND issue.serviceRegion = #{serviceRegion}
        </if>
        <if test="productCode  !=null and productCode != ''">
            AND issue.ProductCode in (${productCode})
        </if>
        <if test="productList != null and productList.size > 0">
            AND issue.ProductCode IN
            <foreach collection="productList" index="index" item="item" open="(" separator="," close=")">
                #{item.productCode}
            </foreach>
        </if>
        <if test="productCodeClassificationList != null and productCodeClassificationList.size > 0">
            AND
            <foreach collection="productCodeClassificationList" item="item" open="(" separator=" OR " close=")">
                ( issue.ProductCode =#{item.productCode}
                <foreach collection="item.issueClassification" item="category" open=" AND  issue_casedetail.IssueClassification IN (" separator="," close=")">
                    #{category.issueClassification}
                </foreach>
                )
            </foreach>
        </if>
        <if test="serviceCode  !=null and serviceCode != ''">
            AND issue.ServiceCode like "%"#{serviceCode}"%"
        </if>
        <if test="custLevel  !=null and custLevel != ''">
            AND ( customerservice.cust_level in (${custLevel})
            <if test="custLevelContainNull  !=null and custLevelContainNull != '' and custLevelContainNull =='NULL'.toString()">
                OR (customerservice.cust_level is null or customerservice.cust_level = '')
            </if>
            )
        </if>

        <!--<choose>
            <when test="custLevel  !=null and custLevel  !='' and custLevel !='NULL'.toString()">
                AND customerservice.cust_level in (${custLevel})
            </when>
            <when test="custLevel =='NULL'.toString()">
                AND (customerservice.cust_level is null or customerservice.cust_level = '')
            </when>
            <otherwise>

            </otherwise>
        </choose>-->
        <if test="crmId  !=null and crmId  !=''">
            AND issue.CrmId = #{crmId}
        </if>
        <if test='additionalExplanationReadType=="0"'>
            AND c.IsRead=0
        </if>
    </select>
    <select id="selectNewIssueListForCustomerServiceManager" resultMap="issueDetailmap">
        select p.*,f.Name as contactname,g.notename as contactNote,h.Name as submitName,h.noteName as submitNote,
        IF(f.phone01 = '' OR f.phone01 IS NULL,f.phone02,f.phone01) as telephone,f.email as mail, f.qq as qq ,
        customer.CustomerName as customerName, customer. CustomerServiceCode as  customerServiceCode  from
        (select  issue.IssueId as issueId,issue.UserId as userId,issue.CcUser as ccUser,
        issue.CrmId as crmId,
        case when issue.IssueStatus IN ('2','3','4','11','12','22','O','VN','Q','R') then 'N' when issue.IssueStatus IN ('7','10','A') then 'Y' ELSE issue.IssueStatus END state,
        issue.submittime as creatdatetime,
        issue.IssueDescription as issuedescription ,issue.ProductCode as productCode,issue.ProductVersion as productVersion,
        issue.ServiceCode as ServiceCode,issue.UserContactId as UserContactId,issue.machineRegion as MachineRegion, issue.serviceRegion as ServiceRegion,
        issue.site as site,issue.environment as environment,issue.SyncStatus as syncStatus, customerservice.cust_level as custlevel,customerservice.ContractExprityDate as contractExprityDate
        <if test='connectArea=="TW"'>
            ,case c.IsRead when 0 THEN false else true end as isRead
        </if>
        <if test='connectArea!="TW"'>
            ,true as isRead
        </if>
        ,issue.ServiceId as ServiceId
        from issue as issue
        JOIN mars_userpersonalinfo b on b.userid = issue.ServiceId
        LEFT join (SELECT IssueId,min(IsRead) as IsRead from issue_contactlist group by IssueId)  c on  c.IssueId=issue.IssueId
        LEFT JOIN mars_customerservice customerservice on issue.ServiceCode = customerservice.CustomerServiceCode and issue.ProductCode = customerservice.ProductCode
        <if test="productCodeClassificationList != null and productCodeClassificationList.size > 0">
            LEFT JOIN  issue_casedetail on issue.IssueId = issue_casedetail.IssueId
        </if>
        where 1=1
        <if test="startTime != ''">
            AND issue.submittime &gt;= #{startTime}
        </if>
        <if test="endTime != ''">
            AND issue.submittime &lt;= #{endTime}
        </if>
        <!--<if test='issueStatus == "N"'>
            <if test='connectArea=="TW"'>
                AND issue.IssueStatus in ('I', 'C', 'N','2','3','4','11','12','22')
            </if>
            <if test='connectArea!="TW"'>
                /*AND issue.IssueStatus = #{issueStatus}*/
                AND issue.IssueStatus in ('I', 'C', 'N')
            </if>
        </if>
        <if test='issueStatus == "Y"'>
            AND issue.IssueStatus in ('Y','7','10')
        </if>
        <if test='issueStatus!="N" and issueStatus!="Y" and issueStatus !=null and issueStatus != ""'>
            AND #{issueStatus} in (issue.IssueStatus, '')
        </if>-->

        <if test='issueStatus !=null and issueStatus != ""'>
            AND issue.IssueStatus in (${issueStatus})
        </if>
        <if test="queryUserId != ''">
            AND issue.ServiceId = #{queryUserId}
        </if>
        <if test="issueType != ''">
            AND issue.IssueType = #{issueType}
        </if>
        <if test="serviceRegion  !=null and serviceRegion  !=''">
            AND issue.serviceRegion = #{serviceRegion}
        </if>
        <if test="productCode  !=null and productCode != ''">
            AND issue.ProductCode in (${productCode})
        </if>
        <if test="productList != null and productList.size > 0">
            AND issue.ProductCode IN
            <foreach collection="productList" index="index" item="item" open="(" separator="," close=")">
                #{item.productCode}
            </foreach>
        </if>
        <if test="productCodeClassificationList != null and productCodeClassificationList.size > 0">
            AND
            <foreach collection="productCodeClassificationList" item="item" open="(" separator=" OR " close=")">
               ( issue.ProductCode =#{item.productCode}
                <foreach collection="item.issueClassification" item="category" open=" AND  issue_casedetail.IssueClassification IN (" separator="," close=")">
                    #{category.issueClassification}
                </foreach>
                )
            </foreach>
        </if>
        <if test="serviceCode  !=null and serviceCode != ''">
            AND issue.ServiceCode like "%"#{serviceCode}"%"
        </if>
        <if test="custLevel  !=null and custLevel != ''">
            AND ( customerservice.cust_level in (${custLevel})
            <if test="custLevelContainNull  !=null and custLevelContainNull != '' and custLevelContainNull =='NULL'.toString()">
                OR (customerservice.cust_level is null or customerservice.cust_level = '')
            </if>
            )
        </if>

        <!--<choose>
            <when test="custLevel  !=null and custLevel  !='' and custLevel !='NULL'.toString()">
                AND customerservice.cust_level in ($custLevel})
            </when>
            <when test="custLevel =='NULL'.toString()">
                AND (customerservice.cust_level is null or customerservice.cust_level = '')
            </when>
            <otherwise>

            </otherwise>
        </choose>-->
        <if test="crmId  !=null and crmId  !=''">
            AND issue.CrmId = #{crmId}
        </if>
        <if test='additionalExplanationReadType=="0"'>
            AND c.IsRead=0
        </if>

        ORDER by creatdatetime DESC limit #{start},#{size}) p
        left join mars_user as u on p.userId = u.Id
        left join mars_customer as customer on  customer.CustomerServiceCode = p.ServiceCode
        LEFT JOIN user_contacts as f on f.Id = p.UserContactId
        LEFT JOIN mars_userpersonalinfo as g  on f.UserId = g.UserId
        LEFT JOIN mars_userpersonalinfo as h on p.UserId = h.UserId
    </select>

    <select id="getIssuesDetailInfoByWorkNo" resultMap="issueDetailmap">
        select  customerissuea.id as issueId,customerissuea.ordernumber as ordernumber,
            customerissuea.crmid as crmId,
            customer.CustomerName as customerName,customerissuea.status as state,
            customerissuea.creatdatetime as creatdatetime,customerissuea.contactname as contactname,
            customerissuea.telephone as telephone,customerissuea.mail as mail,customerissuea.qq as qq ,
            customerissuea.issuedescription as issuedescription ,
            customerissuea.productcode as productCode
        from mars_customerissue as customerissuea
        left join mars_customer as customer
        on customerissuea.customercode = customer.CustomerCode
        where customerissuea.servicestaffcode = #{workNo}
        and (customerissuea.status = 'C'  or customerissuea.status = 'N')
        <if test='issueStatus == "C"'>
            AND  customerissuea.status = 'C'
        </if>
        <if test='issueStatus == "N"'>
            AND  customerissuea.status = 'N'
        </if>
    </select>
    <select id="getIssuesDetailInfoByWorkNoFromNewIssue" resultType="com.digiwin.escloud.issueservice.model.IssueDetailInfoNew">
        select d.*
        from (
            SELECT a.*,case when TIMESTAMPDIFF(minute,a.creatdatetime,NOW()) <![CDATA[<=]]> a.receiptTime
            || ( TIMESTAMPDIFF(MINUTE,a.creatdatetime,NOW()) <![CDATA[>]]> a.receiptTime
            and TIMESTAMPDIFF(minute,a.creatdatetime,p.ProcessTime) <![CDATA[<]]> a.receiptTime
            )  then 'green'
            else 'red' end color
            FROM (select  distinct customerissuea.IssueId as issueId,
            customerissuea.CrmId as crmId,
            customer.CustomerName as customerName,
            customerissuea.IssueStatus as state,
            customerissuea.submittime as creatdatetime,
            f.Name as contactname,
            f.phone01 as telephone,
            f.email as mail,
            f.qq as qq ,
            customerissuea.IssueDescription as issuedescription ,
            customerissuea.ProductCode as productCode,
            h.receiptTime
            from issue as customerissuea
            JOIN mars_userpersonalinfo b on b.userid = customerissuea.ServiceId
            left join mars_customerservicesatff c on c.workno= b.workno
            left JOIN mars_customer as customer on customerissuea.ServiceCode = customer.CustomerServiceCode
            LEFT JOIN user_contacts f on f.Id = customerissuea.UserContactId
            LEFT JOIN serviceregions s ON s.Region = customerissuea.serviceRegion
            LEFT JOIN serviceregions_products h ON h.productCode = customerissuea.ProductCode AND s.Id = h.regionId
            where customerissuea.IssueStatus in('C','N')
            <if test='issueStatus == "C"'>
                AND  customerissuea.IssueStatus = 'C'
            </if>
            <if test='issueStatus == "N"'>
                AND  customerissuea.IssueStatus = 'N'
            </if>
            <if test="workNo != ''">
                and b.workno = #{workNo}
            </if>
            <if test="deptCode != ''">
                AND c.departmentcode =#{deptCode}
            </if>
            )a
            LEFT JOIN issue_progress p ON p.IssueId= a.issueId AND p.ProcessType='Process'
            GROUP BY a.issueId
        ) d
        where 1=1
        <if test='color == "red"'>
            AND d.color = #{color}
        </if>
        ORDER BY d.issueId DESC
    </select>
    <select id="selectIssueProgressById" resultMap="issueprogressmap">
        select * from issue_progress
       where id = #{id}
    </select>
    <update id="UpdateIssueProgressById">
        UPDATE issue_progress
        SET Description=#{description},ProcessHours=#{processHours},SyncStatus=#{syncStatus},ProcessTime=#{processTime},CurrentStatus=#{CurrentStatus}
        where id = #{id}
    </update>
    <update id="UpdateIssueProgress">
        UPDATE issue_progress
        SET CurrentStatus=#{currentStatus}
        <if test="handlerId !=null and handlerId !=''">
            ,handlerId=#{handlerId}
        </if>
        <if test="processor !=null and processor !=''">
            ,Processor=#{processor}
        </if>
        <if test="workno !=null and workno !=''">
            ,workno=#{workno}
        </if>
        where IssueId = #{issueId} and ProcessType= #{processType} and SequenceNum=#{sequeceNum}
    </update>
    <select id="getMaxOrder" parameterType="java.lang.Long" resultType="java.lang.Integer">
        SELECT ifnull(max(SequenceNum),0) from issue_progress where issueId = #{issueId}  limit 1
    </select>
    <select id="getProcessMaxSeqNum" parameterType="java.lang.Long" resultType="java.lang.Integer">
        SELECT ifnull(max(SequenceNum),0) from issue_progress where ProcessType='Process' and issueId = #{issueId}  limit 1
    </select>
    <update id="UpdateIssueProductVersion">
        UPDATE issue issue
        INNER JOIN mars_customerservice cs on cs.CustomerServiceCode = issue.ServiceCode and cs.ProductCode = issue.ProductCode
        SET issue.ProductVersion =  cs.ProductVersion
        where issue.IssueId = #{issueId}
    </update>
    <select id="selectIssue" resultMap="issuemap">
        select * from issue
        where CrmId = #{crmId}
    </select>

    <select id="SelectEmail" resultType="java.lang.String">
        SELECT c.StaffEmail AS Email FROM issue a
        LEFT JOIN mars_userpersonalinfo b ON a.ServiceId=b.userid
        LEFT JOIN immediatesupervisorstaff c ON b.workno=c.WorkNo
        WHERE a.IssueId = #{issueId} and <![CDATA[ifnull(c.StaffEmail, '')<>'']]>
        UNION
        SELECT c.SupervisorEmail AS Email FROM issue a
        LEFT JOIN mars_userpersonalinfo b ON a.ServiceId=b.userid
        LEFT JOIN immediatesupervisorstaff c ON b.workno=c.WorkNo
        WHERE a.IssueId = #{issueId} and <![CDATA[ifnull(c.SupervisorEmail, '')<>'']]>
        UNION
        SELECT c.StaffEmail AS Email FROM issue a
        LEFT JOIN mars_userpersonalinfo b ON a.SupportId=b.userid
        LEFT JOIN immediatesupervisorstaff c ON b.workno=c.WorkNo
        WHERE a.IssueId = #{issueId} and <![CDATA[ifnull(c.StaffEmail, '')<>'']]>
        UNION
        SELECT c.SupervisorEmail AS Email FROM issue a
        LEFT JOIN mars_userpersonalinfo b ON a.SupportId=b.userid
        LEFT JOIN immediatesupervisorstaff c ON b.workno=c.WorkNo
        WHERE a.IssueId = #{issueId} and <![CDATA[ifnull(c.SupervisorEmail, '')<>'']]>
    </select>

    <select id="SelectEmailsBySelfUpdateSystem" resultType="java.lang.String">
        select distinct email from (
            SELECT c.email FROM issue a
            LEFT JOIN mars_userpersonalinfo b ON a.ServiceId=b.userid
            LEFT JOIN mars_customerservicesatff c ON b.workno=c.WorkNo
            WHERE a.IssueId = #{issueId} and <![CDATA[ifnull(c.email, '')<>'']]>
            UNION
            SELECT c.email FROM issue a
            LEFT JOIN mars_userpersonalinfo b ON a.SupportId=b.userid
            LEFT JOIN mars_customerservicesatff c ON b.workno=c.WorkNo
            WHERE a.IssueId = #{issueId} and <![CDATA[ifnull(c.email, '')<>'']]>
            UNION
            SELECT d.email FROM issue a
            LEFT JOIN mars_userpersonalinfo b ON a.ServiceId=b.userid
            LEFT JOIN mars_customerservicesatff c ON b.workno=c.WorkNo
            LEFT JOIN mars_customerservicesatff d ON d.itcode = c.supervisor
            WHERE a.IssueId = #{issueId} and <![CDATA[ifnull(d.email, '')<>'']]>

        ) d

    </select>

    <select id="SelectFollowUpLastTime" resultType="java.lang.String">
       select ProcessTime from issue_progress
       where IssueId = #{issueId} and ProcessType='FollowUp'
       order by Id desc
       LIMIT 0,1
    </select>

    <select id="selectWebIssuesInfoList" resultMap="issueDetailmap">
        select p.*,f.Name as contactname, f.phone01 as telephone,f.email as mail, f.qq as qq ,
        IF(customer.CustomerName IS NULL OR TRIM(customer.CustomerName) = '', d.AE002, customer.CustomerName) as customerName, customer.CustomerServiceCode as  customerServiceCode, product.ProductCategory as productCategory,
        b.name as serviceName from
        (select distinct issue.IssueId as issueId,issue.UserId as userId,issue.CcUser as ccUser,
        issue.CrmId as crmId, issue.IssueStatus as state, issue.submittime as creatdatetime,
        issue.IssueDescription as issuedescription ,issue.ProductCode as productCode,issue.ProductVersion as productVersion,
        issue.ServiceCode as ServiceCode,issue.UserContactId as UserContactId, issue.ServiceId
        from issue as issue
        where 1=1
        <if test="issueStatus != ''">
           AND issue.IssueStatus = #{issueStatus}
        </if>
        <if test="productCode != ''">
            AND issue.ProductCode = #{productCode}
        </if>
        <if test="startTime != ''">
            AND issue.submittime &gt;= #{startTime}
        </if>
        <if test="endTime != ''">
            AND issue.submittime &lt;= #{endTime}
        </if>
        <if test="issueType != ''">
            AND issue.IssueType = #{issueType}
        </if>
        <if test="codeOrName != ''">
            AND issue.ServiceCode like "%"#{codeOrName}"%"
        </if>
        ORDER by creatdatetime DESC limit #{start},#{size}
        ) p
        left JOIN mars_userpersonalinfo b on b.userid = p.ServiceId
        left join mars_customer as customer on customer.CustomerServiceCode = p.ServiceCode
        LEFT JOIN serae as d  on p.ServiceCode = d.AE001
        LEFT JOIN user_contacts as f on f.Id = p.UserContactId
        LEFT JOIN mars_product product on p.ProductCode = product.ProductCode
        where 1=1
        ORDER BY creatdatetime desc
    </select>

    <select id="selectWebIssuesCount" resultType="java.lang.Integer">
        select Count(*)
        from issue as issue
        where 1=1
        <if test="issueStatus != ''">
            AND issue.IssueStatus = #{issueStatus}
        </if>
        <if test="productCode != ''">
            AND issue.ProductCode = #{productCode}
        </if>
        <if test="startTime != ''">
            AND issue.submittime &gt;= #{startTime}
        </if>
        <if test="endTime != ''">
            AND issue.submittime &lt;= #{endTime}
        </if>
        <if test="issueType != ''">
            AND issue.IssueType = #{issueType}
        </if>
        <if test="codeOrName != ''">
            AND issue.ServiceCode like "%"#{codeOrName}"%"
        </if>
    </select>

    <select id="SelectCustomerIssueServiceRegion" resultType="java.lang.String">
        SELECT distinct serviceRegion
        from issue
        where ServiceCode=#{customerServiceCode}
        order by serviceRegion
    </select>

    <select id="SelectIssueMachineRegionByStaffId" resultType="java.lang.String">
        SELECT distinct machineRegion
        from issue
        where ServiceId=#{StaffId}
        order by machineRegion
    </select>

    <select id="SelectIssueServiceRegionByStaffId" resultType="java.lang.String">
        SELECT distinct serviceRegion
        from issue
        where ServiceId=#{StaffId}
        order by serviceRegion
    </select>

    <update id="UpdateIssueAdditionalExplanationReadType">
        UPDATE issue_contactlist
        SET IsRead=1 ,ReadTime=Now()
        where IssueId = #{issueId} AND  IsRead=FALSE
    </update>
    <update id="UpdateIssueAdditionalExplanationSync">
        UPDATE issue_contactlist
        SET SyncStatus=#{syncStatus} ,SyncTime=Now()
        where IssueId = #{issueId}
        <if test="progressId &gt; 0">
            and ProgressId = #{progressId}
        </if>
    </update>
    <update id="UpdateIssueDescription">
        UPDATE issue
        SET IssueDescription=#{issueDescription}
        where IssueId = #{issueId}  AND  ServiceId=#{ServiceId}
    </update>

    <insert id="InsertIssueSummary">
        insert into issue_summary(IssueId,AcceptTime,AcceptHours,ResolvedTime,ResolvedHours,ClosedTime,ClosedHours)
        values (#{issueId},
        <if test="type =='accept'">
           #{processTime},round(count_process_time((select issue.SubmitTime from Issue where IssueId=#{issueId}),#{processTime})/60,2),null,0,null,0
        </if>
        <if test="type =='resolved'">
            null,0,#{processTime},round(count_process_time((select issue.SubmitTime from Issue where IssueId=#{issueId}),#{processTime})/60,2),null,0
        </if>
        <if test="type =='closed'">
            null,0,null,0,#{processTime},round(count_process_time((select issue.SubmitTime from Issue where IssueId=#{issueId}),#{processTime})/60,2)
        </if>
        )
    </insert>

    <update id="UpdateIssueSummary">
        UPDATE issue_summary
        SET
        <if test="type =='accept'">
          AcceptTime=#{processTime},AcceptHours= round(count_process_time((select issue.SubmitTime from Issue where IssueId=#{issueId}),#{processTime})/60,2)
        </if>
        <if test="type =='resolved'">
            ResolvedTime=#{processTime},ResolvedHours= round(count_process_time((select issue.SubmitTime from Issue where IssueId=#{issueId}),#{processTime})/60,2)
        </if>
        <if test="type =='closed'">
            ClosedTime=#{processTime},ClosedHours= round(count_process_time((select issue.SubmitTime from Issue where IssueId=#{issueId}),#{processTime})/60,2)
        </if>
        where IssueId = #{issueId}
    </update>
    <insert id="InsertIssueSummaryForAgreeClose">
        insert into issue_summary(IssueId,AcceptTime,AcceptHours,ResolvedTime,ResolvedHours,ClosedTime,ClosedHours,AgreeCloseTime)
        values (#{issueId}, null,0,null,0,#{processTime},round(count_process_time((select issue.SubmitTime from Issue where IssueId=#{issueId}),#{processTime})/60,2),#{processTime}
        )
    </insert>

    <update id="UpdateIssueSummaryForAgreeClose">
        UPDATE issue_summary
        SET
            ClosedTime=#{processTime},ClosedHours= round(count_process_time((select issue.SubmitTime from Issue where IssueId=#{issueId}),#{processTime})/60,2),AgreeCloseTime=#{processTime}
        where IssueId = #{issueId}
    </update>
    <select id="getIssueSummary" resultType="com.digiwin.escloud.issueservice.model.IssueSummary">
        select * from issue_summary
        where issueId = #{issueId}
    </select>

    <select id="getWebIssueClassification" resultType="map">
        select issueAtt.IssueClassification, issueAtt.IssueClassificationDesc
        from mars_customerservice mcs
        inner join issueclassification_customer_attribute issueAtt on mcs.ProductCode = issueAtt.ProductCode
        where 1=1
          and issueAtt.ServiceRegion = #{serviceRegion}
          and mcs.CustomerServiceCode = #{customerServiceCode}
          and issueAtt.ProductCode = #{productCode}
    </select>
    <select id="getServiceProductCount" resultMap="serviceproductcountmap">
        select a.*,p.ProductShortName as productCategory
        from serviceproduct_target_detail a
        left join mars_product p on a.ProductCode=p.ProductCode
        where a.ServiceRegion = #{serviceRegion}
        and a.TalkDate=#{refreshDate}
        and a.ProductCode in(select productCode from mars_userdefaultproductinfo where UserId=#{staffId})
    </select>

    <update id="updateIssueService">
        UPDATE issue
        SET ServiceId=#{serviceId},SyncStatus=#{syncStatus}
        <if test="department!=null and department!=''">
            ,ServiceDepartment=#{department}
        </if>
        <if test="productCode!=null and productCode!=''">
            ,ProductCode=#{productCode}
        </if>
        where IssueId = #{issueId}
    </update>

    <update id="cancelCloseIssue">
        UPDATE issue
        SET IssueStatus='N',SyncStatus=#{syncStatus}
        where IssueId = #{issueId}
    </update>

    <update id="updateCurrentStatus">
        UPDATE issue_progress
        SET CurrentStatus=#{status},SyncStatus=#{syncStatus}
        where Id = #{progressId}
    </update>

    <select id="checkIssueIsSyncCrm" resultType="java.lang.Integer">
        SELECT a.syncCRM FROM serviceregions_products a
        LEFT JOIN serviceregions b ON a.regionId = b.Id
        WHERE b.Region =#{serviceRegion} AND a.productCode = #{productCode} and a.issueStatus =#{issueStatus}
        limit 1
    </select>

    <select id="selectTrialSubmitedIssueCount" resultType="int">
        select count(*) from issue
        where 1=1 and ServiceCode =#{serviceCode} and ProductCode=#{productCode} and DATE_FORMAT(SubmitTime,'%Y-%m-%d') BETWEEN CAST(#{contractStartDate} AS DATE) AND CAST(#{contractExprityDate} AS DATE)
         and  ( SubmitWay != 'ITMS_Service' and SubmitWay not like CONCAT('SIM','%'))
    </select>

    <select id="getIssueSyncStatus" resultType="java.lang.String">
        SELECT SyncStatus FROM issue
        where IssueId = #{issueId}
    </select>
    <select id="getIssueStatus" resultType="java.lang.String">
        SELECT case when issue.IssueStatus IN ('N','2','3','4','11','12','22','8','O','VN','Q','R') then 'N'
        when issue.IssueStatus IN ('Y','7','10','A') then 'Y'
        else issue.IssueStatus END issueStatus
        FROM issue
        where crmId = #{crmId}
    </select>
    <select id="getUserIdbyMail" resultType="java.lang.String">
        SELECT ID FROM mars_user
        where CustomerServiceCode = #{serviceCode} and Username= #{mail}
        limit 1
    </select>

    <select id="getDefaultPhonebyServiceCode" resultType="java.lang.String">
        SELECT ifnull(GG014,ifnull(GG015,'')) as phone FROM serae
        where AE001 = #{serviceCode}
        limit 1
    </select>

    <select id="getMailbyPhone" resultType="java.lang.String">
        Select mail from (
            Select mail ,CASE when CONCAT(phone,extension) like CONCAT('%',#{phone},#{extension},'%') and (#{phone} !='' and #{extension} !='') then '2'
            when mobilePhone like CONCAT('%',#{mobilePhone},'%') and #{mobilePhone} !='' then '1'
            end as sequeceNum
        from (
            Select TRIM(aj005) as mail ,replace(replace(replace(replace(replace(replace(replace(replace(replace(aj004, ' ', ''), '+', ''), ';', ''), '.', ''), 'ext', ''), '(', ''), ')', ''), '#', ''), '-', '') as phone,
            replace(replace(replace(replace(replace(replace(replace(replace(aj010, ' ', ''), '+', ''), ';', ''), '.', ''), '(', ''), ')', ''), '#', ''), '-', '') as extension,
            replace(replace(replace(replace(replace(replace(replace(replace(aj016, ' ', ''), '+', ''), ';', ''), '.', ''), '(', ''), ')', ''), '#', ''), '-', '') as mobilePhone
            from  seraj where aj001 = #{serviceCode}  and  (aj005 is not null and TRIM(aj005) REGEXP #{regexp})
        ) as K
        ) as  M where sequeceNum is not null
        order by sequeceNum
        limit 1
    </select>

    <select id="getMailbyPhoneFromMars_userpersonalinfo" resultType="java.lang.String">
        Select email from (
            Select email ,CASE when phone like CONCAT('%',#{phone},#{extension},'%') and (#{phone} !='' and #{extension} !='') then '2'
            when phone like CONCAT('%',#{mobilePhone},'%') and #{mobilePhone} !='' then '1'
			end as sequeceNum
        from (
            Select mup.email,
            replace(replace(replace(replace(replace(replace(replace(replace(replace(mup.phone, ' ', ''), '+', ''), ';', ''), '.', ''), 'ext', ''), '(', ''), ')', ''), '#', ''), '-', '') as phone
             from mars_user mu  join mars_userpersonalinfo mup on mu.ID = mup.userid where mu.CustomerServiceCode = #{serviceCode}
        ) as K
        ) as  M where sequeceNum is not null
        order by sequeceNum
        limit 1
    </select>

    <!-- 新增跟這個 ISSUE 有關的預警編號 -->
    <insert id="batchInsertWarningId">
        INSERT INTO issue_warningId_group(warningId, issueId, crmId)
        VALUES
        <foreach item="item" collection="list" separator=",">
            (#{item.warningId}, #{item.issueId}, #{item.crmId})
        </foreach>
    </insert>

    <!-- 新增跟這個 ISSUE 有關的项目任务 -->
    <insert id="batchInsertSourceMap">
        INSERT INTO issue_source_map (issueId, issueCode, sourceType, sourceId, rawId)
        VALUES
        <foreach collection="issueSourceMapList"  item="item" separator="," >
            (#{issueId}, #{issueCode}, #{item.sourceType}, #{item.sourceId}, #{item.rawId})
        </foreach>
    </insert>
    <update id="updateWarningIdGroup">
        UPDATE issue_warningId_group SET crmId = #{crmId} where issueId = #{issueId}
    </update>
    <update id="updateIssueSourceMap">
        UPDATE issue_source_map SET issueCode = #{crmId} where issueId = #{issueId}
    </update>

    <select id="getWarningIds" resultType="java.lang.String">
        SELECT warningId FROM issue_warningId_group
        where 1=1
        <if test="issueId != ''">
            and issueId = #{issueId}
        </if>
        <if test="crmId != ''">
            and crmId = #{crmId}
        </if>
    </select>

    <select id="getIssueSourceMapList" resultMap="issueselectmap">
        SELECT a.IssueId,ifnull(a.CrmId, '') as CrmId,a.ServiceCode,a.ProductCode,a.ProductVersion,mp.ProductCategory productCategory,
                a.UserId, a.CcUser,a.UserContactId,a.IssueDescription,a.IssueStack,a.SubmitWay,
                case when a.IssueStatus IN ( 'N','2','3','4','11','12','22','O','VN','Q','R') then 'N'
                     when a.IssueStatus  IN ('Y','7','10','A') then 'Y'
                     ELSE IssueStatus END IssueStatus,
                a.SubmitTime, ifnull(d.fullname, e.ServiceStaff) as ServiceStaff,
                f.`Name` as userContact_name, f.email as userContact_email, f.phone01 as userContact_phone01,
                f.phone02 as userContact_phone02, f.qq as userContact_qq, a.UserContactId as userContact_id, f.userId as userContact_userId,
                a.RequestPatchId, a.ProgramCode,a.ProgramVersion, a.IssueType, a.SyncStatus,ifnull(a.ServiceId, '') as ServiceId,
                a.planDownloadTime, a.planUpdateTime, a.serviceRegion as serviceRegion,a.machineRegion as machineRegion,a.site as site,a.environment as environment
        FROM issue a
        LEFT JOIN mars_userpersonalinfo b on b.userid = a.ServiceId
        LEFT JOIN mars_customerservicesatff d on d.workno = b.workno
        LEFT JOIN mars_customerservice e on e.CustomerServiceCode = a.ServiceCode and (ifnull(a.ProductCode,'') = '' or e.ProductCode = a.ProductCode)
        LEFT JOIN user_contacts f on f.Id = a.UserContactId
        LEFT JOIN mars_product mp ON mp.ProductCode = a.ProductCode
        LEFT JOIN issue_source_map ism ON ism.issueId = a.IssueId
        WHERE 1=1
        <if test="sourceType != '' and sourceType != null">
            and ism.sourceType = #{sourceType}
        </if>
        <if test="sourceId != '' and sourceId != null">
            AND ism.sourceId = #{sourceId}
        </if>
        <if test="userId != '' and userId != null">
            AND issue.userId = #{userId}
        </if>
        <if test='issuestatus == "N"'>
            AND issue.IssueStatus in ('I', 'C', 'N','2','3','4','11','12','22','8','O','VN','Q','R')
        </if>
        <if test='issuestatus == "Y"'>
            AND issue.IssueStatus in ('Y','7','10','A')
        </if>
        <if test='issuestatus!="N" and issuestatus!="Y" and issuestatus !=null and issuestatus != ""'>
            AND #{issuestatus} in (issue.IssueStatus, '')
        </if>
        ORDER by submitTime DESC
    </select>
    <select id="getIssueSourceMapCount" resultType="java.lang.Long">
        select COUNT(*)
        from issue as issue
        LEFT JOIN issue_source_map ism ON ism.issueId = issue.IssueId
        WHERE 1=1
        <if test="sourceType != '' and sourceType != null">
            and ism.sourceType = #{sourceType}
        </if>
        <if test="sourceId != '' and sourceId != null">
            AND ism.sourceId = #{sourceId}
        </if>
        <if test="userId != '' and userId != null">
            AND issue.userId = #{userId}
        </if>
        <if test='issuestatus == "N"'>
            AND issue.IssueStatus in ('I', 'C', 'N','2','3','4','11','12','22','8','O','VN','Q','R')
        </if>
        <if test='issuestatus == "Y"'>
            AND issue.IssueStatus in ('Y','7','10','A')
        </if>
        <if test='issuestatus!="N" and issuestatus!="Y" and issuestatus !=null and issuestatus != ""'>
            AND #{issuestatus} in (issue.IssueStatus, '')
        </if>
        ORDER by submitTime DESC
    </select>
    <select id="getIssueSum" resultType="com.digiwin.escloud.issueservice.model.IssueSum">
        SELECT sum(case when issue.IssueStatus not in ('V') then 1 else 0 end) as issuesCount ,
            sum(case when issue.IssueStatus not in ('Y','7','10','A','P','V') then 1 else 0 end) as processCount,
            sum(case when issue.IssueStatus in ('Y','7','10','A','P') then 1 else 0 end) as closeCount
        from issue issue
        WHERE issue.productCode in ('15','163','147')
        <!--<if test="serviceCode != '' and serviceCode != null">
            and issue.serviceCode = #{serviceCode}
        </if>-->
        <include refid="authCondition">
            <property name="Id" value="UserId"/>
            <property name="deptId" value="ServiceCode"/>
        </include>
    </select>
    <delete id="deleteProgress">
        delete from issue_progress where id = #{id}
    </delete>


    <update id="updateSyncStatus4Test">
        UPDATE issue i
            LEFT JOIN issue_casedetail ic on ic.IssueId=i.IssueId
            set i.SyncStatus='Z' , ic.SyncStatus='Z' where i.IssueId=#{issueId}
    </update>

    <update id="updateCaseDetailSyncStatus">
        UPDATE issue_casedetail ic
        set ic.SyncStatus='T' where ic.IssueId=#{issueId}
    </update>

    <select id="checkCrmCustomer" resultType="java.lang.Boolean">
        select case when a.contractSource = 1 then true else false end contractSource from mars_customerservice a where a.CustomerServiceCode = #{serviceCode} and a.productCode = #{productCode} limit 1
    </select>

    <select id="selectIssueListbyServiceCodeAndSearchKey" resultMap="issueselectmap">
        SELECT  issue.IssueId,ifnull(issue.CrmId, '') as CrmId,issue.ServiceCode,issue.ProductCode,issue.ProductVersion,
        issue.UserId,issue.UserContactId,issue.IssueDescription,issue.IssueStack,issue.SubmitWay,
        case when issue.IssueStatus IN ('N','2','3','4','11','12','22','8','O','VN','Q','R') then 'N'
        when issue.IssueStatus IN ('Y','7','10','A') then 'Y'
        else issue.IssueStatus END  IssueStatus,
        issue.SubmitTime,
        issue.ServiceId as  ServiceId,
        b.`name` as  ServiceStaff,
        f.`Name` as userContact_name, f.email as userContact_email, f.phone01 as userContact_phone01,
        f.phone02 as userContact_phone02, f.qq as userContact_qq, issue.UserContactId as userContact_id, f.userId as
        userContact_userId,
        issue.RequestPatchId, issue.ProgramCode,issue.ProgramVersion, issue.IssueType, issue.SyncStatus,
        issue.planDownloadTime, issue.planUpdateTime
        ,g.CustomerName,h.ProgramName as programName, product.ProductCategory as productCategory, issue.serviceRegion as serviceRegion, issue.machineRegion as machineRegion
        ,j.name as submitName

        FROM  issue
        LEFT JOIN mars_userpersonalinfo b ON b.userid = issue.ServiceId
        LEFT JOIN mars_product product ON issue.ProductCode = product.ProductCode
        LEFT JOIN user_contacts f on f.Id = issue.UserContactId
        LEFT JOIN mars_customer g on issue.ServiceCode = g.CustomerServiceCode
        LEFT JOIN erppatchs h on issue.RequestPatchId = h.PatchId
        LEFT JOIN mars_userpersonalinfo j on j.userid = issue.UserId
        LEFT JOIN (
        SELECT s.serviceCode,s.productCode,s.confirmIds,GROUP_CONCAT(info.NAME) confirmName
        FROM mars_servicesetting s
        LEFT JOIN mars_userpersonalinfo info ON FIND_IN_SET(info.userid,s.confirmIds )
        WHERE s.confirm='1'
        GROUP BY s.serviceCode,s.productCode
        ) confirm ON confirm.serviceCode= issue.ServiceCode AND confirm.productCode=issue.ProductCode
        WHERE 1=1 and issue.IssueStatus!='V'
        <if test="serviceCode !=''">
            AND issue.ServiceCode=#{serviceCode}
        </if>
        <if test="serviceRegion !=''">
            AND issue.serviceRegion=#{serviceRegion}
        </if>
        and issue.IssueStatus IN ('N','2','3','4','11','12','22','8','O','VN','Q','R')
        <if test="searchKey != null and searchKey != ''">
            and (
            issue.crmId  like CONCAT('%', #{searchKey},'%')
            )
        </if>
        ORDER BY  issue.SubmitTime DESC
        LIMIT #{start} , #{end}
    </select>

    <select id="selectIssueCountByServiceCodeAndSearchKey" resultMap="issuecountmap">
        SELECT count(*) as issuesCount ,
        sum(case when issue.IssueStatus = 'C' or issue.IssueStatus = 'I' then 1 else 0 end) as waitingIssuesCount,
        sum(case when issue.IssueStatus = '11' or issue.IssueStatus = 'R' then 1 else 0 end) as waitingSubmiterCheckCount,
        sum(case when issue.IssueStatus = 'T' then 1 else 0 end) as approvingIssuesCount,
        sum(case when issue.IssueStatus = 'D' then 1 else 0 end) as returnedIssuesCount,
        sum(case when issue.IssueStatus IN ( 'N','2','3','4','11','12','22','8','O','VN','Q','R') then 1 else 0 end) as processingIssuesCount,
        sum(case when issue.IssueStatus IN ( 'Y','7','10','A') then 1 else 0 end) as closedIssuesCount,
        sum(case when issue.IssueStatus = 'P' then 1 else 0 end) as evaluatedIssuesCount,
        sum(case when issue.IssueStatus = 'F' then 1 else 0 end) as invalidIssuesCount,
        sum(case when issue.newReply = 'serviceNewReply' then 1 else 0 end) as newReplyCount
        from issue as issue
        LEFT JOIN mars_userpersonalinfo b ON b.userid = issue.ServiceId
        LEFT JOIN mars_userpersonalinfo j on j.userid = issue.UserId
        WHERE 1=1 and issue.IssueStatus!='V'
        <if test="serviceCode !=''">
            AND issue.ServiceCode=#{serviceCode}
        </if>
        <if test="serviceRegion !=''">
            AND issue.serviceRegion=#{serviceRegion}
        </if>
        and issue.IssueStatus IN ('N','2','3','4','11','12','22','8','O','VN','Q','R')
        <if test="searchKey != null and searchKey != ''">
            and (
            issue.crmId  like CONCAT('%', #{searchKey},'%')
            )
        </if>
    </select>
    <update id="UpdateProcessSeq">
        UPDATE issue_progress
        SET crm_BR002 = #{crm_BR002},crm_BR003=#{crm_BR003}
        WHERE IssueId = #{issueId} and Id=#{Id}
    </update>

    <select id="getEdrEventIdByIssueId" resultType="java.lang.String">
        SELECT sourceId FROM issue_source_map
        where 1=1
        <if test="issueId != ''">
            and issueId = #{issueId}
        </if>
    </select>

    <select id="getEdrEventIdByCrmId" resultType="java.lang.String">
        SELECT sourceId FROM issue_source_map
        where 1=1
        <if test="crmId != ''">
            and issueCode = #{crmId}
        </if>
    </select>

    <select id="getServerId" resultType="java.lang.String">
        select serverId
        from `${aioDBName}`.edr_customer_org_map
        where serviceCode =#{serviceCode} and sid =#{sid}
        limit 1
    </select>

    <select id="getIdByEventId" resultType="java.lang.Long">
        select id
        from `${aioDBName}`.edr_event_kb
        where serverId= #{serverId} and sid =#{sid} and eventId=#{eventId}
        limit 1
    </select>
    <insert id="insertEventIssueStatus" keyProperty="id" keyColumn="id">
        insert into `${aioDBName}`.edr_event_kb(id,sid,serverId,eventId,status,issueCode)
        values(#{id}, #{sid}, #{serverId}, #{eventId}, #{status},#{crmId})
        ON DUPLICATE KEY UPDATE status=#{status},issueCode=#{crmId}
    </insert>
    <insert id="insertEventDetailIssueStatus" keyProperty="id" keyColumn="id">
        insert into `${aioDBName}`.edr_event_kb_detail(id,sid,serverId,eventId,status,issueCode)
        values(#{id}, #{sid}, #{serverId}, #{eventId}, #{status},#{crmId})

    </insert>
    <insert id="updateEventDetailIssueStatus" keyProperty="id" keyColumn="id">
        update `${aioDBName}`.edr_event_kb_detail
        set status=#{status}
        where issueCode=#{crmId}
    </insert>
    <insert id="insertIssueProgressForCC" useGeneratedKeys="true" parameterType="com.digiwin.escloud.issueservice.model.IssueProgress"
            keyProperty="id" keyColumn="Id">
        INSERT INTO issue_progress (IssueId,CrmId,SequenceNum,ProcessType,Processor,Description,ProcessTime,ReplyType,ProcessHours,SyncStatus,CurrentStatus,workno,handlerId)
        VALUES (#{issueId}, #{crmId},#{sequeceNum} , #{processType}, #{processor},#{description},#{processTime},#{replyType},#{processHours},#{syncStatus},#{CurrentStatus},#{workno},#{handlerId})
    </insert>
    <insert id="insertIssueKbshare">
        INSERT INTO issue_kbshare (IssueId,CrmId,ProductCode,SearchText,Kbid,ShareContent,ShareUrl,SubmitTime,FinishSearchChatFile,ChatFileContent,ChatFileSearchText,ChatFileErrorInfo,InvalidChatFileAnswer,ChatfileKnowledgeList,invalidChatFileKnowledgeNo,aiSource)
        VALUES (#{IssueId}, #{CrmId}, #{ProductCode}, #{SearchText}, #{Kbid},#{ShareContent},  #{ShareUrl},#{SubmitTime},#{FinishSearchChatFile},#{ChatFileContent},#{ChatFileSearchText},#{ChatFileErrorInfo},#{InvalidChatFileAnswer},#{ChatfileKnowledgeList},#{invalidChatFileKnowledgeNo},#{aiSource})
    </insert>
    <select id="selectNewIssueSourceMap" resultType="com.digiwin.escloud.issueservice.model.IssueSourceMap">
        SELECT *,p.productCategory,s.ErpSystemName erpSystemName FROM issue_source_map map
        left join mars_product p on p.productCode = map.productCode
        left join erpsystemcode s on s.ProductCode = map.productCode and s.ErpSystemCode = map.erpSystemCode
        where map.issueId=#{issueId} ORDER BY map.id DESC limit 1
    </select>

    <select id="issueFullContentSearch"  resultMap="issuefullcontentsearchmap">
        SELECT issue.IssueId,issue.CrmId,issue.ServiceCode,issue.ProductCode,issue.ProductVersion,
        issue.UserId,issue.UserContactId,issue.IssueDescription,issue.IssueStack,issue.SubmitWay,
        issue.IssueStatus,
        issue.SubmitTime,
        issue.ServiceId,
        issue.RequestPatchId, issue.ProgramCode,issue.ProgramVersion, issue.IssueType, issue.SyncStatus,
        issue.planDownloadTime, issue.planUpdateTime,
        issue.serviceRegion, issue.machineRegion

        FROM(
        SELECT  issue.IssueId,ifnull(issue.CrmId, '') as CrmId,issue.ServiceCode,issue.ProductCode,issue.ProductVersion,
        issue.UserId,issue.UserContactId,issue.IssueDescription,issue.IssueStack,issue.SubmitWay,
        case when issue.IssueStatus IN ('N','2','3','4','11','12','22','8','O','VN','Q','R') then 'N'
        when issue.IssueStatus IN ('Y','7','10','A') then 'Y'
        else issue.IssueStatus END  IssueStatus,
        issue.SubmitTime,
        issue.ServiceId as  ServiceId,
        issue.RequestPatchId, issue.ProgramCode,issue.ProgramVersion, issue.IssueType, issue.SyncStatus,
        issue.planDownloadTime, issue.planUpdateTime,
        issue.serviceRegion as serviceRegion, issue.machineRegion as machineRegion,
        ism.issueSourceType as ism_issueSourceType ,ism.sourceId as ism_sourceId
        FROM  issue
        INNER JOIN issue_source_map ism on issue.issueId = ism.issueId
        where  issue.UserId=#{userId}
        <if test="serviceCode != null and serviceCode != ''">
            and issue.ServiceCode=#{serviceCode}
        </if>
        and issue.ProductCode=#{productCode}) issue
        where IssueDescription  like CONCAT('%',#{msgContent},'%')
        <if test="localId != null and localId != ''">
            and ism_sourceId=#{localId}
        </if>
    </select>

    <select id="selectAthenaIssueSourceMap" resultType="com.digiwin.escloud.issueservice.model.IssueSourceMap">
        SELECT *,p.productCategory,s.ErpSystemName erpSystemName FROM issue_source_map map
        left join mars_product p on p.productCode = map.productCode
        left join erpsystemcode s on s.ProductCode = map.productCode and s.ErpSystemCode = map.erpSystemCode
        where map.issueId=#{issueId} and map.issueSourceType is null ORDER BY map.id DESC limit 1
    </select>
    <select id="getCustomerCodeByServiceCode" resultType="java.lang.String">
        select CustomerCode from mars_customer a where a.CustomerServiceCode = #{serviceCode} limit 1
    </select>
    <select id="checkAgent" resultType="java.lang.Integer">
        SELECT count(*) count
        FROM mars_customerservice a
        WHERE a.CustomerServiceCode =#{serviceCode}
        <if test="productCode != null and productCode != ''">
            AND a.ProductCode=#{productCode}
        </if>
        and DATE_FORMAT( a.contractExprityDate,'%Y-%m-%d')  >= DATE_FORMAT( NOW(),'%Y-%m-%d')
            AND exists ( SELECT 1 FROM agent_mark b WHERE b.productCode = a.productCode AND b.markCode = a.serviceUnitType )
    </select>

    <select id="selectAgentIssueSumarry" resultType="com.digiwin.escloud.issueservice.model.AgentIssueSum">
        SELECT COUNT(a.issueId) usedCount, c.agent_limit_issueCount agentLimitIssueCount
        FROM issue a
        LEFT JOIN issue_casedetail b ON a.issueId = b.issueId
        LEFT JOIN mars_customerservice c ON c.CustomerServiceCode = a.ServiceCode AND c.ProductCode = a.ProductCode
        LEFT JOIN agent_issueclassification d ON d.productCode = a.ProductCode AND d.issueclassificationCode = b.IssueClassification
        WHERE a.ServiceCode =#{serviceCode}
        <if test="productCode != null and productCode != ''">
            AND a.ProductCode=#{productCode}
        </if>
         AND d.issueclassificationCode = b.IssueClassification
         AND DATE_FORMAT( a.submitTime,'%Y-%m-%d') BETWEEN DATE_FORMAT( c.ContractStartDate,'%Y-%m-%d') AND DATE_FORMAT( c.contractExprityDate,'%Y-%m-%d')
         AND exists ( SELECT 1 FROM agent_mark e WHERE e.productCode = c.productCode AND e.markCode = c.serviceUnitType )

    </select>
    <select id="isSendMailByTypeId" resultType="java.lang.Boolean">
        SELECT sendMail
        FROM issue_self_update_type a
        WHERE a.id = #{typeId}
    </select>

    <select id="selectAgentNotifyEmail" resultType="java.lang.Integer">
        select count(*)
        from agent_notify_email
        where productCode =#{productCode} and serviceCode =#{serviceCode} and contractExprityDate =#{contractExprityDate}
    </select>
    <insert id="saveAgentNotifyEmail">
        insert into agent_notify_email(productCode, serviceCode, contractExprityDate)
        value (#{productCode},#{serviceCode},#{contractExprityDate})
    </insert>
    <update id="updateIssueProgressSyncStatus">
        update issue_progress a set a.SyncStatus = #{syncStatus} where a.IssueId = #{issueId}
    </update>


    <select id="getIssueCustomerInfo" resultType="com.digiwin.escloud.issueservice.model.CustomerServiceInfo">
        SELECT issue.ServiceCode as customerServiceCode,issue.ProductCode as productCode,mars_product.ProductCategory as productName,
               IFNULL(serae.GG004,mars_customer.CustomerName) as customerName
        from issue
                 left join mars_product on mars_product.ProductCode = issue.ProductCode
                 left join mars_customer on mars_customer.CustomerServiceCode = issue.ServiceCode
                 left join serae on serae.AE001 =  issue.ServiceCode
        where issue.issueId = #{issueId}
        limit 1
    </select>

    <select id="getCustomerServices" resultType="com.digiwin.escloud.issueservice.model.CustomerServiceInfo">
        SELECT mcs.customerServiceCode,mcs.ProductCode productCode,mcs.ContractState contractState
        from mars_customerservice mcs
        where mcs.CustomerServiceCode = #{customerServiceCode}
    </select>

    <select id="getIsSearchByChatFile" resultType="java.lang.Boolean">
        select enable from plugin_switch a where a.plugInName = #{switchName}  limit 1
    </select>

    <select id="getAcceptIssueProcessId" resultType="java.lang.Long">
        select Id from issue_progress a where a.CrmId = #{crmId} and (a.Description = '您的問題已受理' || a.Description = '您的问题已受理'  || a.Description='Accept Issue' || a.Description='คำถามของคุณได้รับการยอมรับแล้ว' ||  a.Description='Câu hỏi của bạn đã được chấp nhận' )  limit 1
    </select>

    <update id="updateAcceptIssueProgress">
        UPDATE issue_progress a
        SET  a.SyncStatus = #{SyncStatus} ,a.ProcessType=#{ProcessType},a.Processor=#{Processor},a.workno=#{workno},a.ReplyType=#{ReplyType}
        where a.CrmId = #{CrmId} and a.Id=#{issueProgressId}
    </update>
    <select id="getEventIssues" resultType="com.digiwin.escloud.issueservice.model.EventIssue" >
        select a.issueId,a.issueCode crmId,date_format(b.SubmitTime,'%Y-%m-%d %H:%i:%s') submitTime,a.rawId, b.issueStatus
        from issue_source_map a
        left join issue b on a.issueId = b.issueId
        where 1=1
        <if test="sourceId != null and sourceId != ''">
            and a.sourceId like CONCAT( #{sourceId},'%')
        </if>
        <if test="sourceType != null and sourceType != ''">
            and a.sourceType=#{sourceType}
        </if>
        order by b.SubmitTime desc
    </select>

    <select id="getEdrEventIssue" resultType="java.lang.Integer" >
        select count(*) count
        from issue_source_map a
        where a.rawId=#{rawId}
        <if test="sourceId != null and sourceId != ''">
            and a.sourceId like CONCAT( #{sourceId},'%')
        </if>
        <if test="sourceType != null and sourceType != ''">
            and a.sourceType=#{sourceType}
        </if>
    </select>


    <insert id="saveAuthorizedSerail">
        insert into shipmentinfo_serial(shipmentSid, serial)
        VALUES
        <foreach item="item" collection="serail" separator=",">
            (#{shipmentSid},#{item})
        </foreach>
    </insert>

    <update id="updateAuthorizedSerail">
        update shipmentinfo_serial set status = #{status} where shipmentSid = #{shipmentSid} and serial = #{serial}
    </update>
    <select id="getCountByCrmId" resultType="java.lang.Integer">
        select count(*) count from issue a where a.crmId = #{crmId}
    </select>

    <select id="selectUnresolvedIssueList" resultType="com.digiwin.escloud.issueservice.model.IssueUnresolvedCount" >
        SELECT count(*) as unresolvedCount, ProductCode
        FROM issue AS issue
        LEFT JOIN mars_user mu ON mu.ID = issue.userId
        WHERE ( (issue.ProductCode IN ('100','06','999','164','147')  AND ( (mu.UserType !=  '2' and issue.userId is not null and issue.userId != '')
            or (issue.userId is null or issue.userId = '') ) ) OR issue.ProductCode not IN ('100','06','999','164','147')) AND
            IssueStatus not in ('Y', '7', '10','P')
        <if test="productCode  !=null and productCode != ''">
            AND ProductCode IN (${productCode})
        </if>
        <if test="customerServiceCode  !=null and customerServiceCode != ''">
            AND ServiceCode = #{customerServiceCode}
        </if>
        <include refid="authCondition">
            <property name="Id" value="UserId"/>
            <property name="deptId" value="ServiceCode"/>
        </include>
        GROUP BY ProductCode;
    </select>

    <select id="selectIssueStatisticList" resultType="com.digiwin.escloud.issueservice.model.IssueCountStatistic" >
        SELECT count(di)  as issueCount, dateFormat FROM(
            SELECT DISTINCT issue.IssueId AS di,
            CASE
            WHEN DATEDIFF(#{endDate}, #{startDate}) >= 31
            THEN DATE_FORMAT(issue.SubmitTime, '%Y-%m')
            ELSE DATE_FORMAT(issue.SubmitTime, '%Y-%m-%d')
            END as dateFormat
            FROM issue AS issue
            LEFT JOIN mars_customerservice mc ON mc.CustomerServiceCode=issue.ServiceCode and mc.ProductCode = issue.ProductCode
            LEFT JOIN mars_user mu ON mu.ID = issue.userId
            WHERE ( (issue.ProductCode IN ('100','06','999','164','147')  AND ( (mu.UserType !=  '2' and issue.userId is not null and issue.userId != '')
                or (issue.userId is null or issue.userId = '') ) ) OR issue.ProductCode not IN ('100','06','999','164','147')) AND
                issue.SubmitTime BETWEEN #{startDate} AND #{endDate}
            <if test="productCode  !=null and productCode != ''">
                AND ProductCode IN (${productCode})
            </if>
            <if test="customerServiceCode  !=null and customerServiceCode != ''">
                AND ServiceCode = #{customerServiceCode}
            </if>
            <include refid="authCondition">
                <property name="Id" value="UserId"/>
                <property name="deptId" value="ServiceCode"/>
            </include>
        ) iss
        GROUP BY dateFormat;
    </select>

    <select id="selectIssueProductCodeStatisticList" resultType="com.digiwin.escloud.issueservice.model.IssueProductCodeCount" >
        SELECT count(*) as issueCount, productCode
        FROM issue
        LEFT JOIN mars_user mu ON mu.ID = issue.userId
        WHERE ( (issue.ProductCode IN ('100','06','999','164','147')  AND ( (mu.UserType !=  '2' and issue.userId is not null and issue.userId != '')
            or (issue.userId is null or issue.userId = '') ) ) OR issue.ProductCode not IN ('100','06','999','164','147')) AND
            SubmitTime BETWEEN #{startDate} AND #{endDate}
        <if test="productCode  !=null and productCode != ''">
            AND ProductCode IN (${productCode})
        </if>
        <if test="customerServiceCode  !=null and customerServiceCode != ''">
            AND ServiceCode = #{customerServiceCode}
        </if>
        <include refid="authCondition">
            <property name="Id" value="UserId"/>
            <property name="deptId" value="ServiceCode"/>
        </include>
        GROUP BY productCode;
    </select>

    <select id="selectIssueHasChatFileSearch" resultType="int">
        select count(*) from issue_chatfile_config
        where 1=1 and serviceRegion =#{serviceRegion} and productCode=#{productCode}  and  issueChatFileSearch = true
    </select>

    <select id="selectIssueById" resultType="java.lang.String">
        SELECT userId
        FROM issue
        WHERE 1 = 1
          AND issueId = #{issueId}
    </select>
    <update id="updateUserId">
        UPDATE issue
        SET userId = #{userId}
        WHERE issueId = #{issueId}
    </update>
    <insert id="insertIssueChangeSubmiterHistory">
        INSERT INTO issue_changesubmiterhistory (issueId, operatorId, beforeSubmiter, afterSubmiter, changeReson)
        VALUES (#{issueId}, #{operatorId}, #{beforeSubmiter}, #{afterSubmiter}, #{changeReson})
    </insert>

    <select id="selectIssueChangeSubmiterHistory" resultMap="issueChangeSubmiterHistoryUserpersonalinfo">
        SELECT ic.id, ic.issueId, ic.operatorId, m.Username as operatorEmail, mu_operatorId.name as operatorName,
               mu_operatorId.workno as operatorWorkno, ic.beforeSubmiter,
               mu_before.name as beforeSubmiterName, m_before.username as beforeEmail,
               ic.afterSubmiter, mu_after.name as afterSubmiterName, m_after.username as afterEmail,
               ic.changeReson, ic.changeTime
        FROM issue_changesubmiterhistory ic
                 LEFT JOIN mars_userpersonalinfo mu_operatorId ON ic.operatorId = mu_operatorId.userId
                 LEFT JOIN mars_userpersonalinfo mu_before ON ic.beforeSubmiter = mu_before.userId
                 LEFT JOIN mars_userpersonalinfo mu_after ON ic.afterSubmiter = mu_after.userId
                 LEFT JOIN mars_user m ON m.ID = ic.operatorId
                 LEFT JOIN mars_user m_before ON m_before.ID = ic.beforeSubmiter
                 LEFT JOIN mars_user m_after ON m_after.ID = ic.afterSubmiter
        WHERE ic.issueId = #{issueId}
        ORDER BY ic.changeTime desc
    </select>

    <select id="selectUserPersonalInfoByUserId" resultType="com.digiwin.escloud.issueservice.model.MarsUserPersonalInfo">
        SELECT mu.userId, mu.`name`, m.username as email
        FROM mars_userpersonalinfo mu
            LEFT JOIN mars_user_has_servicecode umhs ON mu.userId = umhs.userId
            LEFT JOIN mars_user m ON mu.userId = m.id AND m.id = umhs.userId
        WHERE umhs.customerServiceCode = #{customerServiceCode}
        union
        Select  mu.userId, mu.`name`, m.username as email
        FROM mars_userpersonalinfo mu
        LEFT JOIN mars_user m ON mu.userId = m.id
        WHERE m.customerServiceCode = #{customerServiceCode}
    </select>



</mapper>
