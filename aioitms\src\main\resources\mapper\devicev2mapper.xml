<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.digiwin.escloud.aioitms.device.dao.DeviceV2Mapper">
    <resultMap id="DeviceMap" type="com.digiwin.escloud.aioitms.model.device.AiopsKitDevice">
        <id column="id" property="id"/>
        <result column="eid" property="eid"/>
        <result column="deviceId" property="deviceId"/>
        <result column="deviceName" property="deviceName"/>
        <result column="platform" property="platform"/>
        <result column="ipAddress" property="ipAddress"/>
        <result column="placementPoint" property="placementPoint"/>
        <result column="remark" property="remark"/>
        <result column="aiopskitVersion" property="aiopskitVersion"/>
        <result column="escliVersion" property="escliVersion"/>
        <result column="registerTime" property="registerTime"/>
        <result column="lastCheckInTime" property="lastCheckInTime"/>
        <result column="isDeleted" property="isDeleted"/>

        <collection property="deviceTypeMappingList" columnPrefix="adtm_" resultMap="DeviceTypeMappingMap"/>
    </resultMap>
    <resultMap id="DeviceTypeMappingMap" type="com.digiwin.escloud.aioitms.model.device.AiopsKitDeviceTypeMapping">
        <id column="id" property="id"/>
        <result column="adId" property="adId"/>
        <result column="deviceId" property="deviceId"/>
        <result column="deviceType" property="deviceType"/>
        <result column="deviceTypeName" property="deviceTypeName"/>
        <result column="deviceTypeName_CN" property="deviceTypeName_CN"/>
        <result column="deviceTypeName_TW" property="deviceTypeName_TW"/>
    </resultMap>

    <resultMap id="BaseDeviceInfoMap" type="com.digiwin.escloud.aioitms.device.model.DeviceInfo">
        <id property="id" column="id"/>
        <result property="eid" column="eid"/>
        <result property="deviceId" column="deviceId"/>
        <result property="deviceName" column="deviceName"/>
        <result property="platform" column="platform"/>
        <result property="ipAddress" column="ipAddress"/>
        <result property="placementPoint" column="placementPoint"/>
        <result property="remark" column="remark"/>
        <result property="aiopskitVersion" column="aiopskitVersion"/>
        <result property="escliVersion" column="escliVersion"/>
        <result property="registerTime" column="registerTime"/>
        <result property="lastCheckInTime" column="lastCheckInTime"/>
        <result property="isDeleted" column="isDeleted"/>
        <result property="isOnLine" column="isOnLine"/>
        <result property="isZJZ" column="isZJZ"/>
        <result property="lastCheckInTimeDifferSecond" column="lastCheckInTimeDifferSecond"/>
        <result property="aiopsAuthStatus" column="aiopsAuthStatus"/>

        <collection property="deviceTypeMappingList" column="{id=id,deviceId=deviceId}" javaType="ArrayList"
                    ofType="com.digiwin.escloud.aioitms.model.device.AiopsKitDeviceTypeMapping" select="getDeviceType"/>
    </resultMap>
    <resultMap id="DeviceResultMap" extends="BaseDeviceInfoMap"
               type="com.digiwin.escloud.aioitms.device.model.DeviceInfo">

        <collection property="supplierProductList" column="id" javaType="ArrayList"
                    ofType="com.digiwin.escloud.aiouser.model.supplier.SupplierProduct" select="getDeviceProductV2"/>
    </resultMap>
    <resultMap id="DeviceDetailResultMap" extends="BaseDeviceInfoMap"
               type="com.digiwin.escloud.aioitms.device.model.DeviceInfo">

        <result property="tenantName" column="tenantName"/>
        <result property="serviceCode" column="serviceCode"/>
    </resultMap>
    <resultMap id="DeviceInfoContainAdcdListMap" extends="BaseDeviceInfoMap"
               type="com.digiwin.escloud.aioitms.device.model.DeviceInfo">
        <collection property="deviceCollectDetailList" columnPrefix="adcd_" resultMap="baseDeviceCollectDetailMap"/>
    </resultMap>

    <resultMap id="deviceTypeMap" type="com.digiwin.escloud.aioitms.model.device.AiopsKitDeviceTypeMapping">
        <id property="id" column="id"/>
        <result property="adId" column="adId"/>
        <result property="deviceId" column="deviceId"/>
        <result property="deviceType" column="deviceType"/>
        <result property="deviceTypeName" column="deviceTypeName"/>
    </resultMap>
    <resultMap id="supplierProductMap" type="com.digiwin.escloud.aioitms.device.model.SupplierProductApp">
        <id property="id" column="id"/>
        <result property="sid" column="sid"/>
        <result property="productCode" column="productCode"/>
        <result property="productCategory" column="productCategory"/>
        <result property="productType" column="productType"/>
        <result property="productShortName" column="productShortName"/>
        <result property="productName" column="productName"/>
        <result property="indexFolderName" column="indexFolderName"/>
        <result property="hasRobotTextService" column="hasRobotTextService"/>
        <result property="eLearningName" column="eLearningName"/>
        <result property="repeaterArea" column="repeaterArea"/>
        <result property="productShortNameCN" column="productShortNameCN"/>
        <result property="productShortNameTW" column="productShortNameTW"/>
        <result property="productShortNameUS" column="productShortNameUS"/>
        <result property="productShortNameVN" column="productShortNameVN"/>
        <result property="productShortNameTH" column="productShortNameTH"/>
        <result property="productVersion" column="productVersion"/>
        <collection property="productAppList" resultMap="productAppMap"/>
    </resultMap>
    <resultMap id="productAppMap" type="com.digiwin.escloud.aioitms.collectapp.model.ProductApp">
        <id property="modelCode" column="modelCode"/>
        <result property="modelName" column="modelName"/>
    </resultMap>
    <resultMap id="baseDeviceCollectDetailMap" type="com.digiwin.escloud.aioitms.device.model.DeviceCollectDetail">
        <id property="id" column="id"/>
        <result property="adcId" column="adcId"/>
        <result property="accId" column="accId"/>
        <result property="execParamsContent" column="execParamsContent"/>
        <result property="execParamsVersion" column="execParamsVersion"/>
        <result property="collectName" column="collectName"/>
        <result property="aiId" column="aiId"/>
        <result property="adimId" column="adimId"/>

        <result property="defaultCollectName" column="defaultCollectName"/>
        <result property="uploadDataModelCode" column="uploadDataModelCode"/>
        <result property="execParamsModelCode" column="execParamsModelCode"/>
        <result property="isEnable" column="isEnable"/>
        <result property="interval" column="interval"/>
        <result property="collectorModelCode" column="collectorModelCode"/>
        <result property="collectType" column="collectType"/>
        <result property="cron" column="cron"/>
        <result property="scopeId" column="scopeId"/>
        <result property="runtimeVersion" column="runtimeVersion"/>

        <result property="findDisplayNameByModel" column="findDisplayNameByModel"/>
        <result property="aiopsInstanceName" column="aiopsInstanceName"/>
        <result property="aiopsItem" column="aiopsItem"/>
        <result property="aiopsItemId" column="aiopsItemId"/>
        <result property="aiopsItemName" column="aiopsItemName"/>
        <result property="aiopsItemName_CN" column="aiopsItemName_CN"/>
        <result property="aiopsItemName_TW" column="aiopsItemName_TW"/>
        <result property="isValid" column="isValid"/>

        <result property="oriAiopsItem" column="oriAiopsItem"/>
        <result property="oriAiopsItemId" column="oriAiopsItemId"/>

        <result property="deviceId" column="deviceId"/>
    </resultMap>

    <resultMap id="deviceCollectDetailMap" extends="baseDeviceCollectDetailMap"
               type="com.digiwin.escloud.aioitms.device.model.DeviceCollectDetail">
        <collection property="collectWarningList" column="{accId=accId,adcId=adcId,adcdId=id}" javaType="ArrayList"
                    ofType="com.digiwin.escloud.aioitms.collectwarning.model.CollectWarning" select="getProductCollectWarningListNew"/>
    </resultMap>

    <resultMap id="deviceCollectDetailOneLevelWarningMap" extends="baseDeviceCollectDetailMap"
               type="com.digiwin.escloud.aioitms.device.model.DeviceCollectDetail">
        <collection property="collectWarningList" columnPrefix="acw_" resultMap="baseCollectWarningMap"/>
        <collection property="deviceCollectWarningList" columnPrefix="adcw_" resultMap="deviceCollectWarningMap"/>
    </resultMap>

    <resultMap id="baseDeviceDetailNewResultMap" type="com.digiwin.escloud.aioitms.device.model.DeviceInfoNew">
        <id property="id" column="id"/>
        <result property="eid" column="eid"/>
        <result property="deviceId" column="deviceId"/>
        <result property="deviceName" column="deviceName"/>
        <result property="platform" column="platform"/>
        <result property="ipAddress" column="ipAddress"/>
        <result property="placementPoint" column="placementPoint"/>
        <result property="remark" column="remark"/>
        <result property="registerTime" column="registerTime"/>
        <result property="lastCheckInTime" column="lastCheckInTime"/>
        <result property="isDeleted" column="isDeleted"/>
        <result property="accId" column="accId"/>
        <result property="adcId" column="adcId"/>
        <result property="adcdId" column="adcdId"/>
        <result property="collectCategory" column="collectCategory"/>
        <result property="collectCategoryName" column="collectCategoryName"/>
        <result property="tenantName" column="tenantName"/>
        <result property="findDisplayNameByModel" column="findDisplayNameByModel"/>
        <result property="aiId" column="aiId"/>
        <result property="aiopsItem" column="aiopsItem"/>
        <result property="aiopsItemName" column="aiopsItemName"/>
        <result property="aiopsItemType" column="aiopsItemType"/>
        <result property="aiopsItemTypeName" column="aiopsItemTypeName"/>
        <result property="aiopsInstanceName" column="aiopsInstanceName"/>
    </resultMap>

    <resultMap id="DeviceDetailNewResultMap" extends="baseDeviceDetailNewResultMap"
               type="com.digiwin.escloud.aioitms.device.model.DeviceInfoNew">
        <collection property="deviceTypeMappingList" column="{id=id,deviceId=deviceId}" javaType="ArrayList"
                    ofType="com.digiwin.escloud.aioitms.model.device.AiopsKitDeviceTypeMapping" select="getDeviceType"/>
        <collection property="supplierProductList" column="id" javaType="ArrayList"
                    ofType="com.digiwin.escloud.aiouser.model.supplier.SupplierProduct" select="getDeviceProduct"/>
        <collection property="warningList" column="{accId=accId,adcId=adcId,adcdId=adcdId" javaType="ArrayList"
                    ofType="com.digiwin.escloud.aioitms.collectwarning.model.CollectWarning"
                    select="getProductCollectWarningListNew"/>
    </resultMap>

    <resultMap id="onlyDeviceDetailNewResultMap" extends="baseDeviceDetailNewResultMap"
               type="com.digiwin.escloud.aioitms.device.model.DeviceInfoNew">
        <result property="authStatus" column="authStatus"/>

        <collection property="deviceTypeMappingList" column="{id=adId,deviceId=deviceId}" javaType="ArrayList"
                    ofType="com.digiwin.escloud.aioitms.model.device.AiopsKitDeviceTypeMapping" select="getDeviceType"/>
    </resultMap>

    <resultMap id="baseCollectWarningMap" type="com.digiwin.escloud.aioitms.collectwarning.model.CollectWarning">
        <id property="id" column="id"/>
        <result property="accId" column="accId"/>
        <result property="scopeId" column="scopeId"/>
        <result property="warningCode" column="warningCode"/>
        <result property="warningName" column="warningName"/>
        <result property="momentType" column="momentType"/>
        <result property="description" column="description"/>
        <result property="isWarningEnable" column="isWarningEnable"/>
        <result property="continueStopSettingValue" column="continueStopSettingValue"/>
        <result property="continueStopSettingUnit" column="continueStopSettingUnit"/>
        <result property="sourceAcwId" column="sourceAcwId"/>
        <result property="adcwId" column="adcwId"/>
        <association property="deviceWarningNotify" resultMap="deviceWarningNotifyMap" columnPrefix="adwn_"/>
        <collection property="warningSettingList" resultMap="warningSettingMap" columnPrefix="aws_"/>
    </resultMap>
    <resultMap id="deviceWarningNotifyMap" type="com.digiwin.escloud.aioitms.device.model.DeviceWarningNotify">
        <!--因为DeviceWarningNotify是虚结构，没有对应的表，如果没有指定Id，mybatis无法正常区隔多笔纪录，因此这里需要添加id，但不需要对应的字段接-->
        <id column="id"/>
        <collection property="deviceWarningNotifyMappingByGroupList" columnPrefix="mg_" resultMap="deviceWarningNotifyMappingMap"/>
        <collection property="deviceWarningNotifyMappingByUserList" columnPrefix="mu_" resultMap="deviceWarningNotifyMappingMap"/>
        <collection property="deviceWarningNotifyMappingByAll" columnPrefix="ma_" resultMap="deviceWarningNotifyMappingMap"/>
    </resultMap>
    <resultMap id="deviceWarningNotifyMappingMap" type="com.digiwin.escloud.aioitms.device.model.DeviceWarningNotifyMapping">
        <id property="id" column="id"/>
        <result property="adcwId" column="adcwId"/>
        <result property="notifyCategory" column="notifyCategory"/>
        <result property="sourceId" column="sourceId"/>
        <result property="isDefault" column="isDefault"/>
        <result property="name" column="name"/>
        <result property="telephone" column="telephone"/>
    </resultMap>
    <resultMap id="warningSettingMap" type="com.digiwin.escloud.aioitms.collectwarning.model.WarningSetting">
        <id property="id" column="id"/>
        <result property="acwId" column="acwId"/>
        <result property="levelCode" column="levelCode"/>
        <result property="isInterval" column="isInterval"/>
        <result property="notUploadWarning" column="notUploadWarning"/>
        <result property="conditionExpression" column="conditionExpression"/>
        <result property="intervalValue" column="intervalValue"/>
        <result property="timingTime" column="timingTime"/>
        <result property="schedulerUnit" column="schedulerUnit"/>
        <result property="sinkName" column="sinkName"/>
        <result property="groupFields" column="groupFields"/>
        <result property="offlineSchedulerId" column="offlineSchedulerId"/>
        <result property="offlineUpdateStatus" column="offlineUpdateStatus"/>
        <result property="offlineUpdateTime" column="offlineUpdateTime"/>
        <result property="__version__" column="__version__"/>
        <collection property="warningAnalysisList" resultMap="warningAnalysisMap" columnPrefix="awa_"/>
        <collection property="warningConditionList" resultMap="warningConditionMap" columnPrefix="awc_"/>
        <collection property="warningNotifyTemplateList" resultMap="warningNotifyTemplateMap" columnPrefix="awnt_"/>
    </resultMap>
    <resultMap id="warningAnalysisMap" type="com.digiwin.escloud.aioitms.collectwarning.model.WarningAnalysis">
        <id property="id" column="id"/>
        <result property="awsId" column="awsId"/>
        <result property="analysisWay" column="analysisWay"/>
        <result property="value" column="value"/>
        <result property="unit" column="unit"/>
        <result property="hitCondition" column="hitCondition"/>
        <result property="hitValue" column="hitValue"/>
        <result property="__version__" column="__version__"/>
    </resultMap>
    <resultMap id="warningConditionMap" type="com.digiwin.escloud.aioitms.collectwarning.model.WarningCondition">
        <id property="id" column="id"/>
        <result property="awsId" column="awsId"/>
        <result property="modelField" column="modelField"/>
        <result property="each" column="each"/>
        <result property="eachUnit" column="eachUnit"/>
        <result property="statisticType" column="statisticType"/>
        <result property="operator" column="operator"/>
        <result property="abnormalValue" column="abnormalValue"/>
        <result property="logic" column="logic"/>
        <result property="sequence" column="sequence"/>
        <result property="__version__" column="__version__"/>
    </resultMap>
    <resultMap id="warningNotifyTemplateMap" type="com.digiwin.escloud.aioitms.collectwarning.model.WarningNotifyTemplate">
        <id property="id" column="id"/>
        <result property="awsId" column="awsId"/>
        <result property="templateType" column="templateType"/>
        <result property="title" column="title"/>
        <result property="content" column="content"/>
        <result property="gridContent" column="gridContent"/>
        <result property="suggest" column="suggest"/>
        <result property="helpUrl" column="helpUrl"/>
        <result property="__version__" column="__version__"/>
    </resultMap>

    <resultMap id="deviceCollectWarningMap" type="com.digiwin.escloud.aioitms.device.model.DeviceCollectWarning">
        <id property="id" column="id"/>
        <result property="adcdId" column="adcdId"/>
        <result property="acwId" column="acwId"/>
        <result property="isWarningEnable" column="isWarningEnable"/>
    </resultMap>

    <resultMap id="deviceTypeMapNew" type="com.digiwin.escloud.aioitms.device.model.DeviceType">
        <id property="id" column="id"/>
        <result property="deviceType" column="deviceType"/>
        <result property="deviceTypeName" column="deviceTypeName"/>
        <collection property="collectConfigList" resultMap="collectConfigMap" columnPrefix="cc_"/>
    </resultMap>
    <resultMap id="collectConfigMap" type="com.digiwin.escloud.aioitms.collectconfig.model.CollectConfig">
        <id property="id" column="id"/>
        <result property="scopeId" column="scopeId"/>
        <result property="collectCode" column="collectCode"/>
        <result property="collectName" column="collectName"/>
        <result property="collectCategory" column="collectCategory"/>
        <result property="collectDeviceType" column="collectDeviceType"/>
    </resultMap>

    <resultMap id="deviceTypeOnlineStatisticMap" type="com.digiwin.escloud.aioitms.device.model.DeviceTypeOnlineStatistic">
        <id property="deviceType" column="deviceType"/>
        <result property="total" column="total"/>
        <result property="totalOnline" column="totalOnline"/>
    </resultMap>

    <resultMap id="baseModuleCollectLayeredMap"
               type="com.digiwin.escloud.aioitms.modulecollect.model.ModuleCollectLayered">
        <result column="samclId" property="samclId"/>
    </resultMap>

    <resultMap id="moduleCollectLayeredMap" extends="baseModuleCollectLayeredMap"
               type="com.digiwin.escloud.aioitms.modulecollect.model.ModuleCollectLayered">

        <collection property="moduleCollectMappingList" columnPrefix="mcm_"
                    resultMap="moduleCollectMappingMap"/>
    </resultMap>

    <resultMap id="moduleCollectLayeredByDeviceMap" extends="baseModuleCollectLayeredMap"
               type="com.digiwin.escloud.aioitms.modulecollect.model.ModuleCollectLayered">

        <collection property="moduleCollectMappingList" columnPrefix="mcm_"
                    resultMap="moduleCollectMappingByDeviceMap"/>
    </resultMap>

    <resultMap id="baseModuleCollectMappingMap"
               type="com.digiwin.escloud.aioitms.modulecollect.model.ModuleCollectMapping">
        <id column="id" property="id"/>
        <result column="samcdId" property="samcdId"/>
        <result column="samclId" property="samclId"/>
        <result property="adcId" column="adcId"/>
        <result property="adcdId" column="adcdId"/>
        <result property="accId" column="accId"/>
        <result property="collectName" column="collectName"/>
        <result property="isEnable" column="isEnable"/>
        <result property="isMappedToDevice" column="isMappedToDevice"/>
        <result property="execParamsContent" column="execParamsContent"/>
        <result property="execParamsModelCode" column="execParamsModelCode"/>
        <result property="uploadDataModelCode" column="uploadDataModelCode"/>
        <result property="scopeId" column="scopeId"/>
        <result property="interval" column="interval"/>
        <result property="collectType" column="collectType"/>
        <result property="collectorModelCode" column="collectorModelCode"/>
        <result property="cron" column="cron"/>
        <result property="isValid" column="isValid"/>
        <result property="sourceAccId" column="sourceAccId"/>
        <result property="runtimeVersion" column="runtimeVersion"/>

        <result property="aiopsItem" column="aiopsItem"/>
        <result property="aiopsItemId" column="aiopsItemId"/>
        <result property="oriAiopsItem" column="oriAiopsItem"/>
        <result property="oriAiopsItemId" column="oriAiopsItemId"/>

        <result property="isCustomizableExecParam" column="isCustomizableExecParam"/>
    </resultMap>

    <resultMap id="moduleCollectMappingMap" extends="baseModuleCollectMappingMap"
               type="com.digiwin.escloud.aioitms.modulecollect.model.ModuleCollectMapping">

        <collection property="collectWarningList" column="{accId=accId,scopeId=scopeId}" javaType="ArrayList"
                    ofType="com.digiwin.escloud.aioitms.collectwarning.model.CollectWarning"
                    select="getModuleCollectWarningList"/>
    </resultMap>

    <resultMap id="moduleCollectMappingByDeviceMap" extends="baseModuleCollectMappingMap"
               type="com.digiwin.escloud.aioitms.modulecollect.model.ModuleCollectMapping">

        <collection property="collectWarningList" column="{accId=accId,adcId=adcId,adcdId=adcdId}" javaType="ArrayList"
                    ofType="com.digiwin.escloud.aioitms.collectwarning.model.CollectWarning"
                    select="getProductCollectWarningListNew"/>
    </resultMap>

    <resultMap id="deviceAiopsItemInstance" type="com.digiwin.escloud.aioitms.device.model.DeviceAiopsItemInstance">
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="name_CN" column="name_CN"/>
        <result property="name_TW" column="name_TW"/>

        <collection property="deviceAiopsInstanceList" columnPrefix="dai_" resultMap="deviceAiopsInstance"/>
    </resultMap>

    <resultMap id="deviceAiopsInstance" type="com.digiwin.escloud.aioitms.device.model.DeviceAiopsInstance">
        <result property="eid" column="eid"/>
        <result property="tmcdId" column="tmcdId"/>
        <result property="samcdId" column="samcdId"/>
        <result property="aiopsItemGroup" column="aiopsItemGroup"/>
        <result property="aiopsItemType" column="aiopsItemType"/>
        <result property="aiopsItem" column="aiopsItem"/>
        <result property="aiopsItemId" column="aiopsItemId"/>
        <result property="aiopsAuthStatus" column="aiopsAuthStatus"/>
        <result property="displayName" column="displayName"/>

        <result property="findDisplayNameByModel" column="findDisplayNameByModel"/>

        <result property="execParamsModelCode" column="execParamsModelCode"/>
    </resultMap>

    <resultMap id="deviceDataSourceGroupMap" type="com.digiwin.escloud.aioitms.device.model.DeviceDataSourceInfo">
        <id column="aiopsItem" property="aiopsItem"/>
        <result column="name" property="name"/>
        <result column="name_CN" property="name_CN"/>
        <result column="name_TW" property="name_TW"/>

        <collection property="deviceDataSourceList" columnPrefix="add_" resultMap="aiopsKitDeviceDataSourceMap"/>
    </resultMap>

    <resultMap id="aiopsKitDeviceDataSourceMap"
               type="com.digiwin.escloud.aioitms.device.model.AiopsKitDeviceDataSource">
        <id column="id" property="id"/>
        <result column="adId" property="adId"/>
        <result column="deviceId" property="deviceId"/>
        <result column="deviceName" property="deviceName"/>
        <result column="ipAddress" property="ipAddress"/>
        <result column="dbId" property="dbId"/>
        <result column="dbType" property="dbType"/>
        <result column="dbDisplayName" property="dbDisplayName"/>
        <result column="dbIpAddress" property="dbIpAddress"/>
        <result column="dbPort" property="dbPort"/>
        <result column="dbServerName" property="dbServerName"/>
        <result column="dbInstanceName" property="dbInstanceName"/>
        <result column="platform" property="platform"/>
        <result column="isDelete" property="isDelete"/>

        <result column="displayName" property="displayName"/>
        <result column="eid" property="eid"/>
        <result column="aiopsAuthStatus" property="aiopsAuthStatus"/>
    </resultMap>

    <resultMap id="groupAdcdExecParamMap" type="com.digiwin.escloud.aioitms.device.model.GroupAdcdExecParam">
        <id column="aiopsItemParamGroup" property="aiopsItemParamGroup"/>
        <id column="execParamsModelCode" property="execParamsModelCode"/>
        <id column="aiId" property="aiId"/>
        <result column="samcdId" property="samcdId"/>
        <result column="samclId" property="samclId"/>
        <result column="aiopsItemParamGroupName" property="aiopsItemParamGroupName"/>
        <result column="aiopsItemParamGroupName_CN" property="aiopsItemParamGroupName_CN"/>
        <result column="aiopsItemParamGroupName_TW" property="aiopsItemParamGroupName_TW"/>
        <result column="aiopsItemId" property="aiopsItemId"/>
        <result column="aiopsItem" property="aiopsItem"/>
        <result column="aiopsItemType" property="aiopsItemType"/>
        <result column="adimId" property="adimId"/>

        <result property="findDisplayNameByModel" column="findDisplayNameByModel"/>
        <result property="aiopsInstanceName" column="aiopsInstanceName"/>

        <collection property="groupAdcdExecParamDetailList" columnPrefix="adcd_"
                    resultMap="groupAdcdExecParamDetailMap"/>
    </resultMap>

    <resultMap id="groupAdcdExecParamDetailMap"
               type="com.digiwin.escloud.aioitms.device.model.GroupAdcdExecParam$GroupAdcdExecParamDetail">
        <id column="adcdId" property="adcdId"/>
        <id column="accId" property="accId"/>
        <result column="collectName" property="collectName"/>
        <result column="isEnable" property="isEnable"/>
        <result column="execParamsContent" property="execParamsContent"/>
        <result column="execParamsVersion" property="execParamsVersion"/>
    </resultMap>

    <select id="selectDeviceByDeviceId" resultMap="DeviceMap">
        SELECT ad.*,
               adtm.id AS adtm_id, adtm.adId AS adtm_adId,
               adtm.deviceId AS adtm_deviceId, adtm.deviceType AS adtm_deviceType,
               adt.name AS adtm_deviceTypeName, adt.name_CN AS adtm_deviceTypeName_CN,
               adt.name_TW AS adtm_deviceTypeName_TW
        FROM aiops_device ad
        LEFT JOIN aiops_device_type_mapping adtm ON ad.id = adtm.adId
        LEFT JOIN aiops_device_type adt ON adtm.deviceType = adt.code
        WHERE ad.deviceId = #{deviceId}
    </select>

    <insert id="insertDevice" parameterType="com.digiwin.escloud.aioitms.model.device.AiopsKitDevice">
        INSERT INTO aiops_device (id, eid, deviceId, deviceName, platform, ipAddress,
                                 placementPoint, remark, customerNotes, aiopskitVersion, escliVersion, lastCheckInTime)
        VALUES (#{id}, #{eid}, #{deviceId}, #{deviceName}, #{platform}, #{ipAddress},
                #{placementPoint}, #{remark}, #{customerNotes}, #{aiopskitVersion}, #{escliVersion}, NOW())
    </insert>

    <insert id="insertDevicePart" parameterType="com.digiwin.escloud.aioitms.model.device.AiopsKitDevice">
        INSERT INTO aiops_device (id, deviceId, lastCheckInTime)
        VALUES (#{id}, #{deviceId}, NOW())
    </insert>

    <update id="updateDevice" parameterType="com.digiwin.escloud.aioitms.model.device.AiopsKitDevice">
        UPDATE aiops_device
        <set>
            <if test="placementPoint != null">
                , placementPoint = #{placementPoint}
            </if>
            <if test="remark != null">
                , remark = #{remark}
            </if>
            <if test="customerNotes != null">
                , customerNotes = #{customerNotes}
            </if>
            <if test="deviceChangeSource == null or !'BY_CLOUD'.equals(deviceChangeSource.name())">
                <if test="eid != null and eid != 0">
                    , eid = #{eid}
                </if>
                <if test="deviceName != null and deviceName != ''">
                    , deviceName = #{deviceName}
                </if>
                <if test="platform != null">
                    , platform = #{platform}
                </if>
                <if test="ipAddress != null and ipAddress != ''">
                    , ipAddress = #{ipAddress}
                </if>
                <if test="aiopskitVersion != null and aiopskitVersion != ''">
                    , aiopskitVersion = #{aiopskitVersion}
                </if>
                <if test="escliVersion != null and escliVersion != ''">
                    , escliVersion = #{escliVersion}
                </if>
                , lastCheckInTime = NOW()
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <select id="selectDeviceTypeMappingByDeviceId" resultMap="DeviceTypeMappingMap">
        SELECT *
        FROM aiops_device_type_mapping
        WHERE deviceId = #{deviceId}
    </select>

    <delete id="batchDeleteDeviceTypeMappingById">
        DELETE FROM aiops_device_type_mapping
        WHERE id IN
        <if test="ids != null">
            <foreach collection="ids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </delete>

    <insert id="batchInsertDeviceTypeMapping">
        INSERT INTO aiops_device_type_mapping (id, adId, deviceId, deviceType)
        <if test="deviceTypeMappingList != null">
            <foreach collection="deviceTypeMappingList" item="item" open=" VALUES (" separator="), (" close=")">
                #{item.id}, #{item.adId}, #{item.deviceId}, #{item.deviceType}
            </foreach>
        </if>
    </insert>

    <select id="selectAdIdByDeviceId" resultType="java.lang.Long">
        SELECT id
        FROM aiops_device
        WHERE deviceId = #{deviceId}
    </select>

    <select id="selectDeviceIdByAdId" resultType="java.lang.String">
        SELECT deviceId
        FROM aiops_device
        WHERE id = #{adId}
    </select>

    <select id="selectDeviceAgreementByDeviceId" resultType="com.digiwin.escloud.aioitms.device.model.AiopsKitDeviceAgreement">
        SELECT *
        FROM aiops_device_agreement
        WHERE deviceId = #{deviceId}
    </select>

    <insert id="insertDeviceAgreement" parameterType="com.digiwin.escloud.aioitms.device.model.AiopsKitDeviceAgreement">
        INSERT INTO aiops_device_agreement (id, adId, deviceId, isAgreeDigiwinAiops)
        VALUES (#{id}, #{adId}, #{deviceId}, #{isAgreeDigiwinAiops})
    </insert>

    <update id="updateDeviceAgreement" parameterType="com.digiwin.escloud.aioitms.device.model.AiopsKitDeviceAgreement">
        UPDATE aiops_device_agreement
        SET isAgreeDigiwinAiops = #{isAgreeDigiwinAiops}
        WHERE id = #{id}
    </update>

    <insert id="insertOrUpdateDeviceCustomerInfo" parameterType="com.digiwin.escloud.aioitms.model.device.AiopsKitDevice">
        INSERT INTO aiops_device (id, eid, deviceId, lastCheckInTime)
        VALUES (#{id}, #{eid}, #{deviceId}, NOW())
        ON DUPLICATE KEY UPDATE eid = VALUES(eid), lastCheckInTime = VALUES(lastCheckInTime)
    </insert>

    <select id="selectDeviceDataSourceByDeviceId" resultType="com.digiwin.escloud.aioitms.device.model.AiopsKitDeviceDataSource">
        SELECT *
        FROM aiops_device_datasource
        WHERE deviceId = #{deviceId}
    </select>

    <select id="selectDeviceDataSourceByDeviceIdAndDbId" resultType="com.digiwin.escloud.aioitms.device.model.AiopsKitDeviceDataSource">
        SELECT *
        FROM aiops_device_datasource
        WHERE deviceId = #{deviceId} AND dbId = #{dbId}
    </select>

    <update id="updateDeviceDataSourceIsDeleteByDeviceId">
        UPDATE aiops_device_datasource
        SET isDelete = #{isDelete}
        WHERE deviceId = #{deviceId}
        <if test="dbId != null and dbId != ''">
            AND dbId = #{dbId}
        </if>
    </update>


    <update id="updateDeviceDataSourceByDbId">
        UPDATE aiops_device_datasource
        SET dbHostDevice = 0
        WHERE dbId = #{dbId}
    </update>

    <update id="batchUpdateDeviceDataSourceIsDeleteById">
        UPDATE aiops_device_datasource
        SET isDelete = #{isDelete}
        WHERE 1 != 1
        <if test="ids != null">
            <foreach collection="ids" item="item" open=" OR id IN (" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>

    <delete id="deleteDeviceDataSourceByDeviceId" parameterType="java.lang.String">
        DELETE FROM aiops_device_datasource
        WHERE deviceId = #{deviceId}
    </delete>

    <insert id="batchInsertOrUpdateDeviceDataSource">
        INSERT INTO aiops_device_datasource (id, adId, deviceId, dbId, dbType, dbDisplayName, dbIpAddress, dbPort,
                                             dbServerName, dbInstanceName, dbHostDevice)
        <foreach collection="deviceDataSource" item="item" open=" VALUES (" separator="), (" close=")">
            #{item.id}, #{item.adId}, #{item.deviceId}, #{item.dbId}, #{item.dbType}, #{item.dbDisplayName},
            #{item.dbIpAddress}, #{item.dbPort}, #{item.dbServerName}, #{item.dbInstanceName}, #{item.dbHostDevice}
        </foreach>
        ON DUPLICATE KEY UPDATE isDelete = VALUES(isDelete), dbId = VALUES(dbId), dbType = VALUES(dbType),
                                dbDisplayName = VALUES(dbDisplayName), dbIpAddress = VALUES(dbIpAddress),
                                dbPort = VALUES(dbPort), dbServerName = VALUES(dbServerName),
                                dbInstanceName = VALUES(dbInstanceName),
        dbHostDevice = CASE WHEN VALUES(dbHostDevice) IS NOT NULL THEN VALUES(dbHostDevice) ELSE dbHostDevice END
    </insert>

    <select id="selectDeviceAuthStatus" resultType="java.lang.String">
        SELECT CASE WHEN ad.isDeleted = 1 THEN 'INVALID'
               ELSE IFNULL(ai.aiopsAuthStatus, 'UNAUTH')
               END
        FROM aiops_device ad
        LEFT JOIN aiops_instance ai ON ai.aiopsItemType = 'DEVICE' AND ad.deviceId = ai.aiopsItemId
        WHERE ad.deviceId = #{deviceId}
    </select>

    <select id="selectDeviceIsUndefined" resultType="java.lang.Boolean">
        SELECT COUNT(*) <![CDATA[<=]]> 0
        FROM aiops_device ad
                 INNER JOIN aiops_device_type_mapping adtm ON ad.id = adtm.adId AND IFNULL(adtm.deviceType, '') != ''
        WHERE ad.deviceId = #{deviceId}
    </select>

    <select id="selectDeviceInfoByEidAndDeviceId" resultMap="DeviceMap">
        SELECT ad.*
        FROM aiops_device ad
        WHERE ad.eid = #{eid} AND ad.deviceId = #{deviceId}
    </select>

    <select id="getDeviceList" resultMap="DeviceResultMap">
        SELECT * FROM (
            SELECT DISTINCT ad.id, ad.eid, ad.deviceId, ad.deviceName, ad.platform, ad.ipAddress,
                            ad.placementPoint, ad.remark, ad.customerNotes, ad.aiopskitVersion, escliVersion, ad.registerTime,
                            ad.lastCheckInTime, ad.isDeleted,
                            ad.lastCheckInTime >= DATE_SUB(now(), INTERVAL 3 DAY) AS isOnLine,
                            TIMESTAMPDIFF(SECOND, ad.lastCheckInTime, now()) AS lastCheckInTimeDifferSecond,
                            CASE WHEN adtm.id IS NULL THEN
                                CASE WHEN ad.isDeleted THEN 'INVALID'
                                ELSE 'UNAUTH'
                                END
                            ELSE IFNULL(adim.aiopsAuthStatus, 'UNAUTH')
                            END AS aiopsAuthStatus,case when zjz.COUNT > 0 then 1 ELSE 0 END isZJZ
            FROM aiops_device ad
            LEFT JOIN aiops_device_type_mapping adtm ON adtm.adId = ad.id
            LEFT JOIN (
                SELECT adId, IFNULL(ai.aiopsAuthStatus, '') AS aiopsAuthStatus
                FROM aiops_device_instance_mapping
                INNER JOIN aiops_instance ai ON ai.aiopsItemType = 'DEVICE' AND ai.eid = #{eid} AND aiId = ai.id
            ) adim ON ad.id = adim.adId
            LEFT JOIN (
                SELECT adim.deviceId,COUNT(adim.deviceId) count
                FROM aiops_device_instance_mapping adim
                LEFT JOIN aiops_instance ai ON adim.aiId = ai.id AND ai.eid = #{eid}
                LEFT JOIN aiops_item_type ait ON ait.CODE = ai.aiopsItemType
                WHERE ait.id IN ('20','30') AND adim.hasAddedAiopsInstance =1
                GROUP BY adim.deviceId
            ) zjz ON zjz.deviceId = ad.deviceId
            WHERE 1=1
            <if test="eid != null and eid != 0">
                AND ad.eid = #{eid}
            </if>
            <choose>
                <when test="isUndefineDeviceType != null and isUndefineDeviceType">
                    AND adtm.id IS NULL
                    <if test="undefineDeviceIsDelete != null">
                        AND ad.isDeleted = #{undefineDeviceIsDelete}
                    </if>
                </when>
                <when test="deviceType != null and deviceType != ''">
                    AND adtm.deviceType = #{deviceType}
                </when>
            </choose>
            <if test="deviceNameOrId != null and deviceNameOrId != ''">
                AND (ad.deviceName LIKE CONCAT('%', #{deviceNameOrId}, '%')
                     OR ad.deviceId LIKE CONCAT('%', #{deviceNameOrId}, '%'))
            </if>
            <if test="placementPoint != null and placementPoint != ''">
                AND ad.placementPoint LIKE CONCAT('%', #{placementPoint}, '%')
            </if>
            <if test="ipAddress != null and ipAddress != ''">
                AND ad.ipAddress LIKE CONCAT('%', #{ipAddress}, '%')
            </if>
            <if test="aiopsAuthStatusList != null">
                <foreach collection="aiopsAuthStatusList" item="item"
                         open="AND IFNULL(adim.aiopsAuthStatus, 'UNAUTH') IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="platformList != null">
                <foreach collection="platformList" item="item"
                         open="AND ad.platform IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="isOnline != null">
                <choose>
                    <when test="isOnline">
                        AND ad.lastCheckInTime >= DATE_SUB(now(), INTERVAL 3 DAY)
                    </when>
                    <otherwise>
                        AND ad.lastCheckInTime <![CDATA[<]]> DATE_SUB(now(), INTERVAL 3 DAY)
                    </otherwise>
                </choose>
            </if>
            <if test="searchText != null and searchText != ''">
                AND (ad.deviceName LIKE CONCAT('%', #{searchText}, '%')
                     OR ad.ipAddress LIKE CONCAT('%', #{searchText}, '%')
                     OR ad.placementPoint LIKE CONCAT('%', #{searchText}, '%'))
            </if>
        ) t
        ORDER BY CASE t.aiopsAuthStatus WHEN 'AUTHED' THEN 1
                 WHEN 'NONE' THEN 2
                 WHEN 'UNAUTH' THEN 3
                 ELSE 4 END, t.deviceName
    </select>

    <select id="getDeviceDetail" resultMap="DeviceDetailResultMap">
        SELECT distinct ad.id, ad.eid, ad.deviceId, ad.deviceName, ad.platform, ad.ipAddress, ad.placementPoint,
        ad.remark, ad.customerNotes, ad.registerTime, ad.lastCheckInTime, ad.__version__, ifnull(t.name,'') tenantName,
        ifnull(stm.serviceCode,'') serviceCode,
        TIMESTAMPDIFF(SECOND, ad.lastCheckInTime, now()) AS lastCheckInTimeDifferSecond,
        ad.lastCheckInTime <![CDATA[>=]]> DATE_SUB(now(), INTERVAL 3 DAY) as isOnLine,ai.id as aiid
        from aiops_device ad
        LEFT JOIN aiops_device_type_mapping adtm ON adtm.adId = ad.id
            LEFT JOIN aiops_instance ai ON ai.aiopsItemId = ad.deviceId
        LEFT JOIN tenant t ON t.sid = ad.eid
        left join supplier_tenant_map stm on stm.sid = #{sid} and stm.eid = t.sid
        WHERE 1=1
        <if test="id != 0 and id != '' and id != null">
            AND ad.id = #{id}
        </if>
        <if test="deviceId != '' and deviceId != null">
            AND ad.deviceId = #{deviceId}
        </if>
    </select>

    <select id="getDeviceDetailNew" resultMap="DeviceDetailNewResultMap">
        <!--设备收集项预警项授权控管逻辑:
            1.设备收集项要开启(isEnable=true)
            2.设备收集项需已对应到实际实例，且该实例为无须授权或已授权
            3.设备本身要已授权
        -->
        SELECT ad.id, ad.eid, ad.deviceId, ad.deviceName, ad.platform, ad.ipAddress, ad.placementPoint,
               ad.remark, ad.registerTime, ad.lastCheckInTime, ad.__version__, acc.id AS accId,
               adc.id AS adcId, adcd.id AS adcdId, acc.collectCategory, category.name AS collectCategoryName,
               t.name AS tenantName, ai.id AS aiId, ai.aiopsItem,
               <include refid="com.digiwin.escloud.aioitms.instance.dao.InstanceMapper.getFindDisplayNameByModel">
                   <property name="aiAlias" value="ai"/>
               </include> AS findDisplayNameByModel,
               <choose>
                   <when test="serviceArea == 'TW'">
                       ai2.name_TW AS aiopsItemName,
                   </when>
                   <when test="serviceArea == 'CN'">
                       ai2.name_CN AS aiopsItemName,
                   </when>
                   <otherwise>
                       ai2.name AS aiopsItemName,
                   </otherwise>
               </choose>
               ai.aiopsItemType,
               <choose>
                   <when test="serviceArea == 'TW'">
                       ait.name_TW AS aiopsItemTypeName,
                   </when>
                   <when test="serviceArea == 'CN'">
                       ait.name_CN AS aiopsItemTypeName,
                   </when>
                   <otherwise>
                       ait.name AS aiopsItemTypeName,
                   </otherwise>
               </choose>
               <include refid="com.digiwin.escloud.aioitms.instance.dao.InstanceMapper.getAiopsInstanceDisplayNameSql">
                   <property name="aiAlias" value="ai"/>
               </include> AS aiopsInstanceName
        FROM aiops_device ad
        LEFT JOIN aiops_device_collect adc ON adc.deviceId = ad.deviceId
        INNER JOIN aiops_device_collect_detail adcd ON adcd.adcId = adc.id AND adcd.isEnable = 1
        <if test="adcdId != null and adcdId != 0">
            AND adcd.id = #{adcdId}
        </if>
        inner join (
             select ai.* from aiops_instance ai where 1=1
             and ai.aiopsAuthStatus IN ('NONE', 'AUTHED')
             and ai.aiopsItemType != 'TMP_RH'
             AND EXISTS(
                    SELECT 1 FROM aiops_instance
                    WHERE aiopsItemType = 'DEVICE' AND aiopsItemId = #{deviceId}
                    AND aiopsAuthStatus = 'AUTHED'
             )
             union all
             /*取溫濕度的預警資料*/
             select ai.* from aiops_instance ai
             inner join aiops_temp_rh_instance atri on atri.tmp_rh_id = ai.aiopsItemId
             where 1=1
             AND ai.aiopsAuthStatus IN ('AUTHED')
             and ai.aiopsItemType = 'TMP_RH'
        ) ai ON adcd.aiId = ai.id
        LEFT JOIN aiops_item ai2 ON ai.aiopsItem = ai2.code
        LEFT JOIN aiops_item_type ait ON ai.aiopsItemType = ait.code
        LEFT JOIN aiops_collect_config acc ON acc.id = adcd.accId
        LEFT JOIN aiops_collect_category category ON category.CODE = acc.collectCategory
        LEFT JOIN tenant t ON t.sid = ad.eid
        WHERE 1=1
        <if test="deviceId != null and deviceId != ''">
            AND ad.deviceId = #{deviceId}
        </if>
        <if test="accId != null and accId != ''">
            AND acc.id = #{accId}
        </if>
        LIMIT 1
    </select>
    <select id="getDeviceType" resultMap="deviceTypeMap">
        SELECT adtm.id id, adtm.adId adId, adtm.deviceId deviceId, adtm.deviceType deviceType,
               adt.name deviceTypeName
        FROM aiops_device_type_mapping adtm
        LEFT JOIN aiops_device_type adt ON adt.CODE= adtm.deviceType
        WHERE 1=1
        <if test="id != null and id != 0">
            AND adtm.adId = #{id}
        </if>
        <if test="deviceId != null and deviceId != ''">
            AND adtm.deviceId = #{deviceId}
        </if>
    </select>

    <select id="getDeviceProduct" resultMap="supplierProductMap">
        SELECT distinct sp.id id, sp.productCode productCode, sp.productCategory productCategory, sp.productType productType, sp.productName productName, sp.productShortName,
        apa.modelCode,cm.modelName
        from aiops_device_product_mapping adpm
        LEFT JOIN aiops_product_app apa ON apa.id = adpm.apaId
        LEFT JOIN supplier_product sp ON sp.id = apa.spId
        left join cmdb_model cm on cm.modelCode = apa.modelCode
        WHERE (apa.modelRelateCode IS not NULL and apa.modelRelateCode != '')
        <if test="id != null and id != 0">
            and adpm.adId = #{id}
        </if>
        <if test="searchContent != null and searchContent != ''">
            AND (sp.productName LIKE CONCAT('%',#{searchContent},'%' ) OR cm.modelName LIKE
            CONCAT('%',#{searchContent},'%' ) )
        </if>
        order by sp.productCode,apa.modelCode
    </select>

    <select id="getDeviceProductV2" resultMap="supplierProductMap">
        SELECT distinct sp.id id, sp.productCode productCode, sp.productCategory productCategory, sp.productType productType, sp.productName productName, sp.productShortName,
        apa.modelCode,cm.modelName
        from aiops_device_product_mapping adpm
        LEFT JOIN aiops_product_app apa ON apa.id = adpm.apaId
        LEFT JOIN supplier_product sp ON sp.id = apa.spId
        left join cmdb_model cm on cm.modelCode = apa.modelCode
        WHERE (apa.modelRelateCode IS not NULL and apa.modelRelateCode != '')
        <if test="id != null and id != 0">
            and adpm.adId = #{id}
        </if>
        order by sp.productCode,apa.modelCode
    </select>

    <select id="getDeviceCollectDetail" resultMap="deviceCollectDetailMap">
        SELECT adcd.*,acc.interval,acc.scopeId
        FROM aiops_device_collect_detail adcd
        LEFT JOIN aiops_device_collect adc ON adc.id = adcd.adcId
        LEFT JOIN aiops_device ad ON ad.deviceId = adc.deviceId
        LEFT JOIN aiops_collect_config acc ON acc.id = adcd.accId
        WHERE 1=1
        <if test="adId != null and adId != 0">
            and ad.id = #{adId}
        </if>
        <!--<if test="accId != null and accId != 0">
            and adcd.accId = #{accId}
        </if>-->
        <if test="adcdId != null and adcdId != 0">
            and adcd.id = #{adcdId}
        </if>
        ORDER BY acc.collectCode
    </select>

    <update id="updateDeviceCollectDetail">
        update aiops_device_collect_detail a
        set a.accId = #{accId}
        WHERE a.accId =#{oldAccId} and a.id =#{adcdId}
    </update>
    <update id="updateCollectWarning">
        update aiops_collect_warning a
        set a.accId = #{accId}
        WHERE a.accId =#{oldAccId} and a.id =#{acwId}
    </update>

    <update id="updateDeviceCollectWarning">
        update aiops_device_collect_warning adcw
        set adcw.acwId = #{acwId}, adcw.isWarningEnable = #{isWarningEnable}
        WHERE adcw.acwId = #{oldAcwId} and adcw.adcdId = #{adcdId}
    </update>
    <insert id="insertDeviceCollectWarning">
        insert into aiops_device_collect_warning(id, adcdId, acwId ,isWarningEnable)
        values (#{id}, #{adcdId}, #{acwId}, #{isWarningEnable})
    </insert>

    <select id="getDeviceCollectDetailList" resultMap="deviceCollectDetailMap">
        SELECT adcd.*, acc.interval, acc.scopeId, acc.collectName AS defaultCollectName, acc.uploadDataModelCode,
               acc.execParamsModelCode, acc.collectType, acc.collectorModelCode, acc.cron, acc.runtimeVersion,
               ad.deviceId AS deviceId,ad.eid,
               <include refid="com.digiwin.escloud.aioitms.instance.dao.InstanceMapper.getFindDisplayNameByModel">
                   <property name="aiAlias" value="ai"/>
               </include> AS findDisplayNameByModel,
               <include refid="com.digiwin.escloud.aioitms.instance.dao.InstanceMapper.getAiopsInstanceDisplayNameSql">
                   <property name="aiAlias" value="ai"/>
               </include> AS aiopsInstanceName,
               IFNULL(ai.aiopsItem, '') AS aiopsItem, IFNULL(ai.aiopsItemId, '') AS aiopsItemId,
               IFNULL(ai2.name, '') AS aiopsItemName, IFNULL(ai2.name_CN, '') AS aiopsItemName_CN,
               IFNULL(ai2.name_TW, '') AS aiopsItemName_TW,
               (CASE WHEN IFNULL(ai.aiopsAuthStatus, 'UNAUTH') IN ('NONE', 'AUTHED')
                    AND EXISTS(SELECT 1 FROM aiops_instance
                               WHERE aiopsItemType = 'DEVICE' AND aiopsItemId = ad.deviceId
                                    AND aiopsAuthStatus = 'AUTHED') THEN TRUE
                ELSE FALSE END) AS isValid,
               IFNULL(ai.aiopsItem, '') AS oriAiopsItem, IFNULL(ai.aiopsItemId, '') AS oriAiopsItemId
        FROM aiops_device_collect_detail adcd
        LEFT JOIN aiops_device_collect adc ON adc.id = adcd.adcId
        LEFT JOIN aiops_device ad ON ad.deviceId = adc.deviceId
        LEFT JOIN aiops_collect_config acc ON acc.id = adcd.accId
        LEFT JOIN aiops_instance ai ON adcd.aiId = ai.id
        LEFT JOIN aiops_item ai2 ON ai.aiopsItem = ai2.code
        WHERE 1=1
        <if test="id != null and id != ''">
            and ad.id = #{id}
        </if>
        <if test="search != null and search != ''">
            and (adcd.collectName LIKE '%${search}%' OR acc.collectName LIKE '%${search}%' OR acc.collectCode LIKE '%${search}%')
        </if>
        ORDER BY (IFNULL(aiopsInstanceName, '') = ''), CASE aiopsItem WHEN 'HOST' THEN 0
                                                       WHEN 'CLIENT' THEN 1
                                                       ELSE 99 END,
                 aiopsInstanceName, IFNULL(adcd.collectName, acc.collectName)
    </select>

    <select id="selectDeviceCollectWarningSetList"
            resultType="com.digiwin.escloud.aioitms.device.model.DeviceCollectWarningSet">
        SELECT *
        FROM (
            SELECT adcd.id AS adcdId,
                   acc.id AS accId, acc.uploadDataModelCode, acc.`interval`, acc.cron,
                   acw.id AS acwId, acw.warningCode, acw.warningName, acw.description, adcw.isWarningEnable,
                   adcw.id AS adcwId,
                   (acc.scopeId != 'DefaultConfig') AS isCollectCustomize,
                   (acw.scopeId != 'DefaultConfig' AND acw.scopeId != #{eid}) AS isWarningCustomize,
                   (CASE WHEN IFNULL(ai.aiopsAuthStatus, '') IN ('NONE', 'AUTHED') THEN TRUE
                    ELSE FALSE END) AS isValid
            FROM aiops_device ad
            INNER JOIN aiops_device_collect adc ON ad.id = adc.adId
            INNER JOIN aiops_device_collect_detail adcd ON adc.id = adcd.adcId AND (adcd.isEnable = 1
            <if test="innerRunnerAccIdSet != null">
                <foreach collection="innerRunnerAccIdSet" item="item"
                         open=" OR adcd.accId IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>)
            LEFT JOIN aiops_instance ai ON adcd.aiId = ai.id
            INNER JOIN aiops_collect_config acc ON adcd.accId = acc.id and acc.collectDeviceType != 'TMP_RH'
            INNER JOIN aiops_device_collect_warning adcw ON adcd.id = adcw.adcdId
            INNER JOIN aiops_collect_warning acw ON adcw.acwId = acw.id
            WHERE ad.id = #{adId}
            UNION ALL
            <!-- 取溫濕度的預警項, 因為相同設備會在不同的客戶間流動, 所以只取被授權的預警項 -->
            SELECT adcd.id AS adcdId,
                   acc.id AS accId, acc.uploadDataModelCode, acc.`interval`, acc.cron,
                   acw.id AS acwId, acw.warningCode, acw.warningName, acw.description, adcw.isWarningEnable,
                   adcw.id AS adcwId,
                   (acc.scopeId != 'DefaultConfig') AS isCollectCustomize,
                   (acw.scopeId != 'DefaultConfig' AND acw.scopeId != #{eid}) AS isWarningCustomize,
                   (CASE WHEN IFNULL(ai.aiopsAuthStatus, '') IN ('NONE', 'AUTHED') THEN TRUE
                    ELSE FALSE END) AS isValid
            FROM aiops_device ad
            INNER JOIN aiops_device_collect adc ON ad.id = adc.adId
            INNER JOIN aiops_device_collect_detail adcd ON adc.id = adcd.adcId AND adcd.isEnable = 1
            INNER JOIN (
                SELECT ai.id, ai.aiopsItemId, ai.aiopsAuthStatus
                FROM aiops_instance ai
                INNER JOIN aiops_temp_rh_instance atri ON atri.tmp_rh_id = ai.aiopsItemId
                    AND ai.aiopsItemType = 'TMP_RH' AND atri.device_status = 'IN_USED'
                UNION ALL
                <!--已回收的温湿度设备，预警设定开放-->
                SELECT ai.id, ai.aiopsItemId, ai.aiopsAuthStatus
                FROM aiops_instance ai
                INNER JOIN temp_rh_customer_info trci ON trci.tmp_rh_id = ai.aiopsItemId
                INNER JOIN aiops_temp_rh_instance atri ON trci.tadid = atri.id
                    AND ai.aiopsItemType = 'TMP_RH' AND IFNULL(atri.eid, -1) != trci.eid
            ) ai ON adcd.aiId = ai.id
            INNER join aiops_temp_rh_instance atri ON atri.tmp_rh_id = ai.aiopsItemId
            INNER JOIN aiops_collect_config acc ON adcd.accId = acc.id AND acc.collectDeviceType = 'TMP_RH'
            INNER JOIN aiops_device_collect_warning adcw ON adcd.id = adcw.adcdId
            INNER JOIN aiops_collect_warning acw ON adcw.acwId = acw.id
            WHERE ad.id = #{adId}
        ) a
        ORDER BY warningName
    </select>
    <resultMap id="AicwMap" type="com.digiwin.escloud.aioitms.device.model.AiopsItemCollectWarningSet">

        <result property="acwId" column="acwId"/>
        <result property="warningCode" column="warningCode"/>
        <result property="warningName" column="warningName"/>
        <result property="description" column="description"/>
        <result property="isWarningEnable" column="isWarningEnable"/>
        <result property="isWarningCustomize" column="isWarningCustomize"/>
        <result property="continueStopSettingValue" column="continueStopSettingValue"/>
        <result property="continueStopSettingUnit" column="continueStopSettingUnit"/>
        <result property="scopeId" column="scopeId"/>
        <result property="__version__" column="__version__"/>
        <result property="levelCodeString" column="levelCodeString"/>

        <collection property="aicwdList" ofType="com.digiwin.escloud.aioitms.device.model.AiopsItemCollectWarningSet$AiopsItemCollectWarningSetDetail">
            <result property="acwId" column="d_acwId"/>
            <result property="adcdId" column="d_adcdId"/>
            <result property="adcwId" column="d_adcwId"/>
            <result property="isWarningEnable" column="d_isWarningEnable"/>
            <result property="aiId" column="d_aiId"/>
            <result property="aiopsItem" column="d_aiopsItem"/>
            <result property="deviceId" column="d_deviceId"/>
            <result property="isValid" column="d_isValid"/>
            <result property="isWarningCustomize" column="d_isWarningCustomize"/>
        </collection>
    </resultMap>

<!--    <select id="selectDeviceCollectWarningSetListByEidAndAiopsItemPage"-->
<!--            resultMap="AicwMap">-->
<!--        SELECT * FROM (-->
<!--            SELECT acw.id AS acwId,-->
<!--                   acw.warningCode,-->
<!--                   acw.warningName,-->
<!--                   (acw.scopeId != 'DefaultConfig') AS isWarningCustomize,-->
<!--                   acw.scopeId,-->
<!--                   acw.description,-->
<!--                   acw.isWarningEnable AS isWarningEnable,-->
<!--                   GROUP_CONCAT(DISTINCT aws.levelCode) AS levelCodeString,-->
<!--                   acw.continueStopSettingValue,-->
<!--                   acw.continueStopSettingUnit,-->
<!--                   acw.__version__,-->
<!--                   1 AS priority-->
<!--            FROM aiops_collect_warning acw-->
<!--            LEFT JOIN aiops_warning_setting aws ON aws.acwId = acw.id-->
<!--            INNER JOIN aiops_device_collect_warning adcw ON adcw.acwId = acw.id-->
<!--            INNER JOIN aiops_device_collect_detail adcd ON adcw.adcdId = adcd.id AND (adcd.isEnable = 1-->
<!--        <if test="innerRunnerAccIdSet != null">-->
<!--            <foreach collection="innerRunnerAccIdSet" item="item"-->
<!--                     open=" OR adcd.accId IN (" separator=", " close=")">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--        </if>-->
<!--        )-->
<!--            INNER JOIN aiops_instance ai ON ai.id = adcd.aiId-->
<!--            WHERE ai.eid = #{eid}-->
<!--            AND EXISTS(SELECT 1 FROM aiops_collect_config acc-->
<!--            WHERE acc.id = acw.accId AND acc.isEnable = true)-->
<!--            <foreach collection="scopeIdList" open="AND acw.scopeId IN (" close=")" separator="," item="item">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--            <if test="warningCode != ''">-->
<!--                AND acw.warningCode LIKE CONCAT('%',#{warningCode},'%')-->
<!--            </if>-->
<!--            <if test="aiopsItem != null">-->
<!--                AND ai.aiopsItem = #{aiopsItem}-->
<!--            </if>-->
<!--            <if test="warningName != ''">-->
<!--                AND acw.warningName LIKE CONCAT('%',#{warningName},'%')-->
<!--            </if>-->
<!--            <if test="aiopsAuthStatusList != null">-->
<!--                <foreach collection="aiopsAuthStatusList" item="item"-->
<!--                         open=" AND ai.aiopsAuthStatus IN (" separator=", " close=")">-->
<!--                    #{item}-->
<!--                </foreach>-->
<!--            </if>-->
<!--            <if test="warningLevelList != null and warningLevelList.size()>0">-->
<!--                AND EXISTS (-->
<!--                    SELECT 1 FROM aiops_warning_setting aws2-->
<!--                    WHERE aws2.acwId = acw.id-->
<!--                    AND aws2.levelCode IN-->
<!--                    <foreach collection="warningLevelList" item="item" open="(" close=")" separator=",">-->
<!--                        #{item}-->
<!--                    </foreach>-->
<!--                )-->
<!--            </if>-->
<!--            GROUP BY acw.id, acw.warningCode, acw.warningName, acw.scopeId,-->
<!--                     acw.description, acw.isWarningEnable, acw.continueStopSettingValue,-->
<!--                     acw.continueStopSettingUnit, acw.__version__-->

<!--            UNION ALL-->

<!--            SELECT acw.id AS acwId,-->
<!--                   acw.warningCode,-->
<!--                   acw.warningName,-->
<!--                   (acw.scopeId != 'DefaultConfig') AS isWarningCustomize,-->
<!--                   acw.scopeId,-->
<!--                   acw.description,-->
<!--                   acw.isWarningEnable AS isWarningEnable,-->
<!--                   GROUP_CONCAT(DISTINCT aws.levelCode) AS levelCodeString,-->
<!--                   acw.continueStopSettingValue,-->
<!--                   acw.continueStopSettingUnit,-->
<!--                   acw.__version__,-->
<!--                   2 AS priority-->
<!--            FROM aiops_collect_warning acw-->
<!--            LEFT JOIN aiops_warning_setting aws ON aws.acwId = acw.id-->
<!--            INNER JOIN aiops_device_collect_warning adcw ON adcw.acwId = acw.id-->
<!--            INNER JOIN aiops_device_collect_detail adcd ON adcw.adcdId = adcd.id AND (adcd.isEnable = 1-->
<!--        <if test="innerRunnerAccIdSet != null">-->
<!--            <foreach collection="innerRunnerAccIdSet" item="item"-->
<!--                     open=" OR adcd.accId IN (" separator=", " close=")">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--        </if>-->
<!--        )-->
<!--            INNER JOIN aiops_instance ai ON ai.id = adcd.aiId-->
<!--            WHERE acw.scopeId = 'DefaultConfig' AND ai.eid = #{eid}-->
<!--            AND NOT EXISTS (-->
<!--                SELECT 1 FROM aiops_collect_warning sub-->
<!--                WHERE sub.sourceAcwId = acw.id AND scopeId = #{eid}-->
<!--            )-->
<!--              AND EXISTS(SELECT 1 FROM aiops_collect_config acc-->
<!--                                  WHERE acc.id = acw.accId AND acc.isEnable = true)-->
<!--            <if test="warningCode != ''">-->
<!--                AND acw.warningCode LIKE CONCAT('%',#{warningCode},'%')-->
<!--            </if>-->
<!--            <if test="aiopsItem != null">-->
<!--                AND ai.aiopsItem = #{aiopsItem}-->
<!--            </if>-->
<!--            <if test="warningName != ''">-->
<!--                AND acw.warningName LIKE CONCAT('%',#{warningName},'%')-->
<!--            </if>-->
<!--            <if test="aiopsAuthStatusList != null">-->
<!--                <foreach collection="aiopsAuthStatusList" item="item"-->
<!--                         open=" AND ai.aiopsAuthStatus IN (" separator=", " close=")">-->
<!--                    #{item}-->
<!--                </foreach>-->
<!--            </if>-->
<!--            <if test="warningLevelList != null and warningLevelList.size()>0">-->
<!--                AND EXISTS (-->
<!--                    SELECT 1 FROM aiops_warning_setting aws2-->
<!--                    WHERE aws2.acwId = acw.id-->
<!--                    AND aws2.levelCode IN-->
<!--                    <foreach collection="warningLevelList" item="item" open="(" close=")" separator=",">-->
<!--                        #{item}-->
<!--                    </foreach>-->
<!--                )-->
<!--            </if>-->
<!--            GROUP BY acw.id, acw.warningCode, acw.warningName, acw.scopeId,-->
<!--                     acw.description, acw.isWarningEnable, acw.continueStopSettingValue,-->
<!--                     acw.continueStopSettingUnit, acw.__version__-->

<!--            UNION ALL-->

<!--            SELECT acw.id AS acwId,-->
<!--            acw.warningCode,-->
<!--            acw.warningName,-->
<!--            NULL AS isWarningCustomize,-->
<!--            acw.scopeId,-->
<!--            acw.description,-->
<!--            acw.isWarningEnable AS isWarningEnable,-->
<!--            GROUP_CONCAT(DISTINCT aws.levelCode) AS levelCodeString,-->
<!--            acw.continueStopSettingValue,-->
<!--            acw.continueStopSettingUnit,-->
<!--            acw.__version__,-->
<!--            3 AS priority-->
<!--            FROM aiops_collect_warning acw-->
<!--            LEFT JOIN aiops_warning_setting aws ON aws.acwId = acw.id-->
<!--            INNER JOIN aiops_device_collect_warning adcw ON adcw.acwId = acw.id-->
<!--            INNER JOIN aiops_device_collect_detail adcd ON adcw.adcdId = adcd.id AND (adcd.isEnable = 1-->
<!--        <if test="innerRunnerAccIdSet != null">-->
<!--            <foreach collection="innerRunnerAccIdSet" item="item"-->
<!--                     open=" OR adcd.accId IN (" separator=", " close=")">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--        </if>-->
<!--        )-->
<!--            INNER JOIN aiops_instance ai ON ai.id = adcd.aiId-->
<!--            WHERE ai.eid = #{eid}-->
<!--            AND EXISTS (-->
<!--            SELECT 1 FROM aiops_device ad2-->
<!--            WHERE ad2.deviceId = acw.scopeId-->
<!--            )-->
<!--            AND EXISTS(SELECT 1 FROM aiops_collect_config acc-->
<!--            WHERE acc.id = acw.accId AND acc.isEnable = true)-->
<!--            AND EXISTS(SELECT 1 FROM aiops_collect_warning acw2-->
<!--            WHERE acw2.warningCode = acw.warningCode AND acw2.scopeId = 'DefaultConfig')-->
<!--        AND sourceAcwId IS NOT NULL-->
<!--            <if test="warningCode != ''">-->
<!--                AND acw.warningCode LIKE CONCAT('%',#{warningCode},'%')-->
<!--            </if>-->
<!--            <if test="aiopsItem != null">-->
<!--                AND ai.aiopsItem = #{aiopsItem}-->
<!--            </if>-->
<!--            <if test="warningName != ''">-->
<!--                AND acw.warningName LIKE CONCAT('%',#{warningName},'%')-->
<!--            </if>-->
<!--            <if test="aiopsAuthStatusList != null">-->
<!--                <foreach collection="aiopsAuthStatusList" item="item"-->
<!--                         open=" AND ai.aiopsAuthStatus IN (" separator=", " close=")">-->
<!--                    #{item}-->
<!--                </foreach>-->
<!--            </if>-->
<!--            <if test="warningLevelList != null and warningLevelList.size()>0">-->
<!--                AND EXISTS (-->
<!--                SELECT 1 FROM aiops_warning_setting aws2-->
<!--                WHERE aws2.acwId = acw.id-->
<!--                AND aws2.levelCode IN-->
<!--                <foreach collection="warningLevelList" item="item" open="(" close=")" separator=",">-->
<!--                    #{item}-->
<!--                </foreach>-->
<!--                )-->
<!--            </if>-->
<!--            GROUP BY acw.id, acw.warningCode, acw.warningName, acw.scopeId,-->
<!--                     acw.description, acw.isWarningEnable, acw.continueStopSettingValue,-->
<!--                     acw.continueStopSettingUnit, acw.__version__-->
<!--        ) AS combined_results-->
<!--        WHERE 1=1-->
<!--        <if test="customize != null">-->
<!--            AND isWarningCustomize = #{customize}-->
<!--        </if>-->
<!--        GROUP BY warningCode-->
<!--        HAVING MIN(priority) = priority-->
<!--        ORDER BY warningCode-->
<!--    </select>-->

    <select id="selectDeviceCollectWarningSetListByEidAndAiopsItemPage"
            resultMap="AicwMap">
        SELECT acw.id AS acwId,
        acw.warningCode,
        acw.warningName,
        acw.scopeId,
        acw.description,
        IFNULL(atw.isWarningEnable,acw.isWarningEnable) AS isWarningEnable,
        GROUP_CONCAT(DISTINCT aws.levelCode) AS levelCodeString,
        acw.continueStopSettingValue,
        acw.continueStopSettingUnit,
        acw.__version__
        FROM `aiops_module_collect_mapping` amcm
        left join supplier_aiops_module_class_detail samcd on amcm.samcdId = samcd.id
        LEFT JOIN aiops_collect_warning acw on amcm.accId = acw.accId
        LEFT JOIN aiops_warning_setting aws on aws.acwId = acw.id
        LEFT JOIN aiops_tenant_warning atw ON atw.acwId = acw.id AND atw.eid = #{eid}
        where samcd.aiopsItem = #{aiopsItem} AND acw.id IS NOT NULL
         and amcm.isEnable = true
        <if test="warningName != null and warningName != ''">
            AND acw.warningName LIKE CONCAT('%',#{warningName},'%')
        </if>
        <if test="warningCodeList != null and warningCodeList.size > 0">
            AND acw.warningCode IN
            <foreach collection="warningCodeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="scopeId != null">
            and acw.scopeId=#{scopeId}
        </if>
        <if test="warningLevelList != null and warningLevelList.size()>0">
            AND EXISTS (
            SELECT 1 FROM aiops_warning_setting aws2
            WHERE aws2.acwId = acw.id
            AND aws2.levelCode IN
            <foreach collection="warningLevelList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <choose>
            <when test="customize != null and customize == true">
                AND EXISTS (
                SELECT 1 FROM aiops_collect_warning acw2
                WHERE acw2.warningCode = acw.warningCode
                AND acw2.scopeId = #{eid}
                )
            </when>
            <when test="customize != null and customize == false">
                AND NOT EXISTS (
                SELECT 1 FROM aiops_collect_warning acw2
                WHERE acw2.warningCode = acw.warningCode
                AND acw2.scopeId = #{eid}
                )
            </when>
        </choose>
        GROUP BY acw.id, acw.warningCode, acw.warningName, acw.scopeId,
        acw.description, acw.isWarningEnable, acw.continueStopSettingValue,
        acw.continueStopSettingUnit, acw.__version__
        ORDER BY acw.warningCode,acw.__version__
    </select>

                <select id="selectCustomiseDeviceCollectWarningSetListByEid"
                        resultMap="AicwMap">
                    select acw.id AS acwId,
                    acw.warningCode,
                    acw.warningName,
                    (acw.scopeId = #{eid}) AS isWarningCustomize,
                    acw.scopeId,
                    acw.description,
                    acw.isWarningEnable isWarningEnable,
                    group_concat(distinct aws.levelCode) as levelCodeString,
                    acw.continueStopSettingValue,
                    acw.continueStopSettingUnit,
                    acw.__version__
                    from aiops_collect_warning acw
                    inner join aiops_device_collect_warning adcw on adcw.acwId = acw.id
                    inner join aiops_device_collect_detail adcd on adcw.adcdId = adcd.id AND (adcd.isEnable = 1
                    <if test="innerRunnerAccIdSet != null">
                        <foreach collection="innerRunnerAccIdSet" item="item"
                                 open=" OR adcd.accId IN (" separator=", " close=")">
                            #{item}
                        </foreach>
                    </if>
                    )
                    inner join aiops_instance ai on ai.id = adcd.aiId
                    left join aiops_warning_setting aws on aws.acwId = acw.id
                    where ai.eid = #{eid}  AND EXISTS(SELECT 1 FROM aiops_collect_config acc
                    WHERE acc.id = acw.accId AND acc.isEnable = true)
                    <foreach collection="scopeIdListNotIn" open="AND acw.scopeId NOT IN (" close=")" separator="," item="item">
                        #{item}
                    </foreach>
                    <if test="warningCodeList != null">
                        <foreach collection="warningCodeList" item="item"
                                 open=" AND acw.warningCode IN(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
            <!--        <if test="customize != null">-->
<!--            AND  isWarningCustomize = #{customize}-->
<!--        </if>-->
<!--        <if test="warningCode != ''">-->
<!--            AND acw.warningCode like CONCAT ('%',#{warningCode},'%')-->
<!--        </if>-->
<!--        <if test="warningName != ''">-->
<!--            AND acw.warningName like CONCAT ('%',#{warningName},'%')-->
<!--        </if>-->
<!--        <if test="aiopsItem != null">-->
<!--            AND ai.aiopsItem = #{aiopsItem}-->
<!--        </if>-->
<!--        <if test="aiopsAuthStatusList != null">-->
<!--            <foreach collection="aiopsAuthStatusList" item="item"-->
<!--                     open=" AND ai.aiopsAuthStatus IN (" separator=", " close=")">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--        </if>-->
<!--        <if test="warningLevelList != null and warningLevelList.size()>0">-->
<!--            <foreach collection="warningLevelList" item="item" open="AND aws.levelCode IN (" close=")" separator=",">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--        </if>-->
        GROUP BY
        acw.id,
        acw.warningCode,
        acw.warningName,
        acw.scopeId,
        acw.description,
        acw.isWarningEnable,
        acw.continueStopSettingValue,
        acw.continueStopSettingUnit,
        acw.__version__
    </select>

    <select id="selectDefaultAcwByWarningCode"  resultMap="AicwMap">
        select acw.id AS acwId,
               acw.warningCode,
               acw.warningName,
               false AS isWarningCustomize,
               acw.scopeId,
               acw.description,
               acw.isWarningEnable isWarningEnable,
               group_concat(distinct aws.levelCode) as levelCodeString,
               acw.continueStopSettingValue,
               acw.continueStopSettingUnit,
               acw.__version__
        from aiops_collect_warning acw
                 left join aiops_warning_setting aws on aws.acwId = acw.id
        where  acw.scopeId = 'DefaultConfig' AND EXISTS(SELECT 1 FROM aiops_collect_config acc
        WHERE acc.id = acw.accId AND acc.isEnable = true)
        <foreach collection="warningCodeList" open="and acw.warningCode in (" separator="," close=")" item="item">
            #{item}
        </foreach>
        GROUP BY
        acw.id,
        acw.warningCode,
        acw.warningName,
        acw.scopeId,
        acw.description,
        acw.isWarningEnable,
        acw.continueStopSettingValue,
        acw.continueStopSettingUnit,
        acw.__version__
    </select>
    <select id="selectAcwBySourceAcwId" resultType="Long">
        select id from aiops_collect_warning where sourceAcwId = #{sourceAcwId}
    </select>
<!--    <select id="selectDeviceCollectWarningSetListByEidAndAiopsItem"-->
<!--            resultMap="AicwMap">-->

<!--        SELECT *-->
<!--        FROM (-->
<!--        SELECT-->
<!--        acw.id AS acwId, acw.warningCode, acw.warningName,-->
<!--        (acw.scopeId != 'DefaultConfig') AS isWarningCustomize,-->
<!--        acw.scopeId,-->
<!--        acw.description,-->
<!--        acw.isWarningEnable isWarningEnable,aws.levelCode,acw.continueStopSettingValue,acw.continueStopSettingUnit,acw.__version__,-->
<!--        adcw.id AS d_adcwId,-->
<!--        adcw.acwId AS d_acwId,-->
<!--        adcd.id AS d_adcdId,-->
<!--        adcw.isWarningEnable as d_isWarningEnable,-->
<!--        (CASE WHEN IFNULL(ai.aiopsAuthStatus, '') IN ('NONE', 'AUTHED') THEN TRUE-->
<!--        ELSE FALSE END) AS d_isValid,-->
<!--        ad.deviceId d_deviceId,-->
<!--        ai.id d_aiId,-->
<!--        ai.aiopsItem d_aiopsItem-->
<!--        FROM aiops_device ad-->
<!--        INNER JOIN aiops_device_collect adc ON ad.id = adc.adId-->
<!--        INNER JOIN aiops_device_collect_detail adcd ON adc.id = adcd.adcId AND (adcd.isEnable = 1-->
<!--        <if test="innerRunnerAccIdSet != null">-->
<!--            <foreach collection="innerRunnerAccIdSet" item="item"-->
<!--                     open=" OR adcd.accId IN (" separator=", " close=")">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--        </if>-->
<!--        )-->
<!--        LEFT JOIN aiops_instance ai ON adcd.aiId = ai.id-->
<!--        INNER JOIN aiops_collect_config acc ON adcd.accId = acc.id and acc.collectDeviceType != 'TMP_RH'-->
<!--        INNER JOIN aiops_device_collect_warning adcw ON adcd.id = adcw.adcdId-->
<!--        INNER JOIN aiops_collect_warning acw ON adcw.acwId = acw.id-->
<!--        INNER JOIN aiops_warning_setting aws ON aws.acwId = acw.id-->
<!--        WHERE ad.eid = #{eid}-->
<!--        <foreach collection="scopeIdList" open="AND acw.scopeId IN (" close=")" separator="," item="item">-->
<!--            #{item}-->
<!--        </foreach>-->
<!--        <if test="aiopsItem != null">-->
<!--            AND ai.aiopsItem = #{aiopsItem}-->
<!--        </if>-->
<!--        <if test="aiopsAuthStatusList != null">-->
<!--            <foreach collection="aiopsAuthStatusList" item="item"-->
<!--                     open=" AND ai.aiopsAuthStatus IN (" separator=", " close=")">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--        </if>-->
<!--        <if test="codeList != null">-->
<!--            AND (-->
<!--            <foreach collection="codeList" item="item"-->
<!--                     open="(" separator="OR" close=")">-->
<!--                acw.id =  #{item.acwId} and acw.warningCode = #{item.warningCode}-->
<!--            </foreach>-->
<!--            )-->
<!--        </if>-->
<!--        <if test="acwId != null">-->
<!--            AND acw.id = #{acwId}-->
<!--        </if>-->
<!--        UNION ALL-->
<!--        &lt;!&ndash; 取溫濕度的預警項, 因為相同設備會在不同的客戶間流動, 所以只取被授權的預警項 &ndash;&gt;-->
<!--        SELECT-->

<!--        acw.id AS acwId, acw.warningCode, acw.warningName,-->
<!--        (acw.scopeId != 'DefaultConfig') AS isWarningCustomize,-->
<!--        acw.scopeId,-->
<!--        acw.description,-->
<!--        acw.isWarningEnable isWarningEnable,aws.levelCode,acw.continueStopSettingValue,acw.continueStopSettingUnit,acw.__version__,-->
<!--        adcw.id AS d_adcwId,-->
<!--        adcw.acwId AS d_acwId,-->
<!--        adcd.id AS d_adcdId,-->
<!--        adcw.isWarningEnable as d_isWarningEnable,-->
<!--        (CASE WHEN IFNULL(ai.aiopsAuthStatus, '') IN ('NONE', 'AUTHED') THEN TRUE-->
<!--        ELSE FALSE END) AS d_isValid,-->
<!--        ad.deviceId d_deviceId,-->
<!--        ai.id d_aiId,-->
<!--        ai.aiopsItem d_aiopsItem-->
<!--        FROM aiops_device ad-->
<!--        INNER JOIN aiops_device_collect adc ON ad.id = adc.adId-->
<!--        INNER JOIN aiops_device_collect_detail adcd ON adc.id = adcd.adcId AND adcd.isEnable = 1-->
<!--        INNER JOIN (-->
<!--        SELECT ai.id, ai.aiopsItemId, ai.aiopsAuthStatus,ai.aiopsItem-->
<!--        FROM aiops_instance ai-->
<!--        INNER JOIN aiops_temp_rh_instance atri ON atri.tmp_rh_id = ai.aiopsItemId-->
<!--        AND ai.aiopsItemType = 'TMP_RH' AND atri.device_status = 'IN_USED'-->
<!--        UNION ALL-->
<!--        &lt;!&ndash;已回收的温湿度设备，预警设定开放&ndash;&gt;-->
<!--        SELECT ai.id, ai.aiopsItemId, ai.aiopsAuthStatus,ai.aiopsItem-->
<!--        FROM aiops_instance ai-->
<!--        INNER JOIN temp_rh_customer_info trci ON trci.tmp_rh_id = ai.aiopsItemId-->
<!--        INNER JOIN aiops_temp_rh_instance atri ON trci.tadid = atri.id-->
<!--        AND ai.aiopsItemType = 'TMP_RH' AND IFNULL(atri.eid, -1) != trci.eid-->
<!--        ) ai ON adcd.aiId = ai.id-->
<!--        INNER join aiops_temp_rh_instance atri ON atri.tmp_rh_id = ai.aiopsItemId-->
<!--        INNER JOIN aiops_collect_config acc ON adcd.accId = acc.id AND acc.collectDeviceType = 'TMP_RH'-->
<!--        INNER JOIN aiops_device_collect_warning adcw ON adcd.id = adcw.adcdId-->
<!--        INNER JOIN aiops_collect_warning acw ON adcw.acwId = acw.id-->
<!--        INNER JOIN aiops_warning_setting aws ON aws.acwId = acw.id-->
<!--        WHERE ad.eid = #{eid}-->
<!--        <foreach collection="scopeIdList" open="AND acw.scopeId IN (" close=")" separator="," item="item">-->
<!--            #{item}-->
<!--        </foreach>-->
<!--        <if test="aiopsItem != null">-->
<!--            AND ai.aiopsItem = #{aiopsItem}-->
<!--        </if>-->
<!--        <if test="codeList != null">-->
<!--            AND (-->
<!--            <foreach collection="codeList" item="item"-->
<!--                     open="(" separator="OR" close=")">-->
<!--                acw.id =  #{item.acwId} and acw.warningCode = #{item.warningCode}-->
<!--            </foreach>-->
<!--            )-->
<!--        </if>-->
<!--        <if test="aiopsAuthStatusList != null">-->
<!--            <foreach collection="aiopsAuthStatusList" item="item"-->
<!--                     open=" AND ai.aiopsAuthStatus IN (" separator=", " close=")">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--        </if>-->
<!--        <if test="acwId != null">-->
<!--            AND acw.id = #{acwId}-->
<!--        </if>-->
<!--        ) a-->
<!--        ORDER BY warningCode-->
<!--    </select>-->

    <select id="selectWarningListByAiId" resultType="com.digiwin.escloud.aioitms.device.model.DeviceCollectWarningSet">
        SELECT adcd.id AS adcdId,
               acc.id AS accId, acc.uploadDataModelCode,
               acw.id AS acwId, acw.warningCode, acw.warningName, acw.description, adcw.isWarningEnable,
               adcw.id AS adcwId,
               (acc.scopeId != 'DefaultConfig') AS isCollectCustomize,
               (acw.scopeId != 'DefaultConfig') AS isWarningCustomize,
               (CASE WHEN IFNULL(ai.aiopsAuthStatus, '') IN ('NONE', 'AUTHED') THEN TRUE
                ELSE FALSE END) AS isValid
        FROM aiops_instance ai
        INNER JOIN aiops_device_collect_detail adcd ON ai.id = adcd.aiId AND adcd.isEnable = 1
        INNER JOIN aiops_collect_config acc ON adcd.accId = acc.id
        INNER JOIN aiops_device_collect_warning adcw ON adcd.id = adcw.adcdId
        INNER JOIN aiops_collect_warning acw ON adcw.acwId = acw.id
        WHERE ai.id = #{aiId}
        ORDER BY acw.warningName
    </select>
    <select id="getProductCollectWarningList" resultType="com.digiwin.escloud.aioitms.collectwarning.model.CollectWarning">
        SELECT acw.id, acw.accId, acw.scopeId, acw.warningCode, acw.warningName, acw.momentType, acw.description,
               adcw.isWarningEnable, acw.continueStopSettingValue, acw.continueStopSettingUnit, acw.sourceAcwId,
               adcw.id adcwId
        FROM aiops_device_collect_warning adcw
        LEFT JOIN aiops_device_collect_detail adcd ON adcd.id = adcw.adcdId
        LEFT JOIN aiops_collect_warning acw ON adcw.acwId = acw.id
        WHERE acw.accId= #{accId} AND adcd.id=#{adcdId}
        ORDER BY acw.warningCode
    </select>

    <select id="getProductCollectWarningListNew" resultMap="baseCollectWarningMap">
        SELECT acw.id, acw.accId, acw.scopeId, acw.warningCode, acw.warningName, acw.momentType, acw.description,
               acw.continueStopSettingValue, acw.continueStopSettingUnit, acw.sourceAcwId,

               CASE WHEN adcw.isWarningEnable IS NULL THEN acw.isWarningEnable ELSE adcw.isWarningEnable END isWarningEnable,
               adcw.id adcwId,

               aws.id aws_id, aws.acwId aws_acwId, aws.levelCode aws_levelCode, aws.isInterval aws_isInterval,
               aws.notUploadWarning aws_notUploadWarning, aws.__version__ aws___version__,
               aws.conditionExpression aws_conditionExpression, aws.intervalValue aws_intervalValue,
               aws.timingTime aws_timingTime, aws.schedulerUnit aws_schedulerUnit,
               aws.sinkName aws_sinkName, aws.groupFields aws_groupFields,
               aws.offlineSchedulerId aws_offlineSchedulerId, aws.offlineUpdateStatus aws_offlineUpdateStatus,
               aws.offlineUpdateTime aws_offlineUpdateTime,
               awa.id aws_awa_id, awa.awsId aws_awa_awsId, awa.analysisWay aws_awa_analysisWay, awa.value aws_awa_value,
               awa.unit aws_awa_unit, awa.hitCondition aws_awa_hitCondition, awa.hitValue aws_awa_hitValue,
               awa.__version__ aws_awa___version__,

               awc.id aws_awc_id, awc.awsId aws_awc_awsId, awc.modelField aws_awc_modelField, awc.`each` aws_awc_each,
               awc.eachUnit aws_awc_eachUnit, awc.statisticType aws_awc_statisticType, awc.operator aws_awc_operator,
               awc.abnormalValue aws_awc_abnormalValue, awc.logic aws_awc_logic, awc.sequence aws_awc_sequence,
               awc.__version__ aws_awc___version__,

               awnt.id aws_awnt_id, awnt.awsId aws_awnt_awsId, awnt.templateType aws_awnt_templateType,
               awnt.title aws_awnt_title, awnt.content aws_awnt_content, awnt.gridContent aws_awnt_gridContent,
               awnt.suggest aws_awnt_suggest, awnt.helpUrl aws_awnt_helpUrl, awnt.__version__ aws_awnt___version__,

               adcw.id adwn_id, adwn.id adwn_ma_id, adwn.notifyCategory adwn_ma_notifyCategory,
               adwn.sourceId adwn_ma_sourceId

        FROM aiops_device_collect_warning adcw
        LEFT JOIN aiops_collect_warning acw ON adcw.acwId = acw.id
        LEFT JOIN aiops_device_collect_detail adcd ON adcd.accId= acw.accId AND adcd.id = adcw.adcdId
        LEFT JOIN aiops_device_collect adc ON adc.id = adcd.adcId
        LEFT JOIN aiops_device ad ON ad.deviceId = adc.deviceId

        LEFT JOIN aiops_warning_setting aws ON aws.acwId = acw.id
        LEFT JOIN aiops_warning_analysis awa ON awa.awsId = aws.id
        LEFT JOIN aiops_warning_condition awc ON awc.awsId = aws.id
        LEFT JOIN aiops_warning_notify_template awnt ON awnt.awsId = aws.id AND isOffline = 0

        LEFT JOIN aiops_device_warning_notify_mapping adwn ON adcw.id = adwn.adcwId

        WHERE adcw.adcdId = #{adcdId} AND adc.id = #{adcId} AND acw.scopeId IN (adc.deviceId,ad.eid,'DefaultConfig')
        <if test="accId != null and accId !=''">
            AND acw.accId = #{accId}
        </if>
        ORDER BY acw.warningCode
    </select>

    <select id="getModuleCollectWarningList" resultMap="baseCollectWarningMap">
        select acam.*
        from aiops_collect_warning acam
        where 1=1
        <if test="accId != null and accId != ''">
            and acam.accId= #{accId}
        </if>
        <if test="scopeId != null and scopeId != ''">
            and acam.scopeId = #{scopeId}
        </if>
        <if test="isWarningEnable != null and isWarningEnable != ''">
            and acam.isWarningEnable = #{isWarningEnable}
        </if>
        order by acam.warningCode asc
    </select>

    <select id="selectProductAppForDevice" resultType="com.digiwin.escloud.aioitms.device.model.DeviceProductMapping">
        SELECT *
        FROM aiops_product_app apa
        INNER JOIN aiops_device_product_mapping adpm ON apa.id = adpm.apaId AND adpm.adId = #{adId}
        WHERE apa.spId = #{spId}
    </select>

    <select id="selectAdpmAppIdListByMap" resultType="java.lang.String">
        SELECT adpm.appId
        FROM aiops_device_product_mapping adpm
        INNER JOIN aiops_product_app apa ON adpm.apaId = apa.id
        WHERE 1 = 1
        <if test="adId != null and adId != ''">
            AND adpm.adId = #{adId}
        </if>
        <if test="spId != null and spId != ''">
            AND apa.spId = #{spId}
        </if>
    </select>

    <delete id="deleteProductAppForDevice">
        DELETE adpm
        FROM aiops_device_product_mapping adpm
        INNER JOIN aiops_product_app apa ON adpm.apaId = apa.id
        WHERE 1 != 1
        <trim prefix=" OR (" prefixOverrides="AND" suffix=")">
            <if test="adId != null and adId != ''">
                AND adpm.adId = #{adId}
            </if>
            <if test="spId != null and spId != ''">
                AND apa.spId = #{spId}
            </if>
            <if test="adpmIdList != null">
                <foreach collection="adpmIdList" item="item" open=" AND adpm.id IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
        </trim>
    </delete>

    <delete id="deleteDeviceCollectConfigByMap">
        DELETE adcd
        FROM aiops_device_collect adc
        INNER JOIN aiops_device_collect_detail adcd ON adc.id = adcd.adcId
        INNER JOIN aiops_collect_config acc ON adcd.accId = acc.id
        <if test="isAppServiceUnbind != null and isAppServiceUnbind">
            AND acc.isDeletedByAppServiceUnbind = 1
        </if>
        INNER JOIN aiops_collect_app_mapping acam ON acc.id = acam.accId
        INNER JOIN aiops_product_app apa ON acam.apaId = apa.id
        <if test="spId != null and spId != ''">
            AND apa.spId = #{spId}
        </if>
        INNER JOIN aiops_device_product_mapping adpm ON apa.id = adpm.apaId
        <if test="adId != null and adId != ''">
            AND adpm.adId = #{adId}
        </if>
        <if test="adpmIdList != null">
            <foreach collection="adpmIdList" item="item" open=" AND adpm.id IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
        WHERE 1 != 1
        <trim prefix=" OR (" prefixOverrides="AND" suffix=")">
            <if test="adId != null and adId != ''">
                AND adc.adId = #{adId}
            </if>
        </trim>
    </delete>

    <select id="getCollectConfigsNotInDevice" resultMap="deviceTypeMapNew">
        SELECT adt.id, adt.code AS deviceType, adt.name AS deviceTypeName,
               acc.id AS cc_id, acc.scopeId AS cc_scopeId, acc.collectCode AS cc_collectCode,
               acc.collectName AS cc_collectName, acc.collectCategory AS cc_collectCategory,
               acc.collectDeviceType AS cc_collectDeviceType
        FROM aiops_device_type adt
        LEFT JOIN aiops_collect_config acc ON (acc.collectDeviceType = adt.code
                                               OR acc.collectDeviceType LIKE CONCAT(adt.code, ';%')
                                               OR acc.collectDeviceType LIKE CONCAT('%;', adt.code, ';%')
                                               OR acc.collectDeviceType LIKE CONCAT('%;', adt.code))
        WHERE acc.isEnable = 1
        <if test="scopeId != null and scopeId != ''">
            AND acc.scopeId IN ${scopeId}
        </if>
        <if test="collectItem != null and collectItem != ''">
            AND (acc.collectCode LIKE "%"#{collectItem}"%"
                 OR acc.collectName LIKE "%"#{collectItem}"%")
        </if>
        <if test="deviceType != null and deviceType.size() != 0">
            <foreach collection="deviceType" item="item" open="AND ((" separator=") OR (" close="))">
                acc.collectDeviceType = #{item}
                OR acc.collectDeviceType LIKE CONCAT(#{item}, ';%')
                OR acc.collectDeviceType LIKE CONCAT('%;', #{item}, ';%')
                OR acc.collectDeviceType LIKE CONCAT('%;', #{item})
            </foreach>
        </if>
        <if test="devicePlatform != null and devicePlatform != ''">
            AND ( acc.platform LIKE "%"#{devicePlatform}"%" )
        </if>
        <choose>
            <!--收集项勾选列表呈现的是有购买且有效的租户合约的收集项，另一方面看来就是无效的租户合约的模组收集项不会显示-->
            <when test="validSamcdIdList != null">
                <foreach collection="validSamcdIdList" item="item"
                         open="AND EXISTS (SELECT 1 FROM aiops_module_collect_mapping amcm WHERE
                               (acc.id = amcm.accId OR acc.sourceAccId = amcm.accId)
                               AND samcdId IN(" separator=", " close="))">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND 1 != 1
            </otherwise>
        </choose>
        ORDER BY adt.CODE,acc.collectCode
    </select>

    <select id="getProductAppForDevice" resultType="com.digiwin.escloud.aioitms.device.model.DeviceProductMapping">
        SELECT adpm.*, apa.modelCode
        FROM aiops_device_product_mapping adpm
        LEFT JOIN aiops_product_app apa ON adpm.apaId = apa.id
        <where>
            <if test="adId != null and adId > 0">
                AND adpm.adId = #{adId}
            </if>
            <if test="deviceId != null and deviceId != ''">
                AND adpm.deviceId = #{deviceId}
            </if>
            <if test="appId != null and appId != ''">
                AND adpm.appId = #{appId}
            </if>
            <if test="apaId != null and apaId > 0">
                AND adpm.apaId = #{apaId}
            </if>
            <if test="spId != null and spId > 0">
                AND apa.spId = #{spId}
            </if>
            <if test="modelCode != null and modelCode != ''">
                AND apa.rootModelCode = #{modelCode}
            </if>
        </where>
    </select>

    <insert id="addProductAppForDevice" parameterType="com.digiwin.escloud.aioitms.device.model.DeviceProductMapping">
        insert into aiops_device_product_mapping(id, apaId, adId, deviceId, appId)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.apaId}, #{item.adId}, #{item.deviceId}, #{item.appId})
        </foreach>
    </insert>

    <select id="selectApaModelCodeByApaIdList" resultType="java.util.Map">
        SELECT id AS apaId, modelCode
        FROM aiops_product_app
        WHERE 1=1
        <if test="apaIdList != null">
            <foreach collection="apaIdList" item="item" open=" AND id IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectApaSpIdByApaIdList" resultType="java.util.Map">
        SELECT id AS apaId, spId
        FROM aiops_product_app
        WHERE 1=1
        <if test="apaIdList != null">
            <foreach collection="apaIdList" item="item" open=" AND id IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getDeviceCollect" resultType="com.digiwin.escloud.aioitms.device.model.DeviceCollect">
        select * from aiops_device_collect a where a.deviceId = #{deviceId}
    </select>

    <insert id="addDeviceCollect" parameterType="com.digiwin.escloud.aioitms.device.model.DeviceCollect">
        insert ignore into aiops_device_collect(id, adId, deviceId)
        values(#{id}, #{adId}, #{deviceId})
    </insert>

    <delete id="deleteDeviceCollectDetail">
        delete from aiops_device_collect_detail where adcId =#{adcId}
    </delete>

    <insert id="batchDeviceCollectDetail">
        insert into aiops_device_collect_detail(id, adcId, accId, isEnable, collectName, aiId, adimId,
                                                execParamsVersion, execParamsContent)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.adcId}, #{item.accId}, #{item.isEnable}, #{item.collectName},
             #{item.aiId}, #{item.adimId}, #{item.execParamsVersion}, #{item.execParamsContent})
        </foreach>
    </insert>

    <insert id="batchDeviceCollectWarning" >
        insert into aiops_device_collect_warning(id, adcdId, acwId ,isWarningEnable)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.adcdId}, #{item.acwId}, #{item.isWarningEnable})
        </foreach>
    </insert>

    <select id="selectEidByDeviceId" resultType="java.lang.Long">
        SELECT eid
        FROM aiops_device
        WHERE deviceId = #{deviceId}
    </select>

    <insert id="addCollectDetailForDevice" >
        insert into aiops_device_collect_detail(id, adcId, accId, collectName, isEnable, aiId, adimId)
        values (#{id}, #{adcId}, #{accId}, #{collectName}, #{isEnable}, #{aiId}, #{adimId})
    </insert>

    <select id="selectCollectNameExistInDeviceCollectDetail" resultType="java.lang.Boolean">
        SELECT CASE WHEN COUNT(*) > 0 THEN TRUE ELSE FALSE END
        FROM aiops_device_collect adc
        INNER JOIN aiops_device_collect_detail adcd ON adc.id = adcd.adcId AND adc.deviceId = #{deviceId}
        WHERE adcd.collectName = #{collectName}
              AND adcd.id != #{id}
              AND (adcd.aiId IS NULL OR adcd.aiId = (SELECT aiId FROM aiops_device_collect_detail WHERE id = #{id}))
    </select>

    <update id="updateCollectDetailForDevice">
        update aiops_device_collect_detail
        set collectName = #{collectName}
        where id = #{id}
    </update>

    <delete id="deleteCollectWarningForDevice">
        DELETE adcw, adwnm
        FROM aiops_device_collect_warning adcw
        LEFT JOIN aiops_device_warning_notify_mapping adwnm ON adcw.id = adwnm.adcwId
        WHERE adcw.adcdId = #{adcdId}
    </delete>

    <delete id="deleteDeviceCollectWarningById">
        DELETE adcw, adwnm
        FROM aiops_device_collect_warning adcw
        LEFT JOIN aiops_device_warning_notify_mapping adwnm ON adcw.id = adwnm.adcwId
        WHERE adcw.id = #{adcwId}
    </delete>

    <delete id="deleteCollectDetailForDevice">
        delete from aiops_device_collect_detail
        where id = #{id}
    </delete>

    <update id="updateDeviceCollectInterval" parameterType="com.digiwin.escloud.aioitms.device.model.DeviceCollectDetail">
        update aiops_collect_config a
        set a.interval = #{interval}
        where a.id = #{accId}
    </update>

    <select id="selectCollectConfigCollectorContent" parameterType="Long" resultType="String">
        select collectorContent
        from aiops_collect_config a
        where a.id = #{accId}
    </select>

    <update id="updateCollectConfigCollectorContent">
        update aiops_collect_config acc
        set acc.collectorContent = #{collectorContent},
            acc.cron = #{cron}
        where acc.id = #{accId}
    </update>

    <update id="updateDeviceCollectDetailEnable" parameterType="com.digiwin.escloud.aioitms.device.model.DeviceCollectDetail">
        update aiops_device_collect_detail a
        set a.isEnable = #{isEnable}
        where a.id = #{id}
    </update>

    <update id="updateDeviceCollectDetailAiIdAndAdimId" parameterType="com.digiwin.escloud.aioitms.device.model.DeviceCollectDetail">
        update aiops_device_collect_detail a
        set a.aiId = #{aiId}, a.adimId = #{adimId}
        where a.id = #{id}
    </update>

    <update id="updateDeviceExecParamsContent">
        update aiops_device_collect_detail
        set execParamsVersion = #{execParamsVersion}, execParamsContent = #{execParamsContent}
        where id = #{adcdId}
    </update>

    <update id="batchUpdateDeviceExecParamsContent">
        UPDATE aiops_device_collect_detail
        SET id = id
        <if test="adcdList != null">
            <foreach collection="adcdList" item="item" open=", execParamsVersion = CASE id WHEN "
                     separator=" WHEN " close=" END">
                #{item.id} THEN #{item.execParamsVersion}
            </foreach>
        </if>
        <if test="adcdList != null">
            <foreach collection="adcdList" item="item" open=", execParamsContent = CASE id WHEN "
                     separator=" WHEN " close=" END">
                #{item.id} THEN #{item.execParamsContent}
            </foreach>
        </if>
        WHERE 1 != 1
        <if test="adcdList != null">
            <foreach collection="adcdList" item="item" open=" OR id IN(" separator=", " close=")">
                #{item.id}
            </foreach>
        </if>
    </update>

    <update id="modifyCollectDetailEnableForDevice" parameterType="com.digiwin.escloud.aioitms.device.model.DeviceCollectDetail">
        update aiops_device_collect_detail a set a.isEnable = #{isEnable} where a.id = #{adcdId}
    </update>

    <update id="modifyCollectConfigWarningEnableForDevice" parameterType="com.digiwin.escloud.aioitms.device.model.DeviceCollectDetail">
        update aiops_device_collect_warning a set a.isWarningEnable = #{isWarningEnable} where a.adcdId = #{adcdId}
    </update>
    <select id="selectAdcwIdByMap" resultType="java.lang.Long">
        SELECT id FROM aiops_device_collect_warning
        WHERE acwId = #{acwId} AND adcdId = #{adcdId}
    </select>
    <update id="modifyWarningEnableForDeviceWarning" parameterType="com.digiwin.escloud.aioitms.device.model.DeviceCollectWarning">
        update aiops_device_collect_warning a set a.isWarningEnable = #{isWarningEnable} where a.acwId = #{acwId} and a.adcdId = #{adcdId}
    </update>

    <update id="modifyWarningEnableForDeviceWarningBatch" parameterType="java.util.List">
        UPDATE aiops_device_collect_warning a
        <set>
            a.isWarningEnable = #{isWarningEnable}
        </set>
        WHERE
        a.id IN
        <foreach item="item" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="getDeviceCollectName" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM aiops_device_collect_detail adcd
        WHERE adcd.adcId = #{adcId} AND adcd.collectName = #{collectName}
        <if test="aiId != null and aiId > 0">
            AND adcd.aiId = #{aiId}
        </if>
    </select>
    <select id="selectDeviceCollects" resultType="java.util.Map">
        SELECT adc.deviceId, adcd.id AS adcdId, adcd.accId
        FROM aiops_device_collect_detail adcd
        INNER JOIN aiops_device_collect adc ON adcd.adcId = adc.id
        <if test="excludeScopeId != null and excludeScopeId != ''">
            INNER JOIN aiops_collect_config acc ON adcd.accId = acc.id AND acc.scopeId != #{excludeScopeId}
        </if>
        WHERE adcd.accId = #{accId}
    </select>
    <select id="selectDeviceDataSourceListByAdId"
            resultType="com.digiwin.escloud.aioitms.device.model.AiopsKitDeviceDataSource">
        SELECT *,
               CASE WHEN dbType = 'mssql_v2' THEN
                   CASE WHEN IFNULL(dbInstanceName, '') = '' THEN IFNULL(dbIpAddress, '')
                   ELSE CONCAT(IFNULL(dbIpAddress, ''), '\\', IFNULL(dbInstanceName, ''))
                   END
               WHEN dbType = 'oracle' THEN
                   CONCAT(IFNULL(dbIpAddress, ''), ':', IFNULL(dbPort, ''), '\\', IFNULL(dbServerName, ''))
               ELSE CASE WHEN IFNULL(dbPort, '') = '' THEN IFNULL(dbIpAddress, '')
                    ELSE CONCAT(IFNULL(dbIpAddress, ''), ':', IFNULL(dbPort, ''))
                    END
               END AS displayName
        FROM aiops_device_datasource
        WHERE isDelete = 0 AND adId = #{adId}
        <if test="dbType != null and dbType != ''">
            AND dbType = #{dbType}
        </if>
        <if test="dbIdCollection != null">
            <foreach collection="dbIdCollection" item="item" open=" AND dbId IN(" separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="selectDeviceAppIdListByAdId" resultType="com.digiwin.escloud.aioitms.device.model.DeviceAppInfo">
        SELECT adpm.id AS adpmId,
               sp.productCode, sp.productCategory, sp.productName,
               apa.modelCode,
               IFNULL(cm.modelName, '') AS modelName
        FROM aiops_device_product_mapping adpm
        INNER JOIN aiops_product_app apa ON apa.id = adpm.apaId
        LEFT JOIN cmdb_model cm ON cm.modelCode = apa.modelCode
                  AND (apa.modelRelateCode IS NOT NULL AND apa.modelRelateCode != '')
        LEFT JOIN supplier_product sp ON sp.id = apa.spId
        WHERE adId = #{adId}
        ORDER BY apa.modelCode
    </select>

    <select id="selectDeviceWarningNotify" resultMap="deviceWarningNotifyMappingMap">
        SELECT adwnm.*
        <if test="'BY_USER' == notifyCategory">
            ,u.name ,u.telephone
        </if>
        FROM aiops_device_warning_notify_mapping adwnm
        <if test="'BY_USER' == notifyCategory">
            LEFT JOIN user u ON adwnm.sourceId  = u.id
        </if>
        WHERE adcwId = #{adcwId}
              <if test="notifyCategory != null and notifyCategory != ''">
                  AND notifyCategory = #{notifyCategory}
              </if>
    </select>

    <select id="selectDeviceWarningNotifyExist" resultType="java.lang.Boolean">
        SELECT CASE WHEN COUNT(id) > 0 THEN TRUE ELSE FALSE END
        FROM aiops_device_warning_notify_mapping
        WHERE sourceId = #{sourceId}
        <if test="notifyCategory != null and notifyCategory != ''">
            AND notifyCategory = #{notifyCategory}
        </if>
    </select>

    <select id="selectContinueStopSettingByAdcwId" resultType="com.digiwin.escloud.aioitms.collectwarning.model.CollectWarning">
        SELECT acw.continueStopSettingValue, acw.continueStopSettingUnit
        FROM aiops_device_collect_warning adcw
        INNER JOIN aiops_collect_warning acw ON adcw.acwId = acw.id
        WHERE adcw.id = #{adcwId}
    </select>

    <delete id="deleteDeviceWarningNotifyByAdcwIdAndNotifyCategory">
        DELETE FROM aiops_device_warning_notify_mapping
        WHERE adcwId = #{adcwId} AND notifyCategory = #{notifyCategory}
        <if test="deviceWarningNotifyMappingList != null">
            <foreach collection="deviceWarningNotifyMappingList" item="item" open=" AND sourceId NOT IN (" separator="," close=")">
                #{item.sourceId}
            </foreach>
        </if>
    </delete>

    <insert id="batchInsertDeviceWarningNotify">
        INSERT INTO aiops_device_warning_notify_mapping(id, adcwId, notifyCategory, sourceId, isDefault)
        <foreach collection="deviceWarningNotifyMappingList" item="item" open="VALUES(" separator="), (" close=")">
            #{item.id}, #{item.adcwId}, #{item.notifyCategory}, #{item.sourceId}, #{item.isDefault}
        </foreach>
    </insert>

    <delete id="deleteDeviceWarningNotify">
        DELETE FROM aiops_device_warning_notify_mapping
        WHERE adcwId = #{adcwId}
        <if test="notifyCategory != null and notifyCategory != ''">
            AND notifyCategory = #{notifyCategory}
        </if>
        <if test="adwnmId != null and adwnmId > 0">
            AND id = #{adwnmId}
        </if>
        <if test="adwnmIdList != null and adwnmIdList.size() > 0">
            <foreach collection="adwnmIdList" item="item" open="AND id IN (" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </delete>

    <select id="selectAdcwIdListByDefault" resultType="java.lang.Long">
        SELECT adcwId
        FROM aiops_device_warning_notify_mapping
        WHERE notifyCategory = #{notifyCategory} AND isDefault = 1 AND sourceId = #{oriSourceId}
    </select>

    <delete id="deleteAdcwIdListByExistNewDefault">
        DELETE FROM aiops_device_warning_notify_mapping
        WHERE notifyCategory = #{notifyCategory} AND isDefault = 0 AND sourceId = #{sourceId}
        <if test="adcwIdList != null">
            <foreach collection="adcwIdList" item="item" open=" AND adcwId IN (" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </delete>

    <delete id="deleteDeviceWarningNotifyNotDefault">
        DELETE FROM aiops_device_warning_notify_mapping
        WHERE isDefault = 0 AND sourceId = #{sourceId} AND notifyCategory = #{notifyCategory}
    </delete>

    <update id="updateDeviceWarningNotifyDefault">
        UPDATE aiops_device_warning_notify_mapping
        SET sourceId = #{sourceId}
        WHERE isDefault = 1 AND sourceId = #{oriSourceId} AND notifyCategory = #{notifyCategory}
    </update>

    <select id="selectAdcdByMap" resultMap="deviceCollectDetailMap">
        SELECT *
        FROM aiops_device_collect_detail
        WHERE 1 = 1
        <if test="accId != null and accId > 0">
            AND accId = #{accId}
        </if>
        <if test="adcdId != null and adcdId > 0">
            AND id = #{adcdId}
        </if>
    </select>

    <select id="selectDeviceCollectById" resultType="com.digiwin.escloud.aioitms.device.model.DeviceCollect">
        select * from aiops_device_collect where id = #{adcId}
    </select>

    <select id="selectDeviceCollectDetailByAdcId" resultType="com.digiwin.escloud.aioitms.device.model.DeviceCollectDetail">
        SELECT adcd.*,
               acc.collectName AS defaultCollectName, acc.execParamsModelCode
        FROM aiops_device_collect_detail adcd
        LEFT JOIN aiops_collect_config acc ON adcd.accId = acc.id
        WHERE adcd.adcId = #{adcId}
        <if test="accIdList != null">
            <foreach collection="accIdList" item="item" open=" AND adcd.accId IN(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="execParamsModelCode != null and execParamsModelCode != ''">
            AND acc.execParamsModelCode = #{execParamsModelCode}
        </if>
        <if test="priorityMatchAiId != null and priorityMatchAiId > 0">
            <!--透过优先级影响后续选择执行参数内容，命中的优先选择-->
            ORDER BY IF(adcd.aiId = #{priorityMatchAiId}, 0, 1)
        </if>
    </select>

    <select id="selectDeviceCollectWarningByAdcIdAndAccIdList" resultMap="deviceCollectDetailOneLevelWarningMap">
        SELECT adcd.id, adcd.accId, adcd.collectName,
               acc.collectName AS defaultCollectName, acc.scopeId,
               acw.id AS acw_id, acw.accId AS acw_accId, acw.scopeId AS acw_scopeId, acw.warningCode AS acw_warningCode,
               acw.warningName AS acw_warningName,
               adcw.id AS acw_adcwId
        FROM aiops_device_collect_detail adcd
        INNER JOIN aiops_collect_config acc ON acc.id = adcd.accId AND acc.scopeId = #{defaultScopeId}
        INNER JOIN aiops_collect_warning acw ON adcd.accId = acw.accId AND acw.scopeId = #{defaultScopeId}
        LEFT JOIN aiops_device_collect_warning adcw ON adcd.id = adcw.adcdId
        WHERE adcId = #{adcId}
        <if test="accIdList != null">
            <foreach collection="accIdList" item="item" open=" AND adcd.accId IN(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectDeviceCollectWarningByAdcdIdOrAdcwId" resultMap="baseCollectWarningMap">
        SELECT adcw.id AS adcwId,

               adwnm_g.id AS adwn_mg_id, adwnm_g.notifyCategory AS adwn_mg_notifyCategory,
               adwnm_g.sourceId AS adwn_mg_sourceId,

               adwnm_u.id AS adwn_mu_id, adwnm_u.notifyCategory AS adwn_mu_notifyCategory,
               adwnm_u.sourceId AS adwn_mu_sourceId,

               acw.id, acw.accId, acw.scopeId, acw.warningCode, acw.warningName, acw.momentType, acw.description,
               acw.isWarningEnable, acw.continueStopSettingValue, acw.continueStopSettingUnit, acw.sourceAcwId,

               aws.id aws_id, aws.acwId aws_acwId, aws.levelCode aws_levelCode, aws.isInterval aws_isInterval,
               aws.notUploadWarning aws_notUploadWarning,

               awa.id AS aws_awa_id, awa.awsId AS aws_awa_awsId, awa.analysisWay AS aws_awa_analysisWay,
               awa.value AS aws_awa_value, awa.unit AS aws_awa_unit, awa.hitCondition AS aws_awa_hitCondition,
               awa.hitValue AS aws_awa_hitValue,

               awc.id AS aws_awc_id, awc.awsId AS aws_awc_awsId, awc.modelField AS aws_awc_modelField,
               awc.`each` AS aws_awc_each, awc.eachUnit AS aws_awc_eachUnit, awc.statisticType AS aws_awc_statisticType,
               awc.operator AS aws_awc_operator, awc.abnormalValue AS aws_awc_abnormalValue, awc.logic AS aws_awc_logic,
               awc.sequence AS aws_awc_sequence,

               awnt.id AS aws_awnt_id, awnt.awsId AS aws_awnt_awsId, awnt.templateType AS aws_awnt_templateType,
               awnt.title AS aws_awnt_title, awnt.content AS aws_awnt_content, awnt.gridContent AS aws_awnt_gridContent,
               awnt.suggest AS aws_awnt_suggest, awnt.helpUrl AS aws_awnt_helpUrl
        FROM aiops_device_collect_detail adcd
        INNER JOIN aiops_device_collect_warning adcw ON adcd.id = adcw.adcdId
        LEFT JOIN aiops_device_warning_notify_mapping adwnm_g ON notifyCategory = 'BY_GROUP' AND adcw.id = adwnm_g.adcwId
        LEFT JOIN aiops_device_warning_notify_mapping adwnm_u ON notifyCategory = 'BY_USER' AND adcw.id = adwnm_u.adcwId
        INNER JOIN aiops_collect_warning acw ON adcw.acwId = acw.id AND acw.scopeId = #{defaultScopeId}
        LEFT JOIN aiops_warning_setting aws ON acw.id = aws.acwId
        LEFT JOIN aiops_warning_analysis awa ON acw.id = awa.acwId
        LEFT JOIN aiops_warning_condition awc ON acw.id = awc.acwId
        LEFT JOIN aiops_warning_notify_template awnt ON awnt.awsId = aws.id AND isOffline = 0
        WHERE adcd.id = #{adcdId}
        <if test="adcwId != null and adcwId > 0">
            AND adcw.id = #{adcwId}
        </if>
        <if test="accIdList != null">
            <foreach collection="accIdList" item="item" open=" AND adcd.accId IN(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="findReportDeviceInfo" resultType="com.digiwin.escloud.aioitms.report.model.ReportDeviceInfo">
        SELECT distinct
            ad.eid,ad.deviceId,ad.deviceName,adt.`name` deviceTypeName,adtm.deviceType deviceTypeCode,
            ad.lastCheckInTime > DATE_SUB(now(), INTERVAL 3 DAY) as isOnLine
        from aiops_device ad
         LEFT JOIN aiops_device_type_mapping adtm ON adtm.adId = ad.id
         LEFT JOIN aiops_device_type adt ON adt.code = adtm.deviceType
        WHERE ad.eid = ${eid}
    </select>
    <select id="findAiopsDeviceType" resultType="java.util.Map">
        select * from aiops_device_type;
    </select>

    <select id="selectDeviceTypeList" resultType="java.util.Map">
        SELECT * FROM aiops_device_type ORDER BY id
    </select>

    <select id="selectDeviceTypeOnlineStatistic" resultMap="deviceTypeOnlineStatisticMap">
        /*TODO:因为一台设备可能会挂勾多个设备类型，因此统计数字会虚增，到时候需要考虑挂勾多个时，统计数字算谁的*/
        SELECT IFNULL(adtm.deviceType, 'UNKNOWN') AS deviceType,
               COUNT(ad.id) AS total,
               IFNULL(subOnline.totalOnline, 0) AS totalOnline
        FROM aiops_device ad
        LEFT JOIN aiops_device_type_mapping adtm ON ad.id = adtm.adId
        LEFT JOIN (
            SELECT adtm.deviceType, COUNT(ad.id) AS totalOnline
            FROM aiops_device ad
                     LEFT JOIN aiops_device_type_mapping adtm ON ad.id = adtm.adId
            WHERE ad.eid = #{eid} AND ad.lastCheckInTime >= DATE_SUB(now(), INTERVAL 3 DAY)
            GROUP BY adtm.deviceType
        ) subOnline ON subOnline.deviceType = adtm.deviceType
        WHERE ad.eid = #{eid}
        GROUP BY adtm.deviceType
    </select>

    <select id="selectDeviceOfflineTotal" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM aiops_device ad
        INNER JOIN aiops_device_type_mapping adtm ON ad.id = adtm.adId AND deviceType = 'HOST'
        WHERE ad.eid = #{eid} AND ad.isDeleted = 0 AND ad.lastCheckInTime <![CDATA[<]]> DATE_SUB(now(), INTERVAL 3 DAY)
    </select>

    <select id="selectAdcdSeriesList" resultType="java.util.Map">
        SELECT CONCAT(acc.collectCode, '.', adcd.Id) AS `code`, adcd.collectName AS `name`
        FROM `aiops_device_collect` adc
        INNER JOIN aiops_device_collect_detail adcd ON adc.id = adcd.adcId
        INNER JOIN aiops_collect_config acc ON adcd.accId = acc.Id
        WHERE adc.adId = #{adId}
    </select>

    <update id="updateDeviceIsDeleteStatus">
        UPDATE aiops_device
        SET isDeleted = #{isDeleted}
        WHERE id = #{adId}
    </update>

    <insert id="batchInsertOrUpdateAiopsDeviceInstanceMapping"
            parameterType="com.digiwin.escloud.aioitms.device.model.DeviceInstanceMapping">
        INSERT INTO aiops_device_instance_mapping(id, adId, deviceId, aiId, hasAddedAiopsInstance)
        <foreach collection="list" item="item" open="VALUES(" separator="), (" close=")">
            #{item.id}, #{item.adId}, #{item.deviceId}, #{item.aiId}, #{item.hasAddedAiopsInstance}
        </foreach>
        ON DUPLICATE KEY UPDATE hasAddedAiopsInstance = VALUES(hasAddedAiopsInstance)
    </insert>

    <insert id="batchInsertAiopsDeviceInstanceMapping">
        INSERT INTO aiops_device_instance_mapping(id, adId, deviceId, aiId, hasAddedAiopsInstance)
        <foreach collection="adimList" item="item" open="VALUES(" separator="), (" close=")">
            #{item.id}, #{item.adId}, #{item.deviceId}, #{item.aiId}, #{item.hasAddedAiopsInstance}
        </foreach>
    </insert>

    <insert id="batchUpdateAiopsDeviceInstanceMapping">
        UPDATE aiops_device_instance_mapping
        SET id = id
        <if test="adimList != null">
            <foreach collection="adimList" item="item"
                     open=" , hasAddedAiopsInstance = CASE id WHEN " separator=" WHEN " close="END">
                #{item.id} THEN #{item.hasAddedAiopsInstance}
            </foreach>
        </if>
        WHERE 1 != 1
        <if test="adimList != null">
            <foreach collection="adimList" item="item"
                     open=" OR id IN(" separator=", " close=")">
                #{item.id}
            </foreach>
        </if>
    </insert>

    <update id="updateAdimHasAddedAiopsInstance">
        UPDATE aiops_device_instance_mapping
        SET hasAddedAiopsInstance = #{hasAddedAiopsInstance}
        WHERE id = #{adimId}
    </update>

    <select id="selectDeviceInstanceMappingByAicList"
            resultType="com.digiwin.escloud.aioitms.device.model.DeviceInstanceMapping">
        SELECT *
        FROM aiops_device_instance_mapping
        WHERE 1=1
        <if test="aicList != null">
            <foreach collection="aicList" item="item" open="AND ((" separator=") OR (" close="))">
                deviceId = #{item.sourceDeviceId} AND aiId = #{item.aiId}
            </foreach>
        </if>
    </select>

    <select id="selectAdcdListByAiId" resultMap="baseDeviceCollectDetailMap">
        SELECT adcd.*, adc.deviceId
        FROM aiops_device_collect_detail adcd
        LEFT JOIN aiops_device_collect adc ON adcd.adcId = adc.id
        WHERE aiId = #{aiId}
        <choose>
            <when test="adimId != null and adimId > 0">
                AND adimId = #{adimId}
            </when>
            <otherwise>
                <if test="allowNoAdimId != null and !allowNoAdimId">
                    AND adimId IS NULL
                </if>
            </otherwise>
        </choose>
    </select>

    <update id="updateDeviceDeleteStatus">
        UPDATE aiops_device ad
        <if test="isDeleted and deviceIdList != null">
            left join aiops_device_datasource dd on ad.deviceId=dd.deviceId
        </if>
        SET ad.isDeleted = #{isDeleted}
        <if test="isDeleted and deviceIdList != null">
            , dd.isDelete = #{isDeleted}
        </if>
        WHERE 1 != 1
        <if test="deviceIdList != null">
            <foreach collection="deviceIdList" item="item" open=" OR ad.deviceId IN(" separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </update>
    <delete id="deleteTpInstance" parameterType="java.lang.Long">
        DELETE atmi, ai FROM aiops_tp_module_instance atmi
        INNER JOIN aiops_instance ai ON ai.aiopsItemId = atmi.aiopsItemId
        WHERE
        <foreach collection="aiopsItemIdList" item="item" open="  atmi.aiopsItemId IN (" separator=", " close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteAdimAdcdAdcwAdwnmByMap">
        <!--收集项(acc)相关表是透过外键去做清理的，因此这里只删除acc，其他外键关联表纪录会自动删除-->
        DELETE adim, adcd, adcw, adwnm, acc
        <if test="needDeleteAiopsInstance != null and needDeleteAiopsInstance">
            , ai
        </if>
        FROM aiops_device_instance_mapping adim
        INNER JOIN aiops_instance ai ON adim.aiId = ai.id
        <if test="aiopsItemList != null">
            <foreach collection="aiopsItemList" item="item" open=" AND ai.aiopsItem IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
        <if test="aiopsItemIdList != null">
            <foreach collection="aiopsItemIdList" item="item" open=" AND ai.aiopsItemId IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
        <if test="aiopsItemTypeNotDevice != null and aiopsItemTypeNotDevice">
            AND ai.aiopsItemType != 'DEVICE'
        </if>
        LEFT JOIN aiops_device_collect_detail adcd ON ai.id = adcd.aiId AND adim.id = adcd.adimId
        LEFT JOIN aiops_device_collect_warning adcw ON adcd.id = adcw.adcdId
        LEFT JOIN aiops_device_warning_notify_mapping adwnm ON adcw.id = adwnm.adcwId
        LEFT JOIN aiops_collect_config acc ON adcd.accId = acc.id AND acc.scopeId = #{deviceId}
        WHERE adim.deviceId = #{deviceId}
    </delete>

    <select id="selectDeviceCollectDetailByAiopsItemId" resultMap="moduleCollectLayeredByDeviceMap">
        SELECT IFNULL(amcm.samclId, 0) AS samclId,
               amcm.id AS mcm_id, amcm.samcdId AS mcm_samcdId, amcm.samclId AS mcm_samclId,
               CASE WHEN adcd.accId IS NULL THEN FALSE ELSE TRUE END mcm_isMappedToDevice,
               adcd.adcId AS mcm_adcId, adcd.id AS mcm_adcdId, adcd.accId AS mcm_accId,
               adcd.collectName AS mcm_collectName, adcd.isEnable AS mcm_isEnable,
               adcd.execParamsContent AS mcm_execParamsContent,
               acc.execParamsModelCode AS mcm_execParamsModelCode,
               acc.uploadDataModelCode AS mcm_uploadDataModelCode,
               acc.collectorModelCode AS mcm_collectorModelCode,
               acc.collectType AS mcm_collectType, acc.cron AS mcm_cron,
               acc.interval AS mcm_interval, acc.scopeId AS mcm_scopeId, acc.sourceAccId AS mcm_sourceAccId,
               acc.runtimeVersion AS mcm_runtimeVersion,
               (CASE WHEN IFNULL(ai.aiopsAuthStatus, 'UNAUTH') IN ('NONE', 'AUTHED')
                   AND EXISTS(SELECT 1 FROM aiops_instance
                              WHERE aiopsItemType = 'DEVICE' AND aiopsItemId = #{deviceId}
                                AND aiopsAuthStatus = 'AUTHED') THEN TRUE
                ELSE FALSE END) AS mcm_isValid,
               ai.aiopsItem AS mcm_aiopsItem, ai.aiopsItemId AS mcm_aiopsItemId,
               ai.aiopsItem AS mcm_oriAiopsItem, ai.aiopsItemId AS mcm_oriAiopsItemId,
               (IFNULL(ai.execParamsModelCode, '') = '' || acc.isCustomizableExecParam = true) AS mcm_isCustomizableExecParam /* 多一個參數來判, 前端是否可以打開執行參數的修改畫面*/
        FROM aiops_device_instance_mapping adim
        INNER JOIN aiops_instance ai ON adim.aiId = ai.id AND ai.aiopsItemId = #{aiopsItemId}
        INNER JOIN aiops_device_collect_detail adcd ON ai.id = adcd.aiId AND adim.id = adcd.adimId
        INNER JOIN aiops_collect_config acc ON adcd.accId = acc.id
        INNER JOIN aiops_module_collect_mapping amcm ON ai.samcdId = amcm.samcdId
            AND (acc.id = amcm.accId OR acc.sourceAccId = amcm.accId)
        WHERE adim.deviceId = #{deviceId}
              AND (CASE ai.aiopsItemType WHEN 'PRODUCT_APP' THEN
                      EXISTS(SELECT 1 FROM aiops_device_product_mapping adpm
                             INNER JOIN aiops_collect_app_mapping acam ON adpm.apaId = acam.apaId
                             WHERE adpm.appId = #{aiopsItemId} AND acam.accId = amcm.accId)
                   ELSE TRUE END)
        ORDER BY mcm_collectName
    </select>

    <select id="selectDeviceAiopsInstanceByAiopsItemType" resultMap="deviceAiopsItemInstance">
        SELECT ai2.code, ai2.name, ai2.name_CN, ai2.name_TW,
               ai.eid AS dai_eid, ai.tmcdId AS dai_tmcdId, ai.samcdId AS dai_samcdId,
               ai.aiopsAuthStatus AS dai_aiopsAuthStatus,
               aigm.aiopsItemGroup AS dai_aiopsItemGroup,
               ai.aiopsItemType AS dai_aiopsItemType,
               ai.aiopsItem AS dai_aiopsItem,
               ai.aiopsItemId AS dai_aiopsItemId,
               ai.execParamsModelCode AS dai_execParamsModelCode,
               (CASE ai.aiopsItemType WHEN 'DATABASE' THEN
                    CASE WHEN `add`.dbType = 'mssql_v2' THEN
                        CASE WHEN IFNULL(`add`.dbInstanceName, '') = '' THEN
                            CONCAT(IFNULL(`add`.dbIpAddress, ''), ' (', IFNULL(`add`.dbDisplayName, ''), ')')
                        WHEN IFNULL(`add`.dbDisplayName, '') = '' THEN
                            CONCAT(IFNULL(`add`.dbIpAddress, ''), '\\', IFNULL(`add`.dbInstanceName, ''))
                        ELSE CONCAT(IFNULL(`add`.dbIpAddress, ''), '\\', IFNULL(`add`.dbInstanceName, ''),
                            ' (', IFNULL(`add`.dbDisplayName, ''), ')')
                        END
                    WHEN `add`.dbType = 'oracle' THEN
                        CASE WHEN IFNULL(`add`.dbDisplayName, '') = '' THEN
                            CONCAT(IFNULL(`add`.dbIpAddress, ''), ':', IFNULL(`add`.dbPort, ''), '\\',
                            IFNULL(`add`.dbServerName, ''))
                        ELSE CONCAT(IFNULL(`add`.dbIpAddress, ''), ':', IFNULL(`add`.dbPort, ''), '\\',
                             IFNULL(`add`.dbServerName, ''), ' (', IFNULL(`add`.dbDisplayName, ''), ')')
                        END
                    ELSE CASE WHEN IFNULL(`add`.dbPort, '') = '' THEN
                            CASE WHEN IFNULL(`add`.dbDisplayName, '') = '' THEN IFNULL(`add`.dbIpAddress, '')
                            ELSE CONCAT(IFNULL(`add`.dbIpAddress, ''), ' (', IFNULL(`add`.dbDisplayName, ''), ')')
                            END
                         ELSE CASE WHEN IFNULL(`add`.dbDisplayName, '') = '' THEN
                                  CONCAT(IFNULL(`add`.dbIpAddress, ''), ':', IFNULL(`add`.dbPort, ''))
                              ELSE CONCAT(IFNULL(`add`.dbIpAddress, ''), ':', IFNULL(`add`.dbPort, ''),
                                  ' (', IFNULL(`add`.dbDisplayName, ''), ')')
                              END
                         END
                    END
                WHEN 'SNMP' THEN
                    CONCAT(IFNULL(asi.snmpName, ''), ' (', IFNULL(asi.snmpIpAddress, ''), ':',
                        IFNULL(asi.snmpPort, ''), ')')
                WHEN 'HTTP' THEN
                    CONCAT(IFNULL(ahi.apiDeviceName, ''), ' (', IFNULL(ahi.apiAddress, ''), ')')
                WHEN 'PRODUCT_APP' THEN
                    apa.modelCode
                WHEN 'BACKUP' THEN
                    absi.backupSoftwareDisplayName
                WHEN 'EDR' THEN
                    aei.edrDeviceName
                WHEN 'SMART_METER' THEN
                    asmi.smartMeterDeviceName
                WHEN 'CLOUD_BACKUP' THEN
                    aeci.serviceCode
                WHEN 'TP_PRODUCT_INTEGRATION' THEN
                    atmi.tpModule
                WHEN 'DATA_SRV' then
                    apa.modelCode
                WHEN 'APP_AUTO_UPDATE' then
                    ai2.name
                ELSE
                    'UNKNOWN'
                END) AS dai_displayName,
            CASE ai.aiopsItemType
                WHEN 'PRODUCT_APP' THEN 1
                WHEN 'DATA_SRV' THEN 1
                ELSE 0 END AS dai_findDisplayNameByModel
        FROM aiops_device_instance_mapping adim
        INNER JOIN aiops_instance ai ON adim.aiId = ai.id
        <if test="aiopsItemType != null and aiopsItemType != ''">
            AND ai.aiopsItemType = #{aiopsItemType}
        </if>
        INNER JOIN aiops_item ai2 ON ai.aiopsItem = ai2.code
        INNER JOIN aiops_item_group_mapping aigm ON ai.aiopsItem = aigm.aiopsItem
        <if test="aiopsItemGroupList != null and aiopsItemGroupList.size()>0">
            <foreach collection="aiopsItemGroupList" item="item" open=" AND aigm.aiopsItemGroup IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>

        LEFT JOIN aiops_device_datasource `add` ON ai.aiopsItemType = 'DATABASE'
            AND ai.aiopsItemId = `add`.dbId AND `add`.deviceId = #{deviceId}
        LEFT JOIN aiops_snmp_instance asi ON ai.aiopsItemType = 'SNMP' AND ai.aiopsItemId = asi.snmpId
        LEFT JOIN aiops_http_instance ahi ON ai.aiopsItemType = 'HTTP' AND ai.aiopsItemId = ahi.apiCollectId
        LEFT JOIN aiops_tp_module_instance atmi ON ai.aiopsItemType = 'TP_PRODUCT_INTEGRATION' AND ai.aiopsItemId = atmi.aiopsItemId
        LEFT JOIN (
            SELECT adpm.adId, adpm.appId, apa.modelCode
            FROM aiops_device_product_mapping adpm
            INNER JOIN aiops_product_app apa ON adpm.apaId = apa.id
        ) apa ON ai.aiopsItemType in ('PRODUCT_APP', 'DATA_SRV') AND adim.adId = apa.adId AND ai.aiopsItemId = apa.appId
        LEFT JOIN aiops_backup_software_instance absi ON ai.aiopsItemType = 'BACKUP'
            AND ai.aiopsItemId = absi.backupSoftwareId
        LEFT JOIN aiops_edr_instance aei ON ai.aiopsItemType = 'EDR' AND ai.aiopsItemId = aei.edrId
        LEFT JOIN aiops_smart_meter_instance asmi ON ai.aiopsItemType = 'SMART_METER'
            AND ai.aiopsItemId = asmi.smartMeterId
        LEFT JOIN aiops_eai_cloud_instance aeci ON ai.aiopsItemType = 'CLOUD_BACKUP'
            AND ai.aiopsItemId = aeci.eaiCloudId
        WHERE adim.deviceId = #{deviceId}
        ORDER BY ai2.id, dai_displayName
    </select>

    <select id="selectDeviceAiopsItemSetting"
            resultType="com.digiwin.escloud.aioitms.device.model.DeviceAiopsItemSetting">
        SELECT main.*,
               aigm.aiopsItemGroup,
               ai2.name AS aiopsItemName, ai2.name_CN AS aiopsItemName_CN, ai2.name_TW AS aiopsItemName_TW
        FROM (
            SELECT asi.snmpName AS deviceName, asi.snmpIpAddress AS ipAddress, asi.snmpPort AS port,
                   asi.snmpPlacementPoint AS placementPoint,
                   ai.aiopsItemType, ai.aiopsItem, ai.aiopsItemId, ai.aiopsAuthStatus
            FROM aiops_device_instance_mapping adim
            INNER JOIN aiops_instance ai ON adim.aiId = ai.id AND ai.aiopsItemType = 'SNMP'
            INNER JOIN aiops_snmp_instance asi ON ai.aiopsItemId = asi.snmpId
            WHERE adim.deviceId = #{deviceId}
            UNION ALL
            SELECT ahi.apiDeviceName AS deviceName, ahi.apiAddress AS ipAddress, '' AS port,
                   ahi.apiDevicePlace AS placementPoint,
                   ai.aiopsItemType, ai.aiopsItem, ai.aiopsItemId, ai.aiopsAuthStatus
            FROM aiops_device_instance_mapping adim
            INNER JOIN aiops_instance ai ON adim.aiId = ai.id AND ai.aiopsItemType = 'HTTP'
            INNER JOIN aiops_http_instance ahi ON ai.aiopsItemId = ahi.apiCollectId
            WHERE adim.deviceId = #{deviceId}
        ) main
        INNER JOIN aiops_item ai2 ON main.aiopsItem = ai2.code
        INNER JOIN aiops_item_group_mapping aigm ON main.aiopsItem = aigm.aiopsItem
        INNER JOIN aiops_item_group aig ON aigm.aiopsItemGroup = aig.code
        <where>
            <if test="aiopsItemGroup != null and aiopsItemGroup != ''">
                AND aigm.aiopsItemGroup = #{aiopsItemGroup}
            </if>
            <if test="aiopsItemType != null and aiopsItemType != ''">
                AND main.aiopsItemType = #{aiopsItemType}
            </if>
        </where>
    </select>

    <select id="selectDeviceAiopsInstanceCollectByAiopsItemId" resultMap="moduleCollectLayeredMap">
        <!--查询运维项目可添加收集项列表，需要包含以下两种场景
            1.已经挂到设备上
            2.尚未挂到设备上
            其中有可能挂到设备上，但是其实合约已经过期了，应该要能查出来，但是isValid为false-->
        SELECT IFNULL(amcm.samclId, 0) AS samclId,
               amcm.id AS mcm_id, amcm.samcdId AS mcm_samcdId, amcm.samclId AS mcm_samclId,
               mainAcc.adcId AS mcm_adcId, mainAcc.adcdId AS mcm_adcdId, mainAcc.isEnable AS mcm_isEnable,
               mainAcc.execParamsContent AS mcm_execParamsContent,
               mainAcc.execParamsModelCode AS mcm_execParamsModelCode,
               mainAcc.accId AS mcm_accId, mainAcc.scopeId AS mcm_scopeId, mainAcc.sourceAccId AS mcm_sourceAccId,
               mainAcc.collectName AS mcm_collectName,
               mainAcc.isMappedToDevice AS mcm_isMappedToDevice, mainAcc.isValid AS mcm_isValid
        FROM aiops_instance ai
        INNER JOIN aiops_device_instance_mapping adim ON ai.id = adim.aiId
        INNER JOIN aiops_device ad ON adim.adId = ad.id AND ad.deviceId = #{deviceId}
        INNER JOIN aiops_module_collect_mapping amcm ON ai.samcdId = amcm.samcdId
        <choose>
            <!--收集项勾选列表呈现的是有购买且有效的租户合约的收集项，另一方面看来就是无效的租户合约的模组收集项不会显示-->
            <when test="validSamcdIdList != null and validSamcdIdList.size() > 0">
                <foreach collection="validSamcdIdList" item="item"
                         open="AND amcm.samcdId IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND 1 != 1
            </otherwise>
        </choose>
        LEFT JOIN (
            SELECT 1 AS isMappedToDevice, IFNULL(acc.sourceAccId, acc.id) AS curAccId, acc.execParamsModelCode,
                   acc.id AS accId, acc.scopeId, adcd.collectName, acc.platform, acc.collectDeviceType,
                   acc.sourceAccId, adcd.id AS adcdId, adcd.adcId AS adcId, adcd.execParamsContent,
                   adcd.isEnable, amcm.id IS NOT NULL isValid
            FROM aiops_device_instance_mapping adim
            INNER JOIN aiops_device_collect_detail adcd ON adim.id = adcd.adimId
            INNER JOIN aiops_instance ai ON adim.aiId = ai.id AND adcd.aiId = ai.id AND ai.aiopsItemId = #{aiopsItemId}
            INNER JOIN aiops_collect_config acc ON adcd.accId = acc.id
            LEFT JOIN aiops_module_collect_mapping amcm ON IFNULL(acc.sourceAccId, acc.id) = amcm.accId
            <choose>
                <!--收集项勾选列表呈现的是有购买且有效的租户合约的收集项，另一方面看来就是无效的租户合约的模组收集项不会显示-->
                <when test="validSamcdIdList != null and validSamcdIdList.size() > 0">
                    <foreach collection="validSamcdIdList" item="item"
                             open="AND amcm.samcdId IN(" separator=", " close=")">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    AND 1 != 1
                </otherwise>
            </choose>
            WHERE adim.deviceId = #{deviceId}
            UNION ALL
            SELECT 0 AS isMappedToDevice, acc.id AS curAccId, acc.execParamsModelCode,
                   acc.id AS accId, acc.scopeId, acc.collectName, acc.platform, acc.collectDeviceType,
                   NULL AS sourceAccId, NULL AS adcdId, NULL AS adcId, NULL AS execParamsContent,
                   amcm.isEnable, amcm.id IS NOT NULL isValid
            FROM aiops_collect_config acc
            INNER JOIN aiops_module_collect_mapping amcm ON acc.id = amcm.accId
            <choose>
                <!--收集项勾选列表呈现的是有购买且有效的租户合约的收集项，另一方面看来就是无效的租户合约的模组收集项不会显示-->
                <when test="validSamcdIdList != null and validSamcdIdList.size() > 0">
                    <foreach collection="validSamcdIdList" item="item"
                             open="AND amcm.samcdId IN(" separator=", " close=")">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    AND 1 != 1
                </otherwise>
            </choose>
            WHERE NOT EXISTS (
                    SELECT 1
                    FROM aiops_device_instance_mapping adim
                    INNER JOIN aiops_device_collect_detail adcd ON adim.id = adcd.adimId
                    INNER JOIN aiops_instance ai ON adim.aiId = ai.id AND adcd.aiId = ai.id
                        AND ai.aiopsItemId = #{aiopsItemId}
                    INNER JOIN aiops_collect_config acc2 ON adcd.accId = acc2.id
                    WHERE adim.deviceId = #{deviceId} AND IFNULL(acc2.sourceAccId, acc2.id) = acc.id)
                AND EXISTS (
                    SELECT 1
                    FROM aiops_instance ai
                    WHERE ai.aiopsItemId = #{aiopsItemId} AND (CASE ai.aiopsItemType WHEN 'PRODUCT_APP' THEN
                            EXISTS(SELECT 1 FROM aiops_device_product_mapping adpm
                            INNER JOIN aiops_collect_app_mapping acam ON adpm.apaId = acam.apaId
                            WHERE adpm.appId = #{aiopsItemId} AND acam.accId = amcm.accId)
                        ELSE TRUE END))
        ) mainAcc ON amcm.accId = mainAcc.curAccId
        WHERE ai.aiopsItemId = #{aiopsItemId}
              AND (mainAcc.platform = ad.platform
                   OR mainAcc.platform LIKE CONCAT(ad.platform, ';%')
                   OR mainAcc.platform LIKE CONCAT('%;', ad.platform, ';%')
                   OR mainAcc.platform LIKE CONCAT('%;', ad.platform))
              AND EXISTS (
                  SELECT 1
                  FROM aiops_device_type_mapping adtm
                  WHERE adtm.adId = ad.id
                        AND (mainAcc.collectDeviceType = adtm.deviceType
                             OR mainAcc.collectDeviceType LIKE CONCAT(adtm.deviceType, ';%')
                             OR mainAcc.collectDeviceType LIKE CONCAT('%;', adtm.deviceType, ';%')
                             OR mainAcc.collectDeviceType LIKE CONCAT('%;', adtm.deviceType)))
        ORDER BY mcm_collectName
    </select>

    <select id="selectDeviceCollectAccIdByAiId" resultType="java.lang.Long">
        SELECT adcd.accId
        FROM aiops_device_collect_detail adcd
        INNER JOIN aiops_device_collect adc ON adc.deviceId = #{deviceId} AND adcd.adcId = adc.id
        WHERE adcd.aiId = #{aiId}
    </select>

    <select id="selectDeviceMappingAiopsInstanceItemType"
            resultType="com.digiwin.escloud.aioitms.model.instance.AiopsItemType">
        SELECT ait.*
        FROM aiops_device_instance_mapping adim
        INNER JOIN aiops_instance ai ON adim.aiId = ai.id
        INNER JOIN aiops_item_type ait ON ai.aiopsItemType = ait.code
        WHERE adim.deviceId = #{deviceId} AND adim.hasAddedAiopsInstance = 1
        GROUP BY ait.code
        ORDER BY ait.id
    </select>

    <select id="selectDeviceMappingAiopsInstanceItemGroup"
            resultType="com.digiwin.escloud.aioitms.model.instance.AiopsItemGroup">
        SELECT aig.*
        FROM aiops_device_instance_mapping adim
        INNER JOIN aiops_instance ai ON adim.aiId = ai.id
        INNER JOIN aiops_item_group_mapping aigm ON aigm.aiopsItem = ai.aiopsItem
        INNER JOIN aiops_item_group aig ON aigm.aiopsItemGroup = aig.code
        WHERE adim.deviceId = #{deviceId} AND adim.hasAddedAiopsInstance = 1
        GROUP BY aig.code
        ORDER BY aig.id
    </select>

    <select id="selectAiopsItemGroupById"
            resultType="com.digiwin.escloud.aioitms.model.instance.AiopsItemGroup">
        SELECT aig.*
        FROM aiops_item_group aig
        WHERE aig.id = #{id}
    </select>

    <select id="selectAiopsItemGroup"
            resultType="com.digiwin.escloud.aioitms.model.instance.AiopsItemGroup">
        SELECT aig.*
        FROM aiops_item_group aig
    </select>

    <select id="selectExistDeviceInstanceMapping" resultType="java.util.Map">
        SELECT adim.deviceId, adim.aiId,
               ad.deviceName, ad.ipAddress,
               ai.aiopsItem, ai.aiopsItemId
        FROM aiops_device_instance_mapping adim
        LEFT JOIN aiops_device ad ON adim.adId = ad.id
        LEFT JOIN aiops_instance ai ON adim.aiId = ai.id
        WHERE 1 != 1
        <if test="mapList != null">
            <foreach collection="mapList" item="item" open=" OR ((" separator=") OR (" close="))">
                adim.aiId = #{item.aiId} AND adim.deviceId != #{item.deviceId}
            </foreach>
        </if>
    </select>

    <select id="selectCurrentDeviceTypeDeviceList" resultMap="DeviceMap">
        SELECT *
        FROM aiops_device ad
        WHERE ad.eid = #{eid} AND EXISTS(SELECT 1 FROM aiops_device_type_mapping adtm
                                         WHERE adtm.adId = ad.id AND adtm.deviceType = #{deviceType})
       and ad.id not in (
        select mapping.adId from aiops_device_instance_mapping mapping
        LEFT JOIN aiops_instance as a on a.id = mapping.aiId
        where aiopsAuthStatus = 'INVALID'
        group by mapping.adId

        )
        <if test="selfDeviceId != null and selfDeviceId != ''">
            AND ad.deviceId != #{selfDeviceId}
        </if>
    </select>

    <select id="selectDeviceDataSourceByEid" resultMap="deviceDataSourceGroupMap">
        <!--数据库列表需要考虑几点
            1.数据库备注根据所有关联到设备的数据库设定的连接名根据分号(;)拼接而成(排除地端已删除的)
            2.设备Ip命中数据库Ip优先选择(0_dbId_db.id跟1_dbId_db.id取最小的就会得到优先的那笔，或者唯一的一笔)
        -->
        SELECT CASE main.add_dbType WHEN 'mssql_v2' THEN 'MSSQL'
               WHEN 'mysql_v2' THEN 'MYSQL'
               ELSE UPPER(main.add_dbType)
               END AS aiopsItem,
               ai2.name, ai2.name_CN, ai2.name_TW,
               main.add_id, main.add_adId, main.add_deviceId, main.add_deviceName, main.add_ipAddress,
               main.add_dbId, main.add_dbType, main.add_dbDisplayName,
               main.add_dbIpAddress, main.add_dbPort,
               main.add_dbServerName, main.add_dbInstanceName, main.add_isDelete, main.add_platform,
               CASE main.add_dbType WHEN 'mssql_v2' THEN
                   CASE WHEN IFNULL(main.add_dbInstanceName, '') = '' THEN IFNULL(main.add_dbIpAddress, '')
                   ELSE CONCAT(IFNULL(main.add_dbIpAddress, ''), '\\', IFNULL(main.add_dbInstanceName, ''))
                   END
               WHEN 'oracle' THEN
                   CONCAT(IFNULL(main.add_dbIpAddress, ''), ':', IFNULL(main.add_dbPort, ''), '\\',
                   IFNULL(main.add_dbServerName, ''))
               ELSE CASE WHEN IFNULL(main.add_dbPort, '') = '' THEN IFNULL(main.add_dbIpAddress, '')
                    ELSE CONCAT(IFNULL(main.add_dbIpAddress, ''), ':', IFNULL(main.add_dbPort, ''))
                    END
               END AS add_displayName, main.add_eid,
               IFNULL(ai.aiopsAuthStatus, 'UNAUTH') AS add_aiopsAuthStatus
        FROM aiops_instance ai
        LEFT JOIN (
            SELECT `add`.id AS add_id, `add`.adId AS add_adId, `add`.deviceId AS add_deviceId,
                   ad.deviceName add_deviceName,ad.ipAddress add_ipAddress,
                   `add`.dbId AS add_dbId, `add`.dbType AS add_dbType,
                   IFNULL(add2.dbDisplayName, '') AS add_dbDisplayName,
                   `add`.dbIpAddress AS add_dbIpAddress, `add`.dbPort AS add_dbPort,
                   `add`.dbServerName AS add_dbServerName,
                   `add`.dbInstanceName AS add_dbInstanceName,
                   `add`.isDelete AS add_isDelete, ad.eid AS add_eid, ad.platform AS add_platform
            FROM aiops_device ad
            INNER JOIN aiops_device_datasource `add` ON ad.id = `add`.adId AND `add`.isDelete = 0
            LEFT JOIN (
                SELECT dbId, GROUP_CONCAT(DISTINCT dbDisplayName SEPARATOR ';') AS dbDisplayName
                FROM aiops_device_datasource
                INNER JOIN aiops_device ad2 ON adId = ad2.id AND ad2.eid = #{eid}
                WHERE isDelete = 0
                GROUP BY dbId
            ) add2 ON `add`.dbId = add2.dbId
            WHERE ad.eid = #{eid}
                AND CONCAT(IF(`add`.dbIpAddress = ad.ipAddress, '0_', '1_'), `add`.dbId, '_', `add`.id) IN (
                    SELECT MIN(CONCAT(IF(add3.dbIpAddress = ad3.ipAddress, '0_', '1_'), add3.dbId, '_', add3.id))
                    FROM aiops_device_datasource add3
                    INNER JOIN aiops_device ad3 ON add3.adId = ad3.id AND ad3.eid = #{eid}
                    WHERE add3.isDelete = 0
                    GROUP BY add3.dbId)
        ) main ON ai.aiopsItemId = main.add_dbId
        LEFT JOIN aiops_item ai2 ON CASE main.add_dbType WHEN 'mssql_v2' THEN 'MSSQL'
                                    WHEN 'mysql_v2' THEN 'MYSQL'
                                    ELSE UPPER(main.add_dbType)
                                    END = ai2.code
        WHERE ai.aiopsItemType = 'DATABASE' AND ai.eid = #{eid}
        ORDER BY main.add_dbType
    </select>

    <select id="selectEidListByDeviceIdList" resultType="java.lang.Long">
        SELECT eid
        FROM aiops_device
        <where>
            <if test="deviceIdList != null">
                <foreach collection="deviceIdList" item="item" open=" OR deviceId IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectDeviceInstanceMappingByDeviceId"
            resultType="java.util.Map">
        SELECT adim.id AS adimId, adim.aiId
        FROM aiops_device_instance_mapping adim
        INNER JOIN aiops_instance ai ON ai.aiopsItemType = 'DEVICE' AND adim.aiId = ai.id
        WHERE adim.deviceId = #{deviceId}
    </select>

    <select id="selectAdimIdByDeviceIdAndAiId" resultType="java.lang.Long">
        SELECT adim.id
        FROM aiops_device_instance_mapping adim
        WHERE adim.deviceId = #{deviceId} AND adim.aiId = #{aiId}
    </select>

    <select id="selectDeviceIdListByEidList" resultType="java.lang.String">
        SELECT deviceId
        FROM aiops_device
        WHERE 1 != 1
        <if test="eidList != null">
            <foreach collection="eidList" item="item" open=" OR eid IN(" separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <update id="updateSourceAdimToTargetByAiopsItemIdList">
        UPDATE aiops_device_instance_mapping adim
        INNER JOIN aiops_instance ai ON adim.aiId = ai.id
        <if test="aiopsItemIdList != null">
            <foreach collection="aiopsItemIdList" item="item" open=" AND ai.aiopsItemId IN(" separator=", " close=")">
                #{item}
            </foreach>
        </if>
        SET adId = #{targetAdId}, deviceId = #{targetDeviceId}
        WHERE adim.deviceId = #{sourceDeviceId}
    </update>

    <update id="updateSourceAdcdToTargetByAiopsItemIdList">
        UPDATE aiops_device_collect_detail adcd
        INNER JOIN aiops_device_collect adc ON adcd.adcId = adc.id
        INNER JOIN aiops_instance ai ON adcd.aiId = ai.id
        <if test="aiopsItemIdList != null">
            <foreach collection="aiopsItemIdList" item="item" open=" AND ai.aiopsItemId IN(" separator=", " close=")">
                #{item}
            </foreach>
        </if>
        SET adcd.adcId = #{targetAdcId}
        WHERE adc.deviceId = #{sourceDeviceId}
    </update>

    <select id="selectDeviceDatasourceMatchDbId" resultType="java.lang.String">
        SELECT dbId
        FROM aiops_device_datasource
        WHERE deviceId = #{deviceId}
        <choose>
            <when test="dbIdCollection != null">
                <foreach collection="dbIdCollection" item="item" open=" AND dbId IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND 1 != 1
            </otherwise>
        </choose>
    </select>

    <delete id="deleteAdcdByAiopsItemIdList">
        DELETE adcd, adcw, adwnm
        FROM aiops_device_collect_detail adcd
        INNER JOIN aiops_device_collect adc ON adcd.adcId = adc.id AND adc.deviceId = #{deviceId}
        INNER JOIN aiops_instance ai ON adcd.aiId = ai.id
        <choose>
            <when test="aiopsItemIdList != null">
                <foreach collection="aiopsItemIdList" item="item"
                         open=" AND ai.aiopsItemId IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND 1 != 1
            </otherwise>
        </choose>
        LEFT JOIN aiops_device_collect_warning adcw ON adcd.id = adcw.adcdId
        LEFT JOIN aiops_device_warning_notify_mapping adwnm ON adcw.id = adwnm.adcwId
    </delete>

    <select id="selectDeviceMappingAiIdList" resultType="java.lang.Long">
        SELECT adim.aiId
        FROM aiops_device_instance_mapping adim
        INNER JOIN aiops_instance ai ON adim.aiId = ai.id AND ai.eid = #{oldEid}
        WHERE adim.deviceId = #{deviceId}
        <if test="hasOtherDeviceMapping != null and hasOtherDeviceMapping">
            AND EXISTS (
                SELECT 1
                FROM aiops_device_instance_mapping adim2
                WHERE adim2.aiId = ai.id AND adim2.deviceId != #{deviceId})
        </if>
    </select>

    <update id="updateOnlyOneDeviceAiopsInstanceEid">
        UPDATE aiops_instance ai
        SET ai.eid = #{eid}
        WHERE ai.eid = #{oldEid}
        <choose>
            <when test="onlyOneDeviceMappingAiIdList != null and onlyOneDeviceMappingAiIdList.size() > 0">
                <foreach collection="onlyOneDeviceMappingAiIdList" item="item"
                         open=" AND ai.id IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND 1 != 1
            </otherwise>
        </choose>
    </update>

    <select id="selectNotAiopsInstanceDeviceList" resultMap="DeviceInfoContainAdcdListMap">
        SELECT ad.*,
               adcd.*
        FROM aiops_device ad
        LEFT JOIN (
            SELECT adim2.id, adim2.adId
            FROM aiops_device_instance_mapping adim2
            INNER JOIN aiops_instance ai2 ON ai2.aiopsItemType = 'DEVICE' AND adim2.aiId = ai2.id
            <if test="eid != null and eid != 0">
                AND ai2.eid = #{eid}
            </if>
        ) adim ON ad.id = adim.adId
        LEFT JOIN (
            SELECT adc.adId,
                   adcd.id AS adcd_id, adcd.adcId AS adcd_adcId,
                   adcd.accId AS adcd_accId, IFNULL(adcd.execParamsContent, '') AS adcd_execParamsContent,
                   IFNULL(acc.execParamsModelCode, '') AS adcd_execParamsModelCode
            FROM aiops_device_collect adc
            LEFT JOIN aiops_device_collect_detail adcd ON adc.id = adcd.adcId
            INNER JOIN aiops_collect_config acc ON adcd.accId = acc.id
            <if test="filterExecParamsModelCodeList != null">
                <foreach collection="filterExecParamsModelCodeList" item="item"
                         open="AND IFNULL(acc.execParamsModelCode, '') NOT IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
        ) adcd ON ad.id = adcd.adId
        WHERE ad.isDeleted = 0 AND adim.id IS NULL
        <if test="eid != null and eid != 0">
            AND ad.eid = #{eid}
        </if>
        <if test="deviceId != null and deviceId != ''">
            AND ad.deviceId = #{deviceId}
        </if>
    </select>

    <update id="updateDeviceCollectDetailAiIdAdimIdByAdcdIdList">
        UPDATE aiops_device_collect_detail
        SET aiId = #{aiId}, adimId = #{adimId}
        WHERE 1 != 1
        <if test="adcdIdList != null">
            <foreach collection="adcdIdList" item="item" open=" OR id IN(" separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </update>

    <select id="selectDeviceDataSourceByMap" resultMap="aiopsKitDeviceDataSourceMap">
        SELECT ad.eid, `add`.*
        FROM aiops_device ad
        INNER JOIN aiops_device_datasource `add` ON ad.id = `add`.adId AND `add`.isDelete = 0
            AND IFNULL(`add`.dbId, '') != ''
        <if test="onlyNotAiopsInstance != null and onlyNotAiopsInstance">
            AND NOT EXISTS(SELECT 1 FROM aiops_instance ai WHERE ai.aiopsItemId = `add`.dbId)
        </if>
        <where>
            <if test="eid != null and eid != 0">
                AND ad.eid = #{eid}
            </if>
            <if test="deviceId != null and deviceId != ''">
                AND ad.deviceId = #{deviceId}
            </if>
            <if test="dbIdList != null">
                <foreach collection="dbIdList" item="item" open=" AND dbId IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dbId != null and dbId != ''">
                AND `add`.dbId = #{dbId}
            </if>
        </where>
    </select>

    <select id="selectNotAiIdAndAdimIdAdcdByMap" resultMap="baseDeviceCollectDetailMap">
        SELECT adcd.id, adcd.adcId,
               IFNULL(adcd.execParamsContent, '') AS execParamsContent,
               IFNULL(acc.execParamsModelCode, '') AS execParamsModelCode,
               adcd.accId, ad.deviceId
        FROM aiops_device ad
        INNER JOIN aiops_device_collect adc ON ad.id = adc.adId
        INNER JOIN aiops_device_collect_detail adcd ON adc.id = adcd.adcId
                   AND IFNULL(adcd.aiId, '') = '' AND IFNULL(adcd.adimId, '') = ''
        <if test="execParamsModelCode != null and execParamsModelCode != ''">
            <!--如果有指定特定的执行参数模型代号，就只查设备收集项执行参数有设定(不为空)的内容-->
            AND IFNULL(adcd.execParamsContent, '') != ''
        </if>
        INNER JOIN aiops_collect_config acc ON adcd.accId = acc.id
        <if test="execParamsModelCode != null and execParamsModelCode != ''">
            AND acc.execParamsModelCode = #{execParamsModelCode}
        </if>
        WHERE 1 = 1
        <if test="eid != null and eid != 0">
            AND ad.eid = #{eid}
        </if>
        <if test="deviceId != null and deviceId != ''">
            AND ad.deviceId = #{deviceId}
        </if>
        <if test="deviceIdList != null">
            <foreach collection="deviceIdList" item="item" open=" AND ad.deviceId IN(" separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <update id="batchUpdateDeviceInstanceMappingAiIdByMapList">
        UPDATE aiops_device_instance_mapping
        SET id = id
        <if test="mapList != null">
            <foreach collection="mapList" item="item" open=", aiId = CASE aiId WHEN" separator=" WHEN " close=" END ">
                #{item.oldAiId} THEN #{item.newAiId}
            </foreach>
        </if>
        WHERE 1 != 1
        <if test="mapList != null">
            <foreach collection="mapList" item="item" open=" OR ((" separator=") OR (" close="))">
                deviceId = #{item.deviceId} AND aiId = #{item.oldAiId}
            </foreach>
        </if>
    </update>

    <update id="batchUpdateDeviceCollectDetailAiIdByMapList">
        UPDATE aiops_device_collect_detail adcd
        INNER JOIN aiops_device_collect adc ON adcd.adcId = adc.id
        <if test="mapList != null">
            <foreach collection="mapList" item="item" open=" AND adc.deviceId IN(" separator=", " close=")">
                #{item.deviceId}
            </foreach>
        </if>
        SET adcd.id = adcd.id
        <if test="mapList != null">
            <foreach collection="mapList" item="item" open=", adcd.aiId = CASE WHEN" separator=" WHEN " close=" END ">
                adcd.aiId = #{item.oldAiId} AND adc.deviceId = #{item.deviceId} THEN #{item.newAiId}
            </foreach>
        </if>
        WHERE 1 != 1
        <if test="mapList != null">
            <foreach collection="mapList" item="item" open=" OR aiId IN(" separator=", " close=")">
                #{item.oldAiId}
            </foreach>
        </if>
    </update>

    <select id="selectAdpmListByMapList" resultType="com.digiwin.escloud.aioitms.device.model.DeviceProductMapping">
        SELECT adpm.*, apa.modelCode
        FROM aiops_device_product_mapping adpm
        LEFT JOIN aiops_product_app apa ON adpm.apaId = apa.id
        WHERE 1 != 1
        <if test="mapList != null">
            <foreach collection="mapList" open=" OR (" separator=" OR " close=")">
                (adId = #{adId} AND appId = #{appId})
            </foreach>
        </if>
    </select>

    <delete id="deleteAdpmByAdpmIdList">
        DELETE FROM aiops_device_product_mapping
        WHERE 1 != 1
        <if test="adpmIdList != null">
            <foreach collection="adpmIdList" item="item" open=" OR id IN(" separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </delete>

    <update id="updateAdpmAdIdByAdpmIdList">
        UPDATE aiops_device_product_mapping
        SET adId = #{adId}, deviceId = #{deviceId}
        WHERE 1 != 1
        <if test="adpmIdList != null">
            <foreach collection="adpmIdList" item="item" open=" OR id IN(" separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </update>

    <update id="batchUpdateAiopsDeviceProductMapping">
        UPDATE aiops_device_product_mapping
        SET id = id
        <if test="adpmList != null">
            <foreach collection="adpmList" item="item" open=", adId = CASE id WHEN " separator=" WHEN " close=" END">
                #{item.id} THEN #{item.adId}
            </foreach>
            <foreach collection="adpmList" item="item" open=", deviceId = CASE id WHEN " separator=" WHEN " close=" END">
                #{item.id} THEN #{item.deviceId}
            </foreach>
            <foreach collection="adpmList" item="item" open=", apaId = CASE id WHEN" separator=" WHEN " close=" END">
                #{item.id} THEN #{item.apaId}
            </foreach>
            <foreach collection="adpmList" item="item" open=", appId = CASE id WHEN" separator=" WHEN " close=" END">
                #{item.id} THEN #{item.appId}
            </foreach>
        </if>
        WHERE 1 != 1
        <if test="adpmList != null">
            <foreach collection="adpmList" item="item" open=" OR id IN(" separator=", " close=")">
                #{item.id}
            </foreach>
        </if>
    </update>

    <select id="selectDeviceByEidList" resultMap="DeviceMap">
        SELECT deviceId
        FROM aiops_device
        WHERE eid in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getAiopsInstanceNameByAiIdList" resultType="java.util.Map">
        SELECT ai.id as id,
        <include refid="com.digiwin.escloud.aioitms.instance.dao.InstanceMapper.getAiopsInstanceDisplayNameSql">
            <property name="aiAlias" value="ai"/>
        </include> AS aiopsInstanceName
        FROM aiops_instance ai
        WHERE ai.id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectOnlyDeviceInfoNewByMap" resultMap="onlyDeviceDetailNewResultMap">
        SELECT adcd.id AS adcdId,
               adc.id AS adcId,
               ad.id AS adId, ad.deviceId, ad.deviceName, ad.platform, ad.ipAddress, ad.placementPoint,
               ad.remark, ad.registerTime, ad.lastCheckInTime,
               acc.id AS accId, acc.collectCategory,
               acc2.name AS collectCategoryName,
               ai.id AS aiId, ai.aiopsItem,
               IFNULL(ad.eid, ai.eid) AS eid,
        <include refid="com.digiwin.escloud.aioitms.instance.dao.InstanceMapper.getFindDisplayNameByModel">
            <property name="aiAlias" value="ai"/>
        </include> AS findDisplayNameByModel,
        <choose>
            <when test="serviceArea == 'TW'">
                ai2.name_TW AS aiopsItemName,
            </when>
            <when test="serviceArea == 'CN'">
                ai2.name_CN AS aiopsItemName,
            </when>
            <otherwise>
                ai2.name AS aiopsItemName,
            </otherwise>
        </choose>
               ai.aiopsItemType,
        <choose>
            <when test="serviceArea == 'TW'">
                ait.name_TW AS aiopsItemTypeName,
            </when>
            <when test="serviceArea == 'CN'">
                ait.name_CN AS aiopsItemTypeName,
            </when>
            <otherwise>
                ait.name AS aiopsItemTypeName,
            </otherwise>
        </choose>
        <include refid="com.digiwin.escloud.aioitms.instance.dao.InstanceMapper.getAiopsInstanceDisplayNameSql">
            <property name="aiAlias" value="ai"/>
        </include> AS aiopsInstanceName,
               CASE WHEN IFNULL(ai.aiopsAuthStatus, 'UNAUTH') IN ('AUTHED', 'NONE') THEN TRUE
               ELSE FALSE END AS authStatus
        FROM aiops_device_collect_detail adcd
        LEFT JOIN aiops_device_collect adc ON adcd.adcId = adc.id
        LEFT JOIN aiops_device ad ON adc.adId = ad.id
        LEFT JOIN aiops_collect_config acc ON acc.id = adcd.accId
        LEFT JOIN aiops_collect_category acc2 ON acc2.CODE = acc.collectCategory
        LEFT JOIN aiops_instance ai ON adcd.aiId = ai.id
        LEFT JOIN aiops_item ai2 ON ai.aiopsItem = ai2.code
        LEFT JOIN aiops_item_type ait ON ai.aiopsItemType = ait.code
        <where>
            <if test="adcdIdList != null">
                <foreach collection="adcdIdList" item="item" open=" AND adcd.id IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="adcdId != null and adcdId > 0">
                AND adcd.id = #{adcdId}
            </if>
            <if test="deviceIdList != null">
                <foreach collection="deviceIdList" item="item" open=" AND adc.deviceId IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="deviceId != null and deviceId != ''">
                AND adc.deviceId = #{deviceId}
            </if>
        </where>

    </select>

    <select id="selectDeviceCollectWarningByMap" resultMap="deviceCollectWarningMap">
        SELECT adcw.id, adcw.adcdId, adcw.acwId, adcw.isWarningEnable
        FROM aiops_device_collect_warning adcw
        WHERE 1 = 1
        <if test="adcwId != null and adcwId > 0">
            AND adcw.id = #{adcwId}
        </if>
        <if test="adcdId != null and adcdId > 0">
            AND adcw.adcdId = #{adcdId}
        </if>
        <if test="acwId != null and acwId > 0">
            AND adcw.acwId = #{acwId}
        </if>
        <if test="isWarningEnable != null">
            AND adcw.isWarningEnable = #{isWarningEnable}
        </if>
        <if test="accIdList != null">
            <foreach collection="accIdList" item="item" open="AND EXISTS (
                        SELECT 1 FROM aiops_device_collect_detail adcd
                        INNER JOIN aiops_collect_config acc ON adcd.accId = acc.id
                        WHERE adcd.id = adcw.adcdId AND IFNULL(acc.sourceAccId, acc.id) IN(" separator=", " close="))">
                #{item}
            </foreach>

        </if>
    </select>

    <update id="updateAdcdAdcIdAndAdimIdByMap">
        UPDATE aiops_device_collect_detail adcd
        <if test="map.deviceId != null and map.deviceId != ''">
            INNER JOIN aiops_device_collect adc ON adcd.adcId = adc.id AND adc.deviceId = #{map.deviceId}
        </if>
        SET adcd.adcId = #{adcId},
            adcd.adimId = #{adimId}
        WHERE 1 != 1
        <if test="map.aiId != null and map.aiId > 0">
            OR adcd.aiId = #{map.aiId}
        </if>
        <if test="map.aiIdList != null">
            <foreach collection="map.aiIdList" item="item" open=" OR adcd.aiId IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </update>

    <delete id="deleteAdimByMap">
        DELETE adim
        FROM aiops_device_instance_mapping adim
        WHERE
        <choose>
            <when test="(deviceId != null and deviceId == '') or (aiIdList != null)">
                1 = 1
                <if test="deviceId != null and deviceId != ''">
                    AND adim.deviceId = #{deviceId}
                </if>
                <if test="aiIdList != null">
                    <foreach collection="aiIdList" item="item" open=" AND adim.aiId IN (" separator=", " close=")">
                        #{item}
                    </foreach>
                </if>
            </when>
            <otherwise>
                1 != 1
            </otherwise>
        </choose>
    </delete>

    <select id="selectFixAdcdToOtherAiopsInstance" resultType="java.util.Map">
        SELECT adc.deviceId,
               IFNULL(ad.eid, ai.eid) AS eid,
               adcd.id AS adcdId, adcd.execParamsContent,
               acc.execParamsModelCode
        FROM aiops_device_collect_detail adcd
        LEFT JOIN aiops_device_collect adc ON adcd.adcId = adc.id
        INNER JOIN aiops_collect_config acc ON adcd.accId = acc.id
        LEFT JOIN aiops_device ad ON adc.adId = ad.id
        LEFT JOIN aiops_instance ai ON adcd.aiId = ai.id
        <where>
            <if test="sourceEidList != null">
                <foreach collection="sourceEidList" item="item"
                         open=" AND IFNULL(ad.eid, ai.eid) IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="sourceDeviceIdList != null">
                <foreach collection="sourceDeviceIdList" item="item"
                         open=" AND adc.deviceId IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="sourceAdcdIdList != null">
                <foreach collection="sourceAdcdIdList" item="item"
                         open=" AND adcd.id IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="sourceAccIdList != null">
                <foreach collection="sourceAccIdList" item="item"
                         open=" AND IFNULL(acc.sourceAccId, acc.id) IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="sourceAiopsItemList != null">
                <foreach collection="sourceAiopsItemList" item="item"
                         open=" AND IFNULL(ai.aiopsItem, '') IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <update id="updateAdcdAiIdAndAdimIdByMapList">
        UPDATE aiops_device_collect_detail
        SET id = id
        <foreach collection="mapList" item="item" open=", aiId = CASE id WHEN " separator=" WHEN " close=" END ">
            #{item.adcdId} THEN #{item.aiId}
        </foreach>
        <foreach collection="mapList" item="item" open=", adimId = CASE id WHEN " separator=" WHEN " close=" END ">
            #{item.adcdId} THEN #{item.adimId}
        </foreach>
        WHERE 1 != 1
        <foreach collection="mapList" item="item" open=" OR id IN(" separator=", " close=")">
            #{item.adcdId}
        </foreach>
    </update>

    <delete id="deleteAdcdAdcwAdwnmByMap">
        <!--针对自定义收集项(acc)相关表是透过外键去做清理的，因此这里只删除acc，其他外键关联表纪录会自动删除-->
        DELETE adcd, adcw, adwnm
        <if test="removeAcc != null and removeAcc">
            , acc
        </if>
        FROM aiops_device_collect_detail adcd
        LEFT JOIN aiops_device_collect_warning adcw ON adcd.id = adcw.adcdId
        LEFT JOIN aiops_device_warning_notify_mapping adwnm ON adcw.id = adwnm.adcwId
        LEFT JOIN aiops_collect_config acc ON adcd.accId = acc.id
        <if test="scopeId != null and scopeId != ''">
            AND acc.scopeId = #{scopeId}
        </if>
        WHERE 1 != 1
        <trim prefix=" OR (" prefixOverrides="AND" suffix=")">
            <if test="adcdIdList != null">
                <foreach collection="adcdIdList" item="item" open=" AND adcd.id IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="accId != null and accId > 0">
                AND acc.id = #{accId}
            </if>
        </trim>
    </delete>

    <insert id="insertAddModuleCollectLog" parameterType="com.digiwin.escloud.aioitms.device.model.AddModuleCollectLog">
        INSERT INTO aiops_add_module_collect_log(id, eid, aiId, adimId, deviceId, aiopsItem, aiopsItemId,
                                                 validSamcdIdResult, sourceTmcdResult, addedAdcdIdResult,
                                                 isSuccess, exceptionMsg)
        VALUES (#{id}, #{eid}, #{aiId}, #{adimId}, #{deviceId}, #{aiopsItem}, #{aiopsItemId},
                #{validSamcdIdResult}, #{sourceTmcdResult}, #{addedAdcdIdResult}, #{isSuccess}, #{exceptionMsg})
    </insert>

    <select id="selectDeviceEidByAdcwId" resultType="java.lang.Long">
        SELECT IFNULL(adc.eid, ai.eid) AS eid
        FROM aiops_device_collect_warning adcw
        INNER JOIN aiops_device_collect_detail adcd ON adcw.adcdId = adcd.id
        LEFT JOIN (
            SELECT adc.id, ad.eid
            FROM aiops_device_collect adc
            INNER JOIN aiops_device ad ON adc.adId = ad.id
        ) adc ON adcd.adcId = adc.id
        LEFT JOIN aiops_instance ai ON adcd.aiId = ai.id
        WHERE adcw.id = #{adcwId}
    </select>

    <select id="selectDeviceIdAndAccIdByAdcdIdAndAcwId" resultType="java.util.Map">
        SELECT adc.deviceId, adcd.accId, ad.eid,acw.warningCode
        FROM aiops_device_collect_detail adcd
        INNER JOIN aiops_device_collect adc ON adcd.adcId = adc.id
        LEFT JOIN aiops_device ad ON adc.adId = ad.id
        LEFT JOIN aiops_device_collect_warning adcw ON adcd.id = adcw.adcdId
        LEFT JOIN aiops_collect_warning acw ON adcw.acwId = acw.id
        WHERE adcd.id = #{adcdId} AND acw.id = #{acwId}
    </select>

    <select id="selectDeviceIdAndAccIdByAdcdIdBatch" resultType="java.util.Map">
        SELECT
            adcd.id AS adcdId,
            adcw.id AS adcwId,
            adcw.acwId,
            acw.warningCode
        FROM
            aiops_device_collect_detail adcd
        INNER JOIN aiops_device_collect_warning adcw ON adcw.adcdId = adcd.id
        INNER JOIN aiops_collect_warning acw ON adcw.acwId = acw.id
        WHERE adcw.id IN
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectSimpleAdcdByMap" resultType="com.digiwin.escloud.aioitms.device.model.DeviceCollectDetail">
        SELECT adcd.id, adcd.adcId, adcd.accId
        <if test="containDeviceId != null and containDeviceId">
            , adc.deviceId
        </if>
        FROM aiops_device_collect_detail adcd
        <if test="containDeviceId != null and containDeviceId">
            INNER JOIN aiops_device_collect adc ON adcd.adcId = adc.id
        </if>
        WHERE 1 = 1
        <if test="accId != null and accId > 0">
            AND accId = #{accId}
        </if>
        <if test="accIdList != null">
            <foreach collection="accIdList" item="item" open=" AND accId IN(" separator=", " close=")">
                #{item}
            </foreach>
        </if>
        <if test="adcdId != null and adcdId > 0">
            AND id = #{adcdId}
        </if>
        <if test="isRemove == null or !isRemove">
            <choose>
                <when test="isEnable != null and isEnable">
                    AND isEnable = 0
                </when>
                <otherwise>
                    AND isEnable = 1
                </otherwise>
            </choose>
        </if>
    </select>

    <delete id="deleteAdcdByAccIdList">
        DELETE FROM aiops_device_collect_detail
        WHERE 1 != 1
        <if test="accIdList != null">
            <foreach collection="accIdList" item="item" open=" OR accId IN(" separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </delete>

    <update id="updateAdcdEnableByAccIdList">
        UPDATE aiops_device_collect_detail
        SET isEnable = #{isEnable}
        WHERE 1 != 1
        <if test="accIdList != null">
            <foreach collection="accIdList" item="item" open=" OR accId IN(" separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </update>

    <select id="selectGroupAdcdExecParamByMap" resultMap="groupAdcdExecParamMap">
        <!-- 1.透过映射查询对应执行参数分组的收集项
             2.考虑异步添加设备收集项场景，因此要过滤掉非当前设备平台及非当前设备类型及若是产品应用的话要过滤挂钩的产品之收集项
             3.若adcdId是空的，要判断授权可用性
        -->
        SELECT amcm.samcdId, amcm.samclId, amcm.isEnable,
               IFNULL(amcm.aiopsItemParamGroup, 'UNKNOWN') AS aiopsItemParamGroup,
               IFNULL(aipg.name, 'UNKNOWN') AS aiopsItemParamGroupName,
               IFNULL(aipg.name, 'UNKNOWN') AS aiopsItemParamGroupName_CN,
               IFNULL(aipg.name, 'UNKNOWN') AS aiopsItemParamGroupName_TW,
               acc.execParamsModelCode,
               ai.id AS aiId, ai.aiopsItemId, ai.aiopsItem, ai.aiopsItemType,
               <include refid="com.digiwin.escloud.aioitms.instance.dao.InstanceMapper.getFindDisplayNameByModel">
                   <property name="aiAlias" value="ai"/>
               </include> AS findDisplayNameByModel,
               <include refid="com.digiwin.escloud.aioitms.instance.dao.InstanceMapper.getAiopsInstanceDisplayNameSql">
                   <property name="aiAlias" value="ai"/>
               </include> AS aiopsInstanceName,
               adim.id AS adimId,
               adcd.id AS adcd_adcdId, IFNULL(adcd.accId, acc.id) AS adcd_accId,
               IFNULL(adcd.collectName, acc.collectName) AS adcd_collectName,
               IFNULL(adcd.isEnable, amcm.isEnable) AS adcd_isEnable,
               adcd.execParamsContent AS adcd_execParamsContent, adcd.execParamsVersion AS adcd_execParamsVersion
        FROM aiops_module_collect_mapping amcm
        INNER JOIN aiops_collect_config acc ON amcm.accId = acc.id AND IFNULL(acc.execParamsModelCode, '') != ''
        LEFT JOIN aiops_item_param_group aipg ON amcm.aiopsItemParamGroup = aipg.code
        LEFT JOIN aiops_instance ai ON amcm.samcdId = ai.samcdId
        LEFT JOIN aiops_device_instance_mapping adim ON adim.aiId = ai.id
        LEFT JOIN aiops_device_collect_detail adcd ON amcm.accId = adcd.accId AND adim.id = adcd.adimId
            AND adim.aiId = adcd.aiId
        WHERE (CASE ai.aiopsItemType WHEN 'PRODUCT_APP' THEN
                    EXISTS(SELECT 1 FROM aiops_device_product_mapping adpm
                           INNER JOIN aiops_collect_app_mapping acam ON adpm.apaId = acam.apaId
                           WHERE adpm.appId = ai.aiopsItemId AND acam.accId = acc.id)
               ELSE TRUE END)
              AND EXISTS(SELECT 1 FROM aiops_device ad
                         WHERE ad.id = adim.adId
                            AND (acc.platform LIKE CONCAT('%;', ad.platform)
                                 OR acc.platform LIKE CONCAT('%;', ad.platform, ';%')
                                 OR acc.platform LIKE CONCAT(ad.platform, ';%')
                                 OR acc.platform = ad.platform))
              AND EXISTS(SELECT 1 FROM aiops_device_type_mapping adtm
                         WHERE adtm.adId = adim.adId
                            AND(acc.collectDeviceType LIKE CONCAT('%;', adtm.deviceType)
                                OR acc.collectDeviceType LIKE CONCAT('%;', adtm.deviceType, ';%')
                                OR acc.collectDeviceType LIKE CONCAT(adtm.deviceType, ';%')
                                OR acc.collectDeviceType = adtm.deviceType))
              AND (adcd.id IS NOT NULL
              <choose>
                  <!--收集项勾选列表呈现的是有购买且有效的租户合约的收集项，另一方面看来就是无效的租户合约的模组收集项不会显示-->
                  <when test="validSamcdIdList != null">
                      <foreach collection="validSamcdIdList" item="item"
                               open=" OR amcm.samcdId IN(" separator=", " close=")">
                          #{item}
                      </foreach>
                  </when>
                  <otherwise>
                      OR FALSE
                  </otherwise>
              </choose>)
        <if test="aiopsItemIdList != null">
            <foreach collection="aiopsItemIdList" item="item" open=" AND ai.aiopsItemId IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
        <if test="deviceId != null and deviceId != ''">
            AND adim.deviceId = #{deviceId}
        </if>
        GROUP BY amcm.aiopsItemParamGroup, acc.execParamsModelCode, acc.id, adcd.id
        ORDER BY aiId, aiopsItemParamGroupName, acc.execParamsModelCode, adcd.id
    </select>

    <select id="selectAdcdIdByMap" resultType="java.lang.Long">
        SELECT adcd.id
        FROM aiops_device_collect_detail adcd
        <where>
            <if test="aiId != null and aiId > 0">
                AND aiId = #{aiId}
            </if>
            <if test="adimId != null and adimId > 0">
                AND adimId = #{adimId}
            </if>
            <if test="accId != null and accId > 0">
                AND accId = #{accId}
            </if>
        </where>
        LIMIT 1
    </select>

    <select id="selectAdcdIdListByMap" resultType="java.lang.Long">
        SELECT adcd.id
        FROM aiops_device_collect_detail adcd
        <where>
            <if test="aiId != null and aiId > 0">
                AND aiId = #{aiId}
            </if>
            <if test="aiIdList != null">
                <foreach collection="aiIdList" item="item" open=" AND aiId IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="adimId != null and adimId > 0">
                AND adimId = #{adimId}
            </if>
            <if test="adimIdList != null">
                <foreach collection="adimIdList" item="item" open=" AND adimId IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="accId != null and accId > 0">
                AND accId = #{accId}
            </if>
            <if test="accIdList != null">
                <foreach collection="accIdList" item="item" open=" AND accId IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="deviceId != null and deviceId != ''">
                AND EXISTS(SELECT 1 FROM aiops_device_collect adc
                           WHERE adc.deviceId = #{deviceId} AND adcd.adcId = adc.id)
            </if>
            <if test="deviceIdList != null">
                <foreach collection="deviceIdList" item="item"
                         open=" AND EXISTS(SELECT 1 FROM aiops_device_collect adc
                                           WHERE adc.deviceId IN(" separator=", " close=")  AND adcd.adcId = adc.id)">
                    #{item}
                </foreach>
            </if>
        </where>
        LIMIT 1
    </select>

    <select id="selectUnityExecParamsAiId" resultType="java.lang.Long">
        SELECT id
        FROM aiops_instance
        WHERE IFNULL(execParamsModelCode, '') != ''
        <if test="aiIdCollection != null">
            <foreach collection="aiIdCollection" item="item" open=" AND id IN(" separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <update id="updateAdcdExecParamsByGroupAdcdExecParamDetail">
        UPDATE aiops_device_collect_detail
        SET id = id
        <if test="groupAdcdExecParamDetailList != null">
            <foreach collection="groupAdcdExecParamDetailList" item="item"
                     open=", execParamsContent = CASE id WHEN " separator=" WHEN " close=" END">
                #{item.adcdId} THEN #{item.execParamsContent}
            </foreach>
            <foreach collection="groupAdcdExecParamDetailList" item="item"
                     open=", execParamsVersion = CASE id WHEN " separator=" WHEN " close=" END">
                #{item.adcdId} THEN #{item.execParamsVersion}
            </foreach>
        </if>
        WHERE 1 != 1
        <if test="groupAdcdExecParamDetailList != null">
            <foreach collection="groupAdcdExecParamDetailList" item="item" open=" OR id IN(" separator=", " close=")">
                #{item.adcdId}
            </foreach>
        </if>
    </update>

    <select id="selectAdimExistAccId" resultType="java.lang.Long">
        SELECT adcd.accId
        FROM aiops_device_collect_detail adcd
        WHERE 1 != 1
        <if test="aicList != null">
            <foreach collection="aicList" item="item" open=" OR ((" separator=") OR (" close=")) ">
                adcd.aiId = #{item.aiId} AND adcd.adimId = #{item.adimId}
            </foreach>
        </if>
    </select>

    <update id="updateAiIdByAicList">
        <!--1.adcd表中舊的aiId及adimId換成新的aiId及adimId(條件:oriAdimId及oriAiId)
            2.adcd更新排除掉，原始实例已经存在的收集项
        -->
        UPDATE aiops_device_instance_mapping adim
        LEFT JOIN aiops_device_collect_detail adcd ON adim.aiId = adcd.aiId AND adim.id = adcd.adimId
        <if test="adimExistAccIdList != null">
            <foreach collection="adimExistAccIdList" item="item"
                     open="AND adcd.accId NOT IN(" separator=", " close=")">
                #{item}
            </foreach>
        </if>
        SET adcd.id = adcd.id
        <if test="aicList != null">
            <foreach collection="aicList" item="item" open=", adcd.aiId = CASE WHEN " separator=" WHEN " close="END ">
                (adcd.aiId = #{item.oriAiId} AND adcd.adimId = #{item.oriAdimId}) THEN #{item.aiId}
            </foreach>
            <foreach collection="aicList" item="item" open=", adcd.adimId = CASE WHEN " separator=" WHEN " close="END ">
                (adim.aiId = #{item.oriAiId} AND adim.id = #{item.oriAdimId}) THEN #{item.adimId}
            </foreach>
        </if>
        WHERE 1 != 1
        <if test="aicList != null">
            <foreach collection="aicList" item="item" open=" OR ((" separator=") OR (" close=")) ">
                adim.aiId = #{item.oriAiId} AND adim.id = #{item.oriAdimId}
            </foreach>
        </if>
    </update>

    <delete id="deleteNotUseAdimAdcd">
        DELETE adim, adcd
        FROM aiops_device_instance_mapping adim
        LEFT JOIN aiops_device_collect_detail adcd ON adim.aiId = adcd.aiId AND adim.id = adcd.adimId
        WHERE 1 != 1
        <if test="aicList != null">
            <foreach collection="aicList" item="item" open=" OR ((" separator=") OR (" close=")) ">
                adim.aiId = #{item.oriAiId} AND adim.id = #{item.oriAdimId}
            </foreach>
        </if>
    </delete>

    <select id="selectAdcdIdEidListByMap" resultType="java.util.Map">
        SELECT adcd.id AS adcdId, adcd.execParamsContent,
               ad.eid
        FROM aiops_device_collect_detail adcd
        INNER JOIN aiops_device_collect adc ON adcd.adcId = adc.id
        INNER JOIN aiops_device ad ON adc.adId = ad.id
        <if test="aiopsItemIdList != null or aiopsItemList != null">
            INNER JOIN aiops_instance ai ON adcd.aiId = ai.id
            <if test="aiopsItemIdList != null">
                <foreach collection="aiopsItemIdList" item="item"
                         open=" AND ai.aiopsItemId IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="aiopsItemList != null">
                <foreach collection="aiopsItemList" item="item"
                         open=" AND ai.aiopsItem IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
        </if>
        <where>
            <if test="accIdList != null">
                <foreach collection="accIdList" item="item"
                         open=" AND adcd.accId IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="adcdIdList != null">
                <foreach collection="adcdIdList" item="item"
                         open=" AND adcd.id IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="aiIdList != null">
                <foreach collection="aiIdList" item="item"
                         open=" AND adcd.aiId IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY ad.eid
    </select>

    <select id="selectEffectDeviceCount" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT ad.deviceId)
        FROM aiops_device_collect_detail adcd
        INNER JOIN aiops_device_collect adc ON adcd.adcId = adc.id
        INNER JOIN aiops_device ad ON adc.adId = ad.id
        <where>
            <if test="deviceId != null and deviceId != ''">
                AND adc.deviceId = #{deviceId}
            </if>
            <if test="deviceIdList != null">
                <foreach collection="deviceIdList" item="item"
                         open=" AND adc.deviceId IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="accId != null and accId > 0">
                AND adcd.accId = #{accId}
            </if>
            <if test="accIdList != null">
                <foreach collection="accIdList" item="item"
                         open=" AND adcd.accId IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="eid != null and eid > 0">
                AND ad.eid = #{eid}
            </if>
            <if test="eidList != null">
                <foreach collection="eidList" item="item"
                         open=" AND ad.eid IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectDeviceInfoByMap" resultMap="DeviceMap">
        SELECT ad.*,adtm.deviceType adtm_deviceType
        FROM aiops_device ad
        LEFT JOIN aiops_device_type_mapping adtm ON ad.id = adtm.adId
        <where>
            <if test="eid != null and eid > 0">
                AND ad.eid = #{eid}
            </if>
            <if test="deviceId != null and deviceId != ''">
                AND ad.deviceId = #{deviceId}
            </if>
            <if test="deviceIdList != null">
                <foreach collection="deviceIdList" item="item" open=" AND ad.deviceId IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="eidList != null">
                <foreach collection="eidList" item="item" open=" AND eid IN (" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectExistAdimByMap" resultType="java.util.Map">
        SELECT adim.id AS adimId, adim.adId, adim.deviceId, adim.aiId,
               ai.aiopsItemId
        FROM aiops_device_instance_mapping adim
        INNER JOIN aiops_instance ai ON adim.aiId = ai.id
        <if test="aiopsItemId != null and aiopsItemId != ''">
            AND ai.aiopsItemId = #{aiopsItemId}
        </if>
        <if test="aiopsItem != null and aiopsItem != ''">
            AND ai.aiopsItem = #{aiopsItem}
        </if>
        <if test="aiId != null and aiId > 0">
            AND ai.id = #{aiId}
        </if>
        <where>
            <if test="deviceId != null and deviceId != ''">
                AND adim.deviceId = #{deviceId}
            </if>
            <if test="adimId != null and adimId > 0">
                AND adim.id = #{adimId}
            </if>
        </where>
    </select>

    <select id="queryAiopsItemByAiopsItemId" resultType="java.util.Map">
        SELECT ai.aiopsAuthStatus
        FROM aiops_device_instance_mapping adim
        INNER JOIN aiops_instance ai ON adim.aiId = ai.id AND ai.aiopsItemId = #{aiopsItemId}
        WHERE adim.deviceId = #{deviceId}
        limit 1
    </select>

    <select id="selectCloudManufacturer" resultType="java.lang.String">
        SELECT code
        FROM db_host_bios_manufacturer
        WHERE cloudVendor = 1
    </select>

    <select id="selectDeviceTypeInfoByMap" resultType="java.util.Map">
        <!--返回列表根据ai.__version__倒排序主要是要处理可用授权数不足，优先保留后变更的实例授权-->
        SELECT ad.id, ad.deviceId, ad.eid,
               adtm.id AS adtmId, adtm.deviceType,
               ai.id AS aiId, ai.tmcdId, ai.aiopsItemId, ai.aiopsItem, ai.aiopsItemType,
               IFNULL(ai.aiopsAuthStatus, 'UNAUTH') AS aiopsAuthStatus, ai.__version__
        FROM aiops_device ad
        INNER JOIN aiops_device_type_mapping adtm ON ad.id = adtm.adId
        <if test="sourceDeviceType != null and sourceDeviceType">
            AND adtm.deviceType = #{sourceDeviceType}
        </if>
        INNER JOIN aiops_instance ai ON ai.aiopsItemType = 'DEVICE' AND ad.deviceId = ai.aiopsItemId
        <where>
            <if test="sourceEidList != null">
                <foreach collection="sourceEidList" item="item" open="AND ad.eid IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="sourceDeviceIdList != null">
                <foreach collection="sourceDeviceIdList" item="item" open="AND ad.deviceId IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="sourcePlatformList != null">
                <foreach collection="sourcePlatformList" item="item" open="AND ad.platform IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY ai.__version__ DESC
    </select>

    <update id="updateAdtmAiDeviceTypeByMap">
        UPDATE aiops_device_type_mapping adtm
        INNER JOIN aiops_instance ai ON ai.aiopsItemType = 'DEVICE' AND adtm.deviceId = ai.aiopsItemId
        SET adtm.deviceType = #{targetDeviceType},
            ai.aiopsItem = #{targetDeviceType},
            ai.tmcdId = #{targetTmcdId},
            ai.samcdId = #{targetSamcdId}
        <if test="authedChangeUnauth != null and authedChangeUnauth">
            , ai.aiopsAuthStatus = (CASE WHEN ai.aiopsAuthStatus = 'AUTHED' THEN 'UNAUTH'
                                    ELSE ai.aiopsAuthStatus END)
        </if>
        <where>
            <if test="adtmId != null and adtmId > 0">
                AND adtm.id = #{adtmId}
            </if>
            <if test="adtmIdList != null">
                <foreach collection="adtmIdList" item="item" open=" AND adtm.id IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="adId != null and adId > 0">
                AND adtm.adId = #{adId}
            </if>
            <if test="adIdList != null">
                <foreach collection="adIdList" item="item" open=" AND adtm.adId IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="deviceId != null and deviceId != ''">
                AND adtm.deviceId = #{deviceId}
            </if>
            <if test="deviceIdList != null">
                <foreach collection="deviceIdList" item="item" open=" AND adtm.deviceId IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </update>

    <select id="selectDeviceTypeInvalidAdcdInfoByMap" resultType="java.util.Map">
        SELECT adcd.id AS adcdId,
               acc.id AS accId, (acc.scopeId = adc.deviceId) AS accIsCustomize,
               acw.id AS acwId, (acw.scopeId = adc.deviceId) AS acwIsCustomize
               <if test="innerAccIdList != null">
                   <foreach collection="innerAccIdList" item="item"
                            open=", (acc.id IN (" separator=", " close=")) AS accIsInnerRunner">
                       #{item}
                   </foreach>
               </if>
        FROM aiops_device_collect adc
        INNER JOIN aiops_device_collect_detail adcd ON adc.id = adcd.adcId
        INNER JOIN aiops_collect_config acc ON adcd.accId = acc.id
        <if test="filterAdimAi != null and filterAdimAi">
            INNER JOIN aiops_device_instance_mapping adim ON adc.adId = adim.adId AND adcd.adimId = adim.id
            INNER JOIN aiops_instance ai ON adim.aiId = ai.id AND adcd.aiId = ai.id
            <if test="aiopsItemTypeList != null">
                <foreach collection="aiopsItemTypeList" item="item"
                         open=" AND ai.aiopsItemType IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="aiopsItemType != null and aiopsItemType != ''">
                AND ai.aiopsItemType = #{aiopsItemType}
            </if>
            <if test="aiopsItemList != null">
                <foreach collection="aiopsItemList" item="item"
                         open=" AND ai.aiopsItem IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="aiopsItem != null and aiopsItem != ''">
                AND ai.aiopsItem = #{aiopsItem}
            </if>
        </if>
        LEFT JOIN aiops_device_collect_warning adcw ON adcd.id = adcw.adcdId
        LEFT JOIN aiops_collect_warning acw ON adcw.acwId = acw.id
        <where>
            <if test="deviceIdList != null">
                <foreach collection="deviceIdList" item="item" open=" AND adc.deviceId IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="filterInvalidAcc != null and filterInvalidAcc">
                AND NOT EXISTS(SELECT 1 FROM aiops_module_collect_mapping amcm
                               WHERE amcm.accId = adcd.accId
                                   AND (acc.collectDeviceType LIKE CONCAT(#{targetDeviceType}, ';%')
                                        OR acc.collectDeviceType LIKE CONCAT('%;', #{targetDeviceType}, ';%')
                                        OR acc.collectDeviceType LIKE CONCAT('%;', #{targetDeviceType})
                                        OR acc.collectDeviceType = #{targetDeviceType}))
            </if>
        </where>
    </select>

    <delete id="deleteDeviceTypeInvalidAdcdByMap">
        DELETE adcd
        <if test="deleteAcc != null and deleteAcc">
            , acc
        </if>
        , adcw, re, resm
        <if test="deleteRs != null and deleteRs">
            , rs
        </if>
        <if test="deleteAcw != null and deleteAcw">
            , acw
        </if>
        FROM aiops_device_collect_detail adcd
        INNER JOIN aiops_collect_config acc ON adcd.accId = acc.id
        LEFT JOIN aiops_device_collect_warning adcw ON adcd.id = adcw.adcdId
        LEFT JOIN rule_engine re ON
        <choose>
            <when test="isInnerRunner != null and isInnerRunner">
                adcd.accId = re.adcdId
            </when>
            <otherwise>
                adcd.id = re.adcdId
            </otherwise>
        </choose>
        LEFT JOIN rule_engine_setting_mapping resm ON re.id = resm.reId AND adcw.id = resm.adcwId
        LEFT JOIN rule_setting rs ON resm.rsId = rs.id
        LEFT JOIN aiops_collect_warning acw ON adcw.acwId = acw.id and rs.acwId = acw.id
        WHERE 1 != 1
        <trim prefix=" OR (" prefixOverrides="AND" suffix=")">
            <if test="adcdIdList != null">
                <foreach collection="adcdIdList" item="item" open=" AND adcd.id IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
        </trim>
    </delete>

    <delete id="deleteDeviceTypeInvalidAdimByMap">
        DELETE adim, adpm
        FROM aiops_device_instance_mapping adim
        INNER JOIN aiops_instance ai ON adim.aiId = ai.id
        <if test="aiopsItemTypeList != null">
            <foreach collection="aiopsItemTypeList" item="item"
                     open=" AND ai.aiopsItemType IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
        <if test="aiopsItemType != null and aiopsItemType != ''">
            AND ai.aiopsItemType = #{aiopsItemType}
        </if>
        <if test="aiopsItemList != null">
            <foreach collection="aiopsItemList" item="item"
                     open=" AND ai.aiopsItem IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
        <if test="aiopsItem != null and aiopsItem != ''">
            AND ai.aiopsItem = #{aiopsItem}
        </if>
        LEFT JOIN aiops_device_product_mapping adpm ON adim.adId = adpm.adId
            AND ai.aiopsItemType = 'PRODUCT_APP' AND ai.aiopsItemId = adpm.appId
        WHERE 1 != 1
        <trim prefix=" OR (" prefixOverrides="AND" suffix=")">
            <if test="deviceIdList != null">
                <foreach collection="deviceIdList" item="item" open=" AND adim.deviceId IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="deviceId != null and deviceId != ''">
                AND adim.deviceId = #{deviceId}
            </if>
        </trim>
    </delete>

    <select id="selectAdimIdAiIdAdcdIdListByMap" resultType="java.util.Map">
        SELECT adim.id AS adimId, adcd.id AS adcdId
        <if test="needDeleteAiopsInstance != null and needDeleteAiopsInstance">
            , adim.aiId
        </if>
        FROM aiops_device_instance_mapping adim
        INNER JOIN aiops_instance ai ON adim.aiId = ai.id
        <if test="aiopsItemList != null">
            <foreach collection="aiopsItemList" item="item" open=" AND ai.aiopsItem IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
        <if test="aiopsItemIdList != null">
            <foreach collection="aiopsItemIdList" item="item" open=" AND ai.aiopsItemId IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
        <if test="aiopsItemTypeNotDevice != null and aiopsItemTypeNotDevice">
            AND ai.aiopsItemType != 'DEVICE'
        </if>
        LEFT JOIN aiops_device_collect_detail adcd ON ai.id = adcd.aiId AND adim.id = adcd.adimId
        <where>
            <if test="deviceId != null and deviceId != ''">
                AND adim.deviceId = #{deviceId}
            </if>
        </where>
    </select>

    <select id="selectAdcdTrueEnableByMap" resultType="java.util.Map">
        SELECT adcd.id, adcd.adcId, adcd.accId, adcd.aiId, adcd.adimId,
               ((CASE WHEN adcd.adimId IS NULL THEN ai.aiopsAuthStatus IN ('NONE', 'AUTHED')
                 ELSE (ai.aiopsAuthStatus IN ('NONE', 'AUTHED')
                    AND EXISTS(SELECT 1 FROM aiops_instance ai2
                               WHERE ai2.aiopsItemType = 'DEVICE' AND adim.deviceId = ai2.aiopsItemId
                                    AND ai2.aiopsAuthStatus IN ('NONE', 'AUTHED')))
                 END)
       	       AND adcd.isEnable) AS isEnable,
               ai.eid,
               adc.deviceId
        FROM aiops_device_collect_detail adcd
        INNER JOIN aiops_device_collect adc ON adcd.adcId = adc.id
        INNER JOIN aiops_instance ai ON adcd.aiId = ai.id
        LEFT JOIN aiops_device_instance_mapping adim ON adcd.aiId = adim.aiId AND adcd.adimId = adim.id
        <where>
            <if test="aicList != null and aicList.size() > 0">
                AND (
                <foreach collection="aicList" item="item" open=" ai.aiopsItemId IN(" separator=", " close=")">
                    #{item.aiopsItemId}
                </foreach>
                OR
                <foreach collection="aicList" item="item" open=" adim.deviceId IN(" separator=", " close=")">
                    #{item.aiopsItemId}
                </foreach>
                )
            </if>
            <if test="aiopsItemId != null and aiopsItemId != ''">
                AND (ai.aiopsItemId = #{aiopsItemId} OR adim.deviceId = #{aiopsItemId})
            </if>
        </where>
    </select>

    <select id="selectAdimByMap" resultType="com.digiwin.escloud.aioitms.device.model.DeviceInstanceMapping">
        SELECT adim.id, adim.adId, adim.deviceId, adim.aiId, adim.hasAddedAiopsInstance
        <if test="containAi != null and containAi">
            , ai.eid
        </if>
        FROM aiops_device_instance_mapping adim
        <if test="containAi != null and containAi">
            INNER JOIN aiops_instance ai ON adim.aiId = ai.id
            <if test="aiopsItemId != null and aiopsItemId != ''">
                AND ai.aiopsItemId = #{aiopsItemId}
            </if>
            <if test="aiopsItemIdList != null">
                <foreach collection="aiopsItemIdList" item="item"
                         open=" AND ai.aiopsItemId IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
        </if>
        <where>
            <if test="adimId != null and adimId > 0">
                AND adim.id = #{adimId}
            </if>
            <if test="adimIdList != null">
                <foreach collection="adimIdList" item="item" open=" AND adim.id IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="adId != null and adId > 0">
                AND adim.adId = #{adId}
            </if>
            <if test="deviceId != null and deviceId != ''">
                AND adim.deviceId = #{deviceId}
            </if>
            <if test="deviceIdList">
                <foreach collection="deviceIdList" item="item" open=" AND adim.deviceId IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="aiId != null and aiId > 0">
                AND adim.aiId = #{aiId}
            </if>
            <if test="aiIdList">
                <foreach collection="aiIdList" item="item" open=" AND adim.aiId IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <delete id="deleteProductApp">
        DELETE FROM aiops_device_product_mapping
        WHERE deviceId = #{deviceId}
        <if test="aiopsItemIdList != null">
            <foreach collection="aiopsItemIdList" item="item" open=" AND appId IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </delete>

    <select id="selectDbIdsByDeviceIdList" resultType="com.digiwin.escloud.aioitms.device.model.DBIdTmcdIdMapping">
        SELECT DISTINCT `add`.dbId ,ai.tmcdId
        FROM aiops_device_datasource as `add`
        LEFT JOIN aiops_instance as ai on `add`.dbId = ai.aiopsItemId
        WHERE `add`.isDelete = 0 and `add`.dbHostDevice = true
        <if test="deviceIdList != null">
            <foreach collection="deviceIdList" item="item" open=" AND `add`.deviceId IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <update id="updateDbInstanceAuthStatusUnauth">
        UPDATE aiops_instance 
        SET aiopsAuthStatus = 'UNAUTH'
        WHERE aiopsItemType = 'DATABASE' AND aiopsItemId = #{aiopsItemId}
    </update>

    <select id="selectAdcdSimpleListByMap" resultType="com.digiwin.escloud.aioitms.device.model.DeviceCollectDetail">
        SELECT adcd.id, adcd.adcId, adcd.accId, adcd.isEnable, adcd.aiId, adcd.adimId,
               acc.collectCode, acc.uploadDataModelCode
        FROM aiops_device_collect_detail adcd
        INNER JOIN aiops_collect_config acc ON adcd.accId = acc.id
        <where>
            <if test="adcdId != null and adcdId > 0">
                AND adcd.id = #{adcdId}
            </if>
            <if test="adcdIdList != null">
                <foreach collection="adcdIdList" item="item" open=" AND adcd.id IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="accId != null and accId > 0">
                AND accId = #{accId}
            </if>
            <if test="accIdList != null">
                <foreach collection="accIdList" item="item" open=" AND accId IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="aiId != null and aiId > 0">
                AND aiId = #{aiId}
            </if>
            <if test="aiIdList != null">
                <foreach collection="aiIdList" item="item" open=" AND aiId IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="adimId != null and adimId > 0">
                AND adimId = #{adimId}
            </if>
            <if test="adimIdList != null">
                <foreach collection="adimIdList" item="item" open=" AND adimId IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="deviceId != null and deviceId != ''">
                AND EXISTS(SELECT 1 FROM aiops_device_collect adc
                           WHERE adc.deviceId = #{deviceId} AND adcd.adcId = adc.id)
            </if>
            <if test="deviceIdList != null">
                <foreach collection="deviceIdList" item="item"
                         open=" AND EXISTS(SELECT 1 FROM aiops_device_collect adc
                                           WHERE adc.deviceId IN(" separator=", " close=")  AND adcd.adcId = adc.id)">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectAccSimpleListByDeviceIdList" resultType="com.digiwin.escloud.aioitms.device.model.DeviceCollectDetail">
        SELECT DISTINCT
        adcd.id,adc.deviceId,acc.collectCode,acc.id as accId,acc.collectName
        FROM aiops_device_collect_detail adcd
        INNER JOIN aiops_collect_config acc ON adcd.accId = acc.id
        INNER JOIN aiops_device_collect adc ON adcd.adcId = adc.id
        where 1=1
        <if test="enable != null">
            AND adcd.isEnable = #{enable}
        </if>
        <if test="deviceIdList != null and deviceIdList.size()>0">
            <foreach collection="deviceIdList" separator="," open=" AND adc.deviceId IN(" close=")" item="item">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectDeviceInfoV2ByMap" resultMap="DeviceMap">
        SELECT ad.*,adtm.deviceType adtm_deviceType
        FROM aiops_device ad
        LEFT JOIN aiops_device_type_mapping adtm ON ad.id = adtm.adId
        LEFT JOIN  aiops_instance ai ON ai.aiopsItemId = ad.deviceId
        <where>
            and ai.aiopsAuthStatus !='INVALID'
            <if test="eid != null and eid > 0">
                AND ad.eid = #{eid}
            </if>
            <if test="deviceId != null and deviceId != ''">
                AND ad.deviceId = #{deviceId}
            </if>
            <if test="deviceIdList != null">
                <foreach collection="deviceIdList" item="item" open=" AND ad.deviceId IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="eidList != null">
                <foreach collection="eidList" item="item" open=" AND ad.eid IN (" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectLatestDevicesByEidAndDeviceNames" resultType="java.util.Map">
        SELECT
            ad.*
        FROM
            aiops_device ad
        JOIN (
        SELECT eid, deviceName, MAX( lastCheckInTime ) AS maxLastCheckInTime
        FROM
            aiops_device
        WHERE
            eid = #{eid}
            AND deviceName IN
        <foreach item="item" collection="deviceNameList" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY
            eid, deviceName
        ) latest ON ad.eid = latest.eid
        AND ad.deviceName = latest.deviceName
        AND ad.lastCheckInTime = latest.maxLastCheckInTime;
    </select>

    <select id="getDeviceCntByDeviceType" resultType="com.digiwin.escloud.aioitms.model.device.TenantDeviceCnt">
        SELECT eid,adtm.deviceType,COUNT(*) cnt
        FROM aiops_device ad
        LEFT JOIN aiops_device_type_mapping adtm ON adtm.adId = ad.id
        WHERE 1=1
        <if test="eidList != null and eidList.size() > 0">
            <foreach collection="eidList" item="item" open=" AND ad.eid IN(" separator=", " close=")">
                #{item}
            </foreach>
        </if>
        <if test="deviceTypeList != null and deviceTypeList.size() > 0">
            <foreach collection="deviceTypeList" item="item" open=" AND adtm.deviceType IN(" separator=", " close=")">
                #{item}
            </foreach>
        </if>
        group by eid,adtm.deviceType
    </select>

    <select id="selectResmEnableStatus" resultType="java.lang.Boolean">
        SELECT IF( MIN( main.isWarningEnable ) &lt;= 0, FALSE, TRUE ) AS warningEnable
        FROM (
                 /*默認*/
                 SELECT  acw.isWarningEnable
                 FROM aiops_collect_warning acw
                 WHERE acw.id = #{acwId} AND acw.scopeId = 'DefaultConfig'
                 UNION ALL
                 /*租戶-開關自定義*/
                 SELECT atw.isWarningEnable
                 FROM aiops_tenant_warning atw
                 WHERE eid= #{eid}  AND acwId = #{acwId}
                 UNION ALL
                 /*設備*/
                 SELECT  adcw.isWarningEnable
                 FROM aiops_device_collect_warning adcw
                 WHERE acwId = #{acwId}  AND adcdId = #{adcdId}
             ) main
    </select>

    <select id="selectResmEnableStatusV2" resultType="java.lang.Boolean">
        SELECT IF( MIN( main.isWarningEnable ) &lt;= 0, FALSE, TRUE ) AS warningEnable
        FROM (
            /*默認*/
            SELECT acw.isWarningEnable
            FROM aiops_collect_warning acw
            WHERE acw.warningCode = #{warningCode} AND acw.scopeId = 'DefaultConfig'
            UNION ALL
            /*租戶-開關自定義*/
            SELECT atw.isWarningEnable
            FROM aiops_tenant_warning atw
            LEFT JOIN aiops_collect_warning acw ON acw.id = atw.acwId AND acw.scopeId != 'DefaultConfig'
            WHERE atw.eid = #{eid} AND (atw.acwId = #{acwId} OR acw.warningCode = #{warningCode})
            UNION ALL
            /*設備*/
            SELECT adcw.isWarningEnable
            FROM aiops_device_collect_warning adcw
            WHERE acwId = #{acwId} AND adcdId = #{adcdId}
        ) main
    </select>

    <select id="selectEidByAdcdId" resultType="Long">
        select distinct eid from aiops_device ad
                            inner join aiops_device_collect adc on ad.id = adc.adId
                            inner join aiops_device_collect_detail adcd on adc.id = adcd.adcId
        where adcd.id = #{adcdId}

    </select>

    <select id="selectAcwTenantEnable" resultType="com.digiwin.escloud.aioitms.device.model.AiopsTenantWarning">
        SELECT atw.acwId,atw.isWarningEnable
        FROM aiops_tenant_warning atw
        WHERE eid= #{eid}
        <if test="acwIdList != null and acwIdList.size() > 0">
           <foreach collection="acwIdList" item="item" open=" AND atw.acwId IN(" separator=", " close=")">
               #{item}
           </foreach>
        </if>
    </select>

    <select id="selectAcwTenantEnableByAcwId" resultType="com.digiwin.escloud.aioitms.device.model.AiopsTenantWarning">
        SELECT atw.acwId,atw.isWarningEnable
        FROM aiops_tenant_warning atw
        WHERE eid= #{eid} AND atw.acwId = #{acwId}

    </select>

    <select id="selectKitDeviceIdByAdId" resultType="com.digiwin.escloud.aioitms.model.device.AiopsKitDevice">
        SELECT id,eid,deviceId,deviceName
        FROM aiops_device
        WHERE id = #{adId}
    </select>

    <select id="selectAiIdByAcwIdList"
            resultType="com.digiwin.escloud.aioitms.device.model.AiopsItemCollectWarningSet$AiopsItemCollectWarningSetDetail">
        select adcw.acwId, adcd.aiId, adcd.accId, adcw.adcdId,if(acw.scopeId = #{eid} or acw.scopeId = 'DefaultConfig',false,true)
        isWarningCustomize,adcw.isWarningEnable,adc.deviceId,adcw.id as adcwId,acw.warningCode
        from aiops_device_collect_warning adcw
        inner join aiops_device_collect_detail adcd on adcd.id = adcw.adcdId AND (adcd.isEnable = 1
        <if test="innerRunnerAccIdSet != null">
            <foreach collection="innerRunnerAccIdSet" item="item"
                     open=" OR adcd.accId IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
        )
        inner join aiops_collect_warning acw on acw.id= adcw.acwId
#             因云备援没有设备id,无法查询出adc表数据 因此修改为left
        left join aiops_device_collect adc on adc.id = adcd.adcId
        left join aiops_instance ai on ai.id = adcd.aiId
        where ai.eid = #{eid}
        <if test="warningCodeList != null">
            <foreach collection="warningCodeList" open="and acw.warningCode IN (" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="aiopsItem != null and aiopsItem != ''">
            and ai.aiopsItem = #{aiopsItem}
        </if>
        <if test="aiopsAuthStatusList != null">
            <foreach collection="aiopsAuthStatusList" item="item"
                     open=" AND ai.aiopsAuthStatus IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
        order by adcw.__version__
    </select>
    <select id="selectFixData" resultType="com.digiwin.escloud.aioitms.collectwarning.model.CollectWarning">
        select id,warningCode from aiops_collect_warning where scopeId = 'DefaultConfig' and warningCode in (select warningCode from aiops_collect_warning where scopeId = #{eid}
                                                                                                                                                             and sourceAcwId is null)
    </select>
    <select id="selectNeedFixData" resultType="com.digiwin.escloud.aioitms.collectwarning.model.CollectWarning">
        select warningCode,id from aiops_collect_warning where scopeId = #{eid}
                                                        and sourceAcwId is null
    </select>
    <update id="updateNeedFixData" >
      update aiops_collect_warning set sourceAcwId = #{sourceAcwId} where id = #{id}
    </update>
</mapper>