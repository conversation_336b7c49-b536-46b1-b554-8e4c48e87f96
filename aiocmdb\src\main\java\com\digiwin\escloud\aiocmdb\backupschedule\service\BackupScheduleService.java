package com.digiwin.escloud.aiocmdb.backupschedule.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.digiwin.escloud.aiocmdb.assetmaintenance.bigdata.DataService;
import com.digiwin.escloud.aiocmdb.assetmaintenance.service.AssetMaintenanceService;
import com.digiwin.escloud.aiocmdb.backupschedule.model.BackupScheduleExcelUploadResponse;
import com.digiwin.escloud.aiocmdb.backupschedule.model.BackupScheduleSoftware;
import com.digiwin.escloud.aiocmdb.backupschedule.model.ExcelBackupScheduleData;
import com.digiwin.escloud.aiocmdb.report.dao.ReportDao;
import com.digiwin.escloud.aiocmdb.util.CommonUtils;
import com.digiwin.escloud.aiocmdb.util.ModelUtils;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.RequestUtil;
import com.digiwin.escloud.common.util.SnowFlake;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
@Service
public class BackupScheduleService implements IBackupScheduleService {
    @Autowired
    private AssetMaintenanceService assetMaintenanceService;
    @Autowired
    private DataService dataService;
    @Autowired
    private CommonUtils commonUtils;
    @Autowired
    private ModelUtils modelUtils;
    @Resource
    private ReportDao reportDao;
    @Override
    public List<Map<String, Object>> getAssetList(String modelCode,long eid,String deviceStatus,String filter,int pageNum,int pageSize, boolean auto, JSONObject jsonObject) {
        String queryField = "get_json_object(model,'$.BasicInfo.f_deviceCatalog') , get_json_object(model,'$.BasicInfo.f_sourceDeviceName') , get_json_object(model,'$.BasicInfo.deviceName') ";
        List<Map<String, Object>> resultList = dataService.getBackupScheduleList( modelCode, eid, deviceStatus, filter, queryField, "", "", pageNum, pageSize, jsonObject);
        if(!CollectionUtils.isEmpty(resultList)){
            resultList.stream().forEach(k->{
                if(k.get("MODEL") != null){
                    k.put("field",JSON.parseObject(k.get("MODEL").toString(), JSONObject.class, Feature.OrderedField));
                }

            });
        }
        return resultList;
    }

    @Override
    public long getAssetCount(String modelCode,long eid,String deviceStatus,String filter) {
        return dataService.getBackupScheduleCount(modelCode, eid ,deviceStatus, "","",filter );
    }

    public BaseResponse addRelateAsset(String modelCode, String serviceCode,long eid, String deviceCatalog,String sourceDeviceId,String relateDeviceId,String relateDeviceName,String maintenanceYearMonth,JSONObject jsonObject) {
        BaseResponse res = new BaseResponse();
        try {
            JSONObject jo = new JSONObject();
            jo.put("deviceId", sourceDeviceId );
            jo.put("serviceCode",serviceCode);

            log.debug("当前年月："+maintenanceYearMonth);
            jo.put("maintenanceYearMonth",maintenanceYearMonth);

            jo.put("maintenanceUser",jsonObject.get("owner")==null?"":jsonObject.get("owner"));

            JSONArray relationJA = new JSONArray();
            JSONObject relationJO = new JSONObject();
            relationJO.put("relationCode",deviceCatalog);
            relationJO.put("relationModel",modelCode);

            JSONArray relationItemsJA = new JSONArray();
            JSONObject relationItemJO = new JSONObject();
            relationItemJO.put("deviceId",relateDeviceId);
            relationItemJO.put("deviceName",relateDeviceName);
            relationItemsJA.add(relationItemJO);

            relationJO.put("relationItems",relationItemsJA);
            relationJA.add(relationJO);
            jo.put("relation",relationJA);
            //添加关联
            return assetMaintenanceService.addRelateAsset( deviceCatalog, eid, modelCode, relateDeviceId, relateDeviceName, jo);

        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex.getMessage());
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg(ex.getMessage());
        }
        return res;
    }

    public void addRelateAssetCurrentMonthMaintenance(String modelCode, String serviceCode, long eid, String deviceId,String relateDeviceName, String maintenanceYearMonth,String maintenanceUser,JSONObject jsonObject){
        Map map = assetMaintenanceService.getMaintenanceDetail(modelCode, RequestUtil.getHeaderSid(), eid,maintenanceYearMonth,deviceId);
        if(map==null){
            log.info("新增备份排程的当月维护记录 开始");
            assetMaintenanceService.newCurrentMonthMaintenance(modelCode,RequestUtil.getHeaderSid(), eid,maintenanceYearMonth,maintenanceUser,deviceId,relateDeviceName,false);
        }else{
            log.info("更新备份排程的当月维护记录 开始");
            jsonObject.put("maintenanceCode",map.get("maintenanceCode"));
            jsonObject.put("deviceId",map.get("deviceId"));
            jsonObject.put("maintenanceYearMonth",maintenanceYearMonth);

            JSONObject modelJO = assetMaintenanceService.generateJsonObjectV2(modelCode);
            commonUtils.dealCollectDataNew(jsonObject, modelJO);
            dataService.upsertMaintenance(modelCode,map.get("ROWKEY").toString(),eid,deviceId,map.get("maintenanceCode").toString(),maintenanceYearMonth,maintenanceUser,map.get("maintenanceTime").toString(),JSON.toJSONString(modelJO),map.get("relation")!=null?map.get("relation").toString():"");

            //更新报表数据
            Runnable runnable = () -> assetMaintenanceService.updateDeviceReportData(modelCode, eid, deviceId);
            commonUtils.asyncRun(runnable);

        }
    }

    @Override
    public BaseResponse addBackupschedule(String modelCode, long sid, String serviceCode,long eid, String deviceCatalog,String sourceDeviceName,JSONObject jsonObject) {
        // 1.先生成备份排程资产
        BaseResponse baseResponse = addBackupscheduleAsset(modelCode, sid, eid, deviceCatalog, sourceDeviceName, jsonObject);
        if(baseResponse.getCode().equals(ResponseCode.SUCCESS.toString())){

            JSONObject asset = (JSONObject) baseResponse.getData();
            if(asset.getJSONObject("BasicInfo") == null){
                log.error("备份排程的维护记录无法创建！！");
                return baseResponse;
            }
            String relateDeviceId = asset.getJSONObject("BasicInfo").getString("deviceId");
            String relateDeviceName = asset.getJSONObject("BasicInfo").getString("deviceName");

            //更新报表数据
            Runnable runnable = () -> assetMaintenanceService.updateDeviceReportData(modelCode, eid, relateDeviceId);
            commonUtils.asyncRun(runnable);

            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
            Calendar c = Calendar.getInstance();
            c.setTime(new Date());
            Date m = c.getTime();
            String maintenanceYearMonth = format.format(m);
            log.debug("当前年月："+maintenanceYearMonth);

            //1.生成备份排程的当月维护记录
            log.info("生成备份排程的当月维护记录 开始");
            addRelateAssetCurrentMonthMaintenance(modelCode,serviceCode,eid,relateDeviceId,relateDeviceName,maintenanceYearMonth, StringUtils.isEmpty(asset.getJSONObject("BasicInfo").getString("owner")) ? "" : jsonObject.get("owner").toString(),(JSONObject)baseResponse.getData());
            log.info("生成备份排程的当月维护记录 结束");

            //2.源设备名称+分类不为空的时候，需要添加关联
            if(!StringUtils.isEmpty(sourceDeviceName) && !StringUtils.isEmpty(deviceCatalog)){


                //2.查源设备id
                Map<String, Object> exixtResourceAsset = findResourceAsset(deviceCatalog,sid,eid,sourceDeviceName);
                if(exixtResourceAsset == null){
                    baseResponse.setCode(ResponseCode.SOURCE_ASSET_IS_NULL.toString());
                    baseResponse.setErrMsg(ResponseCode.SOURCE_ASSET_IS_NULL.getMsg());
                    return baseResponse;
                }

                String sourceDeviceId = "";
                if(exixtResourceAsset.get("deviceId") != null){
                    sourceDeviceId = exixtResourceAsset.get("deviceId").toString();
                }
                //3.生成源设备的当月维护记录
                log.info("生成源设备的当月维护记录 开始");
                assetMaintenanceService.addRelateAssetCurrentMonthMaintenance(deviceCatalog,eid,sourceDeviceId,sourceDeviceName,maintenanceYearMonth,StringUtils.isEmpty(asset.getJSONObject("BasicInfo").getString("owner")) ? "" : jsonObject.get("owner").toString());
                log.info("生成源设备的当月维护记录 结束");

                //3.给源设备的资产和维护记录添加关联
                BaseResponse response = addRelateAsset( modelCode,  serviceCode, eid, deviceCatalog, sourceDeviceId, relateDeviceId, relateDeviceName, maintenanceYearMonth, jsonObject);
                if(response.getCode().equals(ResponseCode.SUCCESS.toString())){
                    return response;
                }else {
                    response.setCode(ResponseCode.ASSET_RELATE_CREATE_FAIL.toString());
                    response.setErrMsg(ResponseCode.ASSET_RELATE_CREATE_FAIL.getMsg());
                    return response;
                }
            }
            return baseResponse;
        }
        if(!baseResponse.getCode().equals(ResponseCode.DEVICENAME_IS_NULL.toString())) {
            baseResponse.setCode(ResponseCode.ASSET_CREATE_FAIL.toString());
        }
        baseResponse.setErrMsg(ResponseCode.ASSET_CREATE_FAIL.getMsg());
        return baseResponse;
    }
    public Map<String, Object> findAsset(String modelCode,long sid,long eid,String deviceCatalog,String sourceDeviceName,String backupSchName,String deviceName){
        return dataService.getBackupScheduleDetail(modelCode,eid,deviceCatalog,sourceDeviceName,backupSchName,deviceName);
    }
    public Map<String, Object> findResourceAsset(String modelCode,long sid,long eid,String deviceName){
        deviceName = deviceName.replaceAll("\\\\","\\\\\\\\");
        return dataService.getBackupScheduleDetail(modelCode,eid,"","","",deviceName);
    }
    public BaseResponse addBackupscheduleAsset(String modelCode, long sid, long eid,String deviceCatalog,String sourceDeviceName,JSONObject jsonObject) {
        BaseResponse res = new BaseResponse();
        try {
            if(StringUtils.isEmpty(jsonObject.get("deviceName"))){
                res.setCode(ResponseCode.DEVICENAME_IS_NULL.toString());
                res.setErrMsg(ResponseCode.DEVICENAME_IS_NULL.getMsg());
                return res;
            }
            //开始新增拉
            JSONObject newJsonObject = new JSONObject(true);

            newJsonObject.put("eid",eid);
            newJsonObject.put("deviceId","");
            newJsonObject.put("deviceName",jsonObject.get("deviceName"));
            newJsonObject.put("owner",jsonObject.get("owner")==null?"":jsonObject.get("owner"));
            newJsonObject.put("deviceStatus","Y");

            DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date now = new Date();
            String date = dateFormat.format(now);
            newJsonObject.put("createTime","");
            newJsonObject.put("updateTime","");

            JSONObject modelJO = assetMaintenanceService.generateJsonObjectV2(modelCode);

           /* //将信息更新到模型对象中
            commonUtils.dealCollectData(newJsonObject,modelJO);*/
//            commonUtils.dealCollectDataV2(jsonObject.getJSONObject("field"),modelJO);
            assetMaintenanceService.dealAssetDateV2(jsonObject.getJSONObject("field"),modelJO);
            //选了是
            if(!StringUtils.isEmpty(sourceDeviceName) && !StringUtils.isEmpty(deviceCatalog)){
                //来源设备分类不空时，需要校验来源设备分类+来源设备名称+排程名称是否存在
                Map<String, Object> exixtAsset = findAsset(modelCode,sid,eid,deviceCatalog,sourceDeviceName,jsonObject.get("deviceName").toString(),"");
                if(exixtAsset != null && exixtAsset.get("deviceId") != null){
                    System.out.println("exixtAsset:" + modelCode + eid + deviceCatalog + sourceDeviceName);
                    newJsonObject.put("updateTime",jsonObject.get("updateTime")!=null?jsonObject.get("updateTime").toString():date);
                    newJsonObject.put("createTime",exixtAsset.get("createTime")!=null?exixtAsset.get("createTime").toString():date);
                    newJsonObject.put("deviceId", exixtAsset.get("deviceId"));

                    //将系统字段信息更新到模型对象中
                    commonUtils.dealCollectDataV2(newJsonObject,modelJO);
                    if(exixtAsset.get("MODEL") != null){
                        commonUtils.dealCollectDataV2(JSON.parseObject(exixtAsset.get("MODEL").toString()) ,modelJO);
                    }
                    if(exixtAsset.get("relation") != null){
                        commonUtils.dealCollectDataV2(JSON.parseObject(exixtAsset.get("relation").toString()) ,modelJO);
                    }
                    String rowKey =  modelUtils.getAssetKey(modelCode,modelJO);
                    if(StringUtils.isEmpty(rowKey)){
                        rowKey = exixtAsset.get("deviceId").toString();//ROWKEY
                    }
                    dataService.upsertAsset(modelCode,rowKey,eid,exixtAsset.get("deviceId").toString(),JSONObject.toJSONString(modelJO));
                }else{
                    //开始新增拉
                    newJsonObject.put("createTime",jsonObject.get("createTime")!=null?jsonObject.get("createTime").toString():date);
                    newJsonObject.put("updateTime",jsonObject.get("updateTime")!=null?jsonObject.get("updateTime").toString():date);

                    String deviceId = jsonObject.get("deviceId")==null? String.valueOf(SnowFlake.getInstance().newId()): jsonObject.get("deviceId").toString();
                    String rowKey = deviceId;//ROWKEY
                    newJsonObject.put("deviceId", deviceId);
                    //将系统字段信息更新到模型对象中
                    commonUtils.dealCollectDataV2(newJsonObject,modelJO);
                    log.info("来源设备分类+来源设备名称+排程名称不存在，新增排程资产");
                    dataService.upsertAsset(modelCode,rowKey,eid,deviceId,JSONObject.toJSONString(modelJO));
                }
            }else{//选了否
                log.info("新增排程资产");
                newJsonObject.put("createTime",jsonObject.get("createTime")!=null?jsonObject.get("createTime").toString():date);
                newJsonObject.put("updateTime",jsonObject.get("updateTime")!=null?jsonObject.get("updateTime").toString():date);

                String deviceId = jsonObject.get("deviceId")==null?String.valueOf(SnowFlake.getInstance().newId()) : jsonObject.get("deviceId").toString();
                String rowKey = deviceId;//ROWKEY
                newJsonObject.put("deviceId", deviceId);
                //将系统字段信息更新到模型对象中
                commonUtils.dealCollectDataV2(newJsonObject,modelJO);
                dataService.upsertAsset(modelCode,rowKey,eid,deviceId,JSONObject.toJSONString(modelJO));
            }

            res.setData(modelJO);
            res.setCode(ResponseCode.SUCCESS.toString());
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex.getMessage());
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg(ex.getMessage());
        }
        return res;
    }

    @Override
    public BackupScheduleExcelUploadResponse batchUpload(String nickName,String modelCode, String serviceCode, long eid,MultipartFile file) throws IOException {
        BackupScheduleExcelUploadResponse response = new BackupScheduleExcelUploadResponse();
        EasyExcel.read(file.getInputStream(), ExcelBackupScheduleData.class, new BackupScheduleExcelUploadListener(nickName,modelCode, serviceCode, eid,this,response)).sheet().headRowNumber(1).doRead();
        return response;
    }
    @Override
    public BaseResponse batchDeleteAssetAndRelateAsset(List<Map<String,String>> list, long eid, String modelGroupCode, boolean deleteRelateAsset) {
        BaseResponse res = new BaseResponse();
        if(list==null){
            res.setCode(ResponseCode.ASSET_IS_NOT_SELECTED.toString());
            res.setErrMsg(ResponseCode.ASSET_IS_NOT_SELECTED.getMsg());
            return res;
        }
        for(int i = 0; i< list.size();i++){
            Map<String,String> map =  list.get(i);
            if(map==null){
                res.setCode(ResponseCode.ASSET_IS_NULL.toString());
                res.setErrMsg(ResponseCode.ASSET_IS_NULL.getMsg());
                return res;
            }

            String serviceCode = map.get("serviceCode");
            if(StringUtils.isEmpty(serviceCode)){
                res.setCode(ResponseCode.SERVICECODE_IS_NULL.toString());
                res.setErrMsg(ResponseCode.SERVICECODE_IS_NULL.getMsg());
                return res;
            }
            String modelCode = map.get("modelCode").toString();
            if(StringUtils.isEmpty(modelCode)){
                res.setCode(ResponseCode.MODELCODE_IS_NULL.toString());
                res.setErrMsg(ResponseCode.MODELCODE_IS_NULL.getMsg());
                return res;
            }
            String deviceId = map.get("deviceId").toString();
            if(StringUtils.isEmpty(deviceId)){
                res.setCode(ResponseCode.DEVICEID_IS_NULL.toString());
                res.setErrMsg(ResponseCode.DEVICEID_IS_NULL.getMsg());
                return res;
            }
        }
        if(!CollectionUtils.isEmpty(list)){
            for(int i = 0; i< list.size();i++){
                Map<String,String> map =  list.get(i);
                String serviceCode = map.get("serviceCode").toString();
                String modelCode = map.get("modelCode").toString();
                String deviceId = map.get("deviceId").toString();
                String f_deviceCatalog = map.get("f_deviceCatalog").toString();

                deleteAssetAndRelateAsset( modelCode, f_deviceCatalog, deviceId, serviceCode, eid, modelGroupCode, deleteRelateAsset);

                //更新报表数据
                Runnable runnable = () -> assetMaintenanceService.updateDeviceReportData(modelCode, eid, deviceId);
                commonUtils.asyncRun(runnable);
            }

            /*//更新报表数据
            Runnable runnable = () -> assetMaintenanceService.updateDeviceReportData(modelCode, eid, deviceId);
            commonUtils.asyncRun(runnable);*/
        }


        res.setCode(ResponseCode.SUCCESS.toString());
        return res;
    }

    public void deleteAssetAndRelateAsset(String modelCode, String f_deviceCatalog, String deviceId, String serviceCode ,long eid, String modelGroupCode, boolean deleteRelateAsset) {
        //1.先删除资产
        log.info("删除备份排程设备 开始");
        assetMaintenanceService.deleteAsset(modelCode, deviceId, eid);
        log.info("删除备份排程设备 结束");
        //2.删除资产维护记录
//        deleteMaintenance(modelCode, deviceId, serviceCode);

        //3.删除被关联资产
        if(deleteRelateAsset){
            log.info("删除源设备中的关联关系 开始");
            assetMaintenanceService.deleteRelateAssetForBackupSchedule(deviceId, modelCode, f_deviceCatalog, eid, modelGroupCode);
            log.info("删除源设备中的关联关系 结束");
        }

        //4.删除被关联资产的维护记录(历史维护记录不删除)
        if(deleteRelateAsset){
            log.info("删除源设备维护记录（当月，历史维护记录的关联关系不删除）中的关联关系 开始");
            assetMaintenanceService.deleteRelateAssetMaintenanceForBackupSchedule(deviceId, modelCode, f_deviceCatalog, eid,modelGroupCode);
            log.info("删除源设备维护记录（当月，历史维护记录的关联关系不删除）中的关联关系 结束");
        }
    }


    @Override
    public void deleteAssetAndRelateAsset(String modelCode, String deviceId, String serviceCode ,long eid, String modelGroupCode, boolean deleteRelateAsset) {
        //1.先删除资产
        log.info("删除备份排程设备 开始");
        assetMaintenanceService.deleteAsset(modelCode, deviceId, eid);
        log.info("删除备份排程设备 结束");

        //更新报表数据
        Runnable runnable = () -> assetMaintenanceService.updateDeviceReportData(modelCode, eid,deviceId);
        commonUtils.asyncRun(runnable);
        //2.删除资产维护记录
//        deleteMaintenance(modelCode, deviceId, serviceCode);

        //3.删除被关联资产
        if(deleteRelateAsset){
            log.info("删除源设备中的关联关系 开始");
            Runnable runnable1 = () -> assetMaintenanceService.deleteRelateAsset(deviceId, modelCode, eid, modelGroupCode);
            commonUtils.asyncRun(runnable1);
            log.info("删除源设备中的关联关系 结束");
        }

        //4.删除被关联资产的维护记录(历史维护记录不删除)
        if(deleteRelateAsset){
            log.info("删除源设备维护记录（当月，历史维护记录的关联关系不删除）中的关联关系 开始");
            Runnable runnable2 = () -> assetMaintenanceService.deleteRelateAssetMaintenance(deviceId, modelCode, eid,modelGroupCode);
            commonUtils.asyncRun(runnable2);
            log.info("删除源设备维护记录（当月，历史维护记录的关联关系不删除）中的关联关系 结束");
        }
    }

    @Override
    public BaseResponse getBackupScheduleSoftwareList(){
        List<BackupScheduleSoftware> backSoftList = reportDao.getBackSoftList();
       return BaseResponse.ok(backSoftList);
    }

}
