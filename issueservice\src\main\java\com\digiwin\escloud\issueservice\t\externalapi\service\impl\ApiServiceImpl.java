package com.digiwin.escloud.issueservice.t.externalapi.service.impl;


import com.digiwin.escloud.issueservice.model.*;
import com.digiwin.escloud.issueservice.services.IIssueService;
import com.digiwin.escloud.issueservice.t.externalapi.dao.ApiMapper;
import com.digiwin.escloud.issueservice.t.externalapi.model.FuguanRequestPayload;
import com.digiwin.escloud.issueservice.t.externalapi.model.SCMRequest;
import com.digiwin.escloud.issueservice.t.externalapi.service.ApiService;
import com.digiwin.escloud.issueservice.t.integration.fuguan.mq.issue.IssueFGProducer;
import com.digiwin.escloud.issueservice.t.issuedetail.dao.IssueProcessMapper;
import com.digiwin.escloud.issueservice.t.issuedetail.service.IssueProcessService;
import com.digiwin.escloud.issueservice.t.issuesubmit.dao.IssueSubmitMapper;
import com.digiwin.escloud.issueservice.t.model.cases.CaseHistory;
import com.digiwin.escloud.issueservice.t.model.cases.CaseRemarkDetail;
import com.digiwin.escloud.issueservice.t.model.cases.Cases;
import com.digiwin.escloud.issueservice.t.model.cases.constants.CaseProcessType;
import com.digiwin.escloud.issueservice.t.model.cases.constants.CaseStatus;
import com.digiwin.escloud.issueservice.t.model.cases.constants.SubmitWay;
import com.digiwin.escloud.issueservice.t.model.common.BaseResponse;
import com.digiwin.escloud.issueservice.t.model.common.ResponseStatus;
import com.digiwin.escloud.issueservice.t.model.constant.ERPProductCode;
import com.digiwin.escloud.issueservice.t.model.constant.JiaoFuUserType;
import com.digiwin.escloud.issueservice.t.model.constant.UserType;
import com.digiwin.escloud.issueservice.t.utils.CaseOperationChecker;
import com.digiwin.escloud.issueservice.t.utils.CasesProcessUtils;
import com.digiwin.escloud.issueservice.t.utils.DateUtils;
import com.digiwin.escloud.issueservice.t.utils.StringUtil;
import com.digiwin.escloud.userapi.model.customer.CustomerDetailInfo;
import com.digiwin.escloud.userapi.model.user.UserDetailInfo;
import com.digiwin.escloud.userapi.service.UserServiceFeignClient;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 案件详情接口实现类
 * Created by huly on 2019-7-25
 */
@Service
@Slf4j
public class ApiServiceImpl implements ApiService {
    @Autowired
    private ApiMapper apiMapper;
    @Autowired
    private IssueProcessMapper issueProcessMapper;
    @Autowired
    private IssueSubmitMapper issueSubmitMapper;
    @Autowired
    private CasesProcessUtils casesProcessUtils;
    @Autowired
    private IssueProcessService issueProcessService;
    @Autowired
    private UserServiceFeignClient userServiceFeignClient;
    @Autowired
    private IssueFGProducer fg187Mq;
    @Autowired
    private CaseOperationChecker operationChecker;
    @Autowired
    private IIssueService issueService;

    @Override
    public BaseResponse syncCaseStatusFromT100SCM(SCMRequest reqCase) throws RuntimeException{
        log.info("型管请求参数:{}", reqCase.toString());
        try{
            checkNotEmpty(reqCase);
        } catch (IllegalArgumentException e) {
            return BaseResponse.error(ResponseStatus.WRONG_PARAMETER, e.getMessage());
        }
        //获取当前案件信息
        Cases caseInfo;
        Optional<Cases> issueInfo = getCase(reqCase.getCaseId());
        if(issueInfo.isPresent()) {
            caseInfo = issueInfo.get();
        } else {
            return BaseResponse.error(ResponseStatus.EXPECTATION_FAILED, "没有查询到对应的案件信息");
        }
        //获取当前处理人的信息
        //型管不回传操作人信息，故将当前处理人作为处理人
        if(StringUtils.isEmpty(caseInfo.getServiceId())){
            return BaseResponse.error(ResponseStatus.EXPECTATION_FAILED, "案件当前处理人为空,请联系服务云管理员");
        }
        UserDetailInfo processer = userServiceFeignClient.findUserDetailById(caseInfo.getServiceId());
        Optional<Cases> newCase;
        Optional<BaseResponse> checkResult;
        switch (reqCase.getFlag()) {
            case "1":    //型管退单
                checkResult = checkPermissionForRefuse(caseInfo.getCurrentStatus());
                if(checkResult.isPresent()) return checkResult.get();
                newCase = refuseByT100(reqCase, caseInfo);
                break;
            case "2":    //撤回到型管重新处理
                checkResult = checkPermissionForGetback(caseInfo.getIssueId(), caseInfo.getCurrentStatus());
                if(checkResult.isPresent()) return checkResult.get();
                UserDetailInfo handler = userServiceFeignClient.getUserDetailByWorkNo(reqCase.getCzHandlerId());
                processer = handler;
                newCase = getBack(reqCase, caseInfo, handler);
                break;
            default:     //型管同步案件状态
                checkResult = checkRequestStatus(reqCase);
                if(checkResult.isPresent()) return checkResult.get();
                checkResult = checkPermissionForUpdateStatus(caseInfo.getCurrentStatus());
                if(checkResult.isPresent()) return checkResult.get();
                newCase = updateStatus(reqCase, caseInfo, processer);
                break;
        }
        saveToDatabase(newCase, processer, reqCase.getCzCaseId(),reqCase.getStatus());
        fg187Mq.send(caseInfo.getIssueId(), caseInfo.getMaxSequenceNum()+1);
        Runnable runnable = () -> casesProcessUtils.sendToWorkday(caseInfo.getIssueId(), caseInfo.getMaxSequenceNum()+1, 0);
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        try {
            Thread.sleep(2500); // 休眠2.5秒钟（单位为毫秒）
            executorService.execute(runnable);
        } catch (Exception ex) {
            log.error("sendToWorkday", ex);
        } finally {
            executorService.shutdown();
        }

        return BaseResponse.ok();
    }

    //事务管理，方法必须为public
    @Transactional(rollbackFor = RuntimeException.class)
    public void saveToDatabase(Optional<Cases> newCase, UserDetailInfo processer, String czRequId, String czStatus) throws RuntimeException{
        if(newCase.isPresent()) {
            Cases issue = newCase.get();
            //存表前先判断是否需要同步到CRM，以设置同步标志位
            //issue.setSyncToCrm(issueService.checkIssueIsSyncCrm(issue.getServiceRegion(), issue.getProductCode(),issue.getCurrentStatus()));
            CaseHistory caseHistory = constructCaseHistory(issue, processer, czRequId);
            issueProcessMapper.doUpdateIssue(issue);
            issueProcessMapper.doSaveIssueProgress(caseHistory);
            //产中结案需要回写产中结案时间
            if(CaseProcessType.PC_CLOSED.getProcessType().equals(czStatus)){
                casesProcessUtils.saveIssueSummaryV3(processer.getUserId(),issue.getIssueId(), IssueProcessType.ProductClose.toString(), caseHistory.getProcessTime());
            }
        }
    }

    /**
     * 查找最后一个抛转产中的操作者
     * @param issueId 案件编号
     * @return 案件操作人
     */
    private String lastToT100Processer(long issueId){
        List<String> processers = apiMapper.findToT100Processer(issueId, CaseProcessType.OPEN.getProcessType(),
                CaseProcessType.TURN_TO_T100.getProcessType(), CaseStatus.PC_HANDLING.getStatus());
        assert !processers.isEmpty();
        //获取抛转产中最后一个处理人
        return processers.get(processers.size()-1);
    }

    /**
     * 构造案件处理记录实体
     * @param newCase 案件处理后最新的状态
     * @param processer 案件处理人信息
     * @param t100CaseId T100型管对应的案件需求单号
     * @return 案件处理记录实体
     */
    private CaseHistory constructCaseHistory(Cases newCase, UserDetailInfo processer, String t100CaseId){
        CaseHistory caseHistory = new CaseHistory();
        caseHistory.setIssueId(newCase.getIssueId());
        caseHistory.setCrmId(newCase.getCrmId());
        caseHistory.setSequenceNum(getCaseProcessSeq(newCase.getIssueId())+1);
        caseHistory.setProcessType(newCase.getProcessType());
        caseHistory.setCurrentStatus(newCase.getCurrentStatus());
        caseHistory.setProcessor(processer.getUserId());
        caseHistory.setProcessorName(newCase.getServiceName());
        caseHistory.setProcessTime(StringUtils.isEmpty(newCase.getUpdateTime())? DateUtils.getCurrentTime():newCase.getUpdateTime());
        //内部员工，需要获取工号存到workno
        caseHistory.setWorkno(processer.getWorkno());
        caseHistory.setDescription(newCase.getHandlerDetail());
        caseHistory.setHandlerId(newCase.getServiceId());
        if(!StringUtils.isEmpty(newCase.getHandlerDetail()) && !SyncStatus.DontNeedSync.toString().equals(newCase.getSyncStatus())){
            //有內容再同步且單頭同步狀態不為Z
            caseHistory.setSyncStatus(SyncStatus.EditUnSync.toString());
        }else {
            //內容為空則不同步或單頭為不同步
            caseHistory.setSyncStatus(SyncStatus.DontNeedSync.toString());
        }
        if(String.valueOf(UserType.INNER.getValue()).equals(processer.getUserType())){
            caseHistory.setReplyType("A");
        }
        caseHistory.setProcessHours(0.0);
        caseHistory.setT100CaseId(StringUtils.isEmpty(t100CaseId)? null : t100CaseId);
        return caseHistory;
    }

    /**
     * T100型管退单时，检查操作权限
     * @param caseStatus 案件当前状态
     * @return 响应实体
     */
    private Optional<BaseResponse> checkPermissionForRefuse(String caseStatus){
        //案件当前处理状态是产中时才接受请求，兼容产中处理中：O的状态
        if(!caseStatus.equals(CaseStatus.PC_HANDLING.getStatus()) || !caseStatus.equals(IssueStatus.ProductProcessing.toString())){
            return Optional.of(BaseResponse.error(ResponseStatus.FORBIDDEN, "案件当前状态不是产中处理中，无法拒单."));
        }
        return Optional.empty();
    }

    /**
     * 处理型管退单的情况，也就是型管拒绝接受这个问题
     * 请求参数flag为1
     * @param reqCase 请求的参数对象实体
     * @param issueInfo 当前案件信息
     * @return 更新状态后的案件信息
     */
    private Optional<Cases> refuseByT100(SCMRequest reqCase, Cases issueInfo){
        //获取转产中或抛转产中的处理人为下一阶段处理人
        UserDetailInfo handler = userServiceFeignClient.findUserDetailById(lastToT100Processer(issueInfo.getIssueId()));
        //验证当前处理人是否离职
        handler = casesProcessUtils.validateCurrentServiceStaff(handler);
        //产中拒单，案件主要负责人如果在产中,则主要负责人也撤回到最后一个交付的处理人
        if(issueInfo.getMainCharge() != null && issueInfo.getMainCharge().equals(issueInfo.getServiceId())) {
            issueInfo.setMainCharge(handler.getUserId());
            issueInfo.setMainChargeName(handler.getUsername());
        }
        Cases newCase;
        if(handler.getJiaofuType() == JiaoFuUserType.JIAOFUFUWU.getValue()) {
            newCase = toNewStatus(issueInfo, reqCase, CaseProcessType.XINGGUAN_REVOKE_CZ, CaseStatus.JIAOFU_SERVICE_HANDLING, handler);
        } else {
            newCase = toNewStatus(issueInfo, reqCase, CaseProcessType.XINGGUAN_REVOKE_CZ, CaseStatus.JIAOFU_CONSULTANT_HANDLING, handler);
        }
        newCase.setCzRevoke("Y");
        return Optional.of(newCase);
    }

    /**
     * T100型管撤单时，检查操作权限
     * @param issueId 案件编号
     * @param caseStatus 案件当前状态
     * @return 响应实体
     */
    private Optional<BaseResponse> checkPermissionForGetback(long issueId, String caseStatus){
        if(caseStatus.equals(CaseStatus.PC_HANDLING.getStatus()) || caseStatus.equals(IssueStatus.ProductProcessing.toString())){
            return Optional.of(BaseResponse.error(ResponseStatus.CONFLICT, "案件当前状态是产中处理中，无需撤回."));
        }
        if(caseStatus.equals(CaseStatus.CLOSED.getStatus()) ||
                caseStatus.equals(CaseStatus.AUTO_CLOSE.getStatus())){
            return Optional.of(BaseResponse.error(ResponseStatus.FORBIDDEN, "已结案,不能撤回."));
        }
        //案件必须是交付验证中的状态才能撤回
        if(!caseStatus.equals(CaseStatus.JIAOFU_VERIFICATION.getStatus())){
            return Optional.of(BaseResponse.error(ResponseStatus.FORBIDDEN,
                    "案件当前状态:"+caseStatus+"不能撤回."));
        }
        return Optional.empty();
    }

    /**
     * 交付处理不满意时，型管从服务云撤单到型管重新处理
     * 请求参数flag为2
     * @param reqCase 请求参数
     * @param issueInfo 当前案件信息
     * @param handler 当前处理人
     * @return 更新状态后的案件信息
     */
    private Optional<Cases> getBack(SCMRequest reqCase, Cases issueInfo, UserDetailInfo handler){
        Cases newCase = toNewStatus(issueInfo, reqCase, CaseProcessType.XINGGUAN_CANCEL_JIAOFU_VERIFICATION,
                CaseStatus.PC_HANDLING, handler);
        //从服务云撤单到型管重新处理，默认转移主要负责人到产中，因为系统没有记录主要负责人的转移过程，只能取一种操作
        newCase.setMainCharge(handler.getUserId());
        newCase.setMainChargeName(handler.getUsername());
        return Optional.of(newCase);
    }

    /**
     * T100型管同步案件状态时，检查操作权限
     * @param caseStatus 案件当前状态
     * @return 响应实体
     */
    private Optional<BaseResponse> checkPermissionForUpdateStatus(String caseStatus){
        if(!caseStatus.equals(CaseStatus.PC_HANDLING.getStatus())){
            return Optional.of(BaseResponse.error(ResponseStatus.FORBIDDEN, "案件当前状态不是产中处理中，拒绝操作."));
        }
        //兼容产中处理中的状态： 4  和  O
        if(!caseStatus.equals(IssueStatus.ProductProcessing.toString())){
            return Optional.of(BaseResponse.error(ResponseStatus.FORBIDDEN, "案件当前状态不是产中处理中，拒绝操作."));
        }
        return Optional.empty();
    }

    /**
     * T100型管同步案件状态时，检查同步的案件状态是否合法
     * @param request 请求参数
     * @return 响应实体
     */
    private Optional<BaseResponse> checkRequestStatus(SCMRequest request){
        if(!request.getStatus().equals(CaseStatus.PC_HANDLING.getStatus())
        && !request.getStatus().equals(IssueStatus.ProductProcessing.toString())
        && !request.getStatus().equals(CaseProcessType.PC_CLOSED.getProcessType())){
            return Optional.of(BaseResponse.error(ResponseStatus.WRONG_PARAMETER, "案件状态status错误"));
        }
        return Optional.empty();
    }

    /**
     * 同步型管案件处理状况信息
     * 如果案件主要负责人在产中，那么产中内部转派，案件主要负责人也同步转移
     * @param reqCase 请求参数
     * @param issueInfo 当前状态信息
     * @param processer 操作人信息
     * @return 同步状态后的案件信息
     */
    private Optional<Cases> updateStatus(SCMRequest reqCase, Cases issueInfo, UserDetailInfo processer) throws RuntimeException{
        //产中内部操作
        if(reqCase.getStatus().equals(CaseStatus.PC_HANDLING.getStatus()) || reqCase.getStatus().equals(IssueStatus.ProductProcessing.toString())){
            //如果操作前后处理人不变，则表示只是添加处理说明
            if(reqCase.getCzHandlerId().equals(processer.getWorkno())){
                if(!StringUtils.isEmpty(reqCase.getCzHandlerDetail())) {
                    try {
                        issueProcessService.remark(processer.getUserId(), constructRemarkDetail(issueInfo, reqCase));
                        updateIssuePlanDate(reqCase,issueInfo);
                    } catch (DataAccessException e) {
                        log.error("添加案件处理备注失败:", e);
                        throw new RuntimeException("添加处理说明失败:"+e.getMessage(), e);
                    }
                }
                return Optional.empty();
            } else {
                //产中内部转派
                UserDetailInfo handler = userServiceFeignClient.getUserDetailByWorkNo(reqCase.getCzHandlerId());
                if(handler == null){
                    throw new NullPointerException("没有查询到产中当前处理人信息，工号："+reqCase.getCzHandlerId());
                }
                //2022-04-11 huly 注释 产中转派 不需要更新服务云的主要负责人
                /*issueInfo.setMainCharge(handler.getUserId());
                issueInfo.setMainChargeName(handler.getName());*/
                updateIssuePlanDate(reqCase,issueInfo);
                return Optional.of(toNewStatus(issueInfo, reqCase, CaseProcessType.TRANSFER_DEALER, CaseStatus.PC_HANDLING, handler));
            }
        }
        //产中结案
        if(reqCase.getStatus().equals(CaseProcessType.PC_CLOSED.getProcessType())){
            updateIssuePlanDate(reqCase,issueInfo);
            return pcClose(issueInfo, reqCase);
        }

        return Optional.empty();
    }
    //2023-06-21 产中回写预计完成日，需要更新到单头中
    private void updateIssuePlanDate(SCMRequest reqCase, Cases issueInfo){
        if(!StringUtils.isEmpty(reqCase.getCzHandlerDetail())) {
            try {
                int start = reqCase.getCzHandlerDetail().indexOf("[Plan_date]");
                int end = reqCase.getCzHandlerDetail().indexOf("[Solution]");
                if(start == 0 && end > 0){
                    String planDate = reqCase.getCzHandlerDetail().substring(start + 11,end);
                    issueProcessMapper.updateIssuePlanDate(issueInfo.getIssueId(),planDate);
                }
            } catch (DataAccessException e) {
                log.error("更新型管的预计完成日失败:", e);
                throw new RuntimeException("更新型管的预计完成日失败:"+e.getMessage(), e);
            }
        }
    }

    /**
     * 产中结案,更新案件状态信息
     */
    private Optional<Cases> pcClose(Cases issueInfo, SCMRequest reqCase){
        UserDetailInfo handler = userServiceFeignClient.findUserDetailById(lastToT100Processer(issueInfo.getIssueId()));
        //验证当前处理人是否离职
        handler = casesProcessUtils.validateCurrentServiceStaff(handler);
        Cases newCase = toNewStatus(issueInfo, reqCase, CaseProcessType.PC_CLOSED, CaseStatus.JIAOFU_VERIFICATION, handler);
        return Optional.of(newCase);
    }

    private CaseRemarkDetail constructRemarkDetail(Cases issueInfo, SCMRequest reqCase){
        CaseRemarkDetail remarkDetail = new CaseRemarkDetail();
        remarkDetail.setIssueId(issueInfo.getIssueId());
        remarkDetail.setCreateTime(DateUtils.getCurrentTime());
        remarkDetail.setHandlerId(issueInfo.getServiceId());
        remarkDetail.setSequenceNum(getCaseProcessSeq(issueInfo.getIssueId()));
        remarkDetail.setStatus(issueInfo.getCurrentStatus());
        remarkDetail.setRemarkTitle("产中人员处理记录");
        remarkDetail.setHandlerDetail(reqCase.getCzHandlerDetail());
        return remarkDetail;
    }

    private Cases toNewStatus(Cases issueInfo,
                              SCMRequest reqCase,
                              CaseProcessType processType,
                              CaseStatus caseStatus,
                              UserDetailInfo handler){
        issueInfo.setProcessType(processType.getProcessType());
        issueInfo.setCurrentStatus(caseStatus.getStatus());
        //更新处理人员信息
        issueInfo.setServiceId(handler.getUserId());
        issueInfo.setServiceName(handler.getName());
        issueInfo.setServiceEmail(handler.getEmail());
        issueInfo.setServicePhone(handler.getPhone());
        issueInfo.setServiceDepartment(handler.getDepartmentCode());
        //更新处理描述
        issueInfo.setHandlerDetail(reqCase.getCzHandlerDetail());
        issueInfo.setUpdateTime(StringUtil.getCurrentTime());
        return issueInfo;
    }

    private void checkNotEmpty(SCMRequest reqCase) throws IllegalArgumentException{
        if(Objects.isNull(reqCase)) throw new IllegalArgumentException("请求Body不能为空");
        if(StringUtils.isEmpty(reqCase.getCaseId())) throw new IllegalArgumentException("服务云单号不能为空");
        if(StringUtils.isEmpty(reqCase.getCzCaseId())) throw new IllegalArgumentException("型管需求单号不能为空");
        if(StringUtils.isEmpty(reqCase.getCzHandlerId())) throw new IllegalArgumentException("当前处理人不能为空");
        if(StringUtils.isEmpty(reqCase.getStatus())) throw new IllegalArgumentException("需求单当前状态不能为空");
    }

    /**
     * 根据T服务云案件编号查询案件信息
     * @param crmId T服务云案件编号，'TS'开头
     * @return 返回案件信息对象实体
     */
    private Optional<Cases> getCase(String crmId) throws DataAccessException{
        try{
            Cases issueInfo = issueProcessMapper.getCaseByCrmId(crmId);
            if(issueInfo == null){
                return Optional.empty();
            } else {
                return Optional.of(issueInfo);
            }
        } catch (DataAccessException e){
            log.error("查询案件信息失败,crmId:"+crmId, e);
            throw e;
        }
    }

    /**
     * 获取案件处理记录当前序号
     */
    private int getCaseProcessSeq(long issueId){
        return issueProcessMapper.getMaxOrder(issueId);
    }

    /**
     * 从187提报案件
     * @param parameter 案件信息
     * @return 返回服务云的案件编号
     */
    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public String submitCaseFrom187(FuguanRequestPayload parameter) throws RuntimeException{
        String productCode = transferProductCode(parameter.getProduct()).getCode();
        CustomerDetailInfo customerDetailInfo;
        try{
            customerDetailInfo = userServiceFeignClient.getCustomerInfoByIdAndProduct(parameter.getCustId(), productCode);
        } catch (RuntimeException e){
            throw new RuntimeException("获取客户信息失败:调用userservice.请联系服务云管理员.", e);
        }
        if(customerDetailInfo == null){
            throw new NullPointerException(
                    String.format("没有找到客户信息，请确认客服代号或产品类别是否存在.客服代号:%s,产品代号:%s", parameter.getCustId(), productCode)
            );
        }
        UserDetailInfo handler = getUserDetailByWorkno(parameter.getHandlerId(), "handlerId");
        UserDetailInfo applicant = getUserDetailByWorkno(parameter.getApplicantId(), "applicantId");
        UserDetailInfo processor = getUserDetailByWorkno(parameter.getOperator(), "operator");
        // 资料初始化
        Cases issueInfo = new Cases();
        issueInfo.setSubmitWay(SubmitWay.HD187.getSymbol());
        issueInfo.setSubmitedId(processor.getUserId());
        issueInfo.setServiceCode(customerDetailInfo.getCustomerServiceCode());
        issueInfo.setCustomerFullNameCH(customerDetailInfo.getCustomerFullNameCH());
        issueInfo.setCustName(customerDetailInfo.getCustomerName());
        issueInfo.setProductCode(productCode);
        if(StringUtils.isEmpty(parameter.getCreateTime())) {
            issueInfo.setSubmitTime(DateUtils.getCurrentTime());
        } else {
            //校验提报日期的格式
            try {
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); //huly: 漏洞/bug
                simpleDateFormat.parse(parameter.getCreateTime());
            } catch (ParseException e){
                throw new IllegalArgumentException("不合法的问题提报时间, 期望为空或时间格式为'2019-01-05 09:12:03'或空''");
            }
            issueInfo.setSubmitTime(parameter.getCreateTime());
        }
        if(JiaoFuUserType.JIAOFUGUWEN.getValue() == handler.getJiaofuType()){
            issueInfo.setCurrentStatus(CaseStatus.JIAOFU_CONSULTANT_HANDLING.getStatus());
        } else if (JiaoFuUserType.JIAOFUFUWU.getValue() == handler.getJiaofuType()){
            issueInfo.setCurrentStatus(CaseStatus.JIAOFU_SERVICE_HANDLING.getStatus());
        } else {
            throw new IllegalArgumentException(
                    String.format("没有获取到用户的交付类型，或用户的交付类型不在定义内.工号:%s, 交付类型:%s",
                            handler.getWorkno(), handler.getJiaofuType()));
        }
        issueInfo = getCases(parameter, issueInfo, handler, applicant);
        issueInfo.setUserContactId(casesProcessUtils.getUserContactId(
                issueInfo.getUserId(), issueInfo.getUsername(), issueInfo.getEmail(), issueInfo.getPhone()));
        //存issue表，获取issueId
        issueSubmitMapper.doSaveIssue(issueInfo);
        //生成单号
        issueInfo.setCrmId(CasesProcessUtils.genCrmId(issueInfo.getIssueId()));
        //保存crmId到数据库
        issueSubmitMapper.saveCrmId(issueInfo.getIssueId(), issueInfo.getCrmId());
        issueSubmitMapper.doSaveIssueDetail(issueInfo);
        CaseHistory caseHistory = getCaseHistory(issueInfo, processor, CaseProcessType.OPEN);
        issueSubmitMapper.doSaveIssueProgress(caseHistory);
        return issueInfo.getCrmId();
    }

    /**
     * 从187更新案件信息
     * @param parameter 案件信息
     */
    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void updateCaseFrom187(FuguanRequestPayload parameter) throws RuntimeException{
        if(StringUtils.isEmpty(parameter.getCaseId())){
            throw new IllegalArgumentException("更新案件信息，服务云单号不能为空");
        }
        Cases issueInfo;
        try {
            issueInfo = issueProcessMapper.getCaseByCrmId(parameter.getCaseId());
        } catch (DataAccessException e){
            throw new RuntimeException("查询案件信息出错.案件编号:"+parameter.getCaseId(), e);
        }
        if(issueInfo == null){
            throw new NullPointerException("案件不存在.案件编号:"+parameter.getCaseId());
        }
        String productCode = transferProductCode(parameter.getProduct()).getCode();
        CustomerDetailInfo customerDetailInfo;
        try{
            customerDetailInfo = userServiceFeignClient.getCustomerInfoByIdAndProduct(parameter.getCustId(), productCode);
        } catch (RuntimeException e){
            throw new RuntimeException("获取客户信息失败:调用userservice.请联系服务云管理员.", e);
        }
        if(customerDetailInfo == null){
            throw new NullPointerException("产品不存在，产品代号："+productCode);
        }
        issueInfo.setProductCode(productCode);

        if(operationChecker.forbidden(issueInfo.getCurrentStatus(), CaseProcessType.TRANSFER_DEALER)){
            throw new IllegalArgumentException("案件当前状态不允许操作，请确认服务云案件状态");
        }
        UserDetailInfo handler = getUserDetailByWorkno(parameter.getHandlerId(), "handlerId");
        UserDetailInfo applicant = getUserDetailByWorkno(parameter.getApplicantId(), "applicantId");
        UserDetailInfo processor = getUserDetailByWorkno(parameter.getOperator(), "operator");
        if(JiaoFuUserType.JIAOFUGUWEN.getValue() == handler.getJiaofuType()){
            issueInfo.setCurrentStatus(CaseStatus.JIAOFU_CONSULTANT_HANDLING.getStatus());
        } else if (JiaoFuUserType.JIAOFUFUWU.getValue() == handler.getJiaofuType()){
            issueInfo.setCurrentStatus(CaseStatus.JIAOFU_SERVICE_HANDLING.getStatus());
        } else {
            throw new IllegalArgumentException(
                    String.format("没有获取到用户的交付类型，或用户的交付类型不在定义内.工号:%s, 交付类型:%s",
                            handler.getWorkno(), handler.getJiaofuType()));
        }
        issueInfo = getCases(parameter, issueInfo, handler, applicant);
        //存表前先判断是否需要同步到CRM，以设置同步标志位
        // issueInfo.setSyncToCrm(issueService.checkIssueIsSyncCrm(issueInfo.getServiceRegion(), issueInfo.getProductCode(),issueInfo.getCurrentStatus()));
        //存issue表，获取issueId
        issueProcessMapper.doUpdateIssue(issueInfo);
        issueProcessMapper.doUpdateIssueDetail(issueInfo);
        CaseHistory caseHistory = getCaseHistory(issueInfo, processor, CaseProcessType.TRANSFER_DEALER);
        issueProcessMapper.doSaveIssueProgress(caseHistory);
    }

    private ERPProductCode transferProductCode(String product) throws IllegalArgumentException{
        if(ERPProductCode.T100.toString().equals(product)){
            return ERPProductCode.T100;
        } else if(ERPProductCode.TOPGP.toString().equals(product)){
            return ERPProductCode.TOPGP;
        } else {
            String msg = "期望的值是:";
            for(ERPProductCode code : ERPProductCode.values()){
                msg += code.toString()+",";
            }
            throw new IllegalArgumentException("产品类别错误," + msg);
        }
    }

    private CaseHistory getCaseHistory(Cases newCaseInfo, UserDetailInfo processor, CaseProcessType processType) throws RuntimeException{
        CaseHistory caseHistory = new CaseHistory();
        if(newCaseInfo.getIssueId() == 0){
            throw new IllegalArgumentException("IssueId不能为空");
        } else {
            caseHistory.setIssueId(newCaseInfo.getIssueId());
        }
        caseHistory.setCrmId(newCaseInfo.getCrmId());
        caseHistory.setSequenceNum(newCaseInfo.getMaxSequenceNum() == 0? 1 : newCaseInfo.getMaxSequenceNum()+1);
        caseHistory.setDescription(newCaseInfo.getHandlerDetail());
        caseHistory.setHandlerId(newCaseInfo.getServiceId());
        caseHistory.setHandlerName(newCaseInfo.getServiceName());
        caseHistory.setProcessor(processor.getUserId());
        caseHistory.setWorkno(processor.getWorkno());
        caseHistory.setProcessTime(DateUtils.getCurrentTime());
        caseHistory.setProcessHours(0.0d);
        caseHistory.setProcessType(processType.getProcessType());
        caseHistory.setCurrentStatus(newCaseInfo.getCurrentStatus());
        if(newCaseInfo.isSyncToCrm()){
            //设置需要同步标志位
            caseHistory.setSyncStatus(SyncStatus.EditUnSync.toString());
        }else {
            //设置不需要同步标志位
            caseHistory.setSyncStatus(SyncStatus.DontNeedSync.toString());
        }
        if(String.valueOf(UserType.INNER.getValue()).equals(processor.getUserType())){
            caseHistory.setReplyType("A");
        }
        return caseHistory;
    }

    private Cases getCases(FuguanRequestPayload payload, Cases issueInfo, UserDetailInfo handler, UserDetailInfo applicant) throws IllegalArgumentException{
        if("Y".equals(payload.getIsUrgent())){
            issueInfo.setEmergency(true);
        } else {
            issueInfo.setEmergency(false);
        }
        issueInfo.setSyncStatus(SyncStatus.DontNeedSync.toString());
        issueInfo.setIssueType(IssueType.Issue.toString());
        issueInfo.setMainCharge(handler.getUserId());
        issueInfo.setServiceId(handler.getUserId());
        issueInfo.setServiceName(handler.getName());
        issueInfo.setServiceDepartment(handler.getDepartmentCode());
        issueInfo.setServiceEmail(handler.getEmail());
        issueInfo.setErpSystemCode(payload.getModuleId());
        issueInfo.setProgramCode(payload.getProgramId());
        issueInfo.setQuestionTitle(payload.getQuestionTitle());
        issueInfo.setIssueDescription(payload.getQuestionDescription());
        issueInfo.setIssueClassification(payload.getQuestionType());
        issueInfo.setUserId(applicant.getUserId());
        issueInfo.setUsername(applicant.getName());
        issueInfo.setEmail(applicant.getEmail());
        //payload.getCoordinator();
        issueInfo.setHandlerDetail(payload.getHandlerDetail());
        //payload.getSourceCaseId();
        try {
            issueInfo.setEstimatedWorkHours(Double.parseDouble(payload.getEstimatedWorkHours()));;
        } catch (NumberFormatException e){
            throw new IllegalArgumentException("不合法的预估工时参数,期望整数或者小数");
        }
        if("Y".equals(payload.getPersonalCase())) {
            issueInfo.setIsPersonalCase("Y");
        } else {
            issueInfo.setIsPersonalCase("N");
        }
        if("Y".equals(payload.getIsLiveProcess())) {
            issueInfo.setLiveProcess(true);
        } else {
            issueInfo.setLiveProcess(false);
        }
        processAttachmentUrls(issueInfo, payload);
        return issueInfo;
    }

    /**
     * 处理187传送过来的附件url，拼接到问题描述中去
     */
    private void processAttachmentUrls(Cases issueInfo, FuguanRequestPayload payload){
        if(CollectionUtils.isEmpty(payload.getAnnexFiles())){
            return;
        }
        StringBuilder builder = new StringBuilder(issueInfo.getIssueDescription());
        builder.append("<p>附件地址:</p>");
        builder.append("<u1>");
        for(String url: payload.getAnnexFiles()){
            if(StringUtils.isEmpty(url)){
                return;
            } else {
                builder.append("<li>");
                builder.append(url);
                builder.append("</li>");
            }
        }
        builder.append("</u1>");
        issueInfo.setIssueDescription(builder.toString());
    }

    /**
     * 根据工号获取人员信息
     * @param workno 工号
     * @param role 人员说明
     * @return 人员信息对象
     * @throws RuntimeException 请求
     */
    private UserDetailInfo getUserDetailByWorkno(String workno, @NonNull String role) throws RuntimeException{
        if(StringUtils.isEmpty(workno)){
            throw new IllegalArgumentException(String.format("人员字段%s不能为空", role));
        }
        UserDetailInfo userDetailInfo;
        try{
            userDetailInfo = userServiceFeignClient.getUserDetailByWorkNo(workno);
        } catch (RuntimeException e){
            throw new RuntimeException("获取人员信息出错:调用userservice.请联系服务云管理员", e);
        }
        if(userDetailInfo == null){
            throw new NullPointerException("没有找到人员的信息,工号:"+workno);
        } else {
            return userDetailInfo;
        }
    }
}
