<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.digiwin.escloud.aiobasic.edrv2.dao.EdrCustomerV2Mapper">
    <resultMap id="customerorgMap" type="com.digiwin.escloud.aiobasic.edr.model.edr.CustomerOrgMap">
        <result property="id" column="id"/>
        <result property="sid" column="sid"/>
        <result property="serviceCode" column="serviceCode"/>
        <result property="customerName" column="customerName"/>
        <result property="customerFullName" column="customerFullName"/>
        <result property="serverId" column="serverId"/>
        <result property="orgId" column="orgId"/>
        <result property="location" column="location"/>
        <result property="handleTime" column="handleTime"/>
        <association property="org" columnPrefix="org_" resultMap="orgMap"/>
    </resultMap>
    <resultMap id="orgMap" type="com.digiwin.escloud.aiobasic.edr.model.edr.Org">
        <result property="id" column="id"/>
        <result property="sid" column="sid"/>
        <result property="serverId" column="serverId"/>
        <result property="orgEdrId" column="orgEdrId"/>
        <result property="organizationId" column="organizationId"/>
        <result property="name" column="name"/>
        <result property="workstationsAllocated" column="workstationsAllocated"/>
        <result property="serversAllocated" column="serversAllocated"/>
        <result property="iotAllocated" column="iotAllocated"/>
        <result property="workstationsInUse" column="workstationsInUse"/>
        <result property="serversInUse" column="serversInUse"/>
        <result property="iotInUse" column="iotInUse"/>
        <result property="workstationsNotInUse" column="workstationsNotInUse"/>
        <result property="serversNotInUse" column="serversNotInUse"/>
        <result property="iotNotInUse" column="iotNotInUse"/>
        <result property="expirationDate" column="expirationDate"/>
        <result property="expired" column="expired"/>

        <result property="syncStatus" column="syncStatus"/>
        <result property="voidReason" column="voidReason"/>
        <result property="voidTime" column="voidTime"/>
        <result property="processUserId" column="processUserId"/>
        <result property="processUserName" column="processUserName"/>
    </resultMap>

    <select id="selectCustomerOrg" resultType="com.digiwin.escloud.aiobasic.edrv2.model.EdrCustomerOrg">
        SELECT ss.id, ss.eid, ss.accountId, ss.serviceCode, ss.customerFullName, ss.name, ss.state, ss.processUserName,
               ss.voidReason, ss.activeLicenses, ss.totalLicenses, ss.unlimitedLicenses, ss.unlimitedExpiration,
               ss.expiration, ss.createdAt, ss.updatedAt, t.name customerName
        FROM sentinelone_sites ss
        LEFT JOIN supplier_tenant_map stm ON stm.serviceCode = ss.serviceCode
        LEFT JOIN tenant t ON t.sid = stm.eid
        WHERE ss.id=#{id}
    </select>

    <insert id="createCustomerOrg">
        INSERT INTO sentinelone_sites(id, eid, accountId, serviceCode, customerFullName, name, state, voidReason, activeLicenses,
        totalLicenses, unlimitedLicenses, unlimitedExpiration, expiration)
        VALUES(#{id},#{eid},#{accountId},#{serviceCode},#{customerFullName},#{name},#{state},#{voidReason},
        #{activeLicenses},#{totalLicenses},#{unlimitedLicenses},#{unlimitedExpiration},#{expiration})
    </insert>

    <update id="updateCustomerOrg">
        UPDATE sentinelone_sites
        SET
        <trim suffixOverrides=",">
            <if test="eid != null">
                eid=#{eid},
            </if>
            <if test="accountId != null">
                accountId=#{accountId},
            </if>
            <if test="serviceCode != null and !serviceCode.isEmpty()">
                serviceCode=#{serviceCode},
            </if>
            <if test="customerFullName != null and !customerFullName.isEmpty()">
                customerFullName=#{customerFullName},
            </if>
            <if test="name != null and !name.isEmpty()">
                name=#{name},
            </if>
            <if test="state != null and !state.isEmpty()">
                state=#{state},
            </if>
            <if test="voidReason != null and !voidReason.isEmpty()">
                voidReason=#{voidReason},
            </if>
            <if test="activeLicenses != null">
                activeLicenses=#{activeLicenses},
            </if>
            <if test="totalLicenses != null">
                totalLicenses=#{totalLicenses},
            </if>
            <if test="unlimitedLicenses != null">
                unlimitedLicenses=#{unlimitedLicenses},
            </if>
            <if test="unlimitedExpiration != null">
                unlimitedExpiration=#{unlimitedExpiration},
            </if>
            <if test="expiration != null">
                expiration=#{expiration},
            </if>
            <if test="updatedAt != null">
                updatedAt=#{updatedAt}
            </if>
        </trim>
        WHERE id=#{id}
    </update>

    <select id="selectCustomerOrgSite" resultType="com.digiwin.escloud.aiobasic.edrv2.model.EdrCustomerOrgSite">
        SELECT id, eid, accountId, name, customerFullName, state, updatedAt, processUserName, voidReason
        FROM sentinelone_sites
        WHERE 1=1
        <if test="eidCheck">
            AND eid IS NOT NULL
        </if>
        <if test="eid != null">
            AND eid=#{eid}
        </if>
        <if test="state != null and !state.isEmpty()">
            AND state=#{state}
        </if>
        ORDER BY updatedAt desc
    </select>

    <select id="selectCustomerOrgEid" resultType="com.digiwin.escloud.aiobasic.edrv2.model.EdrCustomerOrgEid">
        SELECT id, eid, serviceCode
        FROM sentinelone_sites
        WHERE id IN
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <update id="deprecateCustomerOrg">
        UPDATE sentinelone_sites
        SET state=#{state}, processUserName=#{processUserName}, voidReason=#{voidReason}, updatedAt = NOW()
        WHERE id = #{id}
    </update>

    <update id="deleteCustomerOrg">
        UPDATE sentinelone_sites
        SET eid = null,
            serviceCode = null,
            customerFullName = null,
            state = CASE
                        WHEN expiration > NOW() THEN 'active'
                        WHEN unlimitedExpiration = 1 THEN 'active'
                        ELSE 'expired'
                    END,
            processUserName = null,
            voidReason = null,
            updatedAt = NOW()
        WHERE id = #{id}
    </update>

    <select id="getClassificationMappingList" resultType="com.digiwin.escloud.aiobasic.edrv2.model.EdrCustomerThreatClassification">
        SELECT enumCode, classification, cnFieldName, twFieldName FROM sentinelone_classification
    </select>

    <select id="getEdrEventList" resultType="java.util.Map">
        SELECT eventId, status AS eventStatus, releaseStatus
        FROM edr_event_kb AS eek
        WHERE serverId = #{serverId}
        <if test=" issueCode == null or issueCode.isEmpty()">
            <if test="eventStatus != null and !eventStatus.isEmpty()">
                <if test="eventStatus != 'unsolved' and eventStatus != 'solved'">
                    AND status = #{eventStatus}
                </if>
                <if test="eventStatus == 'solved'">
                    AND (
                    status = #{eventStatus} OR
                    (status = 'invalided' AND
                    IFNULL
                    ((SELECT eventId
                    FROM edr_event_kb_detail
                    WHERE serverId = #{serverId} AND eventId = eek.eventId AND status = 'solved'
                    ORDER BY updateTime DESC, createTime DESC
                    limit 1), "") = eventId))
                </if>
                <if test="eventStatus == 'unsolved'">
                    AND status IS NOT NULL AND
                    (status = 'invalided' AND
                    IFNULL
                    ((SELECT eventId
                    FROM edr_event_kb_detail
                    WHERE serverId = #{serverId} AND eventId = eek.eventId AND status = 'solved'
                    ORDER BY updateTime DESC, createTime DESC
                    limit 1), "") = "") = false
                </if>
            </if>
        </if>
        <if test="issueCode != null and !issueCode.isEmpty()">
            AND issueCode LIKE CONCAT('%', #{issueCode}, '%')
            <if test="!eventStatus.isEmpty()">
                <if test="eventStatus != 'unsolved' and eventStatus != 'solved'">
                    AND status = #{eventStatus}
                </if>
                <if test="eventStatus == 'solved'">
                    AND (status = #{eventStatus} OR
                    (status = 'invalided' AND
                    IFNULL
                    ((SELECT eventId
                    FROM edr_event_kb_detail
                    WHERE serverId = #{serverId} AND eventId = eek.eventId AND status = 'solved'
                    ORDER BY updateTime DESC, createTime DESC
                    limit 1), "") = eventId))
                </if>
                <if test="eventStatus == 'unsolved'">
                    AND (status IS NULL OR
                    (status = 'invalided' AND
                    IFNULL
                    ((SELECT eventId
                    FROM edr_event_kb_detail
                    WHERE serverId = #{serverId} AND eventId = eek.eventId AND status = 'solved'
                    ORDER BY updateTime DESC, createTime DESC
                    limit 1), "") = ""))
                </if>
            </if>
        </if>
    </select>

    <select id="getCustomerOrgByServiceCode" resultType="java.util.Map">
        SELECT id AS siteId, serviceCode, customerFullName, name AS siteNames
        FROM sentinelone_sites
        WHERE 1=1
        <if test="serviceCode != null and !serviceCode.isEmpty()">
            AND serviceCode LIKE CONCAT('%', #{serviceCode}, '%')
        </if>
        <if test="customerFullName != null and !customerFullName.isEmpty()">
            AND customerFullName LIKE CONCAT('%', #{customerFullName}, '%')
        </if>
        <if test="siteNames != null and !siteNames.isEmpty()">
            AND name LIKE CONCAT('%', #{siteNames}, '%')
        </if>
    </select>

    <select id="getEdrEventDetailList" resultType="com.digiwin.escloud.aiobasic.edrv2.model.EdrEventDetail">
        SELECT eventId ,serverId, eventDesc AS eventDescription, status AS eventStatus, releaseStatus, issueCode,
        statusRemark, releaseRemark
        FROM edr_event_kb
        WHERE serverId = #{serverId}
        AND eventId IN (
        <foreach collection="eventIdList" item="eventId" separator=",">
        #{eventId}
        </foreach>)
    </select>

    <select id="getEdrEventInvalidedDetail" resultType="java.util.Map">
        SELECT eventId ,serverId, status AS eventStatus, issueCode, createTime
        FROM edr_event_kb_detail
        WHERE serverId = #{serverId}
        AND eventId IN (
        <foreach collection="eventIdList" item="eventId" separator=",">
            #{eventId}
        </foreach>)
        ORDER BY eventId, updateTime DESC, createTime DESC
    </select>

    <select id="selectCustomerOrgAuthorizationInfo" resultType="com.digiwin.escloud.aiobasic.edrv2.model.EdrCustomerOrgAuthorizationInfo">
        SELECT ss.expiration AS expiredDate, DATEDIFF(ss.expiration, CURDATE()) AS remainDay,
        COUNT(CASE WHEN sa.deviceType LIKE '%server%' THEN 1 END) AS usedSRV,
        COUNT(CASE WHEN sa.deviceType NOT LIKE '%server%' THEN 1 END) AS usedPC
        FROM (
        SELECT *
        FROM sentinelone_sites
        WHERE 1=1
        <if test="eid != null">
            AND eid=#{eid}
        </if>
        ORDER BY expiration DESC
        LIMIT 1
        ) AS ss
        JOIN sentinelone_agents AS sa
        ON ss.eid = sa.eid
        WHERE sa.isUninstalled IS NULL OR sa.isUninstalled = 0
    </select>

    <select id="selectCustomerOrgAuthorizationInfoBySiteIds" resultType="com.digiwin.escloud.aiobasic.edrv2.model.EdrCustomerOrgAuthorizationInfo">
        SELECT s.expiration AS expiredDate, DATEDIFF(s.expiration, CURDATE()) AS remainDay,
        COUNT(CASE WHEN a.deviceType LIKE '%server%' THEN 1 END) AS usedSRV,
        COUNT(CASE WHEN a.deviceType NOT LIKE '%server%' THEN 1 END) AS usedPC
        FROM sentinelone_agents a
        LEFT JOIN sentinelone_sites s ON a.siteId = s.id
        WHERE 1=1
        <if test="siteIds != null and !siteIds.isEmpty()">
            AND a.siteId IN (
            <foreach collection="siteIds" item="id" separator=",">
                #{id}
            </foreach>
            )
        </if>
    </select>

    <select id="getProcessById" resultType="java.util.Map">
        SELECT processCode, processDesc AS processDescription
        FROM edr_process_kb
        WHERE sid = #{sid}
        AND LOWER(processCode) IN (
        <foreach collection="threatNameList" item="threatName" separator=",">
            LOWER(#{threatName})
        </foreach>)
    </select>

    <select id="getCustomerOrgById" resultType="java.util.Map">
        SELECT id, name AS customerOrgName
        FROM sentinelone_sites
        WHERE id IN (
        <foreach collection="customerOrgIdList" item="customerOrgId" separator=",">
            #{customerOrgId}
        </foreach>)
    </select>

    <select id="getCustomerNameByOrgIds" resultType="com.digiwin.escloud.aiobasic.edr.model.edr.EdrReportOrgCnNameList">
        SELECT id AS orgId, customerFullName, customerFullName AS location FROM sentinelone_sites
        WHERE id in (
            <foreach collection="idList" item="id" separator=",">
                #{id}
            </foreach>
            )
        AND customerFullName IS NOT NULL
        AND customerFullName != ''
    </select>

    <select id="checkOrgVoid" resultType="java.lang.String">
        SELECT state FROM sentinelone_sites
        WHERE id = #{id}
    </select>

    <select id="getEidByServiceCode" resultType="java.lang.Long">
        SELECT eid FROM supplier_tenant_map WHERE serviceCode=#{serviceCode}
    </select>

    <select id="getReportCustomerData" resultType="com.digiwin.escloud.aiobasic.report.model.EdrReportCustomerData">
        SELECT stm.eid, t.name, t.customerFullNameCH, t.customerFullNameEN, stm.serviceCode
        FROM tenant t
        LEFT JOIN supplier_tenant_map stm ON stm.eid = t.sid
        LEFT JOIN edr_report_record err ON err.serviceCode = stm.serviceCode
        WHERE err.id = #{reportId}
    </select>

    <select id="getIdByEid" resultType="java.lang.String">
        SELECT id FROM sentinelone_sites WHERE eid = #{eid}
    </select>

    <select id="getOrgs" parameterType="java.util.Map" resultMap="customerorgMap">
        SELECT serviceCode, id as org_id, name as org_name, expiration as org_expirationDate
        FROM sentinelone_sites
        WHERE 1=1
        AND state = 'active'
        <if test="serviceCode!=null and serviceCode!=''">
            and serviceCode= #{serviceCode}
        </if>
        order by expiration
    </select>
</mapper>