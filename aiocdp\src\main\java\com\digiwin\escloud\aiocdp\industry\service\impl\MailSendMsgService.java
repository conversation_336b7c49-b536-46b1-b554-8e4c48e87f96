package com.digiwin.escloud.aiocdp.industry.service.impl;

import com.digiwin.escloud.aiocdp.constant.UcdpConst;
import com.digiwin.escloud.aiocdp.industry.annotation.SendTypeCode;
import com.digiwin.escloud.aiocdp.industry.model.SendMsgReqBody;
import com.digiwin.escloud.aiocdp.industry.service.ISendMsgItem;
import com.digiwin.escloud.aiocdp.utils.CommonMailService;
import com.digiwin.escloud.aiocdp.utils.MessageUtils;
import com.digiwin.escloud.aiomail.Mail;
import com.digiwin.escloud.aiomail.MailSourceType;
import com.digiwin.escloud.common.feign.AioMailFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date: 2025-05-06 20:10
 * @Description
 */
@RefreshScope
@SendTypeCode("EMAIL")
@Slf4j
@Service
public class MailSendMsgService implements ISendMsgItem {

    @Resource
    private AioMailFeignClient aioMailFeignClient;
    @Value("${digiwin.user.defaultlanguage}")
    private String defaultLanguage;
    @Autowired
    private CommonMailService commonMailService;
    @Resource
    private MessageUtils messageUtils;

    @Override
    public Object buildMsg(SendMsgReqBody sendMsgReqBody) {
        List<SendMsgReqBody.MsgDto> msgDtos = sendMsgReqBody.getMsgDtos();
        if (CollectionUtils.isEmpty(msgDtos)) {
            return null;
        }
        List<String> allUserIds = sendMsgReqBody.getAllUserIds();
        if (CollectionUtils.isEmpty(allUserIds)) {
            return null;
        }
        List<String> receivers = new ArrayList<>();
        receivers.addAll(allUserIds);
        SendMsgReqBody.MsgDto msgDto = msgDtos.get(0);
        Mail mail = new Mail();
        mail.setSubject(msgDto.getTitle());
        mail.setMessage(msgDto.getContent());
        mail.setReceivers(receivers);
        mail.setMailSourceType(MailSourceType.CustomContent);
        mail.setSourceId("INDUSTRY");
        mail.setPriority(1);
        mail.setLanguage(defaultLanguage);
        return mail;
    }

    @Override
    public void sendMsg(SendMsgReqBody sendMsgReqBody) {
        Object mailDto = buildMsg(sendMsgReqBody);

        if (ObjectUtils.isEmpty(mailDto)) {
            return;
        }
        Mail mail = (Mail) mailDto;
        List<String> receivers = mail.getReceivers();
        if (CollectionUtils.isEmpty(receivers)) {
            return;
        }
        String message = mail.getMessage();
        for (String receiver : receivers) {
            try {
                Mail finalMail = new Mail();
                BeanUtils.copyProperties(mail, finalMail);
                finalMail.setReceivers(Stream.of(receiver).collect(Collectors.toList()));
                String encodeMail = URLEncoder.encode(receiver, "UTF-8");
                String finalMsg = message.replace(UcdpConst.EMAIL_PARAM, encodeMail);
                finalMail.setMessage(finalMsg);
                aioMailFeignClient.sendMail(finalMail);
                Thread.sleep(1000);
            } catch (Exception e) {
                log.error("sendMailMsg error", e);
            }
        }
    }
}
