package com.digiwin.escloud.aiobasic.edrv2.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class EdrCustomerOrgSite {
    private String id;
    private String eid;
    private String accountId;
    private String name;
    private String customerFullName;
    private String state;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt;

    // 作廢相關信息
    private String processUserName;
    private String voidReason;
}
