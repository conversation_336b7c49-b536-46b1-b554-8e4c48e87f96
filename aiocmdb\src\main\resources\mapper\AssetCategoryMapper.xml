<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.digiwin.escloud.aiocmdb.asset.dao.AssetCategoryMapper">

    <!-- AssetCategoryClassification 相关SQL -->
    <insert id="insertAssetCategoryClassification" parameterType="com.digiwin.escloud.aiocmdb.asset.model.AssetCategoryClassification">
        INSERT INTO asset_category_classification (
            categoryName, categoryname_CN, categoryname_TW, code, iconUrl,
            parentId, categoryLevel, canDelete, canEdit
        ) VALUES (
            #{categoryName}, #{categoryname_CN}, #{categoryname_TW}, #{code}, #{iconUrl},
            #{parentId}, #{categoryLevel}, #{canDelete}, #{canEdit}
        )
    </insert>

    <update id="updateAssetCategoryClassification" parameterType="com.digiwin.escloud.aiocmdb.asset.model.AssetCategoryClassification">
        UPDATE asset_category_classification SET
                                                 categoryName = #{categoryName},
                                                 categoryname_CN = #{categoryname_CN},
                                                 categoryname_TW = #{categoryname_TW},
            code = #{code},
                                                 iconUrl = #{iconUrl},
                                                 parentId = #{parentId},
                                                 categoryLevel = #{categoryLevel},
                                                 canDelete = #{canDelete},
                                                 canEdit = #{canEdit}
        WHERE id = #{id}
    </update>

    <delete id="deleteAssetCategoryClassification">
        DELETE FROM asset_category_classification WHERE id = #{id}
    </delete>

    <select id="selectAssetCategoryClassificationList" resultType="com.digiwin.escloud.aiocmdb.asset.model.AssetCategoryClassification">
        SELECT * FROM asset_category_classification ORDER BY id DESC
    </select>

    <select id="selectAssetCategoryClassificationById" resultType="com.digiwin.escloud.aiocmdb.asset.model.AssetCategoryClassification">
        SELECT * FROM asset_category_classification WHERE id = #{id}
    </select>

    <select id="countByCategoryName" resultType="int">
        SELECT COUNT(1) FROM asset_category_classification
        WHERE categoryName = #{categoryName}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <select id="countAssetCategoryByClassificationId" resultType="int">
        SELECT COUNT(1) FROM asset_category WHERE classificationId = #{classificationId}
    </select>

    <!-- AssetCategory 相关SQL -->
    <insert id="insertAssetCategory" parameterType="com.digiwin.escloud.aiocmdb.asset.model.AssetCategory">
        INSERT INTO asset_category (
            sid, scopeId, classificationId, categoryNumber, categoryName,
            categoryname_CN, categoryname_TW, iconUrl, modelCode, sinkName,
            status, creationMode, aiopsItem
        ) VALUES (
            #{sid}, #{scopeId}, #{classificationId}, #{categoryNumber}, #{categoryName},
            #{categoryname_CN}, #{categoryname_TW}, #{iconUrl}, #{modelCode}, #{sinkName},
            #{status}, #{creationMode}, #{aiopsItem}
        )
    </insert>

    <update id="updateAssetCategory" parameterType="com.digiwin.escloud.aiocmdb.asset.model.AssetCategory">
        UPDATE asset_category SET
            classificationId = #{classificationId},
            categoryName = #{categoryName},
            categoryname_CN = #{categoryname_CN},
            categoryname_TW = #{categoryname_TW},
            iconUrl = #{iconUrl},
            status = #{status},
            creationMode = #{creationMode},
            aiopsItem = #{aiopsItem}
        WHERE id = #{id}
    </update>

    <delete id="deleteAssetCategory">
        DELETE FROM asset_category WHERE id = #{id}
    </delete>

    <select id="selectAssetCategoryList" resultType="com.digiwin.escloud.aiocmdb.asset.model.AssetCategory">
        SELECT ac.*, acrs.ruleSettingValue rule_ruleSettingValue,accr.ruleNumber rule_ruleNumber
        FROM asset_category ac
        LEFT JOIN asset_category_coding_rule_setting_result acrs ON ac.id = acrs.objId
        LEFT JOIN asset_category_coding_rule accr ON accr.id = acrs.ruleId
        <where>
            <if test="classificationId != null">
                AND ac.classificationId = #{classificationId}
            </if>
        </where>
        ORDER BY ac.id DESC
    </select>

    <select id="selectAssetCategoryById" resultType="com.digiwin.escloud.aiocmdb.asset.model.AssetCategory">
        SELECT * FROM asset_category WHERE id = #{id}
    </select>

    <select id="countBySidScopeIdCategoryNumber" resultType="int">
        SELECT COUNT(1) FROM asset_category
        WHERE sid = #{sid} AND scopeId = #{scopeId} AND categoryNumber = #{categoryNumber}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- CmdbModelDataFieldRelationMapping 相关SQL -->
    <insert id="insertCmdbModelDataFieldRelationMapping" parameterType="com.digiwin.escloud.aiocmdb.asset.model.CmdbModelDataFieldRelationMapping">
        INSERT INTO cmdb_model_data_field_relation_mapping (
            targetModelCode, targetModelFieldName, targetModelFieldJsonPath,
            accId, sourceModelCode, sourceModelFieldName, sourceModelFieldJsonPath,
            transformRuleType, transformRule, description
        ) VALUES (
            #{targetModelCode}, #{targetModelFieldName}, #{targetModelFieldJsonPath},
            #{accId}, #{sourceModelCode}, #{sourceModelFieldName}, #{sourceModelFieldJsonPath},
            #{transformRuleType}, #{transformRule}, #{description}
        )
    </insert>

    <delete id="deleteCmdbModelDataFieldRelationMappingByTargetModelCode">
        DELETE FROM cmdb_model_data_field_relation_mapping WHERE targetModelCode = #{targetModelCode}
    </delete>

    <select id="selectCmdbModelDataFieldRelationMappingByTargetModelCode" resultType="com.digiwin.escloud.aiocmdb.asset.model.CmdbModelDataFieldRelationMapping">
        SELECT * FROM cmdb_model_data_field_relation_mapping
        WHERE targetModelCode = #{targetModelCode}
        ORDER BY id DESC
    </select>

    <select id="countByTargetModelCodeAndFieldName" resultType="int">
        SELECT COUNT(1) FROM cmdb_model_data_field_relation_mapping
        WHERE targetModelCode = #{targetModelCode} AND targetModelFieldName = #{targetModelFieldName}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- AssetCategoryCodingRule 相关SQL -->
    <select id="selectAllAssetCategoryCodingRule" resultType="com.digiwin.escloud.aiocmdb.asset.model.AssetCategoryCodingRule">
        SELECT * FROM asset_category_coding_rule ORDER BY id DESC
    </select>

    <!-- AssetCategoryCodingRuleSettingResult 相关SQL -->
    <select id="selectAssetCategoryCodingRuleSettingResultByObjId" resultType="com.digiwin.escloud.aiocmdb.asset.model.AssetCategoryCodingRuleSettingResult">
        SELECT * FROM asset_category_coding_rule_setting_result WHERE objId = #{objId}
    </select>

    <delete id="deleteAssetCategoryCodingRuleSettingResult">
        DELETE FROM asset_category_coding_rule_setting_result WHERE objId = #{objId}
    </delete>

    <insert id="insertAssetCategoryCodingRuleSettingResult" parameterType="com.digiwin.escloud.aiocmdb.asset.model.AssetCategoryCodingRuleSettingResult">
        INSERT INTO asset_category_coding_rule_setting_result (
            id, objType, objId,
            ruleId, ruleSettingValue
        ) VALUES (
                     #{id}, #{objType}, #{objId},
                     #{ruleId}, #{ruleSettingValue}
                 )
    </insert>
</mapper>