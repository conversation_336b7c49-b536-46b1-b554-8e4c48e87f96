package com.digiwin.escloud.aiobasic.operatelog.aspect;

import com.digiwin.escloud.aiobasic.operatelog.annotation.OperateLog;
import com.digiwin.escloud.aiobasic.operatelog.handler.OperateLogParamHandler;
import com.digiwin.escloud.aiobasic.operatelog.model.OperateLogSaveParam;
import com.digiwin.escloud.aiobasic.operatelog.model.OperateLogType;
import com.digiwin.escloud.aiobasic.operatelog.service.impi.OperateLogService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Aspect
@Component
@Slf4j
public class OperateLogAspect {
    @Autowired
    private OperateLogService operateLogService;
    @Autowired
    private List<OperateLogParamHandler> methodHandlers;

    @Pointcut("@annotation(com.digiwin.escloud.aiobasic.operatelog.annotation.OperateLog)")
    public void OperateLogPointCut() {
    }

    @AfterReturning(value = "OperateLogPointCut()")
    public void saveOperLog(JoinPoint joinPoint) {
        OperateLogSaveParam param = new OperateLogSaveParam();
        try {
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            OperateLog annotation = method.getAnnotation(OperateLog.class);
            if (Objects.isNull(annotation)) {
                return;
            }

            OperateLogType op = annotation.op();
            List<Object> arg = Arrays.asList(joinPoint.getArgs());

            // 取得日誌參數
            methodHandlers.stream()
                    .filter(methodHandler -> methodHandler.isSupport(op))
                    .forEach(methodHandler -> {
                        methodHandler.handle(arg, param);
                    });

            // 保存操作日誌
            if (Objects.nonNull(param)) {
                operateLogService.saveRecord(param);
            }

        } catch (Exception e) {
            log.error("saveOperLog error", e);
        }
    }
}
