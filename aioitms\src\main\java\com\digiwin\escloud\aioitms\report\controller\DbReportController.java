package com.digiwin.escloud.aioitms.report.controller;

import cn.hutool.core.lang.Pair;
import com.digiwin.escloud.aiobasic.operatelog.annotation.OperateLog;
import com.digiwin.escloud.aiobasic.operatelog.model.OperateLogType;
import com.digiwin.escloud.aioitms.model.report.ReportDTO;
import com.digiwin.escloud.aioitms.report.model.OperateLogSaveParam;
import com.digiwin.escloud.aioitms.report.model.UpdateEsParam;
import com.digiwin.escloud.aioitms.report.model.db.*;
import com.digiwin.escloud.aioitms.report.service.db.DbReportService;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.RequestUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 2022-06-15 13:38
 * @Description
 */
@Slf4j
@RestController
@RequestMapping("/db/report")
public class DbReportController {

    @Resource
    private DbReportService dbReportService;

    @ApiOperation(value = "分页查询报告")
    @PostMapping("/list")
    public BaseResponse getDbReports(@RequestBody DbQueryParam dbQueryParam) {
        try {
            return BaseResponse.ok(dbReportService.getDbReports(dbQueryParam));
        } catch (Exception e) {
            log.error("getDbReports", e);
            return BaseResponse.error(e);
        }
    }

    @OperateLog(op = OperateLogType.Generate)
    @ApiOperation(value = "生成报告")
    @PostMapping
    public BaseResponse generateReport(@RequestBody DbReportRecord dbReportRecord) {
        try {
            dbReportRecord.setAppCode(RequestUtil.getHeaderAppCode());
            dbReportRecord.setApiToken(RequestUtil.getHeaderToken());
            return BaseResponse.ok(dbReportService.generateReport(dbReportRecord));
        } catch (Exception e) {
            log.error("generateReport", e);
            return BaseResponse.error(e);
        }
    }

    /**
     * 清除报告参考值的缓存。
     * 此方法映射到 DELETE 请求路径 "/clear/report/ref/value"。
     * 它调用服务层来清除用于报告中的缓存参考值。
     *
     * @return BaseResponse 表示操作的结果。如果成功，返回“ok”消息；
     * 如果发生异常，则返回错误消息。
     */
    @DeleteMapping("/clear/report/ref/value")
    public BaseResponse clearReportRefValueCache() {
        try {
            dbReportService.clearReportRefValueCache();
            return BaseResponse.ok("ok");
        } catch (Exception e) {
            log.error("generateReport", e);
            return BaseResponse.error(e);
        }
    }


    @ApiOperation(value = "通过id查询报告")
    @GetMapping("/{id}")
    public BaseResponse getDbReportData(@PathVariable String id,
                                        @RequestParam String dbType,
                                        @RequestParam(value = "productCode", required = false) String productCode) {
        try {
            return BaseResponse.ok(dbReportService.getDbReportData(id, dbType, productCode));
        } catch (Exception e) {
            log.error("getDbReportData", e);
            return BaseResponse.error(e);
        }
    }

    @ApiOperation(value = "通过id查询报告状态")
    @GetMapping("/{id}/status")
    public BaseResponse getDbReportStatus(@PathVariable long id) {
        try {
            return BaseResponse.ok(dbReportService.getDbReportStatus(id));
        } catch (Exception e) {
            log.error("getDbReportData", e);
            return BaseResponse.error(e);
        }
    }

    @ApiOperation(value = "保存报告数据")
    @PostMapping("/data")
    public BaseResponse saveDbReportData(@RequestParam String dbType,
                                         @RequestParam(value = "productCode", required = false) String productCode,
                                         @RequestBody Object obj) {
        try {
            dbReportService.saveDbReportData(obj, dbType, productCode);
            return BaseResponse.ok();
        } catch (Exception e) {
            log.error("saveDbReportData", e);
            return BaseResponse.error(e);
        }
    }

    @ApiOperation(value = "保存报告节点数据")
    @PostMapping("/item")
    public BaseResponse saveDbReportItem(@RequestBody UpdateEsParam updateEsParam) {
        try {
            dbReportService.saveDbReportItem(updateEsParam);
            return BaseResponse.ok();
        } catch (Exception e) {
            log.error("saveDbReportItem", e);
            return BaseResponse.error(e);
        }
    }

    @ApiOperation(value = "批量保存報告節點數據")
    @PostMapping("/batch/item")
    public BaseResponse saveDbReportItems(@RequestBody UpdateEsParam updateEsParam) {
        try {
            dbReportService.saveDbReportItems(updateEsParam);
            return BaseResponse.ok();
        } catch (Exception e) {
            log.error("saveDbReportItem", e);
            return BaseResponse.error(e);
        }
    }

    @OperateLog(op = OperateLogType.Send2MIS)
    @ApiOperation(value = "发送报告")
    @PutMapping("/{id}/send")
    public BaseResponse sendDbReport(@PathVariable long id,
                                     @RequestBody(required = false) SendDbReportParam param ) {
        try {
            return BaseResponse.ok(dbReportService.sendDbReport(id, param));
        } catch (Exception e) {
            log.error("sendDbReport", e);
            return BaseResponse.error(e);
        }
    }

    @OperateLog(op = OperateLogType.Delete)
    @ApiOperation(value = "通过id删除报告")
    @DeleteMapping("/{id}")
    public BaseResponse deleteDbReport(@PathVariable String id,
                                       @RequestParam(value = "productCode", required = false) String productCode,
                                       @RequestParam String dbType,
                                       @RequestParam(value = "eid", required = false) Long eid,
                                       @RequestParam(value = "userId", required = false) String userId,
                                       @RequestParam(value = "userName", required = false) String userName,
                                       @RequestParam(value = "reportType", required = false) String reportType) {
        try {
            return BaseResponse.ok(dbReportService.deleteDbReport(id, dbType, productCode));
        } catch (Exception e) {
            log.error("deleteDbReport", e);
            return BaseResponse.error(e);
        }
    }

    @ApiOperation(value = "阅读报告")
    @PostMapping("/read")
    public BaseResponse readDbReport(@RequestBody LogDbRecord logDbRecord) {
        try {
            dbReportService.readDbReport(logDbRecord);
            return BaseResponse.ok();
        } catch (Exception e) {
            log.error("readDbReport", e);
            return BaseResponse.error(e);
        }
    }

    @ApiOperation(value = "查询阅读记录")
    @PostMapping("/{id}/read/log/list")
    public BaseResponse getReadLogs(@PathVariable long id,
                                    @RequestParam(value = "pageNum", required = true, defaultValue = "1") int pageNum,
                                    @RequestParam(value = "pageSize", required = true, defaultValue = "10") int pageSize) {
        try {
            return BaseResponse.ok(dbReportService.getReadLogs(id, pageNum, pageSize));
        } catch (Exception e) {
            log.error("readDbReport", e);
            return BaseResponse.error(e);
        }
    }

    @ApiOperation(value = "刷新报告结论")
    @PostMapping("/refresh/conclusion")
    public BaseResponse refreshConclusion(@RequestBody UpdateEsParam updateEsParam) {
        try {
            dbReportService.refreshConclusion(updateEsParam);
            return BaseResponse.ok();
        } catch (Exception e) {
            log.error("refreshConclusion", e);
            return BaseResponse.error(e);
        }
    }


    @ApiOperation(value = "获取体检报告和基础运维报告数据，伙伴平台线索足迹使用")
    @GetMapping("/getDbReportList")
    public ResponseBase<Map<String, List<ReportDTO>>> getDbReportList(@RequestParam("eid")Long eid) {
        return dbReportService.getDbReportList(eid);
    }

    @ApiOperation(value = "版更評估報告，調閱操作記錄")
    @PostMapping("/operatelog")
    public BaseResponse operatelog(@RequestBody OperateLogSaveParam operateLogSaveParam) {
        try {
            return dbReportService.operatelog(operateLogSaveParam);
        } catch (Exception e) {
            log.error("operatelog", e);
            return BaseResponse.error(e);
        }
    }
}
