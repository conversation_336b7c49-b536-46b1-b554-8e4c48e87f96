<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="escloud.issueschedulemapperv3">
    <select id="selectUnCloseCaseList" parameterType="java.util.Map" resultType="java.lang.Long">
        select a.IssueId as issueId
        from issue a left join issue_casedetail b on a.IssueId = b.IssueId
        where a.IssueStatus = 'R'
              and a.__version__ &lt;= DATE_FORMAT(#{dateTime},'%Y-%m-%d %H:%i:%S')
    </select>
    <select id="getIssueKbConditionList" resultType="com.digiwin.escloud.issueservice.t.model.issuekb.IssueKbCondition">
        select ikc.id, ikc.serviceRegion, ikc.productCode,mp.ProductCategory, ikc.conditionName,ikc.issueClassification,
        ikc.serviceDepartment, ikc.supportDepartment, ikc.issueDespKeyId, ikc.ipDespKeyId,
        ikc.hourCondition, ikc.`hour`, ikc.faq, ikc.`status`, ikc.createUserId, ikc.updateUserId,mup.name updateUser
        from issue_kb_condition ikc
        LEFT JOIN mars_product mp ON ikc.productCode = mp.ProductCode
        LEFT JOIN mars_userpersonalinfo mup ON mup.userid = ikc.updateUserId
        where ikc.`delete` = 0
        <if test="serviceRegion != null and serviceRegion != ''">
            AND ikc.serviceRegion = #{serviceRegion}
        </if>
        <if test="productCode != null and productCode != ''">
            AND ikc.productCode = #{productCode}
        </if>
        <if test="status != null and status != ''">
            AND ikc.`status` = ${status}
        </if>
        ORDER BY ikc.serviceRegion,ikc.productCode,ikc.`status` desc
    </select>
    <select id="getIssueKbKeyWordList" resultType="com.digiwin.escloud.issueservice.t.model.issuekb.IssueKbKeyWord">
        select ikk.id, ikk.serviceRegion, ikk.name,ikk.keyword, ikk.updateUserId,mup.name updateUser
        from issue_kb_keyword ikk
        LEFT JOIN mars_userpersonalinfo mup ON mup.userid = ikk.updateUserId
        where ikk.`delete` = 0
        <if test="serviceRegion != null and serviceRegion != ''">
            AND ikk.serviceRegion = #{serviceRegion}
        </if>
        ORDER BY ikk.serviceRegion,ikk.name asc
    </select>

    <select id="getCNIssueList" resultType="com.digiwin.escloud.issueservice.t.model.issuekb.IssueKbOverview">
         SELECT ikc.id ikcId, i.serviceRegion,i.ProductCode productCode,i.crmId issueCode,i.ClosedTime closeTime,i.issueDespToAI,
            TRIM(fnStripTags(ifnull(ip.Description,''))) processDespToAI,iks.ChatFileSearchText issueToChatFile ,iks.ChatFileContent answerToChatFile,i.ServiceId serviceId,i.SupportId supportId,
            i.IssueClassification issueClassification,i.ErpSystemCode erpSystemCode,
            replace(ifnull(mup.`language`,'zh-CN'),'_','-') lang,i.targetArea,
            mc.ProductVersion applicationVersion,
            IFNULL(kb.businessCode,'') businessCodes,
            ifnull(km.moduleCode,if(km1.id IS NULL,'',km1.moduleCode)) moduleCodes,
            ip.innerDes,i.total_work_hours,0 faq,'' faqUserId
         from
            (SELECT issue.issueId,issue.userId,issue.servicecode,issue.serviceRegion,issue.ProductCode ,issue.crmId ,
                    sm.ClosedTime ,TRIM(fnStripTags(ifnull(issue.IssueDescription,''))) issueDespToAI,
                    issue.ServiceId ,issue.ServiceDepartment,issue.SupportId ,issue.SupportDepartment,
                    icd.IssueClassification ,icd.ErpSystemCode ,icd.total_work_hours,
                    IFNULL(issue.serviceRegion,'') targetArea
            FROM issue
            inner JOIN issue_casedetail icd ON issue.IssueId = icd.IssueId
            inner JOIN issue_summary sm ON issue.IssueId = sm.IssueId
            WHERE issue.IssueStatus IN ('Y','7','P') AND DATE_FORMAT(sm.ClosedTime,'%Y-%m-%d') = DATE_SUB(CURDATE(), INTERVAL 1 DAY)
		 ) i
        LEFT JOIN issue_progress ip ON i.IssueId = ip.IssueId AND ip.ReplyType = 'A' AND TRIM(fnStripTags(ifnull(ip.Description,''))) != ''
        inner JOIN issue_kb_condition ikc ON i.serviceRegion = ikc.serviceRegion AND i.ProductCode = ikc.productCode AND ikc.`delete` = 0 AND ikc.`status`=1
        LEFT JOIN issue_kb_keyword ikb ON ikb.serviceRegion = i.serviceRegion  and ikc.issueDespKeyId = ikb.id
        LEFT JOIN issue_kbshare iks ON iks.IssueId = i.IssueId
        LEFT JOIN mars_userpersonalinfo mup ON mup.userid = i.UserId
        LEFT JOIN mars_customerservice mc ON mc.ProductCode = i.ProductCode AND mc.CustomerServiceCode = i.ServiceCode
        LEFT JOIN kb_business kb ON kb.productCode = i.ProductCode AND i.IssueClassification = if(replace(ifnull(mup.`language`,'zh-CN'),'_','-')='zh-CN', kb.descCN,if(replace(ifnull(mup.`language`,'zh-CN'),'_','-')='zh-TW', kb.descTW,if(replace(ifnull(mup.`language`,'zh-CN'),'_','-')='en-US', kb.descUS,if(replace(ifnull(mup.`language`,'zh-CN'),'_','-')='vi-VN', kb.descVN,if(replace(ifnull(mup.`language`,'zh-CN'),'_','-')='th-TH', kb.descTH,'')))))
        LEFT JOIN kb_module km ON km.productCode = i.ProductCode AND i.ErpSystemCode = if(replace(ifnull(mup.`language`,'zh-CN'),'_','-')='zh-CN', km.nameCN,if(replace(ifnull(mup.`language`,'zh-CN'),'_','-')='zh-TW', km.nameTW,if(replace(ifnull(mup.`language`,'zh-CN'),'_','-')='en-US', km.nameUS,if(replace(ifnull(mup.`language`,'zh-CN'),'_','-')='vi-VN', km.nameVN,if(replace(ifnull(mup.`language`,'zh-CN'),'_','-')='th-TH', km.nameTH,'')))))
        LEFT JOIN kb_module km1 ON km1.productCode = i.ProductCode AND 'ALL'= if(replace(ifnull(mup.`language`,'zh-CN'),'_','-')='zh-CN', km1.nameCN,if(replace(ifnull(mup.`language`,'zh-CN'),'_','-')='zh-TW', km1.nameTW,if(replace(ifnull(mup.`language`,'zh-CN'),'_','-')='en-US', km1.nameUS,if(replace(ifnull(mup.`language`,'zh-CN'),'_','-')='vi-VN', km1.nameVN,if(replace(ifnull(mup.`language`,'zh-CN'),'_','-')='th-TH', km1.nameTH,'')))))
        WHERE if(IFNULL(ikc.issueClassification,'')='','1=1',FIND_IN_SET(i.IssueClassification,ikc.issueClassification))
        AND if(IFNULL(ikc.supportDepartment,'')='','1=1',FIND_IN_SET(i.SupportDepartment,ikc.supportDepartment))
        AND if(IFNULL(ikc.serviceDepartment,'')='','1=1',FIND_IN_SET(i.ServiceDepartment,ikc.serviceDepartment))
        AND i.CrmId NOT in (SELECT issueCode FROM issue_kb_overview )
    </select>

    <select id="getTWIssueList" resultType="com.digiwin.escloud.issueservice.t.model.issuekb.IssueKbOverview">
         SELECT ikc.id ikcId, i.serviceRegion,i.ProductCode productCode,i.crmId issueCode,i.ClosedTime closeTime,i.issueDespToAI,
            TRIM(fnStripTags(ifnull(ip.Description,''))) processDespToAI,iks.ChatFileSearchText issueToChatFile ,iks.ChatFileContent answerToChatFile,i.ServiceId serviceId,i.SupportId supportId,
            i.IssueClassification issueClassification,i.ErpSystemCode erpSystemCode,
            replace(ifnull(mup.`language`,'zh-CN'),'_','-') lang,i.targetArea,
            mc.ProductVersion applicationVersion,
            IFNULL(kb.businessCode,'') businessCodes,
            ifnull(km.moduleCode,if(km1.id IS NULL,'',km1.moduleCode)) moduleCodes,
            ip.innerDes,i.total_work_hours,case when ip.ReplyType = 'F' THEN 1 ELSE 0 end faq,case when ip.ReplyType = 'F' THEN if(mup1.userId is null,ip.Processor,mup1.userId) ELSE '' end faqUserId
         from
            (SELECT issue.issueId,issue.userId,issue.servicecode,issue.serviceRegion,issue.ProductCode ,issue.crmId ,
                    sm.ClosedTime ,TRIM(fnStripTags(ifnull(issue.IssueDescription,''))) issueDespToAI,
                    issue.ServiceId ,issue.ServiceDepartment,issue.SupportId ,issue.SupportDepartment,
                    icd.IssueClassification ,icd.ErpSystemCode ,icd.total_work_hours,
                    IFNULL(issue.serviceRegion,'') targetArea
            FROM issue
            inner JOIN issue_casedetail icd ON issue.IssueId = icd.IssueId
            inner JOIN issue_summary sm ON issue.IssueId = sm.IssueId
            WHERE issue.IssueStatus IN ('Y','7','P') AND DATE_FORMAT(sm.ClosedTime,'%Y-%m-%d') = DATE_SUB(CURDATE(), INTERVAL 1 DAY)
		 ) i
        LEFT JOIN issue_progress ip ON i.IssueId = ip.IssueId AND ip.ReplyType in ('A','F')  AND TRIM(fnStripTags(ifnull(ip.Description,''))) != ''
        inner JOIN issue_kb_condition ikc ON i.serviceRegion = ikc.serviceRegion AND i.ProductCode = ikc.productCode AND ikc.`delete` = 0 AND ikc.`status`=1
        LEFT JOIN issue_kb_keyword ikb ON ikb.serviceRegion = i.serviceRegion  and ikc.issueDespKeyId = ikb.id
        LEFT JOIN issue_kbshare iks ON iks.IssueId = i.IssueId
        LEFT JOIN mars_userpersonalinfo mup ON mup.userid = i.UserId
        LEFT JOIN mars_userpersonalinfo mup1 ON mup1.workno = ip.Processor
        LEFT JOIN mars_customerservice mc ON mc.ProductCode = i.ProductCode AND mc.CustomerServiceCode = i.ServiceCode
        LEFT JOIN kb_business kb ON kb.productCode = i.ProductCode AND i.IssueClassification = if(replace(ifnull(mup.`language`,'zh-TW'),'_','-')='zh-CN', kb.descCN,if(replace(ifnull(mup.`language`,'zh-TW'),'_','-')='zh-TW', kb.descTW,if(replace(ifnull(mup.`language`,'zh-TW'),'_','-')='en-US', kb.descUS,if(replace(ifnull(mup.`language`,'zh-TW'),'_','-')='vi-VN', kb.descVN,if(replace(ifnull(mup.`language`,'zh-TW'),'_','-')='th-TH', kb.descTH,'')))))
        LEFT JOIN kb_module km ON km.productCode = i.ProductCode AND i.ErpSystemCode = if(replace(ifnull(mup.`language`,'zh-TW'),'_','-')='zh-CN', km.nameCN,if(replace(ifnull(mup.`language`,'zh-TW'),'_','-')='zh-TW', km.nameTW,if(replace(ifnull(mup.`language`,'zh-TW'),'_','-')='en-US', km.nameUS,if(replace(ifnull(mup.`language`,'zh-TW'),'_','-')='vi-VN', km.nameVN,if(replace(ifnull(mup.`language`,'zh-TW'),'_','-')='th-TH', km.nameTH,'')))))
        LEFT JOIN kb_module km1 ON km1.productCode = i.ProductCode AND 'ALL'= if(replace(ifnull(mup.`language`,'zh-TW'),'_','-')='zh-CN', km1.nameCN,if(replace(ifnull(mup.`language`,'zh-TW'),'_','-')='zh-TW', km1.nameTW,if(replace(ifnull(mup.`language`,'zh-TW'),'_','-')='en-US', km1.nameUS,if(replace(ifnull(mup.`language`,'zh-TW'),'_','-')='vi-VN', km1.nameVN,if(replace(ifnull(mup.`language`,'zh-TW'),'_','-')='th-TH', km1.nameTH,'')))))
        WHERE if(IFNULL(ikc.issueClassification,'')='','1=1',FIND_IN_SET(i.IssueClassification,ikc.issueClassification))
        AND if(IFNULL(ikc.supportDepartment,'')='','1=1',FIND_IN_SET(i.SupportDepartment,ikc.supportDepartment))
        AND if(IFNULL(ikc.serviceDepartment,'')='','1=1',FIND_IN_SET(i.ServiceDepartment,ikc.serviceDepartment))
        AND i.CrmId NOT in (SELECT issueCode FROM issue_kb_overview )
    </select>
    <insert id="batchInsertIssueKbOverviews">
        <!-- 批量插入 -->
        INSERT INTO issue_kb_overview (
        id, ikerId, ikcId, serviceRegion, productCode, issueCode,
        closeTime, issueDespToAI, processDespToAI, titleFromAI,
        answerFromAI, keywordFromAI, kbId, issueToChatFile,
        answerToChatFile, serviceId, supportId, faq, faqUserId,
        issueclassification, erpSystemCode, lang, targetArea,
        applicableVersion, updateUserId, status, statusAI, result,
        businessCodes, moduleCodes
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id}, #{item.ikerId}, #{item.ikcId}, #{item.serviceRegion}, #{item.productCode}, #{item.issueCode},
            #{item.closeTime}, #{item.issueDespToAI}, #{item.processDespToAI}, #{item.titleFromAI},
            #{item.answerFromAI}, #{item.keywordFromAI}, #{item.kbId}, #{item.issueToChatFile},
            #{item.answerToChatFile}, #{item.serviceId}, #{item.supportId}, #{item.faq}, #{item.faqUserId},
            #{item.issueclassification}, #{item.erpSystemCode}, #{item.lang}, #{item.targetArea},
            #{item.applicableVersion}, #{item.updateUserId}, #{item.status}, #{item.statusAI}, #{item.result},
            #{item.businessCodes}, #{item.moduleCodes}
            )
        </foreach>
    </insert>

    <insert id="saveIssueKbOverview">
        INSERT INTO issue_kb_overview (
        ikerId, ikcId, serviceRegion, productCode, issueCode,
        closeTime, issueDespToAI, processDespToAI, titleFromAI,
        answerFromAI, keywordFromAI, kbId, issueToChatFile,
        answerToChatFile, serviceId, supportId, faq, faqUserId,
        issueclassification, erpSystemCode, lang, targetArea,
        applicableVersion, updateUserId, status, statusAI, result,
        businessCodes, moduleCodes
        ) VALUES
        (
            #{ikerId}, #{ikcId}, #{serviceRegion}, #{productCode}, #{issueCode},
            #{closeTime}, #{issueDespToAI}, #{processDespToAI}, #{titleFromAI},
            #{answerFromAI}, #{keywordFromAI}, #{kbId}, #{issueToChatFile},
            #{answerToChatFile}, #{serviceId}, #{supportId}, #{faq}, #{faqUserId},
            #{issueclassification}, #{erpSystemCode}, #{lang}, #{targetArea},
            #{applicableVersion}, #{updateUserId}, #{status}, #{statusAI}, #{result},
            #{businessCodes}, #{moduleCodes}
        )
    </insert>

    <insert id="updateIssueKbOverview">
        update issue_kb_overview
        set titleFromAI=#{titleFromAI}, answerFromAI=#{answerFromAI},
        keywordFromAI=#{keywordFromAI}, statusAI = #{statusAI}, result = #{result}
        where id = #{id}
    </insert>
    <insert id="saveIssueKbExecuteRecord" useGeneratedKeys="true" parameterType="com.digiwin.escloud.issueservice.t.model.issuekb.IssueKbExecuteRecord"
            keyProperty="id" keyColumn="id">
        insert into issue_kb_execute_record (serviceRegion, productCode, ikcId, closeDate, `count`, status)
        values(#{serviceRegion}, #{productCode}, #{ikcId}, DATE_SUB(CURDATE(), INTERVAL 1 DAY), #{count}, #{status})
    </insert>
    <select id="selectIssueKbAIStatus" resultType="java.lang.Integer">
        select count(*) from issue_kb_overview a where a.ikerId = #{ikerId} and a.statusAI != 1
    </select>
    <update id="updateIssueKbExecuteRecord">
        update issue_kb_execute_record a
        set a.status = #{status}
        where a.id = #{id}
    </update>
</mapper>