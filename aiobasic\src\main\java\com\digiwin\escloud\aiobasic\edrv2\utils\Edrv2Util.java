package com.digiwin.escloud.aiobasic.edrv2.utils;

import com.alibaba.fastjson.JSONObject;
import com.digiwin.escloud.common.model.ResponseBase;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.util.*;

import static com.digiwin.escloud.aiobasic.edrv2.constant.Edrv2Const.*;


@Slf4j
@Component
public class Edrv2Util {
    @Autowired
    RestTemplate restTemplate;

    private static HttpEntity<MultiValueMap<String, String>> requestEntity(String token) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", token);
        return new HttpEntity<>(headers);
    }

    public ResponseBase getOnlineTotalAmount(String url, String token, String siteIds, String timeFromString, String timeToString) {
        StringBuilder urlBuilder = new StringBuilder(url + AGENT_COUNT_URI)
                .append("?isActive=true&siteIds=").append(siteIds);
        if (StringUtils.hasText(timeFromString)) {
            urlBuilder.append("&lastActiveDate__gte=").append(timeFromString);
        }
        if (StringUtils.hasText(timeToString)) {
            urlBuilder.append("&lastActiveDate__lte=").append(timeToString);
        }
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<ResponseBase> responseEntity = restTemplate.exchange(urlBuilder.toString(), HttpMethod.GET, requestEntity(token), ResponseBase.class);
        return responseEntity.getBody();
    }

    public ResponseBase getOfflineTotalAmount(String url, String token, String siteIds, String timeFromString, String timeToString) {
        StringBuilder urlBuilder = new StringBuilder(url + AGENT_COUNT_URI)
                .append("?isActive=false&siteIds=").append(siteIds);
        if (StringUtils.hasText(timeFromString)) {
            urlBuilder.append("&lastActiveDate__gte=").append(timeFromString);
        }
        if (StringUtils.hasText(timeToString)) {
            urlBuilder.append("&lastActiveDate__lte=").append(timeToString);
        }
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<ResponseBase> responseEntity = restTemplate.exchange(urlBuilder.toString(), HttpMethod.GET, requestEntity(token), ResponseBase.class);
        return responseEntity.getBody();
    }

    public ResponseBase getHealthTotalAmount(String url, String token, String siteIds, String timeFromString, String timeToString) {
        StringBuilder urlBuilder = new StringBuilder(url + AGENT_COUNT_URI)
                .append("?infected=false&siteIds=").append(siteIds);
        if (StringUtils.hasText(timeFromString)) {
            urlBuilder.append("&lastActiveDate__gte=").append(timeFromString);
        }
        if (StringUtils.hasText(timeToString)) {
            urlBuilder.append("&lastActiveDate__lte=").append(timeToString);
        }
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<ResponseBase> responseEntity = restTemplate.exchange(urlBuilder.toString(), HttpMethod.GET, requestEntity(token), ResponseBase.class);
        return responseEntity.getBody();
    }

    public ResponseBase getInfectedTotalAmount(String url, String token, String siteIds, String timeFromString, String timeToString) {
        StringBuilder urlBuilder = new StringBuilder(url + AGENT_COUNT_URI)
                .append("?infected=true&siteIds=").append(siteIds);
        if (StringUtils.hasText(timeFromString)) {
            urlBuilder.append("&lastActiveDate__gte=").append(timeFromString);
        }
        if (StringUtils.hasText(timeToString)) {
            urlBuilder.append("&lastActiveDate__lte=").append(timeToString);
        }
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<ResponseBase> responseEntity = restTemplate.exchange(urlBuilder.toString(), HttpMethod.GET, requestEntity(token), ResponseBase.class);
        return responseEntity.getBody();
    }

    public ResponseBase getNotDisabledTotalAmount(String url, String token, String siteIds, String timeFromString, String timeToString) {
        StringBuilder urlBuilder = new StringBuilder(url + AGENT_COUNT_URI)
                .append("?operationalStates=na&siteIds=").append(siteIds);
        if (StringUtils.hasText(timeFromString)) {
            urlBuilder.append("&lastActiveDate__gte=").append(timeFromString);
        }
        if (StringUtils.hasText(timeToString)) {
            urlBuilder.append("&lastActiveDate__lte=").append(timeToString);
        }
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<ResponseBase> responseEntity = restTemplate.exchange(urlBuilder.toString(), HttpMethod.GET, requestEntity(token), ResponseBase.class);
        return responseEntity.getBody();
    }

    public ResponseBase getDisabledTotalAmount(String url, String token, String siteIds, String timeFromString, String timeToString) {
        StringBuilder urlBuilder = new StringBuilder(url + AGENT_COUNT_URI)
                .append("?operationalStates=auto_fully_disabled,fully_disabled&siteIds=").append(siteIds);
        if (StringUtils.hasText(timeFromString)) {
            urlBuilder.append("&lastActiveDate__gte=").append(timeFromString);
        }
        if (StringUtils.hasText(timeToString)) {
            urlBuilder.append("&lastActiveDate__lte=").append(timeToString);
        }
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<ResponseBase> responseEntity = restTemplate.exchange(urlBuilder.toString(), HttpMethod.GET, requestEntity(token), ResponseBase.class);
        return responseEntity.getBody();
    }

    public ResponseBase getDisabledAndNotRebootedTotalAmount(String url, String token, String siteIds, String timeFromString, String timeToString) {
        StringBuilder urlBuilder = new StringBuilder(url + AGENT_COUNT_URI)
                .append("?operationalStates=partially_disabled&siteIds=").append(siteIds);
        if (StringUtils.hasText(timeFromString)) {
            urlBuilder.append("&lastActiveDate__gte=").append(timeFromString);
        }
        if (StringUtils.hasText(timeToString)) {
            urlBuilder.append("&lastActiveDate__lte=").append(timeToString);
        }
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<ResponseBase> responseEntity = restTemplate.exchange(urlBuilder.toString(), HttpMethod.GET, requestEntity(token), ResponseBase.class);
        return responseEntity.getBody();
    }

    public ResponseBase getAgentErrorTotalAmount(String url, String token, String siteIds, String timeFromString, String timeToString) {
        StringBuilder urlBuilder = new StringBuilder(url + AGENT_COUNT_URI)
                .append("?operationalStates=disabled_error,db_corruption&siteIds=").append(siteIds);
        if (StringUtils.hasText(timeFromString)) {
            urlBuilder.append("&lastActiveDate__gte=").append(timeFromString);
        }
        if (StringUtils.hasText(timeToString)) {
            urlBuilder.append("&lastActiveDate__lte=").append(timeToString);
        }
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<ResponseBase> responseEntity = restTemplate.exchange(urlBuilder.toString(), HttpMethod.GET, requestEntity(token), ResponseBase.class);
        return responseEntity.getBody();
    }

    public ResponseBase getEndpointList(String url, String token, String accountId, String siteIds, String applicationIds) {
        StringBuilder urlBuilder = new StringBuilder(url + APPLICATION_ENDPOINTS_URI)
                .append("?accountIds=").append(accountId)
                .append("&filteredSiteIds=").append(siteIds)
                .append("&applicationIds=").append(applicationIds)
                .append("&limit=1000");
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<ResponseBase> responseEntity = restTemplate.exchange(urlBuilder.toString(), HttpMethod.GET, requestEntity(token), ResponseBase.class);
        return responseEntity.getBody();
    }

    public Map<String, Object> sendRequest(String url, String token, HttpMethod httpMethod, Map<String, Object> requestBody) {
        try {
            // 将 Map 转换为 JSON 字符串
            ObjectMapper objectMapper = new ObjectMapper();
            String jsonRequestBody = objectMapper.writeValueAsString(requestBody);

            // http請求
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", token);
            HttpEntity<String> request = new HttpEntity<>(jsonRequestBody, headers);
            ResponseEntity<JSONObject> response = restTemplate.exchange(url, httpMethod, request, JSONObject.class);

            // 解析數據
            JSONObject jo = response.getBody();

            // 處理錯誤
            JSONObject errors = jo.getJSONObject("errors");
            if (Objects.nonNull(errors)) {
                log.error("Send Request Error: ", errors);
                Map<String, Object> res = new HashMap<>();
                res.put("error", errors);
                return res;
            }

            // 處理放行成功data內容是ArrayList的情況，指取第一筆
            Object resObj = jo.getOrDefault("data", null);
            if (Objects.nonNull(resObj) && resObj instanceof ArrayList) {
                List<Map<String, Object>> list = (List<Map<String, Object>>) resObj;
                return list.get(0);
            }

            JSONObject res = jo.getJSONObject("data");
            return res.getInnerMap();
        } catch (Exception e) {
            log.error("Send Request Error: ", e);
            Map<String, Object> res = new HashMap<>();
            res.put("error", e.getMessage());
            return res;
        }
    }

}
