package com.digiwin.escloud.aiocdp.industry.controller;

import com.digiwin.escloud.aiocdp.industry.model.SendMsgReqBody;
import com.digiwin.escloud.aiocdp.industry.service.IIndustryService;
import com.digiwin.escloud.common.response.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Base64;

/**
 * <AUTHOR>
 * @Date: 2025-05-06 11:10
 * @Description
 */
@Slf4j
@RestController
@RequestMapping("/api/industry")
public class IndustryController {

    @Resource
    private IIndustryService industryService;
    // 1x1像素透明GIF的Base64编码（43字节）
    private static final String GIF_BASE64 = "R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7";
    private static final byte[] TRANSPARENT_GIF = Base64.getDecoder().decode(GIF_BASE64);

    @PostMapping(value = "/send/msg")
    public BaseResponse sendMsg(@RequestBody() SendMsgReqBody msgReqBody) {
        return industryService.sendMsg(msgReqBody);
    }

    @GetMapping(value = "/redirect")
    public void setRedirect(@RequestParam(value = "sourceId", required = false, defaultValue = "0") String sourceId,
                            @RequestParam(value = "sourceUrl", required = false, defaultValue = "") String sourceUrl,
                            @RequestParam(value = "urlStorage", required = false, defaultValue = "") String urlStorage,
                            @RequestParam(value = "sourceText", required = false, defaultValue = "") String sourceText,
                            @RequestParam(value = "sourceType", required = false, defaultValue = "") String sourceType,
                            @RequestParam(value = "userId", required = false, defaultValue = "") String userId,
                            @RequestParam(value = "operateType", required = false, defaultValue = "") String operateType,
                            @RequestParam(value = "workbenchToken", required = false, defaultValue = "") String workbenchToken,
                            HttpServletResponse response) throws IOException {
        industryService.setRedirect(sourceId, sourceUrl, urlStorage, sourceText, sourceType, userId, operateType, workbenchToken, response);
    }

    @GetMapping(value = "/read/feedback")
    public ResponseEntity<byte[]> readFeedback(@RequestParam(value = "sourceId") String sourceId,
                                               @RequestParam(value = "sourceType") String sourceType,
                                               @RequestParam(value = "userId", required = false, defaultValue = "") String userId,
                                               @RequestParam(value = "operateType", required = false, defaultValue = "SCHEME_READ_FROM_MAIL") String operateType) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.IMAGE_GIF);
        headers.setContentLength(TRANSPARENT_GIF.length);
        industryService.readFeedback(sourceId, sourceType, userId, operateType);
        return new ResponseEntity<>(TRANSPARENT_GIF, headers, HttpStatus.OK);
    }

    @GetMapping(value = "/read/num")
    public BaseResponse getReadNum(@RequestParam(value = "sourceId") String sourceId,
                                   @RequestParam(value = "sourceType") String sourceType,
                                   @RequestParam(value = "operateType", required = false, defaultValue = "SCHEME_READ_FROM_MAIL") String operateType) {
        return BaseResponse.ok(industryService.getReadNum(sourceId, sourceType, operateType));
    }

    @GetMapping(value = "/sync/staff")
    public BaseResponse syncStaff() {
        industryService.syncStaff();
        return BaseResponse.ok();
    }
}
