package com.digiwin.escloud.aiocmdb.asset.model;

import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 模型数据字段关系映射表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@ApiModel(value = "CmdbModelDataFieldRelationMapping对象", description = "模型数据字段关系映射表")
public class CmdbModelDataFieldRelationMapping implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("目表数据modelCode")
    private String targetModelCode;

    @ApiModelProperty("目表数据模型字段名")
    private String targetModelFieldName;

    @ApiModelProperty("目表数据模型字段JsonPath(数据驱动使用)")
    private String targetModelFieldJsonPath;

    @ApiModelProperty("收集项id (accId)")
    private Long accId;

    @ApiModelProperty("上传数据modelCode")
    private String sourceModelCode;

    @ApiModelProperty("上传数据模型字段名")
    private String sourceModelFieldName;

    @ApiModelProperty("上传数据模型字段JsonPath(数据驱动使用)")
    private String sourceModelFieldJsonPath;

    @ApiModelProperty("转换规则类型")
    private String transformRuleType;

    @ApiModelProperty("转换规则")
    private String transformRule;

    @ApiModelProperty("描述")
    private String description;

    private LocalDateTime createDate;

    private LocalDateTime updateDate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTargetModelCode() {
        return targetModelCode;
    }

    public void setTargetModelCode(String targetModelCode) {
        this.targetModelCode = targetModelCode;
    }

    public String getTargetModelFieldName() {
        return targetModelFieldName;
    }

    public void setTargetModelFieldName(String targetModelFieldName) {
        this.targetModelFieldName = targetModelFieldName;
    }

    public String getTargetModelFieldJsonPath() {
        return targetModelFieldJsonPath;
    }

    public void setTargetModelFieldJsonPath(String targetModelFieldJsonPath) {
        this.targetModelFieldJsonPath = targetModelFieldJsonPath;
    }

    public Long getAccId() {
        return accId;
    }

    public void setAccId(Long accId) {
        this.accId = accId;
    }

    public String getSourceModelCode() {
        return sourceModelCode;
    }

    public void setSourceModelCode(String sourceModelCode) {
        this.sourceModelCode = sourceModelCode;
    }

    public String getSourceModelFieldName() {
        return sourceModelFieldName;
    }

    public void setSourceModelFieldName(String sourceModelFieldName) {
        this.sourceModelFieldName = sourceModelFieldName;
    }

    public String getSourceModelFieldJsonPath() {
        return sourceModelFieldJsonPath;
    }

    public void setSourceModelFieldJsonPath(String sourceModelFieldJsonPath) {
        this.sourceModelFieldJsonPath = sourceModelFieldJsonPath;
    }

    public String getTransformRuleType() {
        return transformRuleType;
    }

    public void setTransformRuleType(String transformRuleType) {
        this.transformRuleType = transformRuleType;
    }

    public String getTransformRule() {
        return transformRule;
    }

    public void setTransformRule(String transformRule) {
        this.transformRule = transformRule;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public LocalDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

    public LocalDateTime getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(LocalDateTime updateDate) {
        this.updateDate = updateDate;
    }

    @Override
    public String toString() {
        return "CmdbModelDataFieldRelationMapping{" +
            "id = " + id +
            ", targetModelCode = " + targetModelCode +
            ", targetModelFieldName = " + targetModelFieldName +
            ", targetModelFieldJsonPath = " + targetModelFieldJsonPath +
            ", accId = " + accId +
            ", sourceModelCode = " + sourceModelCode +
            ", sourceModelFieldName = " + sourceModelFieldName +
            ", sourceModelFieldJsonPath = " + sourceModelFieldJsonPath +
            ", transformRuleType = " + transformRuleType +
            ", transformRule = " + transformRule +
            ", description = " + description +
            ", createDate = " + createDate +
            ", updateDate = " + updateDate +
        "}";
    }
}
