package com.digiwin.escloud.aiocdp.industry.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.digiwin.escloud.aiocdp.constant.IndustryConst;
import com.digiwin.escloud.aiocdp.industry.base.SendBaseFactoryService;
import com.digiwin.escloud.aiocdp.industry.dao.IIndustryDao;
import com.digiwin.escloud.aiocdp.industry.model.CdpLog;
import com.digiwin.escloud.aiocdp.industry.model.PageParams;
import com.digiwin.escloud.aiocdp.industry.model.SendMsgReqBody;
import com.digiwin.escloud.aiocdp.industry.model.Staff;
import com.digiwin.escloud.aiocdp.industry.service.IIndustryService;
import com.digiwin.escloud.aiocdp.utils.UcdpUtil;
import com.digiwin.escloud.common.controller.ParamCheckHelp;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.Md5Utils;
import com.digiwin.escloud.common.util.RequestUtil;
import com.digiwin.escloud.common.util.SnowFlake;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date: 2025-05-06 11:15
 * @Description
 */
@RefreshScope
@Slf4j
@Service
public class IndustryService implements IIndustryService, ParamCheckHelp {

    @Resource
    private SendBaseFactoryService sendBaseFactoryService;
    @Resource
    private IIndustryDao industryDao;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private LogService logService;
    @Resource
    private UcdpUtil ucdpUtil;
    @Value("${digiwin.industry.dbname:appsmith}")
    private String industryDBName;
    @Value("${digiwin.kbs.url:https://kbs.apps.digiwincloud.com.cn}")
    private String kbsUrl;

    @Override
    @Async("asyncPoolTaskExecutor")
    public BaseResponse sendMsg(SendMsgReqBody msgReqBody) {
        Optional<BaseResponse> optResponse = checkParamIsEmpty(msgReqBody.getSendTypes(), "sendTypes");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }
        optResponse = checkParamIsEmpty(msgReqBody.getMsgDtos(), "msgDtos");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }
        sendBaseFactoryService.sendMsgData(msgReqBody);
        return BaseResponse.ok();
    }

    private CdpLog buildCdpLog(Long eid, String userId, String sourceId, String sourceText, String sourceType, String sourceUrl,
                               String urlStorage, String operateType, String operateContent, String operateResult) {
        CdpLog cdpLog = new CdpLog();
        cdpLog.setId(SnowFlake.getInstance().newId());
        cdpLog.setEid(eid);
        cdpLog.setUserId(userId);
        cdpLog.setSourceId(sourceId);
        cdpLog.setSourceText(sourceText);
        cdpLog.setSourceType(sourceType);
        cdpLog.setSourceUrl(sourceUrl);
        cdpLog.setUrlStorage(urlStorage);
        cdpLog.setUrlMd5(StringUtils.isEmpty(sourceUrl) ? null : Md5Utils.getMd5(sourceUrl));
        cdpLog.setOperateType(operateType);
        cdpLog.setOperateContent(operateContent);
        cdpLog.setOperateResult(operateResult);
        cdpLog.setStartTime(LocalDateTime.now());
        return cdpLog;
    }


    @Override
    public void setRedirect(String sourceId, String sourceUrl, String urlStorage, String sourceText, String sourceType, String userId,
                            String operateType, String workbenchToken, HttpServletResponse response) throws IOException {
        CdpLog cdpLog = buildCdpLog(RequestUtil.getHeaderEid(), userId, sourceId, sourceText, sourceType, sourceUrl, urlStorage, operateType,
                null, null);
        logService.saveCdpLog(Stream.of(cdpLog).collect(Collectors.toList()));
        if (!StringUtils.isEmpty(sourceUrl)) {
            if (IndustryConst.KBS_URL_STORAGE.equals(urlStorage) && !StringUtils.isEmpty(workbenchToken)) {
                String kbsPath = sourceUrl.replace(kbsUrl, "");
                String kbsRealUrl = kbsUrl + "/sso-login?userToken=" + workbenchToken + "&routerLink=" + kbsPath;
                response.sendRedirect(kbsRealUrl);
                return;
            }
            response.sendRedirect(sourceUrl);
        }
    }

    @Override
    public Long readFeedback(String sourceId, String sourceType, String userId, String operateType) {
        CdpLog cdpLog = buildCdpLog(RequestUtil.getHeaderEid(), userId, sourceId, null, sourceType, null, null, operateType,
                null, null);
        logService.saveCdpLog(Stream.of(cdpLog).collect(Collectors.toList()));
        return stringRedisTemplate.opsForValue().increment(IndustryConst.INDUSTRY_SCHEME_READ_NUM + sourceType + ":" + sourceId, 1);
    }

    @Override
    public String getReadNum(String sourceId, String sourceType, String operateType) {
        int readNum = industryDao.getReadNum(industryDBName, sourceId, operateType);
        return String.valueOf(readNum);
    }

    @Scheduled(cron = "${digiwin.sync.staff.cron:0 13 21 * * *}")
    @Override
    @Async("asyncPoolTaskExecutor")
    public void syncStaff() {
        log.info("syncStaff start");
        Long pageSize = 500L;
        Long pageNumber = 1L;
        long startTime = System.currentTimeMillis(); // 获取开始时间
        long timeout = 5 * 60 * 1000; // 设置超时时间为10分钟（毫秒）

        try {
            while (true) {
                long currentTime = System.currentTimeMillis(); // 获取当前时间
                if (currentTime - startTime > timeout) {
                    log.warn("syncStaff timeout, breaking loop");
                    break;
                }

                PageParams pageParams = new PageParams(pageNumber, pageSize);
                try {
                    JSONArray staffRes = ucdpUtil.queryDigiwinStaff(pageParams);
                    Thread.sleep(500);
                    if (CollectionUtils.isEmpty(staffRes)) {
                        break;
                    }
                    List<Staff> staffList = new ArrayList<>();
                    for (int i = 0; i < staffRes.size(); i++) {
                        JSONObject jsonObject = staffRes.getJSONObject(i);
                        Staff staff = new Staff();
                        staff.setWorkno(jsonObject.getString("employeeId"));
                        String employeeEmail = jsonObject.getString("employeeEmail");
                        staff.setEmail(employeeEmail);
                        if (!StringUtils.isEmpty(employeeEmail)) {
                            String itcode = employeeEmail.replaceAll(IndustryConst.DIGIWIN_MAIL, "");
                            staff.setItcode(itcode);
                        }
                        staff.setFullname(jsonObject.getString("employeeName"));
                        staff.setDptId(jsonObject.getString("deptId"));
                        staff.setDptName(jsonObject.getString("deptName"));
                        staff.setDptNameCN(jsonObject.getString("deptCHName"));
                        staff.setDptNameTW(jsonObject.getString("deptName"));
                        staff.setUpperDptId(jsonObject.getString("leaderDeptId"));
                        staff.setUpperDptName(jsonObject.getString("leaderDeptName"));
                        staff.setUpperDptNameCN(jsonObject.getString("leaderDeptCHName"));
                        staff.setUpperDptNameTW(jsonObject.getString("leaderDeptName"));
                        staff.setSupervisor(jsonObject.getString("leaderEmployeeId"));
                        String dimission = jsonObject.getString("dimission");
                        String workStatus = "work";
                        if (!StringUtils.isEmpty(dimission)) {
                            workStatus = ("已离职").equals(dimission) ? "quit" : "work";
                        } else {
                            workStatus = "quit";
                        }
                        staff.setWorkStatus(workStatus);
                        staffList.add(staff);
                    }
                    industryDao.saveStaff(industryDBName, staffList);
                } catch (Exception ex) {
                    log.error("queryDigiwinStaff error", ex);
                    break;
                }
                log.info("syncStaff pageNumber:{}", pageNumber);
                pageNumber++;
            }
        } catch (Exception e) {
            log.error("syncStaff error", e);
        } finally {
            log.info("syncStaff end");
        }
    }
}
