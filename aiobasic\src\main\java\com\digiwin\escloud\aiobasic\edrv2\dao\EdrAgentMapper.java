package com.digiwin.escloud.aiobasic.edrv2.dao;

import com.digiwin.escloud.aiobasic.edrv2.model.EdrAgentIsWarning;
import com.digiwin.escloud.aiobasic.edrv2.model.EdrAgentIsWarningParam;
import com.digiwin.escloud.aiobasic.report.model.EdrAgentField;
import com.digiwin.escloud.aiobasic.edrv2.model.EdrAgentParam;
import com.digiwin.escloud.aiobasic.edrv2.model.EdrAgentWhiteList;
import com.digiwin.escloud.common.response.BaseResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Mapper
public interface EdrAgentMapper {
    List<EdrAgentField> getAgent(EdrAgentParam edrAgentParam);
    List<Map<String, Object>> getOSTotalAmount(@Param("eid") String eid,
                                               @Param("timeFrom") Date timeFrom,
                                               @Param("timeTo") Date timeTo);
    List<EdrAgentField> getAgentByAgentId(@Param("siteId") String siteId,
                                          @Param("agentIdList") List<String> agentIdList);
    BaseResponse saveAgentWhiteList(List<EdrAgentWhiteList> agentWhiteList);
    Integer upsertAgent(EdrAgentField edrAgentFieldRequest);
    List<Map<String, Object>> getAgentSiteIdByEid(@Param("eid") Long eid);
    Integer updateAgentStatus(@Param("operationalState") String operationalState,
                              @Param("agentClosedTime") Date agentClosedTime,
                              @Param("agentIds") List<String> agentIds);
    Integer removeAgent(@Param("siteId") Long siteId,
                        @Param("agentId") String agentId,
                        @Param("agentIdList") List<String> agentIdList);
    List<String> getOsNameList(@Param("eid") Long eid);
    Integer moveAgent(@Param("agentId") String agentId,
                      @Param("siteName") String siteName,
                      @Param("groupTitle") String groupTitle);
    Map<String, Object> getHealthTotalAmount(@Param("eid")Long eid,
                                             @Param("siteIds") List<String> siteIds);

    void updateAgentNetworkStatus(@Param("IDS") List<Map<String, String>> IDS,
                                  @Param("status") String status);
    List<EdrAgentField> getAgentById(@Param("idList") List<String> idList);
    Integer updateAgentScanStatus(@Param("siteIds") List<String> siteIds,
                                  @Param("agentIds") List<String> agentIds,
                                  @Param("scanStatus") String scanStatus);
    Integer updateIsUninstalled(@Param("siteId") String siteId, @Param("agentId") String agentId);
    Integer updateIsPendingUninstall(@Param("siteId") String siteId, @Param("agentId") String agentId);
    Integer updateAgentIsWarning(EdrAgentIsWarningParam param);
    List<EdrAgentIsWarning> selectAgentIsWarning(@Param("siteId") String siteId,
                                                 @Param("isWarning") Boolean isWarning);
}
