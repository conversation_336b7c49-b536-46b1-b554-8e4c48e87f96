package com.digiwin.escloud.issueservice.v3.service.impl;

import com.alibaba.fastjson.JSON;
import com.digiwin.escloud.common.feign.AioIssueFeignClient;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.integration.api.iam.req.user.IamAuthoredUser;
import com.digiwin.escloud.integration.service.GmcService;
import com.digiwin.escloud.issueservice.cache.IssueSwitchCache;
import com.digiwin.escloud.issueservice.constant.Constants;
import com.digiwin.escloud.issueservice.dao.IIssueDao;
import com.digiwin.escloud.issueservice.model.*;
import com.digiwin.escloud.issueservice.services.IProductService;
import com.digiwin.escloud.issueservice.services.ITagService;
import com.digiwin.escloud.issueservice.services.IUserService;
import com.digiwin.escloud.issueservice.services.Impl.KcfService;
import com.digiwin.escloud.issueservice.statemode.*;
import com.digiwin.escloud.issueservice.t.integration.fuguan.mq.issue.IssueFGProducer;
import com.digiwin.escloud.issueservice.t.integration.scm.model.ToT100Flag;
import com.digiwin.escloud.issueservice.t.integration.scm.mq.ScmProducer;
import com.digiwin.escloud.issueservice.t.issuedetail.service.impl.IssueProcessServiceImpl;
import com.digiwin.escloud.issueservice.t.issuesubmit.dao.IssueSubmitMapper;
import com.digiwin.escloud.issueservice.t.model.cases.Cases;
import com.digiwin.escloud.issueservice.t.model.cases.Module;
import com.digiwin.escloud.issueservice.t.model.cases.constants.CaseProcessType;
import com.digiwin.escloud.issueservice.t.model.cases.constants.CaseStatus;
import com.digiwin.escloud.issueservice.t.model.cases.constants.SubmitWay;
import com.digiwin.escloud.issueservice.t.model.cases.dto.T100CaseReqP;
import com.digiwin.escloud.issueservice.t.model.common.BaseResponse;
import com.digiwin.escloud.issueservice.t.model.common.ResponseStatus;
import com.digiwin.escloud.issueservice.t.model.constant.JiaoFuUserType;
import com.digiwin.escloud.issueservice.t.model.constant.UserType;
import com.digiwin.escloud.issueservice.t.utils.CasesProcessUtils;
import com.digiwin.escloud.issueservice.v3.dao.IIssueDaoV3;
import com.digiwin.escloud.issueservice.v3.dao.IIssueDetailDaoV3;
import com.digiwin.escloud.issueservice.v3.service.IIssueDetailServiceV3;
import com.digiwin.escloud.messagelibrary.Model.MessageDestination;
import com.digiwin.escloud.userapi.model.customer.ServiceStaffCond;
import com.digiwin.escloud.userapi.model.user.UserDetailInfo;
import com.digiwin.escloud.userapi.service.UserServiceFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * @author:daixy
 * @date:2018-10-17
 * @decription:
 */
@Service
@Slf4j
public class IssueDetailServiceV3 implements IIssueDetailServiceV3 {
    @Value("${digiwin.issue.connectarea}")
    private String connectarea;
    @Value("${digiwin.visitor.public.user.service.code}")
    private String visitorServiceCode;
    @Autowired
    private IIssueDetailDaoV3 issueDetailDao;
    @Autowired
    private IIssueDaoV3 issueDao;
    @Autowired
    private IIssueDao oldIssueDao;
    @Autowired
    private IssueSwitchCache issueSwitchCache;
    @Autowired
    private IUserService userService;
    @Autowired
    private UserServiceFeignClient userServiceFeignClient;
    @Autowired
    private IProductService productService;
    @Autowired
    private IssueServiceV3 issueService;
    @Autowired
    private ITagService tagService;
    @Autowired
    private GmcService gmcService;
    @Autowired
    private KcfService kcfService;
    @Autowired
    private AioIssueFeignClient aioIssueClient;
    @Autowired
    IssueProcessServiceImpl issueProcessService;
    @Autowired
    private IssueSubmitMapper issuesMapper;
    @Autowired
    private ScmProducer scmMq;
    @Autowired
    private IssueFGProducer fg187Mq;
    @Autowired
    private CasesProcessUtils casesProcessUtils;
    @Override
    public List<IssueClassificationData> selectClassification(String productCode) {
        return issueDetailDao.selectClassification(productCode);
    }

    @Override
    public List<ErpSystemCodeData> selectSystemCode(String productCode) {
        return issueDetailDao.selectSystemCode(productCode);
    }

    @Override
    public List<ErpProgramData> selectProgramCode(String productCode, String erpSystemCode) {
        return issueDetailDao.selectProgramCode(productCode, erpSystemCode);
    }

//    @Override
//    @Transactional
    /*public boolean updateIssue(long issueId, String crmId, String version, List<IssueProgress> issueProgressList, IssueCasedetail issueCasedetail, String userId) {
        IssueCasedetail dbData = issueDetailDao.selectIssueDetail(issueId);
        int success = 0;
        String issueStatus = issueDao.SelectIssueStatus(issueId);
        if (issueStatus.equals(IssueStatus.Processing.toString())||issueStatus.equals(IssueStatus.Closed.toString())){
            success = 1;
        }
        else {
            success =  issueDao.UpdateIssueStatusByStaff(issueId, IssueStatus.Processing.toString(), IssueStatus.Submited.toString(), "", "");
        }
        if (success > 0) {
            if (dbData != null) {
                issueDetailDao.updateDetailIssue(issueCasedetail);
            } else {
                issueDetailDao.insertDetailIssue(issueCasedetail);
            }
            for (IssueProgress issueProgress:issueProgressList) {
                //issueDetailDao.updateIssueProductVersion(issueId,version);


                if (issueProgress.getId() == 0) {

                    DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    issueProgress.setProcessTime(sdf.format(new Date()));
                    issueProgress.setReplyType(ReplyType.A.toString());

                    issueProgress.setSyncStatus("T");
                    if (IssueProcessType.Process.toString().equals(issueProgress.getProcessType())|| IssueProcessType.Close.toString().equals(issueProgress.getProcessType())) {
                        if (!StringUtils.isEmpty(issueProgress.getProcessor())){
                            if (issueProgress.getProcessor().trim().length()==5){
                                issueProgress.setWorkno(issueProgress.getProcessor().trim());
                                UserDetailInfo userDetailByWorkNo = userServiceFeignClient.getUserDetailByWorkNo(issueProgress.getProcessor().trim());
                                if (userDetailByWorkNo!=null){
                                    issueProgress.setProcessor(userDetailByWorkNo.getUserId());
                                    if (IssueProcessType.Process.toString().equals(issueProgress.getProcessType()))
                                        issueProgress.setHandlerId(userDetailByWorkNo.getUserId());
                                }
                            }
                            else {
                                issueProgress.setWorkno(issueProgress.getWorkno());
                                issueProgress.setProcessor(issueProgress.getProcessor());
                                if (IssueProcessType.Process.toString().equals(issueProgress.getProcessType()))
                                    issueProgress.setHandlerId(issueProgress.getProcessor());
                            }
                        }
                    }
                    issueProgress.setSequeceNum(issueDao.getMaxOrder(issueId));
                    if (IssueProcessType.Process.toString().equals(issueProgress.getProcessType()))
                        issueProgress.setCurrentStatus(IssueStatus.Processing.toString());
                    else
                        issueProgress.setCurrentStatus(issueStatus);
                    if(!StringUtils.isEmpty(issueProgress.getDescription())){
                        if("Accept Issue".equals(issueProgress.getDescription())){
                            issueProgress.setProcessType(IssueProcessType.Accept.toString());
                            //接单时，更新当前处理人为接单的人员
                            Issue issue = new Issue();
                            issue.setIssueId(issueId);
                            issue.setServiceId(userId);
                            StaffUserInfo staffUserInfo = userService.GetServiceStaffInfoByUserId(issueProgress.getProcessor());//(issue.getUserId());

                            if (staffUserInfo != null) {
                                issue.setDepartment(String.valueOf(staffUserInfo.getDepartment()));
                            }

                            issueDao.updateProcessor(issue);
                        }
                    }
                    issueDao.InsertIssueProgress(issueId, crmId, issueProgress);
                    //更新总工时
                    issueDetailDao.updateDetailIssueTotalWorkHours(issueId);
                } else {
                    IssueProgress issue = issueDao.SelectIssueProgressById(issueProgress.getId());
                    if (!issue.getDescription().equals(issueProgress.getDescription()) ||
                            issue.getProcessHours() != issueProgress.getProcessHours()||
                            issue.getProcessType().equals(IssueProcessType.Close.toString())) {
                        issueProgress.setSyncStatus("T");
                        *//*if (IssueProcessType.Process.toString().equals(issueProgress.getProcessType()))
                            issueProgress.setCurrentStatus(IssueStatus.Processing.toString());
                        else
                            issueProgress.setCurrentStatus(issueStatus);*//*
                        issueDao.UpdateIssueProgressById(issueProgress);
                        //更新总工时
                        issueDetailDao.updateDetailIssueTotalWorkHours(issueId);
                    }
                }
            }

            return true;
        }
        return false;
    }*/

    /**
     * 关闭案件
     *
     * @param issueId
     * @return
     */
    /*@Override
    @Transactional
    public boolean CloseIssue(long issueId, String userId, String productVersion, String crmId, String deptId, List<IssueProgress> issueProgress, IssueCasedetail issueCasedetail, String syncStatus) {
        int success = 0;
        success = issueDao.UpdateIssueStatusByStaff(issueId, IssueStatus.Closed.toString(), IssueStatus.Processing.toString(), deptId, userId);
        //更新同步状态
        //同步狀態為S表示不同步至CRM
        if(!IssueStatus.Trial.toString().equals(syncStatus)) {
            issueDetailDao.updateIssueStatus(issueId);
        }
        if (success > 0) {
            //写入单身
            return updateIssue(issueId, crmId,productVersion, issueProgress, issueCasedetail,userId);
        }
        return false;
    }*/

   public String getIssueSyncStatusV3(long issueId, String productCode,String serviceCode, String currentIssueStatus){
       String syncStatus = SyncStatus.DontNeedSync.toString();//默认
       //2021-11-23 huly：访客和体验客户提单 都不同步到crm
       //1 如果是访客，直接不同步
       if(visitorServiceCode.equals(serviceCode)){
           return syncStatus;
       }else {
           IssueSwitch issueSwitch = issueSwitchCache.selectIssueSwitch(productCode);

           if(issueSwitch != null && !StringUtils.isEmpty(issueSwitch.getSyncCrmMoment())){
               //有设置开关的情况下，在判断是否是体验客户，是体验客户，直接不同步，是crm客户，在判断开会里面的同步时机
               if(oldIssueDao.checkCrmCustomer(serviceCode,productCode)){
                   if(SyncMoment.Submit.toString().equals(issueSwitch.getSyncCrmMoment())){ //提单同步，比如大陆T
                       String issueSyncStatus = issueDao.getIssueSyncStatus(String.valueOf(issueId));
                       syncStatus = SyncStatus.SyncSuccess.toString().equals(issueSyncStatus)|| SyncStatus.WaitSync.toString().equals(issueSyncStatus)? SyncStatus.EditUnSync.toString():issueSyncStatus;
                   }else if(SyncMoment.Close.toString().equals(issueSwitch.getSyncCrmMoment())){ //结案（交付结案）时同步,比如东南亚T
                       if(IssueStatus.Closed.toString().equals(currentIssueStatus))
                       {
                           syncStatus = SyncStatus.UnSync.toString();
                       }else {
                           syncStatus = SyncStatus.DontNeedSync.toString();
                       }
                   }
               }
           }
       }

       return syncStatus;
   }
    public String getIssueProcessSyncStatusV3(String productCode,String serviceCode, String currentIssueStatus){
        String syncStatus = SyncStatus.DontNeedSync.toString();//默认
        //1 如果是访客，直接不同步
        if(visitorServiceCode.equals(serviceCode)){
            return syncStatus;
        }else {
            IssueSwitch issueSwitch = issueSwitchCache.selectIssueSwitch(productCode);

            if(issueSwitch != null && !StringUtils.isEmpty(issueSwitch.getSyncCrmMoment())){
                //有设置开关的情况下，在判断是否是体验客户，是体验客户，直接不同步，是crm客户，在判断开会里面的同步时机
                if(oldIssueDao.checkCrmCustomer(serviceCode,productCode)){
                    if (SyncMoment.Submit.toString().equals(issueSwitch.getSyncCrmMoment())) { //提单同步，比如大陆T
                        syncStatus = SyncStatus.EditUnSync.toString();
                    } else if (SyncMoment.Close.toString().equals(issueSwitch.getSyncCrmMoment())) { //结案（交付结案）时同步,比如东南亚T
                        if (IssueStatus.Closed.toString().equals(currentIssueStatus)) {
                            syncStatus = SyncStatus.EditUnSync.toString();
                        } else {
                            syncStatus = SyncStatus.DontNeedSync.toString();
                        }
                    }
                }
            }
        }

        return syncStatus;
    }

    /**
     * 删除一笔单身
     * @param id
     * @return
     */
    @Override
    public BaseResponse deleteProgress(long id, long issueId,String newReply ) {
        //校验案件状态
        /*Issue issue = issueDao.SelectIssueByIssueId(String.valueOf(issueId));
        if (issue != null){
            if(!(IssueStatus.Processing.isSame(issue.getIssueStatus())
                    || IssueStatus.ProductProcessing.isSame(issue.getIssueStatus())
                    || IssueStatus.ServiceCheck.isSame(issue.getIssueStatus())
                    || IssueStatus.SubmiterCheck.isSame(issue.getIssueStatus()))){
                return BaseResponse.error(ResponseStatus.ISSUE_STATUS_IS_NOT_PROCRESS);
            }
        }*/
        //判断单身是否被引用回复
        if(issueDao.getReplyId(id) > 0){
            return BaseResponse.error(ResponseStatus.REPLYID_EXIST);
        }
        //更新单头的newReply
        issueDao.updateIssueNewReplyForDelete(issueId,id,newReply);
        if(issueDao.deleteProgress(id) > 0){
            //更新总工时
            issueDetailDao.updateDetailIssueTotalWorkHours(issueId);
            return BaseResponse.ok();
        }else {
            return BaseResponse.error(ResponseStatus.DELETE_FAILD);
        }
    }

    @Override
    @Transactional
    public BaseResponse accept(String userId, long issueId, IssueProgress issueProgress){
        //2021-09-15 huly 状态模式
        Issue issue = issueDao.SelectIssueByIssueId(String.valueOf(issueId));
        IssueContext context = stateMode(issue.getIssueStatus());
        context.accept(context);
        String newIssueStatus = context.getIssueStatus();
        if(StringUtils.isEmpty(newIssueStatus)){
            return BaseResponse.error(ResponseStatus.ISSUE_STATUS_CAN_NOT_ACCEPT);
        }
        //1 更新单头，更新当前处理人为接单的人员
        StaffUserInfo staffUserInfo = userService.GetServiceStaffInfoByUserId(userId);
        boolean success = issueDao.updateIssueService(String.valueOf(issueId), userId, userId, getIssueSyncStatusV3(issueId, issue.getProductCode(), issue.getServiceCode(), newIssueStatus), staffUserInfo.getDepartment(), "", newIssueStatus);
        if(success){
            //2 新增单身
            issueProgress = progress(issueId, userId,issue.getProductCode(),issue.getServiceCode(),issue.getIssueStatus(), "", ReplyType.A.toString(), IssueProcessType.Accept.toString(),true , issueProgress);
            if("CN".equals(issue.getServiceRegion())){
                issueProgress.setDescription("问题受理中");
            }
            issueDao.InsertIssueProgress(issueId, issue.getCrmId(), issueProgress);
            //3 更新总工时
            issueDetailDao.updateDetailIssueTotalWorkHours(issueId);
            //4 记录接单时间
            casesProcessUtils.saveIssueSummaryV3( userId, issueId, IssueProcessType.Accept.toString(), issueProgress.getProcessTime());
            //5 如果是大陆T，需要同步187、scm、workday
            if("CN".equals(connectarea) && Constants.getTProductCodes().contains(issue.getProductCode())){
                syncForT(issueId, issueProgress, newIssueStatus);
                //接单，要记录时间、时长、操作人，后面报表需要
                issueProcessService.recordReply(issueId, userId,issueProgress.getProcessTime());
            }
            return BaseResponse.ok();
        }
        return BaseResponse.error(ResponseStatus.ACCEPT_FAIL);
    }

    

    /**
     * 如果是大陆T，需要同步187、scm、workday
     * @param issueId
     * @param issueProgress
     */
    private void syncForT(long issueId, IssueProgress issueProgress, String newIssueStatus) {
        int sequenceNum = issueProgress.getSequeceNum() + 1;
        double processHours = issueProgress.getProcessHours();
        //1 同步187
        fg187Mq.send(issueId, sequenceNum);
        //2 同步scm
        if(IssueStatus.Closed.toString().equals(newIssueStatus)){
            //交付结案案件直接结案要通知型管结案信息
            scmMq.send(issueId);
        }
        //3 同步workday
        Runnable runnable = () -> casesProcessUtils.sendToWorkday(issueId,sequenceNum,processHours);
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        try {
            executorService.execute(runnable);
        } catch (Exception ex) {
            log.error("sendToWorkday", ex);
        } finally {
            executorService.shutdown();
        }
    }


    @Override
    @Transactional
    public BaseResponse reassignment(String userId, long issueId, String productCode, String serviceId, IssueProgress issueProgress, String canTransferMainCharge, String language, String issueClassification, int issueLevel){

        //2021-09-15 huly 状态模式
        Issue issue = issueDao.SelectIssueByIssueId(String.valueOf(issueId));
        IssueContext context = stateMode(issue.getIssueStatus());
        context.reassignment(context,productCode);
        String newIssueStatus = context.getIssueStatus();
        if(StringUtils.isEmpty(newIssueStatus)){
            return BaseResponse.error(ResponseStatus.ISSUE_STATUS_CAN_NOT_REASSIGNMENT);
        }
        //转派时产品线换了，需要清空问题分类 模组代号 程式代号 程式版号
        if(!productCode.equals(issue.getProductCode())){
            issueDetailDao.updateDetailIssueOtherInfo(issueId);
        }

        if("CN".equals(connectarea) && Constants.getTProductCodes().contains(productCode)){
            return reassignmentForT(issueId, productCode, userId, serviceId, language, canTransferMainCharge, issueProgress, issueClassification, issueLevel, issue);
        }else {
            //如果指定客服 就按照指定客服，如果没指定，走群组，群组没设置，走产品默认客服
            StaffUserInfo staffUserInfo = getServiceStaffInfo(serviceId,productCode,issue);

            //1 更新单头 转派后主要责任人也会变
            boolean success = issueDao.updateIssueService(String.valueOf(issueId), staffUserInfo.getUserId(), staffUserInfo.getUserId() , getIssueSyncStatusV3(issueId, productCode, issue.getServiceCode(), newIssueStatus), staffUserInfo.getDepartment(), productCode, newIssueStatus);
            //2 更新单身
            if (success) {
                issueDao.InsertIssueProgress(issueId, issue.getCrmId(), progress(issueId, userId,productCode,issue.getServiceCode(),newIssueStatus, "", ReplyType.A.toString(), IssueProcessType.Reassignment.toString(),false , issueProgress));
                //更新总工时
                issueDetailDao.updateDetailIssueTotalWorkHours(issueId);
                //3 发送邮件通知新客服
                issue.setProductCode(productCode);
                sendReassignmentMail(userId,issue);

                return BaseResponse.ok();
            }
            return BaseResponse.error(ResponseStatus.REASSIGNMENT_FAIL);
        }
    }

    /**
     * {"handlerDetail":"<p>这种</p>","issueClassification":"T0121","workHours":1,"canTransferMainCharge":"N",
     *                 "account":["00b23f20d4584b2686738bcb5b352bd7"],"issueId":4004575,"currentStatus":"2","serviceDepartment":"BW0310","issueLevel":2}
     * @param issueId
     * @param userId
     * @param serviceId
     * @param language
     * @param canTransferMainCharge
     * @param issueProgress
     * @return
     */
    private BaseResponse reassignmentForT(long issueId, String productCode, String userId, String serviceId, String language, String canTransferMainCharge, IssueProgress issueProgress, String issueClassification, int issueLevel, Issue issue){
        Cases cases = new Cases();
        cases.setIssueId(issueId);
        cases.setProductCode(productCode);
        if(StringUtils.isEmpty(serviceId)){
            cases.setAccount(Arrays.asList(dealTService(issue,issue.getProductCode())));
        }else {
            cases.setAccount(Arrays.asList(serviceId));
        }
        cases.setCanTransferMainCharge(canTransferMainCharge);
        cases.setIssueClassification(issueClassification);
        cases.setIssueLevel(issueLevel);
        cases.setWorkHours(issueProgress.getProcessHours());
        cases.setHandlerDetail(issueProgress.getDescription());
        cases.setCurrentStatus(issue.getIssueStatus());
        return issueProcessService.transferDealer(userId, language, cases, "servicecloud");
    }

    /**
     * 分配T的客服
     * @param issue
     * @param productCode
     */
    private String dealTService(Issue issue, String productCode) {
        ServiceStaffCond cond = new ServiceStaffCond();
        IssueCasedetail issueCasedetail = issueDetailDao.selectIssueDetail(issue.getIssueId());
        cond.setCustomerServiceCode(issue.getServiceCode());
        cond.setProductCode(issue.getProductCode());
        cond.setErpSystemCode(issueCasedetail.getErpSystemCode());
        if (!StringUtils.isEmpty(issueCasedetail.getErpSystemCode())) {
            Module module = issuesMapper.getModuleByModuleCode(productCode, issueCasedetail.getErpSystemCode().toUpperCase());
            if (!ObjectUtils.isEmpty(module)) {
                cond.setArea(module.getArea());
            }else{
                cond.setArea("");
            }
        }

        List<String> windowStaffs = userServiceFeignClient.getServiceStaff(cond);

        if (!CollectionUtils.isEmpty(windowStaffs))
        {
            //取窗口人员
            String windowWorkno = windowStaffs.get(0);
            UserDetailInfo windowUser;
            try{
                windowUser = userServiceFeignClient.getUserDetailByWorkNo(windowWorkno);
                if(windowUser != null){
                    return windowUser.getUserId();
                }
            } catch (RuntimeException e) {
                log.error("调用userservice获取问题处理窗口人员信息出错, 请联系管理员." + e.toString());
            }
        }
        return "";
    }

    private StaffUserInfo getServiceStaffInfo(String serviceId,String productCode, Issue issue){
        StaffUserInfo staffUserInfo = new StaffUserInfo();
        try{
            if(!StringUtils.isEmpty(serviceId)){
                staffUserInfo = userService.GetServiceStaffInfoByUserId(serviceId);
            }else {
                String moduleCode = (issue.getIssueCasedetail() !=null && !StringUtils.isEmpty(issue.getIssueCasedetail().getErpSystemCode())) ? issue.getIssueCasedetail().getErpSystemCode() : "";
                ServiceStaffGetResponse serviceStaffGetResponse = userService.GetServiceStaffInfo(
                        issue.getUserId(), issue.getServiceCode(), productCode
                        , issue.getServiceRegion() == null ? "" : issue.getServiceRegion(), issue.getIssueDescription(),issue.getIssueClassificationNo()==null?"":issue.getIssueClassificationNo()
                        , issue.getSubmitWay()==null?"":issue.getSubmitWay(),moduleCode,issue.getUserId() == null?"":issue.getUserId()
                );

                System.out.print(serviceStaffGetResponse);
                if (serviceStaffGetResponse != null && serviceStaffGetResponse.getCode().equals("0")) {
                    staffUserInfo = serviceStaffGetResponse.getStaffUserInfo();
                    if (staffUserInfo == null) {//2021-09-15 huly 走产品线默认处理人
                        staffUserInfo = issueDao.getDefaultStaffUserInfo(issue.getProductCode());
                    }
                }
            }
        }catch (Exception ex){
            log.error(ex.toString());
        }
        //huly: 修复漏洞/bug 增加非空判断
        if (staffUserInfo != null){
            issue.setServiceStaff(staffUserInfo.getUserId());
            issue.setDepartment(staffUserInfo.getDepartment());
            issue.setServiceId(staffUserInfo.getUserId());
        }
        return staffUserInfo;
    }



    private void sendMailByISVTurn(Issue issue){
        try {
            String[] mx = {issueService.PrepareIssueMailToCCByISVTurn(issue)};
            issueService.simpleSend(MessageDestination.MAILMESSAGEDESTINATION, mx);
        } catch (Exception ex) {
            log.error(ex.toString());
        }
    }

    private void sendReassignmentMail(String userId,Issue issue){
        try {
            String[] mx = {issueService.PrepareIssueMailToNewCC(userId, issue)};
            issueService.simpleSend(MessageDestination.MAILMESSAGEDESTINATION, mx);
        } catch (Exception ex) {
            log.error(ex.toString());
        }
    }

    @Override
    @Transactional
    public BaseResponse reply(String userId, long issueId, boolean isOpenToSubmit, boolean isNoticeCustomer, long replyId, IssueDetailInfo issueDetailInfo, Issue issue){

        if(issueDetailInfo == null || CollectionUtils.isEmpty(issueDetailInfo.getIssueProgresses())){
            return BaseResponse.error(ResponseStatus.PARAM_VERIFY);
        }

        //2021-09-15 huly 状态模式
        Issue issueTemp = issueDao.SelectIssueByIssueId(String.valueOf(issueId));
        //正常回复不改状态，但是状态C的时候 回复 后 状态改成N
        String newIssueStatus = issueTemp.getIssueStatus();
        if(IssueStatus.Submited.toString().equals(issueTemp.getIssueStatus())){
            newIssueStatus = IssueStatus.Processing.toString();
        }else{
            //20250617:去掉下面逻辑，回复时，正常情况下，案件状态不变。考虑一种特殊场景，T的产中处理中是4，但是在新易聊回复后变成O了。因状态模式里O表示产中处理中。
            /*IssueContext context = stateMode(issueTemp.getIssueStatus());
            context.reply(context);
            newIssueStatus = context.getIssueStatus();
            if(StringUtils.isEmpty(newIssueStatus)){
                return BaseResponse.error(ResponseStatus.ISSUE_STATUS_CAN_NOT_REPLY);
            }*/
        }

        //1 更新单头
        boolean success = issueDao.updateIssueService(String.valueOf(issueId), "", "", "", "", "", newIssueStatus);
        // 更新单头客户最新回复, 公开给客户看 才算客服最新回复
        if(isOpenToSubmit){
            updateIssueNewReply(String.valueOf(issueId), NewReplyEnum.ServiceNewReply.toString());
        }

        //2 更新子单头
        updateIssueCasedetail(issueId, issueTemp.getProductCode(),issueTemp.getServiceCode(), newIssueStatus ,issueDetailInfo.getIssueCasedetail());
        //3 更新单身
        if (success) {
            for (IssueProgress issueProgress:issueDetailInfo.getIssueProgresses()) {
                //引用回复
                if(replyId != 0){
                    issueProgress.setReplyId(replyId);
                }
                issueProgress = progress(issueId, userId, issueTemp.getProductCode(),issueTemp.getServiceCode(),newIssueStatus,"", ReplyType.A.toString(), IssueProcessType.Process.toString(), isOpenToSubmit, issueProgress);
                long progressId = issueDao.InsertIssueProgress(issueId, issueTemp.getCrmId(), issueProgress);
                //更新总工时
                issueDetailDao.updateDetailIssueTotalWorkHours(issueId);
                //处理附件
                issueService.SaveAttachments(issueId, progressId, issue);
                //4 如果是大陆T，需要同步187、scm、workday
                if("CN".equals(connectarea) && Constants.getTProductCodes().contains(issueTemp.getProductCode())){
                    syncForT(issueId, issueProgress,newIssueStatus);
                }
                //第一次响应时，要记录时间、时长、操作人，后面报表需要
                issueProcessService.recordReply(issueId, userId,issueProgress.getProcessTime());
            }
            return BaseResponse.ok();
        }
        return BaseResponse.error(ResponseStatus.REPLY_FAIL);
    }

    @Override
    @Transactional
    public BaseResponse batchReply(String userId, List<IssueDetailInfo> issueDetailInfoList){

        for(int i = 0;i < issueDetailInfoList.size(); i++){
            IssueDetailInfo issueDetailInfo = issueDetailInfoList.get(i);
            IssueCasedetail issueCasedetail = issueDetailInfo.getIssueCasedetail();
            List<IssueProgress> issueProgresses = issueDetailInfo.getIssueProgresses();
            //更新单头产品线以及状态
            issueDao.updateIssueService(String.valueOf(issueDetailInfo.getIssueId()), "", "", getIssueSyncStatusV3(issueDetailInfo.getIssueId(), issueDetailInfo.getProductCode(),issueDetailInfo.getCustomerServiceCode(), issueDetailInfo.getState()), "", issueDetailInfo.getProductCode(), issueDetailInfo.getState());

            //更新子单头的问题类型
            if(issueCasedetail != null){
                issueDetailDao.updateIssueClassification(issueCasedetail);
            }

            //如果工时和回复内容不空，需要新增一笔回复的单身
            if(!CollectionUtils.isEmpty(issueProgresses)){
                for(int k = 0 ; k<issueProgresses.size();k++){
                    IssueProgress issueProgress = issueProgresses.get(k);
                    if(!StringUtils.isEmpty(issueProgress.getDescription()) && issueProgress.getProcessHours() != 0){
                        //记录回复的单身，处理人是userId
                        issueProgress = progress(issueDetailInfo.getIssueId(), userId, issueDetailInfo.getProductCode(),issueDetailInfo.getCustomerServiceCode(),issueDetailInfo.getState(),"", ReplyType.A.toString(), IssueProcessType.Process.toString(), true, issueProgress);
                        issueDao.InsertIssueProgress(issueDetailInfo.getIssueId(), issueDetailInfo.getCrmId(), issueProgress);
                    }
                    //如果是大陆T，需要同步187、scm、workday
                    if("CN".equals(connectarea) && Constants.getTProductCodes().contains(issueDetailInfo.getProductCode())){
                        syncForT(issueDetailInfo.getIssueId(), issueProgress,issueDetailInfo.getState());
                    }

                }
            }
            //如果结案，需要记录结案的单身,处理人是userId
            if(IssueStatus.Closed.toString().equals(issueDetailInfo.getState())){
                IssueProgress issueProgress = new IssueProgress();
                issueProgress.setDescription("Close Issue");
                if("CN".equals(issueDetailInfo.getServiceRegion())){
                    issueProgress.setDescription("结案");
                }
                Issue issue = issueDao.SelectIssueByIssueId(String.valueOf(issueDetailInfo.getIssueId()));
                issueProgress = progress(issueDetailInfo.getIssueId(), userId, issueDetailInfo.getProductCode(),issueDetailInfo.getCustomerServiceCode(),issueDetailInfo.getState(),"", ReplyType.A.toString(), IssueProcessType.Close.toString(), true, issueProgress);
                issueDao.InsertIssueProgress(issueDetailInfo.getIssueId(), issueDetailInfo.getCrmId(), issueProgress);
                //更新summary 记录结案时间
                casesProcessUtils.saveIssueSummaryV3(userId, issueDetailInfo.getIssueId(), IssueProcessType.Close.toString(), issueProgress.getProcessTime());
                //更新summary 记录提交人结案时间
                casesProcessUtils.saveIssueSummaryV3(userId, issueDetailInfo.getIssueId(), IssueProcessType.AgreeClose.toString(), issueProgress.getProcessTime());
                //更新reply_min
                issueProcessService.updateIssueReplyMin(issueDetailInfo.getIssueId(), issue.getSubmitTime(), issueProgress.getProcessTime());
                //更新end_min
                issueProcessService.updateIssueEndMin(issueDetailInfo.getIssueId(), issue.getSubmitTime(), issueProgress.getProcessTime());
                // 更新单头客服最新回复
                updateIssueNewReply(String.valueOf(issueDetailInfo.getIssueId()), NewReplyEnum.Null.toString());
                //如果是大陆T，需要同步187、scm、workday
                if("CN".equals(connectarea) && Constants.getTProductCodes().contains(issueDetailInfo.getProductCode())){
                    syncForT(issueDetailInfo.getIssueId(), issueProgress, issueDetailInfo.getState());
                }
            }else {
                //记录响应时间
                DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                issueProcessService.recordReply(issueDetailInfo.getIssueId(), userId, sdf.format(new Date())); //记录响应时间
                // 更新单头客服最新回复
                updateIssueNewReply(String.valueOf(issueDetailInfo.getIssueId()), NewReplyEnum.ServiceNewReply.toString());
            }
            //更新总工时
            issueDetailDao.updateDetailIssueTotalWorkHours(issueDetailInfo.getIssueId());
            //标签，先删 后新增
            tagService.addTagMapping(String.valueOf(issueDetailInfo.getIssueId()),"issue",CollectionUtils.isEmpty(issueDetailInfo.getTags()) ? new ArrayList<>()  : issueDetailInfo.getTags().stream().map(o->o.getTagId()).collect(Collectors.toList()));
        }

        return BaseResponse.ok();
    }

    @Override
    @Transactional
    public BaseResponse updateReply(String userId, long issueId, boolean isOpenToSubmit, boolean isNoticeCustomer, String newReply, Issue issue, List<String> deleteFiles){
        // 更新单头客户最新回复, 公开给客户看 才算客服最新回复
        if(NewReplyEnum.ServiceNewReply.toString().equals(newReply)){
            //客服修改是否公开时，需要更新单头的newReply
            // issueDao.updateIssueNewReplyForDelete(issueId);
            boolean oldIsOpenToSubmit = issueDao.getIsOpenToSubmit(issue.getIssueProgresses().get(0).getId());
            if(isOpenToSubmit != oldIsOpenToSubmit){
                if(isOpenToSubmit){
                    updateIssueNewReply(String.valueOf(issueId), NewReplyEnum.ServiceNewReply.toString());
                }else {
                    issueDao.updateIssueNewReplyForEdit(issueId,newReply);
                }
            }
        }else {
            boolean oldIsOpenToSubmit = issueDao.getIsOpenToSubmit(issue.getIssueProgresses().get(0).getId());
            if(isOpenToSubmit != oldIsOpenToSubmit){
                if(isOpenToSubmit){
                    updateIssueNewReply(String.valueOf(issueId), NewReplyEnum.CustomerNewReply.toString());
                }else {
                    issueDao.updateIssueNewReplyForEdit(issueId,newReply);
                }
            }
        }
        //1 更新单头
        Issue issueTemp = issueDao.SelectIssueByIssueId(String.valueOf(issueId));
        issueDao.updateIssueService(String.valueOf(issueId), "", "", getIssueSyncStatusV3(issueId, issueTemp.getProductCode(),issueTemp.getServiceCode(), issueTemp.getIssueStatus()), "", "", "");

        //2 更新子单头
        updateIssueCasedetail(issueId, issueTemp.getProductCode(),issueTemp.getServiceCode(), issueTemp.getIssueStatus() ,issue.getIssueCasedetail());
        //3 更新单身
        for (IssueProgress issueProgress:issue.getIssueProgresses()) {
            DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            issueProgress.setProcessTime(sdf.format(new Date()));
            issueProgress.setOpenToSubmit(isOpenToSubmit);
            issueProgress.setSyncStatus(getIssueProcessSyncStatusV3(issueTemp.getProductCode(),issueTemp.getServiceCode(), issueTemp.getIssueStatus()));
            issueDao.UpdateIssueProgressById(issueProgress);
            //更新总工时
            issueDetailDao.updateDetailIssueTotalWorkHours(issueId);
            //新增附件
            issueService.SaveAttachments(issueId,issueProgress.getId(), issue);

        }

        //删除附件
        dealFile(deleteFiles);
        return BaseResponse.ok();
    }

    /**
     * 编辑回复时，会删除附件，
     * @param fdFiles
     */
    private void dealFile(List<String> fdFiles){
        try {
            if (fdFiles != null && fdFiles.size() > 0) {
                for (int i = 0; i < fdFiles.size(); i++) {
                    issueService.deleteIssueAttachmentFileV3(fdFiles.get(i));
                }
            }

        }catch (Exception e){
            log.error(e.toString());
        }
    }
    private void updateIssueCasedetail(long issueId, String productCode, String serviceCode, String issueStatus,IssueCasedetail issueCasedetail){
        IssueCasedetail dbData = issueDetailDao.selectIssueDetail(issueId);

        issueCasedetail.setSyncStatus(getIssueProcessSyncStatusV3(productCode,serviceCode,issueStatus));
        if (dbData != null) {
            //当回复时，问题类型选中T0161时，需要更新客制bug负责人类型和客制bug负责人
            if(!Constants.getBugCodes().contains(issueCasedetail.getIssueClassification())){
                issueCasedetail.setBugOwner(dbData.getBugOwner());
                issueCasedetail.setBugOwnerType(dbData.getBugOwner());
            }
            issueDetailDao.updateDetailIssue(issueCasedetail);
        } else {
            issueDetailDao.insertDetailIssue(issueCasedetail);
        }
    }

    /**
     * 构造一笔单身
     * @param issueId
     * @param userId
     * @return
     */
    public IssueProgress progress(long issueId,String userId, String productCode,String serviceCode,String issueStatus, String workNo, String replyType, String issueProcessType, boolean isOpenToSubmit, IssueProgress issueProgress){
        issueProgress.setIssueId(issueId);
        DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        issueProgress.setProcessTime(sdf.format(new Date()));
        issueProgress.setReplyType(replyType);
        issueProgress.setProcessor(userId);
        if(StringUtils.isEmpty(workNo)){
            StaffUserInfo staffUserInfo = userService.GetServiceStaffInfoByUserId(userId);
            if(staffUserInfo != null){
                issueProgress.setWorkno(staffUserInfo.getWorkNo());
            }
        }else {
            issueProgress.setWorkno(workNo);
        }
        issueProgress.setSequeceNum(issueDao.getMaxOrder(issueId));
        issueProgress.setProcessType(issueProcessType);
        issueProgress.setOpenToSubmit(isOpenToSubmit);

        issueProgress.setSyncStatus(getIssueProcessSyncStatusV3(productCode,serviceCode, issueStatus));
        return issueProgress;
    }

    /**
     * 问题分类在调转产中前已经调另一个api更新了，这里就不更新问题分类。但T的转产中需要问题分类，这里就增加问题分类的参数。
     * @param userId
     * @param issueId
     * @param productCode
     * @param productIssueClassification
     * @param language
     * @param issueProgress
     * @param issueClassification
     * @param canTransferMainCharge
     * @return
     */
    @Override
    public BaseResponse turnToProduct(String userId, long issueId, String productCode, String productIssueClassification, String language, IssueProgress issueProgress, String issueClassification, String canTransferMainCharge){
        try{
            //2021-09-15 huly 状态模式
            Issue issueTemp = issueDao.SelectIssueByIssueId(String.valueOf(issueId));
            IssueContext context = stateMode(issueTemp.getIssueStatus());
            context.turnToProduct(context);
            String newIssueStatus = context.getIssueStatus();
            if(StringUtils.isEmpty(newIssueStatus)){
                return BaseResponse.error(ResponseStatus.ISSUE_STATUS_CAN_NOT_TURN_TO_PRODUCT);
            }

            if("CN".equals(connectarea) && Constants.getTProductCodes().contains(productCode)){
                return turnToProductForT(userId,"CN".equals(language) ? "zh-CN" : "zh-TW",issueId, issueClassification,canTransferMainCharge,issueProgress);
            }else{
                if(StringUtils.isEmpty(productIssueClassification)){
                    return BaseResponse.error(ResponseStatus.PARAM_VERIFY);
                }

                //校验模组/应用
                IssueCasedetail issueCasedetail = issueDao.SelectIssueCasedetail(String.valueOf(issueId));
                if(issueCasedetail ==null || StringUtils.isEmpty(issueCasedetail.getErpSystemCode())){
                    return BaseResponse.error(ResponseStatus.ERP_SYSTEMCODE_IS_NULL);
                }
                if(issueService.checkErpSystemCode(productCode, issueCasedetail.getErpSystemCode()) == 0){
                    return BaseResponse.error(ResponseStatus.ERP_SYSTEMCODE_IS_WRONG);
                }
                // 找出客代下之特殊註記
                //CustomerSpecialRemarkInfo customerSpecialRemarkInfo =issueDao.getCustomerSpecialRemarkInfo(issueTemp.getServiceCode());
                // 找出該客代該產品線之產品註記
                List<CustomerProductRemarkInfo> customerProductRemarkInfoList = issueDao.getCustomerProductRemarkInfo(issueTemp.getServiceCode(),issueTemp.getProductCode());
                String customerProductRemarkInfo ="";
                if (customerProductRemarkInfoList != null) {
                    //html語言為為了於禪道中有換行的功能而使用
                    customerProductRemarkInfo = customerProductRemarkInfoList.stream()
                            .map(n -> String.valueOf("<p>"+n.getDate())+" "+String.valueOf(n.getFullname())+"</p><p>"+String.valueOf(n.getRemark())+"</p>")
                            .collect(Collectors.joining("", "",""));
                }
                //转产中，形成禅道单
                HashMap map = new HashMap();
                String productId = "";
                if(ProductIssueClassification.bug.toString().equals(productIssueClassification)){
                    map = productService.createBug(issueService.bug(issueId , issueTemp.getCrmId(), userId, issueProgress, issueTemp,customerProductRemarkInfo));
                }else {
                    // taskID=11941 判斷非T產品, 為Athena產品線,轉產中選擇轉功能建議需求單時 加傳verify參數
                    if(!Constants.getTProductCodes().contains(productCode) && StringUtils.isEmpty(issueProgress.getVerify())){
                        return BaseResponse.error(ResponseStatus.WRONG_PARAMETER, "verify不能为空");
                    }
                    map = productService.createStory(issueService.story(issueId ,issueTemp.getCrmId(),userId, issueProgress, issueTemp,customerProductRemarkInfo));
                }
                if(map == null || map.get("status") == null ){
                    BaseResponse baseResponse = new BaseResponse();
                    baseResponse.setStatusDescription(ResponseStatus.TURN_TO_PRODUCT_FAIL.getMessage());
                    baseResponse.setStatus(ResponseStatus.TURN_TO_PRODUCT_FAIL.getCode());
                    baseResponse.setResponse(ResponseStatus.TURN_TO_PRODUCT_FAIL.getMessage());
                    return baseResponse;
                }
                if(!"1".equals(map.get("status").toString())){
                    BaseResponse baseResponse = new BaseResponse();
                    baseResponse.setStatus(ResponseStatus.TURN_TO_PRODUCT_FAIL.getCode());
                    baseResponse.setStatusDescription(ResponseStatus.TURN_TO_PRODUCT_FAIL.getMessage());
                    baseResponse.setResponse(ResponseStatus.TURN_TO_PRODUCT_FAIL.getMessage());
                    if(map.get("result") != null){
                        baseResponse.setStatusDescription(map.get("result") != null ? map.get("result").toString() : ResponseStatus.TURN_TO_PRODUCT_FAIL.getMessage());
                        baseResponse.setResponse(map.get("result") != null ? map.get("result").toString() : ResponseStatus.TURN_TO_PRODUCT_FAIL.getMessage());
                    }

                    return baseResponse;
                }

                if("1".equals(map.get("status").toString())){
                    if(map.get("id") == null || "".equals(map.get("id"))){
                        BaseResponse baseResponse = new BaseResponse();
                        baseResponse.setStatus(ResponseStatus.TURN_TO_PRODUCT_FAIL.getCode());
                        baseResponse.setStatusDescription("产中回写的单号为空");
                        baseResponse.setResponse("产中回写的单号为空");
                        if("TW".equals(language)){
                            baseResponse.setStatusDescription("產中回寫的單號為空");
                            baseResponse.setResponse("產中回寫的單號為空");
                        }

                        return baseResponse;
                    }
                    productId = map.get("id").toString();
                    if(map.get("itCode") != null){
                        StaffUserInfo staffUserInfo = userService.GetServiceStaffInfo(map.get("itCode").toString());
                        if(staffUserInfo != null && !StringUtils.isEmpty(staffUserInfo.getFullName())){
                            if("CN".equals(language)){
                                issueProgress.setDescription(issueProgress.getDescription() + "（转给产中：" +staffUserInfo.getFullName() + "）");
                            }else {
                                issueProgress.setDescription(issueProgress.getDescription() + "（轉給產中：" +staffUserInfo.getFullName() + "）");
                            }
                        }
                    }
                }

                //2 更新单头
                boolean success = issueDao.updateIssueService(String.valueOf(issueId), "", "", "", "", productCode, newIssueStatus);
                if(success){

                    //3 更新单身
                    issueProgress = progress(issueId, userId, productCode, issueTemp.getServiceCode(), newIssueStatus,"", ReplyType.A.toString(), IssueProcessType.TurnToProduct.toString(),true , issueProgress);

                    //单身拼接转产中的信息
                    if("CN".equals(language)){
                        String type = ProductIssueClassification.bug.toString().equals(productIssueClassification) ? "Bug单" : "功能建议需求单";
                        issueProgress.setDescription(issueProgress.getDescription() + "  (产中案件编号：" + productId + "     产中问题类型：" + type + ")");
                    }else {
                        String type = ProductIssueClassification.bug.toString().equals(productIssueClassification) ? "Bug單" : "功能建議需求單";
                        issueProgress.setDescription(issueProgress.getDescription() + "  (產中案件編號：" + productId + "     產中問題類型：" + type + ")");
                    }

                    issueDao.InsertIssueProgress(issueId, issueTemp.getCrmId(), issueProgress);
                    //4 单号回写服务云
                    issueDao.saveIssueSourceMap(issueId, issueTemp.getCrmId(), productId, IssueSourceType.PRODUCT.toString(), productIssueClassification, productCode, issueCasedetail.getErpSystemCode());
                    //5 记录转产中时间
                    casesProcessUtils.saveIssueSummaryV3(userId, issueId, "TurnToProduct", issueProgress.getProcessTime());
                    return BaseResponse.ok(productId);
                }
            }

        }catch (Exception ex){
            ex.printStackTrace();
            BaseResponse baseResponse = new BaseResponse();
            baseResponse.setStatusDescription(ex.toString());
            baseResponse.setStatus(ResponseStatus.TURN_TO_PRODUCT_FAIL.getCode());
            baseResponse.setResponse(ex.toString());
            return baseResponse;
        }
        return BaseResponse.error(ResponseStatus.TURN_TO_PRODUCT_FAIL);
    }

    /**
     * {"handlerDetail":"<p>测试转产中</p>","issueId":4004582,"currentStatus":"2","issueClassification":"T0161","workHours":3,"canTransferMainCharge":"N"}
     * @param userId
     * @param language
     * @return
     */
    public BaseResponse turnToProductForT(String userId, String language,long issueId, String issueClassification,String canTransferMainCharge,IssueProgress issueProgress){
        T100CaseReqP issue = new T100CaseReqP();
        issue.setIssueId(issueId);
        issue.setCanTransferMainCharge(canTransferMainCharge);
        issue.setIssueClassification(issueClassification);
        issue.setWorkHours(issueProgress.getProcessHours());
        issue.setHandlerDetail(issueProgress.getDescription());
        //获取登陆人信息
        UserDetailInfo user;
        try {
            user = userServiceFeignClient.findUserDetailById(userId);
        } catch (RuntimeException e) {
            log.error("调用userservice获取用户信息失败:", e);
            return BaseResponse.error(ResponseStatus.INTERNAL_SERVER_ERROR, "获取当前登陆用户信息失败.");
        }
        //判定是首次转产中还是
        if(Boolean.FALSE.equals(issueDetailDao.checkTurnToProductForT(issueId))){ //修复bug 调整equals值比较
            return issueProcessService.toT100(userId, language, user.getJiaofuType() == JiaoFuUserType.JIAOFUFUWU.getValue() ? JiaoFuUserType.JIAOFUFUWU : JiaoFuUserType.JIAOFUGUWEN, issue,
                    CaseProcessType.TURN_TO_T100, CaseStatus.PC_HANDLING, ToT100Flag.TO_T100,"servicecloud");
        }else{
            return issueProcessService.toT100(userId, language, user.getJiaofuType() == JiaoFuUserType.JIAOFUFUWU.getValue() ? JiaoFuUserType.JIAOFUFUWU : JiaoFuUserType.JIAOFUGUWEN, issue,
                    CaseProcessType.TURN_TO_T100, CaseStatus.PC_HANDLING, ToT100Flag.RETURN_TO_T100, "servicecloud" );
        }
    }
    @Override
    public BaseResponse turnToISV(String userId, long issueId, String serviceId, String description, IssueCasedetail issueCasedetail){
        try{
            Issue issue = issueDao.SelectIssueByIssueId(String.valueOf(issueId));
            if(issueCasedetail == null || StringUtils.isEmpty(issueCasedetail.getErpSystemCode())){
                return BaseResponse.error(ResponseStatus.ERP_SYSTEMCODE_IS_NULL);
            }
            issue.setIssueCasedetail(issueCasedetail);
            String erpSystemCode = issueCasedetail.getErpSystemCode();
            IssueContext context = stateMode(issue.getIssueStatus());
            context.turnToISV(context);
            String newIssueStatus = context.getIssueStatus();
            if(StringUtils.isEmpty(newIssueStatus)){
                return BaseResponse.error(ResponseStatus.ISSUE_STATUS_CAN_NOT_TURN_TO_ISV);
            }
            String servicerId = "";//ISV租户id
            Long sid = 0L;
            try{
                Map<String, Object> itemDetail = gmcService.getItemDetail(erpSystemCode);
                if (ObjectUtils.isEmpty(itemDetail)) {
                    log.info("getItemDetail is null" + erpSystemCode);
                    issueService.saveISVLog(issue.getIssueId(),issue.getUserId(),"getItemDetail is null:" + erpSystemCode);
                    return BaseResponse.error(ResponseStatus.IAM_NOT_HAVE_SUPPLIER_BY_ITEMCODE);
                }
                if(!ObjectUtils.isEmpty(itemDetail.get("servicerId"))){
                    servicerId = itemDetail.get("servicerId").toString();
                }

            }catch (Exception e)
            {
                issueService.saveISVLog(issue.getIssueId(),issue.getUserId(),"turnToISV:getItemDetail exception");
                return BaseResponse.error(ResponseStatus.IAM_NOT_HAVE_SUPPLIER_BY_ITEMCODE);
            }

            if(org.apache.commons.lang.StringUtils.isEmpty(servicerId)){
                issueService.saveISVLog(issue.getIssueId(),issue.getUserId(),"turnToISV:servicerId is null");
                return BaseResponse.error(ResponseStatus.IAM_NOT_HAVE_SUPPLIER_BY_ITEMCODE);
            }
            sid = issueDao.getSid(servicerId);
            if(sid == null){
                issueService.saveISVLog(issue.getIssueId(),issue.getUserId(),"turnToISV:sid is null");
                return BaseResponse.error(ResponseStatus.ISV_TENANT_NOT_EXISTS_IN_SERVICECLOUD);
            }
            if(sid != 0L) {
                //转案件到ISV
                BaseResponse response = createISVIssue(issue, sid, description,erpSystemCode,userId);
                if(response.getStatus() != ResponseStatus.OK.getCode()){
                    return response;
                }
                String aioIssueCode = (String)response.getResponse();
                log.info("aioIssueCode:" + aioIssueCode);
                //更新服务云案件
                updateDigiwinIssue(issueId, aioIssueCode, issue, serviceId,userId);
                return BaseResponse.ok();
            }else {
                log.info("ISV tenant not exist in servicecloud");
                issueService.saveISVLog(issueId,userId,"ISV tenant not exist in servicecloud:sid=" + sid);
                return BaseResponse.error(ResponseStatus.ISV_TENANT_NOT_EXISTS_IN_SERVICECLOUD);
            }

        }catch (Exception ex){
            ex.printStackTrace();
            BaseResponse baseResponse = new BaseResponse();
            baseResponse.setStatusDescription(ex.toString());
            baseResponse.setStatus(ResponseStatus.TURN_TO_ISV_FAIL.getCode());
            baseResponse.setResponse(ex.toString());
            return baseResponse;
        }
    }


    /**
     * 鼎捷邮件，单头状态是VN(ISV处理中)，单身操作类型是TurnToISV，记录issue_source_map
     * @param aioIssueCode
     */
    public void updateDigiwinIssue(long issueId,String aioIssueCode,Issue issue,String serviceId,String userId){
        if(StringUtils.isEmpty(aioIssueCode)){
            issueService.saveISVLog(issueId,userId,"aioIssueCode is null");
            return;
        }
        issueDao.updateIssueForISV(issueId, IssueStatus.ISVProcessing.toString(),serviceId);
        saveISVIssueProgress(issue,userId);
        issueService.saveIssueSourceMap(issueId,issue.getCrmId(),aioIssueCode);
    }

    public void saveISVIssueProgress(Issue issue,String userId) {
        try {
            IssueProgress progress = new IssueProgress();
            String date = issue.getSubmitTime();

            progress.setProcessTime(date);
            String syncStatus = getIssueProcessSyncStatusV3(issue.getProductCode(), issue.getServiceCode(), issue.getIssueStatus());
            if(connectarea.equals("TW")){
                syncStatus = syncStatus.equals(SyncStatus.DontNeedSync.toString()) ? syncStatus : SyncStatus.ProgressUnSync.toString();
            }
            progress.setSyncStatus(syncStatus);
            progress.setProcessType(IssueProcessType.TurnToISV.toString());
            progress.setReplyType(ReplyType.A.toString());
            progress.setProcessor(userId);//操作人是客服，当前操作用户
            progress.setSequeceNum(issueDao.getMaxOrder(issue.getIssueId()));
            progress.setCurrentStatus(IssueStatus.ISVProcessing.toString());
            progress.setOpenToSubmit(false);
            if("CN".equals(issue.getServiceRegion())){
                progress.setDescription("转ISV");
            }else {
                progress.setDescription("轉ISV");
            }

            issueDao.InsertIssueProgress(issue.getIssueId(), issue.getCrmId(), progress);
        } catch (Exception ex) {
            log.info("saveISVIssueProgress error:"+ex.toString());
        }
    }
    public BaseResponse createISVIssue(Issue issue,Long sid, String description,String erpSystemCode,String userId){
        String aioIssueCode = "";//ISV案件的issueCode
        try {
            String flag = issueDao.getFlag(sid);
            log.info("flag:"+flag);
            String serviceCode = (flag + issue.getServiceCode()).trim();
            log.info("serviceCode:"+serviceCode);
            Long eid = issueDao.getEid(serviceCode,sid);
            log.info("eid:"+eid);
            if(eid == null){
                issueService.saveISVLog(issue.getIssueId(),userId,"eid is null");
                return BaseResponse.error(ResponseStatus.TURN_TO_ISV_FAIL);
            }
            long userSid = 0L;
            if(sid != null){
                try{
                    String tenantId = issueDao.getTenantIdByServiceCode(sid, serviceCode);
                    if (org.springframework.util.StringUtils.isEmpty(tenantId)) {
                        issueService.saveISVLog(issue.getIssueId(),issue.getUserId(),"tenantId not exist");
                        return BaseResponse.error(ResponseStatus.TURN_TO_ISV_FAIL);
                    }
                    IamAuthoredUser iamAuthoredUser = kcfService.getIamAuthoredUser(issue.getUserId(),tenantId);
                    userSid = iamAuthoredUser.getSid();
                }catch (Exception e){
                    issueService.saveISVLog(issue.getIssueId(),issue.getUserId(),"获取iam用户信息异常");
                    return BaseResponse.error(ResponseStatus.TURN_TO_ISV_FAIL);
                }

                String workNo = "";
                String workName = "";
                UserDetailInfo detail = userServiceFeignClient.findUserDetailById(issue.getServiceId());
                if(!ObjectUtils.isEmpty(detail)){
                    workNo = detail.getWorkno();
                    workName = detail.getName();
                }

                com.digiwin.escloud.aioissue.model.Issue aioIssue = new com.digiwin.escloud.aioissue.model.Issue();
                aioIssue.setSid(sid);
                aioIssue.setEid(eid);
                aioIssue.setServiceCode(serviceCode);
                aioIssue.setProductCode(erpSystemCode);
                if("CN".equals(connectarea)){
                    aioIssue.setIssueDescription("【" + workName + "转单：】" + description);
                }else{
                    aioIssue.setIssueDescription("【" + workName + "轉單：】" + description);
                }

                aioIssue.setSubmitWay(com.digiwin.escloud.issueservice.t.model.cases.constants.SubmitWay.DIGIWIN.toString());
                aioIssue.setIssueSubmitMode(SubmitWay.DIGIWIN.toString());
                aioIssue.setServiceRegion(issue.getServiceRegion());
                aioIssue.setMachineRegion(issue.getMachineRegion());
                aioIssue.setIssueType(IssueType.Issue.toString());
                aioIssue.setUserSid(userSid);
                aioIssue.setWarningItemCollection(Arrays.asList(issue.getCrmId()));
                aioIssue.setSourceType(IssueSourceType.ISV.toString());
                UserContact userContact = issue.getUserContact() ;
                if(userContact != null){
                    com.digiwin.escloud.aiouser.model.usercontact.UserContact aioUserContact = new com.digiwin.escloud.aiouser.model.usercontact.UserContact();
                    aioUserContact.setUserId(userSid);
                    aioUserContact.setEmail(userContact.getEmail());
                    aioUserContact.setName(userContact.getName());
                    aioUserContact.setPhone(userContact.getPhone01());
                    aioUserContact.setServiceCode(serviceCode);
                    aioIssue.setUserContact(aioUserContact);
                }

                com.digiwin.escloud.common.response.BaseResponse<String> response = aioIssueClient.submitIssueForISV(JSON.toJSONString(aioIssue),null,issue.getCrmId(),workNo,workName,description,sid,eid);
                if(ResponseCode.SUCCESS.getCode().equals(response.getCode())){
                    aioIssueCode = response.getData();
                }else{
                    issueService.saveISVLog(issue.getIssueId(),issue.getUserId(),"aioIssueClient.submitIssue 失败");
                    return BaseResponse.error(ResponseStatus.TURN_TO_ISV_FAIL);
                }
            }
        }
        catch (Exception ex) {
            issueService.saveISVLog(issue.getIssueId(),userId,"turnToISV error:" + ex.toString());
            log.info("turnToISV error:"+ex.toString());
            return BaseResponse.error(ResponseStatus.TURN_TO_ISV_FAIL);
        }
        return BaseResponse.ok(aioIssueCode);
    }

    @Override
    public BaseResponse productHandleToServiceCloud(ProductIssue productIssue){
        if(productIssue == null || StringUtils.isEmpty(productIssue.getItcode())
                || StringUtils.isEmpty(productIssue.getIssueId())
                || StringUtils.isEmpty(productIssue.getProductNumber())){
            return BaseResponse.error(ResponseStatus.PARAM_VERIFY);
        }
        Long issueId = issueDao.getIssueIdByCrmId(productIssue.getIssueId());
        if(issueId == null){
            return BaseResponse.error(ResponseStatus.ISSUEID_IS_WRONG);
        }

        Issue issue = issueDao.SelectIssueByIssueId(String.valueOf(issueId));
        //如果是由禅道bug转需求 服务云需要记录新的需求单号
        if(productIssue.getChange() && !StringUtils.isEmpty(productIssue.getType())){
            issueDao.saveIssueSourceMap(issueId, productIssue.getIssueId(), productIssue.getProductNumber(), IssueSourceType.PRODUCT.toString(), productIssue.getType(), issue.getProductCode(), productIssue.getErpSystemCode());
        }

        // 更新子單頭1.需求評審結果 2.需求處理回覆 3.需求預計完成日 當不為空時才處理
        if(!Objects.isNull(productIssue.getRequ_assessmentResult()) || !Objects.isNull(productIssue.getRequ_plannedCompletionDate()) || !Objects.isNull(productIssue.getRequ_processReply())){
            // 表示至少有一個不回空，則要對其做處理
            String assUser = null;
            if(!Objects.isNull(productIssue.getRequ_assessmentResult()) && !StringUtils.isEmpty(productIssue.getItcode())){
                assUser= issueDetailDao.getUserWorkNo(productIssue.getItcode());
                if(StringUtils.isEmpty(assUser)) {
                    assUser = productIssue.getItcode();
                }
            }
            issueDao.UpdateProductRequestReplyInfo(issueId, productIssue.getRequ_assessmentResult(), assUser,productIssue.getRequ_plannedCompletionDate(), productIssue.getRequ_processReply());
            StaffUserInfo staffUserInfo = userService.GetServiceStaffInfo(productIssue.getItcode());
            issueDetailDao.saveIssueRequRecord(issueId,!Objects.isNull(staffUserInfo.getUserId())?staffUserInfo.getUserId():productIssue.getItcode(),productIssue.getRequ_assessmentResult(),assUser, productIssue.getRequ_plannedCompletionDate(), productIssue.getRequ_processReply(),"ProductCenter");
        }
        //1 更新单身
        StaffUserInfo staffUserInfo = userService.GetServiceStaffInfo(productIssue.getItcode());
        IssueProgress issueProgress = progress(issueId, staffUserInfo.getUserId(),issue.getProductCode(),issue.getServiceCode(),issue.getIssueStatus(), staffUserInfo.getWorkNo(), ReplyType.A.toString(), IssueProcessType.ProductProcess.toString(),true , new IssueProgress());
        issueProgress.setDescription(productIssue.getDescription());
        issueDao.InsertIssueProgress(issueId, issue.getCrmId(), issueProgress);

        return BaseResponse.ok();
    }

    @Override
    public BaseResponse productClose(ProductIssue productIssue){
        log.info("禅道传过来的入参：" + JSON.toJSONString(productIssue));
        if(productIssue == null || StringUtils.isEmpty(productIssue.getItcode())
                || StringUtils.isEmpty(productIssue.getIssueId())
                || StringUtils.isEmpty(productIssue.getProductNumber())){
            return BaseResponse.error(ResponseStatus.PARAM_VERIFY);
        }
        Long issueId = issueDao.getIssueIdByCrmId(productIssue.getIssueId());
        if(issueId == null){
            return BaseResponse.error(ResponseStatus.ISSUEID_IS_WRONG);
        }
        //2021-09-15 huly 状态模式
        Issue issue = issueDao.SelectIssueByIssueId(String.valueOf(issueId));
        IssueContext context = stateMode(issue.getIssueStatus());
        context.productClose(context, issue.getProductCode());
        String newIssueStatus = context.getIssueStatus();
        if(StringUtils.isEmpty(newIssueStatus)){
            return BaseResponse.error(ResponseStatus.ISSUE_STATUS_CAN_NOT_PRODUCT_CLOSE);
        }
        // 更新子單頭1.需求評審結果 2.需求處理回覆 3.需求預計完成日 當不為空時才處理
        if(!Objects.isNull(productIssue.getRequ_assessmentResult()) || !Objects.isNull(productIssue.getRequ_plannedCompletionDate()) || !Objects.isNull(productIssue.getRequ_processReply())){
            // 表示至少有一個不回空，則要對其做處理
            String assUser = null;
            if(!Objects.isNull(productIssue.getRequ_assessmentResult()) && !StringUtils.isEmpty(productIssue.getItcode())){
                assUser= issueDetailDao.getUserWorkNo(productIssue.getItcode());
                if(StringUtils.isEmpty(assUser)) {
                    assUser = productIssue.getItcode();
                }
            }
            issueDao.UpdateProductRequestReplyInfo(issueId, productIssue.getRequ_assessmentResult(),assUser, productIssue.getRequ_plannedCompletionDate(), productIssue.getRequ_processReply());
            StaffUserInfo staffUserInfo = userService.GetServiceStaffInfo(productIssue.getItcode());
            issueDetailDao.saveIssueRequRecord(issueId,!Objects.isNull(staffUserInfo.getUserId())?staffUserInfo.getUserId():productIssue.getItcode(),productIssue.getRequ_assessmentResult(),assUser, productIssue.getRequ_plannedCompletionDate(), productIssue.getRequ_processReply(),"ProductCenter");
        }
        //1 更新单头 如果服务云已经结案，就不要更新单头状态
        boolean success = true;
        if(!IssueStatus.Closed.toString().equals(issue.getIssueStatus())){
            success = issueDao.updateIssueService(String.valueOf(issueId), "", "","", "", "", newIssueStatus);
        }

        //2 更新单身
        if(success){
            StaffUserInfo staffUserInfo = userService.GetServiceStaffInfo(productIssue.getItcode());
            IssueProgress issueProgress = progress(issueId, staffUserInfo.getUserId(),issue.getProductCode(),issue.getServiceCode(),newIssueStatus, staffUserInfo.getWorkNo(), ReplyType.A.toString(), IssueProcessType.ProductClose.toString(),true , new IssueProgress());
            issueProgress.setDescription(productIssue.getDescription());
            issueDao.InsertIssueProgress(issueId, issue.getCrmId(), issueProgress);

            //记录产中结案时间
            casesProcessUtils.saveIssueSummaryV3(staffUserInfo.getUserId(), issueId, "ProductClose", issueProgress.getProcessTime());
            return BaseResponse.ok();
        }
        return BaseResponse.error(ResponseStatus.PRODUCT_CLOSE_FAIL);
    }
    @Override
    public
    BaseResponse getLatestProductInfo(long issueId, String sourceType){
        List<Map<String,String>> list = issueDetailDao.getLatestProduct(issueId, sourceType);
        if(!CollectionUtils.isEmpty(list)){
            return BaseResponse.ok(list.get(0));
        }
        return BaseResponse.ok();
    }

    @Override
    @Transactional
    public BaseResponse serviceCheckPass(String userId, long issueId, IssueProgress issueProgress){

        try{
            //2021-09-15 huly 状态模式
            Issue issue = issueDao.SelectIssueByIssueId(String.valueOf(issueId));
            IssueContext context = stateMode(issue.getIssueStatus());
            context.serviceCheckPass(context,issue.getProductCode());
            String newIssueStatus = context.getIssueStatus();
            if(StringUtils.isEmpty(newIssueStatus)){
                return BaseResponse.error(ResponseStatus.ISSUE_STATUS_CAN_NOT_SERVICE_CHECK_PASS);
            }
            // 先更新禅道状态
            List<Map<String, String>> list = productUpdateDTO(issueId, IssueSourceType.PRODUCT.toString());
            if(!CollectionUtils.isEmpty(list)){
                list.stream().forEach(o->o.put("status",ProductStatus.serviceVerify.toString()));
                productService.updateStatus(list);
            }

            //1 更新单头
            boolean success = issueDao.updateIssueService(String.valueOf(issueId), "", "","", "", "", newIssueStatus);
            //2 更新单身
            if (success) {
                issueProgress = progress(issueId, userId, issue.getProductCode(),issue.getServiceCode(),newIssueStatus,"", ReplyType.A.toString(), IssueProcessType.AgreeProductClose.toString(),false , issueProgress);
                issueDao.InsertIssueProgress(issueId, issue.getCrmId(), issueProgress);

                //记录服务验证通过时间
                casesProcessUtils.saveIssueSummaryV3(userId, issueId, IssueProcessType.AgreeProductClose.toString(), issueProgress.getProcessTime());

            }
        }catch (Exception ex){
            log.error(ex.toString());
            BaseResponse baseResponse = new BaseResponse();
            baseResponse.setStatus(ResponseStatus.SERVICE_CHECK_PASS_FAIL.getCode());
            baseResponse.setStatusDescription(ex.toString());
            baseResponse.setResponse(ex.toString());
            return baseResponse;
        }
        return BaseResponse.ok();
    }
    /**
     * 构造更新禅单据的dto
     * @param issueId
     * @return
     */
    public List<Map<String, String>> productUpdateDTO(long issueId, String issueSourceType){
        return issueDetailDao.getLatestProduct(issueId, issueSourceType);
    }

    @Override
    @Transactional
    public BaseResponse serviceCheckNotPass(String userId, long issueId, IssueProgress issueProgress){
        try{
            if(StringUtils.isEmpty(issueProgress.getDescription())){
                return BaseResponse.error(ResponseStatus.PARAM_VERIFY);
            }

            //2021-09-15 huly 状态模式
            Issue issue = issueDao.SelectIssueByIssueId(String.valueOf(issueId));
            IssueContext context = stateMode(issue.getIssueStatus());
            context.serviceCheckNotPass(context);
            String newIssueStatus = context.getIssueStatus();
            if(StringUtils.isEmpty(newIssueStatus)){
                return BaseResponse.error(ResponseStatus.ISSUE_STATUS_CAN_NOT_SERVICE_CHECK_NOT_PASS);
            }

            // 先更新禅道状态
            List<Map<String, String>> list = productUpdateDTO(issueId, IssueSourceType.PRODUCT.toString());
            if(!CollectionUtils.isEmpty(list)){
                list.stream().forEach(o->{o.put("status",ProductStatus.active.toString());o.put("comment",StringUtils.isEmpty(issueProgress.getDescription()) ? "" : issueProgress.getDescription());});
                productService.updateStatus(list);

            }

            //1 更新单头
            boolean success = issueDao.updateIssueService(String.valueOf(issueId), "", "","", "", "", newIssueStatus);

            //2 更新单身
            if (success) {
                issueDao.InsertIssueProgress(issueId, issue.getCrmId(), progress(issueId, userId, issue.getProductCode(),issue.getServiceCode(),newIssueStatus,"", ReplyType.A.toString(), IssueProcessType.DisAgreeProductClose.toString(),false , issueProgress));

            }
        }catch (Exception ex){
            log.error(ex.toString());
            BaseResponse baseResponse = new BaseResponse();
            baseResponse.setStatus(ResponseStatus.SERVICE_CHECK_NOT_PASS_FAIL.getCode());
            baseResponse.setStatusDescription(ex.toString());
            baseResponse.setResponse(ex.toString());
            return baseResponse;
        }
        return BaseResponse.ok();

    }

    @Override
    @Transactional
    public BaseResponse serviceClose(String userId, long issueId, boolean isNoticeCustomer, IssueProgress issueProgress){

        //2021-09-15 huly 状态模式
        Issue issue = issueDao.SelectIssueByIssueId(String.valueOf(issueId));
        IssueContext context = stateMode(issue.getIssueStatus());
        context.serviceClose(context,issue.getProductCode());
        String newIssueStatus = context.getIssueStatus();
        if(StringUtils.isEmpty(newIssueStatus)){
            return BaseResponse.error(ResponseStatus.ISSUE_STATUS_CAN_NOT_SERVICE_CLOSE);
        }

        if("CN".equals(connectarea) && Constants.getTProductCodes().contains(issue.getProductCode())){
            return serviceCloseForT(userId,issueId,isNoticeCustomer,issue.getIssueStatus(),issueProgress);
        }else {
            //如果案件提交人是自己 并且走提交人验证，这时候案件状态到已结案
            if(IssueStatus.SubmiterCheck.toString().equals(newIssueStatus) && userId.equals(issue.getUserId())){
                newIssueStatus = IssueStatus.Closed.toString();
            }

            //當使用者在新版易聊結案時,系統判斷立案人與結案人不同 and 案件的產品線代號是161 and 立案人是鼎捷人員時, 案件狀態改為R (提交人验证中)
            if("161".equals(issue.getProductCode()) && !userId.equals(issue.getUserId()) && IssueStatus.Closed.toString().equals(newIssueStatus)){
                UserDetailInfo detail = userServiceFeignClient.findUserDetailById(issue.getUserId());
                //判断登录用户是否为内部用户
                if(Integer.parseInt(detail.getUserType()) == UserType.INNER.getValue()) {
                    newIssueStatus = IssueStatus.SubmiterCheck.toString();
                }
            }

            //如果结案，需要清除最新回复等
            if(IssueStatus.Closed.toString().equals(newIssueStatus)){
                // 更新单头客户最新回复, 清空最新回复
                updateIssueNewReply(String.valueOf(issueId), NewReplyEnum.Null.toString());
                //如果案件状态为Y，需要更新禅道
                updateProductStatus(issueId);

            }
            //1 更新单头
            boolean success = issueDao.updateIssueService(String.valueOf(issueId), "", "",getIssueSyncStatusV3(issueId, issue.getProductCode(),issue.getServiceCode(), newIssueStatus), "", "", newIssueStatus);
            //更新子单头的同步状态
            issueDao.updateIssueDetailSyncStatus(String.valueOf(issueId),getIssueProcessSyncStatusV3(issue.getProductCode(),issue.getServiceCode(), newIssueStatus));
            //2 更新单身
            if (success) {
                issueProgress = progress(issueId, userId,issue.getProductCode(),issue.getServiceCode(),newIssueStatus,"", ReplyType.A.toString(), IssueProcessType.Close.toString(),true , issueProgress);
                if("CN".equals(issue.getServiceRegion())){
                    if("Close Issue".equals(issueProgress.getDescription())){
                        issueProgress.setDescription("结案");
                    }
                }
                issueDao.InsertIssueProgress(issueId, issue.getCrmId(), issueProgress);
                //记录服务结案时间
                casesProcessUtils.saveIssueSummaryV3(userId,issueId, IssueProcessType.Close.toString(), issueProgress.getProcessTime());
                //更新reply_min
                issueProcessService.updateIssueReplyMin(issueId, issue.getSubmitTime(), issueProgress.getProcessTime());

                //如果反馈人结案,需要记录反馈人同意结案时间
                if(IssueStatus.Closed.toString().equals(newIssueStatus)){
                    casesProcessUtils.saveIssueSummaryV3(userId,issueId, IssueProcessType.AgreeClose.toString(), issueProgress.getProcessTime());
                    //更新end_min
                    issueProcessService.updateIssueEndMin(issueId, issue.getSubmitTime(), issueProgress.getProcessTime());
                }
                //更新預警項狀態
                //huly: 修复漏洞/bug IssueSubmitMode.ATHENA 改成 IssueSubmitMode.ATHENA.name()，
                if(!IssueSubmitMode.ATHENA.name().equals(issue.getSubmitWay()) && IssueStatus.Closed.toString().equals(newIssueStatus)){
                    issue.setIssueStatus(IssueStatus.Closed.toString());
                    // 更新預警項
                    issueService.updateWarningNoticeStatus(issue);
                    // 更新事件狀態
                    String EDREventIssue = issueDao.checkIssueIsEdrEvent(issue.getCrmId());
                    if(!StringUtils.isEmpty(EDREventIssue)){
                        issueService.updateEventNoticeStatus(issue);
                    }
                }
                //3 通知客户
                if(isNoticeCustomer){
                    if(IssueStatus.Closed.toString().equals(newIssueStatus)){
                        issueService.SendCloseMailToSubmiter(issue);
                    }else {
                        issueService.SendCloseMailToSubmiterCheck(issue);
                    }

                }
                return BaseResponse.ok();
            }
        }

        return BaseResponse.error(ResponseStatus.SERVICE_CLOSE_FAIL);
    }

    public BaseResponse serviceCloseForT(String userId, long issueId, boolean isNoticeCustomer, String issueStatus, IssueProgress issueProgress){

        IssueCasedetail issueCasedetail = issueDetailDao.selectIssueDetail(issueId);
        Cases cases = new Cases();
        cases.setIssueId(issueId);
        cases.setIssueClassification(issueCasedetail.getIssueClassification());
        cases.setIssueLevel(StringUtils.isEmpty(issueCasedetail.getIssueLevel()) ? 0 : Integer.valueOf(issueCasedetail.getIssueLevel()));
        cases.setCurrentStatus(issueStatus);
        cases.setErpSystemCode(issueCasedetail.getErpSystemCode());
        cases.setWorkHours(0);
        cases.setHandlerDetail(issueProgress.getDescription());
        //completeByFuwu 和 completeByGuWen 是一致的
        return issueProcessService.completeByFuwu(userId, "zh-CN", cases,"servicecloud",isNoticeCustomer);
    }

    @Override
    @Transactional
    public BaseResponse ISVReply(String issueCode, String description){

        Long issueId = issueDao.selectIssueIdBySourceId(issueCode,IssueSourceType.ISV.toString());
        if(issueId == null) {
            issueService.saveISVLog(issueId,"","issueId is null");
            return BaseResponse.error(ResponseStatus.ISSUE_DETAIL_IS_NULL);
        }
        //2021-09-15 huly 状态模式
        Issue issue = issueDao.SelectIssueByIssueId(String.valueOf(issueId));
        if(ObjectUtils.isEmpty(issue)){
            issueService.saveISVLog(issueId,"","issue not find");
            return BaseResponse.error(ResponseStatus.ISSUE_DETAIL_IS_NULL);
        }
        //1 更新单头
        boolean success = issueDao.updateIssueService(String.valueOf(issueId), "", "","", "", "", issue.getIssueStatus());
        //更新子单头的同步状态
        issueDao.updateIssueDetailSyncStatus(String.valueOf(issueId),getIssueProcessSyncStatusV3(issue.getProductCode(),issue.getServiceCode(), issue.getIssueStatus()));
        //2 更新单身
        if (success) {
            IssueProgress issueProgress = new IssueProgress();
            issueProgress = progress(issueId, "Vescloud",issue.getProductCode(),issue.getServiceCode(),issue.getIssueStatus(),"", ReplyType.A.toString(), IssueProcessType.ISVProcess.toString() ,true , issueProgress);
            String syncStatus = getIssueProcessSyncStatusV3(issue.getProductCode(),issue.getServiceCode(), issue.getIssueStatus());
            if(connectarea.equals("CN")){
                //大陆保存依据产品线设定
                issueProgress.setSyncStatus(syncStatus);
            }else {
                //台湾 如果不需要同步就是Z，如果需要同步需要设定为P，这样台湾会单独同步这笔数据
                issueProgress.setSyncStatus(!SyncStatus.DontNeedSync.toString().equals(syncStatus) ? SyncStatus.ProgressUnSync.toString() : syncStatus);
            }
            issueProgress.setDescription(description);
            issueDao.InsertIssueProgress(issueId, issue.getCrmId(), issueProgress);

            return BaseResponse.ok();
        }
        return BaseResponse.error(ResponseStatus.SERVICE_CLOSE_FAIL);
    }

    @Override
    @Transactional
    public BaseResponse ISVServiceClose(String issueCode, String workName, String tenantId, String tenantName){

        Long issueId = issueDao.selectIssueIdBySourceId(issueCode,IssueSourceType.ISV.toString());
        if(issueId == null) {
            issueService.saveISVLog(issueId,"","issueId is null");
            return BaseResponse.error(ResponseStatus.ISSUE_DETAIL_IS_NULL);
        }
        //2021-09-15 huly 状态模式
        Issue issue = issueDao.SelectIssueByIssueId(String.valueOf(issueId));
        if(ObjectUtils.isEmpty(issue)){
            issueService.saveISVLog(issueId,"","issue not find");
            return BaseResponse.error(ResponseStatus.ISSUE_DETAIL_IS_NULL);
        }
        IssueContext context = stateMode(issue.getIssueStatus());
        context.serviceClose(context,issue.getProductCode());

        String newIssueStatus = context.getIssueStatus();
        if(StringUtils.isEmpty(newIssueStatus)){
            return BaseResponse.error(ResponseStatus.ISSUE_STATUS_CAN_NOT_SERVICE_CLOSE);
        }

        //如果结案，需要清除最新回复等
        if(IssueStatus.Closed.toString().equals(newIssueStatus)){
            // 更新单头客户最新回复, 清空最新回复
            updateIssueNewReply(String.valueOf(issueId), NewReplyEnum.Null.toString());
            //如果案件状态为Y，需要更新禅道
//            updateProductStatus(issueId);

        }
        //1 更新单头
        String hSyncStatus = getIssueSyncStatusV3(issueId, issue.getProductCode(),issue.getServiceCode(), newIssueStatus);
        if(connectarea.equals("TW")){
            hSyncStatus = hSyncStatus.equals(SyncStatus.DontNeedSync.toString()) ? hSyncStatus : SyncStatus.HeaderUnSync.toString();
        }
        boolean success = issueDao.updateIssueService(String.valueOf(issueId), "", "", hSyncStatus, "", "", newIssueStatus);
        //更新子单头的同步状态
        issueDao.updateIssueDetailSyncStatus(String.valueOf(issueId),getIssueProcessSyncStatusV3(issue.getProductCode(),issue.getServiceCode(), newIssueStatus));
        //2 更新单身
        if (success) {
            IssueProgress issueProgress = new IssueProgress();
            issueProgress = progress(issueId, "Vescloud",issue.getProductCode(),issue.getServiceCode(),newIssueStatus,"", ReplyType.A.toString(), IssueProcessType.Close.toString() ,false , issueProgress);
            issueProgress.setSyncStatus(SyncStatus.DontNeedSync.toString());
            if("CN".equals(connectarea)){
                issueProgress.setDescription("【ISV结案，ISV单号：" + issueCode +
                                             "，ISV客服姓名:" + workName +
                                             "，ISV租户名称：" + tenantName + "(" + tenantId + ")】" + "结案");
            }else{
                issueProgress.setDescription("【ISV結案，ISV單號：" + issueCode +
                                             "，ISV客服姓名:" + workName +
                                             "，ISV租戶名稱：" + tenantName + "(" + tenantId + ")】"+ "結案");
            }
            issueDao.InsertIssueProgress(issueId, issue.getCrmId(), issueProgress);
            //记录服务结案时间
            casesProcessUtils.saveIssueSummaryV3("Vescloud",issueId, IssueProcessType.Close.toString(), issueProgress.getProcessTime());
            //记录reply_min
            issueProcessService.updateIssueReplyMin(issueId, issue.getSubmitTime(), issueProgress.getProcessTime());
            //如果反馈人结案,需要记录反馈人同意结案时间
            if(IssueStatus.Closed.toString().equals(newIssueStatus)){
                casesProcessUtils.saveIssueSummaryV3("Vescloud", issueId, IssueProcessType.AgreeClose.toString(), issueProgress.getProcessTime());
                //记录end_min
                issueProcessService.updateIssueEndMin(issueId, issue.getSubmitTime(), issueProgress.getProcessTime());
            }

            //3 通知客户
            issueService.SendCloseMailToSubmiter(issue);
            return BaseResponse.ok();
        }
        return BaseResponse.error(ResponseStatus.SERVICE_CLOSE_FAIL);
    }

    @Override
    @Transactional
    public BaseResponse ISVTurnToDigiwin(String issueCode, String workName, String tenantId, String tenantName, String desc){
        Long issueId = issueDao.selectIssueIdBySourceId(issueCode,IssueSourceType.ISV.toString());
        if(issueId == null) {
            issueService.saveISVLog(issueId,"","issueId is null");
            return BaseResponse.error(ResponseStatus.ISSUE_DETAIL_IS_NULL);
        }
        //2021-09-15 huly 状态模式
        Issue issue = issueDao.SelectIssueByIssueId(String.valueOf(issueId));
        if(ObjectUtils.isEmpty(issue)){
            issueService.saveISVLog(issueId,"","issue not find");
            return BaseResponse.error(ResponseStatus.ISSUE_DETAIL_IS_NULL);
        }
        //1 更新单头
        boolean success = issueDao.updateIssueService(String.valueOf(issueId), "", "","", "", "", IssueStatus.Processing.toString());
        //更新子单头的同步状态
        issueDao.updateIssueDetailSyncStatus(String.valueOf(issueId),getIssueProcessSyncStatusV3(issue.getProductCode(),issue.getServiceCode(), IssueStatus.Processing.toString()));
        //2 更新单身
        if (success) {
            IssueProgress issueProgress = new IssueProgress();
            issueProgress = progress(issueId, "Vescloud",issue.getProductCode(),issue.getServiceCode(),IssueStatus.Processing.toString(),"", ReplyType.A.toString(), IssueProcessType.Process.toString(),false , issueProgress);
            issueProgress.setSyncStatus(SyncStatus.DontNeedSync.toString());
            if("CN".equals(connectarea)){
                issueProgress.setDescription("【ISV转鼎捷，ISV单号：" + issueCode +
                        "，ISV客服姓名:" + workName +
                        "，ISV租户名称：" + tenantName + "(" + tenantId + ")】" + desc) ;


            }else{
                issueProgress.setDescription("【ISV轉鼎捷，ISV單號：" + issueCode +
                        "，ISV客服姓名:" + workName +
                        "，ISV租戶名稱：" + tenantName + "(" + tenantId + ")】" + desc) ;

            }
            issueDao.InsertIssueProgress(issueId, issue.getCrmId(), issueProgress);

            //3 通知客服
            sendMailByISVTurn(issue);
            return BaseResponse.ok();
        }
        return BaseResponse.error(ResponseStatus.REPLY_FAIL);
    }
    private void updateProductStatus(long issueId){
        try{
            List<Map<String,String>> list = issueDetailDao.getAllProduct(issueId,IssueSourceType.PRODUCT.toString());
            if(CollectionUtils.isEmpty(list)){
                return;
            }
            list.stream().forEach(o->o.put("status",ProductStatus.serviceClose.toString()));
            productService.updateStatus(list);

        } catch (Exception ex) {
            log.error(ex.toString());
        }

    }
    private IssueContext stateMode(String issueStatus){
        IssueContext context = new IssueContext();
        switch (issueStatus){
            case "I":
                context.setState(new Incomplete());
                break;
            case "C":
                context.setState(new Submited());
                break;
            case "2":
                context.setState(new Processing());
                break;
            case "3":
                context.setState(new Processing());
                break;
            case "N":
                context.setState(new Processing());
                break;
            case "4":
                context.setState(new ProductProcessing());
                break;
            case "O":
                context.setState(new ProductProcessing());
                break;
            case "VN":
                context.setState(new ISVProcessing());
                break;
            case "11":
                context.setState(new SubmiterCheck());
                break;
            case "R":
                context.setState(new SubmiterCheck());
                break;
            case "12":
                context.setState(new ServiceCheck());
                break;
            case "Q":
                context.setState(new ServiceCheck());
                break;
            case "7":
                context.setState(new Closed());
                break;
            case "10":
                context.setState(new Closed());
                break;
            case "Y":
                context.setState(new Closed());
                break;
            default:
                break;
        }

        return context;
    }

    @Override
    @Transactional
    public BaseResponse cancelClose(String userId, long issueId, boolean isNoticeCustomer, IssueDetailInfo issueDetailInfo){

        //2021-09-15 huly 状态模式
        Issue issue = issueDao.SelectIssueByIssueId(String.valueOf(issueId));
        IssueContext context = stateMode(issue.getIssueStatus());
        context.cancelClose(context);
        String newIssueStatus = context.getIssueStatus();

        if(StringUtils.isEmpty(newIssueStatus)){
            return BaseResponse.error(ResponseStatus.ISSUE_STATUS_CAN_NOT_CANCEL_CLOSE);
        }

        //1 更新单头
        boolean success = issueDao.updateIssueService(String.valueOf(issueId), "", "",getIssueSyncStatusV3(issueId, issue.getProductCode(),issue.getServiceCode(), newIssueStatus), "", "", newIssueStatus);

        //2 更新单身
        if (success) {
            for (IssueProgress issueProgress:issueDetailInfo.getIssueProgresses()) {
                if("CN".equals(issue.getServiceRegion())){
                    if("Cancel Close Issue".equals(issueProgress.getDescription())){
                        issueProgress.setDescription("取消结案");
                    }
                }
                issueProgress = progress(issueId, userId,issue.getProductCode(),issue.getServiceCode(),newIssueStatus, "", ReplyType.A.toString(), IssueProcessType.CancelClose.toString(),true , issueProgress);
                issueDao.InsertIssueProgress(issueId, issue.getCrmId(), issueProgress);

                //如果是大陆T，需要同步187、scm、workday
                if("CN".equals(connectarea) && Constants.getTProductCodes().contains(issue.getProductCode())){
                    syncForT(issueId, issueProgress, newIssueStatus);
                }
            }
            //3 通知客户
            if(isNoticeCustomer){
                issueService.SendCloseMailToSubmiter(issue);
            }

            //更新預警項狀態
            //huly: 修复漏洞/bug IssueSubmitMode.ATHENA 改成 IssueSubmitMode.ATHENA.name()，
            if(!IssueSubmitMode.ATHENA.name().equals(issue.getSubmitWay())){
                issue.setIssueStatus(newIssueStatus);
                // 更新預警項
                issueService.updateWarningNoticeStatus(issue);
                // 更新事件狀態
                String EDREventIssue = issueDao.checkIssueIsEdrEvent(issue.getCrmId());
                if(!StringUtils.isEmpty(EDREventIssue)){
                    issueService.updateEventNoticeStatus(issue);
                }
            }

            return BaseResponse.ok();
        }
        return BaseResponse.error(ResponseStatus.SERVICE_CLOSE_FAIL);
    }

    @Override
    public BaseResponse followUpIssue(String userId, long issueId, String desc) {
        try {
            //状态模式
            Issue issue = issueDao.SelectIssueByIssueId(String.valueOf(issueId));
            IssueContext context = stateMode(issue.getIssueStatus());
            context.followup(context);
            String newIssueStatus = context.getIssueStatus();

            if(StringUtils.isEmpty(newIssueStatus)){
                return BaseResponse.error(ResponseStatus.ISSUE_STATUS_CAN_NOT_FOLLOWUP);
            }

            int followUpRes=checkFollowUp(issueId, userId, desc, issue.getServiceId());
            if (followUpRes==0){
                //如果是大陆T，需要同步187、scm、workday
                if("CN".equals(connectarea) && Constants.getTProductCodes().contains(issue.getProductCode())){
                    IssueProgress issueProgress = new IssueProgress();
                    issueProgress.setSequeceNum(issueDao.getMaxOrder(issueId) + 1);
                    issueProgress.setProcessHours(0);
                    syncForT(issueId, issueProgress, newIssueStatus);
                }

                return BaseResponse.ok();
            }
            else if (followUpRes==2) {
                return BaseResponse.error(ResponseStatus.FREQUENT_FOLLOWUP);
            }
        } catch (Exception ex) {
            log.error(ex.toString());
        }
        return BaseResponse.error(ResponseStatus.FOLLOWUP_FAIL);
    }
    public int checkFollowUp(long issueId, String userId, String desc, String serviceId) {
        int followUpResult=0;
        try {
            String lastTime=issueDao.GetIssueFollowUpLastTime(issueId);
            boolean canFollowUp;
            if (lastTime==null){
                canFollowUp=true;
            }
            else{
                if (lastTime.equals("")){
                    canFollowUp=true;
                }
                else {
                    Calendar nowTime = Calendar.getInstance();
                    nowTime.add(Calendar.MINUTE, -30);
                    DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    Date lastDateTime=sdf.parse(lastTime);
                    canFollowUp=lastDateTime.before(nowTime.getTime());
                }
            }
            boolean res=false;
            if (canFollowUp){
                Issue issueTmp = issueService.GetIssueProgress(String.valueOf(issueId),userId,true);

                IssueProgress issueProgress = new IssueProgress();
                issueProgress.setDescription(desc);
                issueProgress.setHandlerId(serviceId);
                res=issueDao.InsertIssueProgress(issueId, issueTmp.getCrmId(), progress(issueId, userId, issueTmp.getProductCode(),issueTmp.getServiceCode(),issueTmp.getIssueStatus(),"", ReplyType.Q.toString(), IssueProcessType.FollowUp.toString(),true , issueProgress)) > 0;

                // 更新单头客户最新回复
                updateIssueNewReply(String.valueOf(issueId), NewReplyEnum.CustomerNewReply.toString());

                if (res){
                    issueService.SendFollowUpMail(issueTmp,desc);
                }
                else
                    followUpResult=1;
            }
            else
                followUpResult=2;
        }catch (Exception ex) {
            log.error(ex.toString());
            followUpResult=1;
        }
        return followUpResult;
    }
    public boolean updateIssueNewReply(String issueId, String replyType){
        //如果replyType = null，那么清空replyCount
        //先查单头里面的回复类型，如果跟replyType相同，那么replyCount 增加1；如果不相同，那么就设置初始值1；
        if(StringUtils.isEmpty(replyType)){
            return issueDao.updateIssueNewReply(issueId, replyType, 0);
        }
        Map<String,String> map = issueDao.selectIssueReply(issueId);
        String oldReplyType = map.get("newReply") !=null ? String.valueOf(map.get("newReply")) : "";
        String replyCount = map.get("replyCount") !=null ? String.valueOf(map.get("replyCount")) : "0";
        if(oldReplyType.equals(replyType)){
            return issueDao.updateIssueNewReply(issueId, replyType, Integer.valueOf(replyCount) + 1);
        }else {
            return issueDao.updateIssueNewReply(issueId, replyType, 1);
        }
    }
    @Override
    @Transactional
    public BaseResponse askBySubmiter(String userId, long issueId, String crmId, long replyId, IssueProgress issueProgress, Issue issue){
        if(issueProgress == null || StringUtils.isEmpty(issueProgress.getDescription())){
            return BaseResponse.error(ResponseStatus.PARAM_VERIFY);
        }

        //状态模式
        Issue issueTemp = issueDao.SelectIssueByIssueId(String.valueOf(issueId));
        IssueContext context = stateMode(issueTemp.getIssueStatus());
        context.askBySubmiter(context);
        String newIssueStatus = context.getIssueStatus();

        if(StringUtils.isEmpty(newIssueStatus)){
            return BaseResponse.error(ResponseStatus.ISSUE_STATUS_CAN_NOT_ASK_BY_SUBMITER);
        }

        //1 更新单头客户最新回复
        boolean success = updateIssueNewReply(String.valueOf(issueId), NewReplyEnum.CustomerNewReply.toString());
        //3 更新单身
        if(success) {
            //引用回复
            if(replyId != 0){
                issueProgress.setReplyId(replyId);
            }
            issueProgress.setHandlerId(issueTemp.getServiceId());
            issueProgress = progress(issueId, userId, issueTemp.getProductCode(),issueTemp.getServiceCode(), newIssueStatus,"", ReplyType.Q.toString(), IssueProcessType.AddQuestion.toString(),true , issueProgress);
            long progressId = issueDao.InsertIssueProgress(issueId, crmId, issueProgress);

            //处理附件
            issueService.SaveAttachments(issueId, progressId, issue);
            //如果是大陆T，需要同步187、scm、workday
            if("CN".equals(connectarea) && Constants.getTProductCodes().contains(issueTemp.getProductCode())){
                syncForT(issueId, issueProgress, newIssueStatus);
            }
            return BaseResponse.ok();
        }
        return BaseResponse.error(ResponseStatus.ASK_BY_SUBMITER_FAIL);
    }

    @Override
    @Transactional
    public BaseResponse submiterAgreeClose(String userId, long issueId, IssueProgress issueProgress){
        if(issueProgress == null || StringUtils.isEmpty(issueProgress.getDescription())){
            return BaseResponse.error(ResponseStatus.PARAM_VERIFY);
        }

        //2021-09-15 huly 状态模式
        Issue issue = issueDao.SelectIssueByIssueId(String.valueOf(issueId));
        IssueContext context = stateMode(issue.getIssueStatus());
        context.submiterAgreeClose(context);
        String newIssueStatus = context.getIssueStatus();
        if(StringUtils.isEmpty(newIssueStatus)){
            return BaseResponse.error(ResponseStatus.ISSUE_STATUS_CAN_NOT_SUBMITER_AGREE_CLOSE);
        }

        if("CN".equals(connectarea) && Constants.getTProductCodes().contains(issue.getProductCode())){
            return closeByCustomerForT(userId, issueId);
        }else {
            //1 更新单头
            boolean success = issueDao.updateIssueService(String.valueOf(issueId), "","", getIssueSyncStatusV3(issueId, issue.getProductCode(),issue.getServiceCode(), newIssueStatus), "", "", newIssueStatus);

            //2 提交人同意结案后，单头的最新回复清空
            updateIssueNewReply(String.valueOf(issueId), NewReplyEnum.Null.toString());

            //3 更新单身
            if (success) {
                issueProgress = progress(issueId, userId, issue.getProductCode(),issue.getServiceCode(),newIssueStatus, "", ReplyType.Q.toString(), IssueProcessType.AgreeClose.toString(),true , issueProgress);
                issueDao.InsertIssueProgress(issueId, issue.getCrmId(), issueProgress);

                //记录提交人同意结案时间
                casesProcessUtils.saveIssueSummaryV3(userId,issueId, IssueProcessType.AgreeClose.toString(), issueProgress.getProcessTime());

                //记录end_min
                issueProcessService.updateIssueEndMin(issueId, issue.getSubmitTime(), issueProgress.getProcessTime());
                //如果案件状态为Y，需要更新禅道
                updateProductStatus(issueId);

                //更新預警項狀態
                //huly: 修复漏洞/bug IssueSubmitMode.ATHENA 改成 IssueSubmitMode.ATHENA.name()，
                if(!IssueSubmitMode.ATHENA.name().equals(issue.getSubmitWay()) && IssueStatus.Closed.toString().equals(newIssueStatus)){
                    issue.setIssueStatus(newIssueStatus);
                    // 更新預警項
                    issueService.updateWarningNoticeStatus(issue);
                    // 更新事件狀態
                    String EDREventIssue = issueDao.checkIssueIsEdrEvent(issue.getCrmId());
                    if(!StringUtils.isEmpty(EDREventIssue)){
                        issueService.updateEventNoticeStatus(issue);
                    }
                }

                return BaseResponse.ok();
            }
        }

        return BaseResponse.error(ResponseStatus.SUBMITER_AGREE_CLOSE_FAIL);
    }

    public BaseResponse closeByCustomerForT(String userId, long issueId){
        return issueProcessService.closeCase(userId, issueId, new Cases());
    }

    public BaseResponse submiterDisAgreeCloseForT(String userId, long issueId, String language, IssueProgress issueProgress){
        Cases cases = new Cases();
        cases.setIssueId(issueId);
        cases.setHandlerDetail(issueProgress.getDescription());
        return issueProcessService.doNotAgreeClose(userId, language, cases);
    }
    @Override
    @Transactional
    public BaseResponse submiterClose(String userId, long issueId, IssueProgress issueProgress){
        if(issueProgress == null || StringUtils.isEmpty(issueProgress.getDescription())){
            return BaseResponse.error(ResponseStatus.PARAM_VERIFY);
        }

        //2021-09-15 huly 状态模式
        Issue issue = issueDao.SelectIssueByIssueId(String.valueOf(issueId));
        IssueContext context = stateMode(issue.getIssueStatus());
        context.submiterClose(context);
        String newIssueStatus = context.getIssueStatus();
        if(StringUtils.isEmpty(newIssueStatus)){
            return BaseResponse.error(ResponseStatus.ISSUE_STATUS_CAN_NOT_SUBMITER_CLOSE);
        }

        if("CN".equals(connectarea) && Constants.getTProductCodes().contains(issue.getProductCode())){
            return closeByCustomerForT(userId, issueId);
        }else {
            //1 更新单头
            boolean success = issueDao.updateIssueService(String.valueOf(issueId), "","", getIssueSyncStatusV3(issueId, issue.getProductCode(),issue.getServiceCode(),newIssueStatus), "", "", newIssueStatus);

            //2 提交人同意结案后，单头的最新回复清空
            updateIssueNewReply(String.valueOf(issueId), NewReplyEnum.Null.toString());

            //3 更新单身
            if (success) {
                issueProgress = progress(issueId, userId, issue.getProductCode(),issue.getServiceCode(),newIssueStatus,"", ReplyType.Q.toString(), IssueProcessType.Close.toString(),true , issueProgress);
                issueDao.InsertIssueProgress(issueId, issue.getCrmId(), issueProgress);

                //记录提交人同意结案时间
                casesProcessUtils.saveIssueSummaryV3(userId,issueId, IssueProcessType.AgreeClose.toString(), issueProgress.getProcessTime());

                //更新end_min
                issueProcessService.updateIssueEndMin(issueId, issue.getSubmitTime(), issueProgress.getProcessTime());
                //如果案件状态为Y，需要更新禅道
                updateProductStatus(issueId);

                //更新預警項狀態
                //huly: 修复漏洞/bug IssueSubmitMode.ATHENA 改成 IssueSubmitMode.ATHENA.name()，
                if(!IssueSubmitMode.ATHENA.name().equals(issue.getSubmitWay()) && IssueStatus.Closed.toString().equals(newIssueStatus)){
                    issue.setIssueStatus(newIssueStatus);
                    // 更新預警項
                    issueService.updateWarningNoticeStatus(issue);
                    // 更新事件狀態
                    String EDREventIssue = issueDao.checkIssueIsEdrEvent(issue.getCrmId());
                    if(!StringUtils.isEmpty(EDREventIssue)){
                        issueService.updateEventNoticeStatus(issue);
                    }
                }
                return BaseResponse.ok();
            }
        }

        return BaseResponse.error(ResponseStatus.SUBMITER_AGREE_CLOSE_FAIL);
    }

    @Override
    @Transactional
    public BaseResponse autoClose(String userId, long issueId){

        //2021-09-15 huly 状态模式
        Issue issue = issueDao.SelectIssueByIssueId(String.valueOf(issueId));
        IssueContext context = stateMode(issue.getIssueStatus());
        context.submiterAgreeClose(context);
        String newIssueStatus = context.getIssueStatus();
        if(StringUtils.isEmpty(newIssueStatus)){
            return BaseResponse.error(ResponseStatus.ISSUE_STATUS_CAN_NOT_SUBMITER_AGREE_CLOSE);
        }

        //1 更新单头
        boolean success = issueDao.updateIssueService(String.valueOf(issueId), "","", getIssueSyncStatusV3(issueId, issue.getProductCode(),issue.getServiceCode(),newIssueStatus), "", "", newIssueStatus);

        //2 提交人同意结案后，单头的最新回复清空
        updateIssueNewReply(String.valueOf(issueId), NewReplyEnum.Null.toString());

        //3 更新单身
        if (success) {
            IssueProgress issueProgress = progress(issueId, userId, issue.getProductCode(),issue.getServiceCode(),newIssueStatus, "", ReplyType.Q.toString(), IssueProcessType.AgreeClose.toString(),true , new IssueProgress());
            issueProgress.setDescription("Auto Close");
            issueDao.InsertIssueProgress(issueId, issue.getCrmId(), issueProgress);

            //记录提交人同意结案时间
            casesProcessUtils.saveIssueSummaryV3(userId,issueId, IssueProcessType.AutoClose.toString(), issueProgress.getProcessTime());

            //更新end_min
            issueProcessService.updateIssueEndMin(issueId, issue.getSubmitTime(), issueProgress.getProcessTime());
            //如果案件状态为Y，需要更新禅道
            updateProductStatus(issueId);
            return BaseResponse.ok(issue);
        }
        return BaseResponse.error(ResponseStatus.SUBMITER_AGREE_CLOSE_FAIL);
    }
    @Override
    @Transactional
    public BaseResponse submiterDisAgreeClose(String userId, long issueId, String language, IssueProgress issueProgress){

        if(issueProgress == null || StringUtils.isEmpty(issueProgress.getDescription())){
            return BaseResponse.error(ResponseStatus.PARAM_VERIFY);
        }

        //2021-09-15 huly 状态模式
        Issue issue = issueDao.SelectIssueByIssueId(String.valueOf(issueId));
        IssueContext context = stateMode(issue.getIssueStatus());
        context.submiterDisAgreeClose(context);
        String newIssueStatus = context.getIssueStatus();

        if(StringUtils.isEmpty(newIssueStatus)){
            return BaseResponse.error(ResponseStatus.ISSUE_STATUS_CAN_NOT_SUBMITER_DIS_AGREE_CLOSE);
        }

        if("CN".equals(connectarea) && Constants.getTProductCodes().contains(issue.getProductCode())){
            return submiterDisAgreeCloseForT(userId, issueId, language, issueProgress);
        }else {
            //1 更新单头
            boolean success = issueDao.updateIssueService(String.valueOf(issueId), "","", "", "", "", newIssueStatus);

            //2 提交人不同意结案后，单头的最新回复算客户最新回复
            updateIssueNewReply(String.valueOf(issueId), NewReplyEnum.CustomerNewReply.toString());

            //3 更新单身
            if (success) {
                issueProgress = progress(issueId, userId, issue.getProductCode(),issue.getServiceCode(),newIssueStatus, issue.getProductCode(), ReplyType.Q.toString(), IssueProcessType.DisAgreeClose.toString(),true , issueProgress);
                issueDao.InsertIssueProgress(issueId, issue.getCrmId(), issueProgress);

                //更新預警項狀態
                //huly: 修复漏洞/bug IssueSubmitMode.ATHENA 改成 IssueSubmitMode.ATHENA.name()，
                if(!IssueSubmitMode.ATHENA.name().equals(issue.getSubmitWay())){
                    issue.setIssueStatus(newIssueStatus);
                    // 更新預警項
                    issueService.updateWarningNoticeStatus(issue);
                    // 更新事件狀態
                    String EDREventIssue = issueDao.checkIssueIsEdrEvent(issue.getCrmId());
                    if(!StringUtils.isEmpty(EDREventIssue)){
                        issueService.updateEventNoticeStatus(issue);
                    }
                }

                return BaseResponse.ok();
            }
        }

        return BaseResponse.error(ResponseStatus.SUBMITER_DIS_AGREE_CLOSE_FAIL);
    }

    @Override
    @Transactional
    public BaseResponse updateIssueDetail(long issueId,Issue issue){
        try{
            //更新单头
            issueDao.updateIssueOtherInfo(issue);
            //更新子单头
            Issue issueTemp = issueDao.SelectIssueByIssueId(String.valueOf(issueId));
            updateIssueCasedetail(issueId,issueTemp.getProductCode(),issue.getServiceCode(),issueTemp.getIssueStatus(), issue.getIssueCasedetail());
            return BaseResponse.ok();
        }catch (Exception e){
            log.error(e.toString());
        }
        return BaseResponse.error(ResponseStatus.UPDATE_FAILD);
    }

    @Override
    public  List<RequAssessmentResultCodeData> selectRequAssessmentResult(String origin) {
        return issueDetailDao.selectRequAssessmentResult(origin);
    }

    @Override
    @Transactional
    public BaseResponse saveIssueRequ(String userId, long issueId, IssueCasedetail issueCasedetail){

        if(issueCasedetail == null ){
            return BaseResponse.error(ResponseStatus.PARAM_VERIFY);
        }
        try{
            if(!StringUtils.isEmpty(issueCasedetail.getRequAssUserFromProduct())){
                //找出工號
                String assUser= issueDetailDao.getUserWorkNo(issueCasedetail.getRequAssUserFromProduct());
                if(!StringUtils.isEmpty(assUser)) {
                    issueCasedetail.setRequAssUserFromProduct(assUser);
                }
            }
            // 新增或更新需求
            updateIssueRequ(issueId,issueCasedetail);
            // 紀錄
            issueDetailDao.saveIssueRequRecord(issueId,userId, issueCasedetail.getRequAssResultFromProduct(),issueCasedetail.getRequAssUserFromProduct(), issueCasedetail.getRequPlanComDateFromProduct(),  issueCasedetail.getRequProcessReplyFromProduct(),"EsEasyTalk");
                                 //(long issueId,String userId,String requ_assessmentResult, String requ_plannedCompletionDate, String requ_processReply, String infoSource)
            //saveIssueRequRecord()
            return BaseResponse.ok();
        }catch (Exception e){
            log.error(e.toString());
        }
        return BaseResponse.error(ResponseStatus.UPDATE_FAILD);
    }

    private void updateIssueRequ(long issueId,IssueCasedetail issueCasedetail){
        IssueCasedetail dbData = issueDetailDao.selectIssueDetail(issueId);

        if (dbData != null) {
            issueDetailDao.updateIssueRequ(issueId,issueCasedetail);
        } else {
            issueDetailDao.insertIssueRequ(issueId,issueCasedetail);
        }
    }

    /**
     * 验证转ISV的按钮是否可见：客户的案件；立案的客户购买的所有合约里面是否有ISV产品线；案件状态必须满足not in（10,11,12,22,4,7,8,D,I,O,P,Q,R,T,V,Y）；ISV租户是否存在于服务云
     * @param issueId
     * @return
     */
    @Override
    public BaseResponse checkTurnToISV(long issueId){
        IssueCasedetail issueCasedetail = issueDetailDao.selectIssueDetail(issueId);
        if(StringUtils.isEmpty(issueCasedetail.getErpSystemCode())){
            return BaseResponse.error(ResponseStatus.PARAM_VERIFY);
        }

        Issue issue = issueDao.SelectIssueByIssueId(String.valueOf(issueId));
        UserDetailInfo detail = userServiceFeignClient.findUserDetailById(issue.getUserId());
        //判断案件是否为客户案件
        if(Integer.parseInt(detail.getUserType()) == UserType.INNER.getValue()) {
            return BaseResponse.error(ResponseStatus.ISSUE_IS_NOT_FROM_CUSTOMER);
        }

        int checkHasISVContact = issueDao.checkHasISVContact(issue.getServiceCode());
        if(checkHasISVContact == 0){
            return BaseResponse.error(ResponseStatus.CUSTOMER_NOT_HAS_ISV_CONTRACT);
        }

        int checkIssueStatus = issueDao.checkIssueStatus(issueId);
        if(checkIssueStatus > 0){
            return BaseResponse.error(ResponseStatus.ISSUE_STATUS_CAN_NOT_TURN_TO_ISV);
        }

        Long sid = issueService.getISVTenantInServiceCloud(issueCasedetail.getErpSystemCode());
        if(sid == null || sid == 0L){
            return BaseResponse.error(ResponseStatus.ISV_TENANT_NOT_EXISTS_IN_SERVICECLOUD);
        }

        return BaseResponse.ok(true);
    }

    @Override
    public BaseResponse updateIssuedescription(long issueId,Issue issue){
        Issue oldIssue = issueDao.SelectIssueByIssueId(String.valueOf(issueId));
        issue.setIssueId(issueId);
        issue.setSyncStatus(getIssueSyncStatusV3(issueId, oldIssue.getProductCode(), oldIssue.getServiceCode(), oldIssue.getIssueStatus()));
        return BaseResponse.ok(issueDao.updateIssuedescription(issue));
    }
}
