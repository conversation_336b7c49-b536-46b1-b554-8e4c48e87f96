<#import "./common/common.macro.ftl" as common>
<@common.commonScript />
<table align=center style="border: solid 1px #dad7d7; width:960px;font-family: 微软雅黑">
        <tr>
            <td width=960>
                <img src="cid:imageMailHeader"/>
            </td>
        </tr>
        <tr>
            <td style="padding-top:10px;">
                    <span style="font-size:25px;text-align: center;display:block;margin-bottom:10px;">
                        ${I18n.warning_notice_title}
                    </span>
            </td>
        </tr>
        <tr style="padding: 0px 12px; display: block;">
            <td style="padding-left:10px;padding-right:10px;" width=960>
                <div id='appellation' style='margin-bottom:10px;'>
                </div>
                <p>
                    <span style="font-size:14px;color:rgba(127, 127, 127, 0.85);">
                        ${I18n.warning_user_hello}，
                    </span>
                </p>
                <div id='abstract' style='margin-bottom:10px;margin-left: 32px;'>
                    <p style='text-align: left;'>
                            <span style='font-size:14px; color:rgba(127, 127, 127, 0.85)'>
                                    ${I18n.warning_at}<span style="font-weight:bold">[${warningTime}]</span>${I18n.warning_exist_problems}:
                            </span>
                    </p>
                </div>
                <div id='details' style='margin-bottom:10px;'>
                    <div id='detail_%s' style='margin-bottom:10px;margin-left:32px;'>
                        <p style='margin-bottom:10px;text-align: left;background-color: rgba(46, 176, 252, 0.12);padding: 6px 16px;margin-top: 16px;line-height: 32px;font-size: 14px;color: #2b4f85'>
                            <#if aiopsItem == "S1EDR_HOST">
                                [${I18n.warning_s1edr}] [${aiopsInstanceName}] ${content}
                            <#else>
                                [${aiopsItemName}] [${aiopsInstanceName}] ${content}
                            </#if>
                        </p>
                        <table style='width: 100%;padding-left: 32px;'>
                            <tr style='line-height: 32px;'>
                                <td style='width:50%;position: relative;'>
                                    <div style='width: 80px;float: left;color: rgba(127, 127, 127, 0.85);font-size: 14px;'>${I18n.warning_level}：</div>
                                    <#if warningLevel == "FATAL">
                                        <div style='position: absolute;top: 0px;right: 0px;bottom: 0px;left: 80px;float: left;color: #f04134;text-align: left;font-size: 14px;'>${I18n.level_fatal}</div>
                                    <#elseif warningLevel == "ERROR">
                                        <div style='position: absolute;top: 0px;right: 0px;bottom: 0px;left: 80px;float: left;color: #ffbf00;text-align: left;font-size: 14px;'>${I18n.level_error}</div>
                                    <#elseif warningLevel == "WARNING">
                                        <div style='position: absolute;top: 0px;right: 0px;bottom: 0px;left: 80px;float: left;color: #0e77d1;text-align: left;font-size: 14px;'>${I18n.level_warning}</div>
                                    <#elseif warningLevel == "INFO">
                                        <div style='position: absolute;top: 0px;right: 0px;bottom: 0px;left: 80px;float: left;color: #00a854;text-align: left;font-size: 14px;'>${I18n.level_info}</div>
                                    <#else>
                                        <div style='position: absolute;top: 0px;right: 0px;bottom: 0px;left: 80px;float: left;color:rgba(5, 47, 111, 0.85);text-align: left;font-size: 14px;'>${warningLevel}</div>
                                    </#if>
                                </td>
                            </tr>
                            <tr style='line-height: 32px;'>
                                <#if aiopsItem == "HOST" ||  aiopsItem == "CLIENT">
                                    <td style='width:50%;position: relative;'>
                                        <div style='width: 80px;float: left;color: rgba(127, 127, 127, 0.85);font-size: 14px;'>${I18n.warning}${I18n.device}：</div>
                                        <div style='position: absolute;top: 0px;right: 0px;bottom: 0px;left: 80px;float: left;color:rgba(5, 47, 111, 0.85);text-align: left;font-size: 14px;'>[${aiopsItemName}] ${aiopsInstanceName}</div>
                                    </td>
                                    <td style='width:50%;position: relative;'>
                                        <div style='width: 80px;float: left;color: rgba(127, 127, 127, 0.85);font-size: 14px;padding-left: 16px;'>${I18n.device}IP：</div>
                                        <div style='position: absolute;top: 0px;right: 0px;bottom: 0px;left: 80px;float: left;color:rgba(5, 47, 111, 0.85);text-align: left;font-size: 14px;'>${ipAddress}</div>
                                    </td>
                                <#elseif aiopsItem == "S1EDR_HOST">
                                    <td style='width:100%;position: relative;'>
                                        <div style='width: 80px;float: left;color: rgba(127, 127, 127, 0.85);font-size: 14px;'>${I18n.warning}${I18n.device}：</div>
                                        <div style='position: absolute;top: 0px;right: 0px;bottom: 0px;left: 80px;float: left;color:rgba(5, 47, 111, 0.85);text-align: left;font-size: 14px;'>[${I18n.warning_s1edr}] ${aiopsInstanceName}</div>
                                    </td>
                                <#else>
                                    <td style='width:100%;position: relative;'>
                                        <div style='width: 80px;float: left;color: rgba(127, 127, 127, 0.85);font-size: 14px;'>${I18n.warning}${I18n.device}：</div>
                                        <div style='position: absolute;top: 0px;right: 0px;bottom: 0px;left: 80px;float: left;color:rgba(5, 47, 111, 0.85);text-align: left;font-size: 14px;'>[${aiopsItemName}] ${aiopsInstanceName}</div>
                                    </td>
                                </#if>
                            </tr>
                            <tr style='line-height: 32px;'>
                                <td style='width:50%;position: relative;'>
                                    <div style='width: 80px;float: left;color: rgba(127, 127, 127, 0.85);font-size: 14px;'>${I18n.collected_time}：</div>
                                    <div style='position: absolute;top: 0px;right: 0px;bottom: 0px;left: 80px;float: left;color:rgba(5, 47, 111, 0.85);text-align: left;font-size: 14px;'>${colletedTime}</div>
                                <td style='width:50%;position: relative;'>
                                    <div style='width: 80px;float: left;color: rgba(127, 127, 127, 0.85);font-size: 14px;'>${I18n.warning}${I18n.count}：</div>
                                    <div style='position: absolute;top: 0px;right: 0px;bottom: 0px;left: 80px;float: left;color:rgba(5, 47, 111, 0.85);text-align: left;font-size: 14px;'>${warningCount}</div>
                                </td>
                            </tr>
                            <tr style='line-height: 32px;'>
                                <td colspan='2' style='width:100%;position: relative;'>
                                    <div style='width: 80px;float: left;color: rgba(127, 127, 127, 0.85);font-size: 14px;'>${I18n.handling_suggestions}：</div>
                                    <#if aiopsItem == "S1EDR_HOST">
                                        <br>
                                        <div style='color:rgba(5, 47, 111, 0.85);text-align: left;font-size: 14px;line-height: 24px;'>
                                            <#if suggest??>${suggest}</#if>
                                        </div>
                                    <#else>
                                        <div style='margin-left: 80px;color:rgba(5, 47, 111, 0.85);text-align: left;font-size: 14px;padding-top: 6px;line-height: 24px;'>
                                            <#if suggest??>${suggest}</#if>
                                        </div>
                                    </#if>
                                </td>
                            </tr>
                            <tr style='line-height: 32px;'>
                                <td colspan='2' style='width:100%;position: relative;'>
                                    <div style='width: 80px;float: left;color: rgba(127, 127, 127, 0.85);font-size: 14px;'>${I18n.question_details}：</div>
                                </td>
                            </tr>
                            <tr>
                                <td colspan='2' style='width:100%;position: relative;color:rgba(127, 127, 127, 0.85);'>
                                    <#if contentList?? && (contentList?size > 0) && titleList?? && (titleList?size > 0)>
                                        <table style="border-spacing:0;margin-top:12px;width: 100%;">
                                            <tr>
                                                <#assign ind = -1>
                                                <#list titleList as t_title>
                                                    <th style ="padding: 8px 16px;background-color: rgba(222,222,222,0.3);font-size: 14px; font-weight: initial;text-align: left;color: rgba(127, 127, 127, 0.85)">
                                                        ${t_title}
                                                        <#if t_title == "溫濕度設備採集時間">
                                                            <#assign ind = t_title_index >
                                                        </#if>
                                                    </th>
                                                </#list>
                                            </tr>
                                            <#list contentList as a_content>
                                                <tr>
                                                    <#list a_content as t_content>
                                                        <td style="padding: 12px;border-bottom:1px solid rgba(222,222,222,0.75);color:#2B4F85;background-color: #ffffff;font-size: 14px; font-weight: initial;text-align: left;">
                                                            <#if ind != -1 && ind == t_content_index>
                                                                ${(t_content+'000')?number?number_to_datetime?string("yyyy-MM-dd HH:mm:ss")!""}
                                                            <#else>
                                                                ${t_content!''}
                                                            </#if>
                                                        </td>
                                                    </#list>
                                                </tr>
                                            </#list>
                                        </table>
                                    </#if>
                                </td>

                            </tr>
                        </table>
                    </div>
                </div>
            </td>
        </tr>
        <tr>
            <td style="padding-left:54px;font-size:13px;color:#99979a" width=960>
                <p>
                    <#if serviceArea == "CN">
                        * ${I18n.warning_website_CN}：<a href=${I18n.warning_web_url_CN} >${I18n.warning_web_url_CN}</a>
                    <#else>
                        * ${I18n.warning_website_TW}：<a href=${I18n.warning_web_url_TW} >${I18n.warning_web_url_TW}</a>
                    </#if>
                    <br>
                    * ${I18n.warning_contact_us}
                    <br>
                    * ${I18n.warning_automatic_email}
                </p>
            </td>
        </tr>
        <tr>
            <td width=960>
                <img src="cid:imageMailFooter"/>
            </td>
        </tr>
        <tr>
            <td width=960 style="padding-left: 10px;font-size: 14px">
                <p>${I18n.information_security_declaration}</p>
            </td>
        </tr>
    </table>