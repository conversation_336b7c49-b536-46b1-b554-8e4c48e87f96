package com.digiwin.escloud.aioitms.report.model.db;

import com.digiwin.escloud.aioitms.report.model.base.ReportRecord;
import com.digiwin.escloud.common.model.UnitType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date: 2022-06-14 19:03
 * @Description
 */
@ApiModel("数据库报表纪录")
@Data
public class DbReportRecord extends ReportRecord {

    @ApiModelProperty("主键")
    private long id;

    @ApiModelProperty("报表类型")
    private int reportType;
    @ApiModelProperty("设备Id")
    private String deviceId;
    @ApiModelProperty("报告状态")
    private int reportStatus;
    @ApiModelProperty("数据库类型")
    private String dbType;
    @ApiModelProperty("数据库实例名(mssql用)")
    private String dbInstanceName;
    @ApiModelProperty("数据库服务名(oracle用)")
    private String dbServerName;
    @ApiModelProperty("数据库Id")
    private String dbId;
    @ApiModelProperty("数据库名称")
    private String dbName;

    @ApiModelProperty("设备Id列表for顯示用")
    private String deviceIds; // 顯示列表用

    @ApiModelProperty("设备名稱列表for顯示用")
    private String deviceNames;

    @ApiModelProperty("设备Id列表for生成報告用")
    private List<String> deviceIdList; // 生成報告用

    @ApiModelProperty("生成報告時, 實際在計算用的 deviceId for 計算用")
    private String curProcDeviceId; //生成報告時, 實際在計算用的 deviceId

    public String getDeviceIds() {

        if (Optional.ofNullable(deviceIds).isPresent()) {
            return deviceIds;
        }
        if (CollectionUtils.isEmpty(deviceIdList)) {
            return "";
        }

        this.deviceIds = String.join(",", deviceIdList);
        return this.deviceIds;
    }

    @ApiModelProperty("产品代号")
    private String productCode;
    @ApiModelProperty("原始产品代号")
    private String oriProductCode = "";
    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("时间聚合间隔")
    private Integer groupInterval;

    @ApiModelProperty("时间聚合单位")
    private UnitType unitType;

    @ApiModelProperty("报告部分名称")
    private String partReportName;

    @ApiModelProperty("mis端報告發送時間")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime misReportSendTime;

    @ApiModelProperty("報告發送時間")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reportSendTime;

    @ApiModelProperty("EDR報告紀錄id")
    private Long edrReportRecordId;


    @ApiModelProperty("報告發送人")
    private String reportSender;

    @ApiModelProperty("報告接收人")
    private String receiverMail;

    @JsonIgnore
    private String apiToken; //FIXME 因在不同線程內, 會取不到 token, 故需在傳入時, 先行取得

    @JsonIgnore
    private boolean useDefReportType;

    @JsonIgnore
    @ApiModelProperty(value = "获取实际类型(前端不关注)，如果包含产品代号，返回产品_数据库类型，反之返回数据类型", hidden = true)
    public String getCurrentType() {
        if (useDefReportType) {
            // 回傳預設產品線
            return "9999_" + dbType;
        }
        if (StringUtils.isBlank(productCode)) {
            return dbType;
        }
        if ("MSSQL_ERP".equals(dbType)) {
            // MSSQL_ERP(版更評估報告) 會選擇多個產品線
            return dbType;
        }
        return productCode + "_" + dbType;
    }

    @JsonIgnore
    @ApiModelProperty(value = "克隆一份DbReportRecord的資料(前端不关注)，", hidden = true)
    public DbReportRecord cloneDbReportRecord() {
        // 避免拼發操作時, 用到不正確的 curProcDeviceId
        DbReportRecord newDbReportRecord = new DbReportRecord();
        BeanUtils.copyProperties(this, newDbReportRecord);
        return newDbReportRecord;
    }

    // SQLServer报告 特有分数 在伙伴平台 线索足迹使用
    private Double totalScore;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    private String projectName;

    public static DbReportRecord objectToDbReportRecord(Object object) {
        if (Objects.nonNull(object) && object instanceof DbReportRecord) {
            return (DbReportRecord) object;
        }
        return null;
    }
}
