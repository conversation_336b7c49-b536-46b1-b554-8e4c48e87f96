package com.digiwin.escloud.aioitms.report.service.db.impl;

import com.digiwin.escloud.aioitms.report.annotation.DbTypeCode;
import com.digiwin.escloud.aioitms.report.model.db.DbReportRecord;
import com.digiwin.escloud.aioitms.report.model.db.SqlServerErpReport;
import com.digiwin.escloud.aioitms.report.service.db.impl.product.SqlServerErpBase;
import com.digiwin.escloud.common.util.StringUtil;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.Callable;

@DbTypeCode("MSSQL_ERP")
@Service
public class SqlServerErpData extends SqlServerErpBase<SqlServerErpReport> {

    @Override
    protected List<Callable<Object>> getReportItems(DbReportRecord dbReportRecord) {
        List<Callable<Object>> reportItems = super.getReportItems(dbReportRecord);

        reportItems.add(buildEnvironment(dbReportRecord));
        reportItems.add(buildDatabase(dbReportRecord));

        return reportItems;
    }
}
