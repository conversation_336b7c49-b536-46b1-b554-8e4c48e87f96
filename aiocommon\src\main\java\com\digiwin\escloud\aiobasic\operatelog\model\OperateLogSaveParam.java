package com.digiwin.escloud.aiobasic.operatelog.model;

import com.digiwin.escloud.common.util.DateUtil;
import lombok.Data;

import java.time.LocalDateTime;

import static com.digiwin.escloud.common.util.DateUtil.DATE_TIME_FORMATTER;

@Data
public class OperateLogSaveParam {
    private String eid;
    private String processUserName;
    private String processUserId;
    private String startTime;
    private String endTime;
    private Integer startPage;
    private Integer endPage;
    private Integer sizePage;
    private String operateType;
    private String reportId;

    public void setStartTime(String startTime) {
        LocalDateTime localDateTime = DateUtil.tryParseLocalDateTime(startTime).orElse(null);
        this.startTime = DateUtil.getSomeDateFormatString(localDateTime, DATE_TIME_FORMATTER);
    }

    public void setEndTime(String endTime) {
        LocalDateTime localDateTime = DateUtil.tryParseLocalDateTime(endTime).orElse(null);
        this.endTime = DateUtil.getSomeDateFormatString(localDateTime, DATE_TIME_FORMATTER);
    }
}
