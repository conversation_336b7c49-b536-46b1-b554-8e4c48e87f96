package com.digiwin.escloud.aioitms.report.model.db;

import com.digiwin.escloud.aioitms.report.annotation.EsIndexCode;
import com.digiwin.escloud.aioitms.report.model.base.ProductDbReport;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Data
@Component
@NoArgsConstructor
@EsIndexCode("MSSQL_ERP") // 都要一樣才會配得到
@Document(indexName = "mssql_erp_" + "#{@dbEsAttribute.indexName}", shards = 3)
public class SqlServerErpReport extends ProductDbReport {
    private List<environment> environment;
    private List<database> database;

    @Data
    public static class environment {
        public String deviceId;
        public Map<String, Object> srvSpec;
        public List<Map<String, Object>> webSites;
        public List<Map<String, Object>> networks;
        public Map<String, Object> erpSetting;
    }

    @Data
    public static class database {
        private List<String> deviceList;
        private String sourceDbId;
        private Map<String, Object> serverInfo;
        private Map<String, Object> dbNameSize;
    }
}
