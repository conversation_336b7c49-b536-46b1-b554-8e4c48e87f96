package com.digiwin.escloud.issueservice.v3.controller;

import com.digiwin.escloud.issueservice.v3.service.IAIService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;


@Api(value = "AI相关")
@RestController
@RequestMapping("/api/ai")
@Slf4j
public class AIController {

    @Autowired
    IAIService aiService;

    @PostMapping(value = "/text/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public ResponseEntity<Flux<ServerSentEvent<Object>>> generateTextStream(
            @RequestParam(value = "askSessionId", defaultValue = "", required = false) String askSessionId,
            @RequestBody String askMessage) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("X-Accel-Buffering", "no");
        headers.add("Content-Type", "text/event-stream");
        headers.add("Cache-Control", "no-cache");
        return ResponseEntity.ok()
                .headers(headers)
                .body(aiService.generateTextStream(askMessage, askSessionId));
    }
}
