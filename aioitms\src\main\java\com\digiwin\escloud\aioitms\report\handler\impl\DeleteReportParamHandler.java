package com.digiwin.escloud.aioitms.report.handler.impl;

import com.digiwin.escloud.aiobasic.operatelog.model.OperateLogSaveParam;
import com.digiwin.escloud.aiobasic.operatelog.model.OperateLogType;
import com.digiwin.escloud.aioitms.report.handler.DbReportParamHandler;
import com.digiwin.escloud.common.util.DateUtil;
import com.digiwin.escloud.common.util.IntegerUtil;
import com.digiwin.escloud.common.util.LongUtil;
import com.digiwin.escloud.common.util.StringUtil;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.digiwin.escloud.common.util.DateUtil.DATE_TIME_FORMATTER;

@Component
public class DeleteReportParamHandler implements DbReportParamHandler {
    @Override
    public boolean isSupport(OperateLogType op) {
        return OperateLogType.Delete.equals(op);
    }

    @Override
    public void handle(List<Object> param, OperateLogSaveParam logParam) {
        String id = StringUtil.toString(param.get(0));
        Long eid = LongUtil.objectToLong(param.get(3));
        String userId  = StringUtil.toString(param.get(4));
        String userName = StringUtil.toString(param.get(5));
        Integer reportType = IntegerUtil.objectToInteger(param.get(6));

        // 檢查參數不為空及是否為版更評估(reportType = 4)
        if (IntegerUtil.isEmpty(reportType) || reportType != 4) {
            return;
        }

        logParam.setEid(StringUtil.toString(eid));
        logParam.setProcessUserId(userId);
        logParam.setProcessUserName(userName);
        logParam.setReportId(id);
        logParam.setOperateType(OperateLogType.Delete.getCode());
        logParam.setStartTime(DateUtil.getNowFormatString(DATE_TIME_FORMATTER));
    }
}
