package com.digiwin.escloud.aiobasic.edrv2.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.digiwin.escloud.aiobasic.edrv2.constant.Edrv2Const;
import com.digiwin.escloud.aiobasic.edrv2.dao.EdrApplicationMapper;
import com.digiwin.escloud.aiobasic.edrv2.model.EdrApplication;
import com.digiwin.escloud.aiobasic.edrv2.model.EdrApplicationEndpointParam;
import com.digiwin.escloud.aiobasic.edrv2.model.EdrApplicationParam;
import com.digiwin.escloud.aiobasic.edrv2.service.IEdrApplicationService;
import com.digiwin.escloud.aiobasic.edrv2.utils.Edrv2Util;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.response.PageInfo;
import com.digiwin.escloud.common.util.*;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.util.StringUtils;


import java.lang.reflect.Field;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.digiwin.escloud.aiobasic.edr.util.DateUtils.DATE_TIME_FORMATTER;
import static com.digiwin.escloud.aiobasic.edrv2.constant.Edrv2Const.*;

@Service
public class EdrApplicationService implements IEdrApplicationService {
    @Autowired
    EdrApplicationMapper edrApplicationMapper;
    @Autowired
    Edrv2Util edrv2Util;

    @Override
    public BaseResponse saveApplication(List<EdrApplication> edrApplicationList) {

        // region 參數檢查
        if (CollectionUtils.isEmpty(edrApplicationList)) {
            return BaseResponse.error(ResponseCode.EDR_APPLICATION_LIST_IS_EMPTY);
        }
        // endregion

        // 檢查同步數據是否不存在於原廠
        String siteId = edrApplicationList.stream().map(EdrApplication::getSiteId).findFirst().orElse(null);
        String eid = edrApplicationList.stream().map(EdrApplication::getEid).findFirst().orElse(null);

        if (StringUtils.hasText(siteId)) {
            EdrApplicationParam edrApplicationParam = new EdrApplicationParam(eid, siteId);

            List<EdrApplication> applicationList = edrApplicationMapper.getApplicationList(edrApplicationParam);

            // 取得不在最新清單中的applicationId
            List<String> finalEdrApplicationList = edrApplicationList.stream().map(EdrApplication::getApplicationId).collect(Collectors.toList());
            List<String> idsToDelete = applicationList.stream()
                    .filter(app -> !finalEdrApplicationList.contains(app.getApplicationId()))
                    .map(EdrApplication::getApplicationId)
                    .collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(idsToDelete)) {
                edrApplicationMapper.removeApplication(LongUtil.objectToLong(siteId), idsToDelete);
            }
        }

        // 保存application清單
        edrApplicationList = edrApplicationList.stream().peek(app -> {
            if (Objects.isNull(app.getId())) {
                app.setId(SnowFlake.getInstance().newIdStr());
            }
        }).collect(Collectors.toList());

        return BaseResponse.ok(edrApplicationMapper.upsertApplication(edrApplicationList));
    }

    @Override
    public BaseResponse getApplication(EdrApplicationParam param) {

        // 分頁查詢
        PageHelper.startPage(param.getPageNum(), param.getPageSize());
        List<EdrApplication> applicationList = edrApplicationMapper.getApplicationList(param);
        PageInfo<EdrApplication> pageInfo = new PageInfo(applicationList);

        Map<String, Object> res = convertToMap(pageInfo);

        // 處理更新時間
        Calendar now = Calendar.getInstance();

        // 取得syncLastDate
        Calendar syncLastDate = (Calendar) now.clone();
        syncLastDate.set(Calendar.DAY_OF_WEEK, Calendar.THURSDAY);
        syncLastDate.set(Calendar.HOUR_OF_DAY, 1);
        syncLastDate.set(Calendar.MINUTE, 30);
        syncLastDate.set(Calendar.SECOND, 0);

        if (now.before(syncLastDate)) {
            // 如果當前時間在本週四 13:30 之前，設為上週四 13:30
            syncLastDate.add(Calendar.WEEK_OF_YEAR, -1);
        }

        // 取得syncPreDate
        Calendar syncPreDate = (Calendar) syncLastDate.clone();
        syncPreDate.add(Calendar.WEEK_OF_YEAR, 1);

        res.put("syncLastDate", syncLastDate.getTime());
        res.put("syncPreDate", syncPreDate.getTime());

        return BaseResponse.ok(res);
    }

    @Override
    public BaseResponse getEndpointList(EdrApplicationEndpointParam param) {
        List<String> params = new ArrayList<>();
        if (ObjectUtils.isEmpty(param.getEid())) {
            params.add("eid");
        }
        if (ObjectUtils.isEmpty(param.getSiteId())) {
            params.add("siteId");
        }
        if (ObjectUtils.isEmpty(param.getApplicationId())) {
            params.add("applicationId");
        }
        if (ObjectUtils.isEmpty(param.getPageNum())) {
            params.add("pageNum");
        }
        if (ObjectUtils.isEmpty(param.getPageSize())) {
            params.add("pageSize");
        }
        if (!params.isEmpty()) {
            return BaseResponse.dynamicError(ResponseCode.PARAM_IS_EMPTY, params.stream().collect(Collectors.joining(", ")));
        }

        List<Map<String, Object>> dataList = new ArrayList<>();

        String accountId = edrApplicationMapper.getAccountIdsBySiteId(param.getSiteId());
        Boolean isNewSource = ACCOUNTID_NEW.equals(accountId);
        String url = isNewSource ? SENTINELONE_URI_NEW : SENTINELONE_URI;
        String token = isNewSource ? TOKEN_NEW : TOKEN;

        // 執行url請求
        List<Map<String, Object>> response = (List<Map<String, Object>>) edrv2Util.getEndpointList(url, token, accountId, param.getSiteId(), param.getApplicationId()).getData();
        if (response != null) {
            response.forEach(item -> {
                Map<String, Object> map = new HashMap<>();
                map.put("endpointId", item.get("endpointId"));
                map.put("endpointName", item.get("endpointName"));
                map.put("os", item.get("osType"));
                String lastScanDate = item.get("lastScanDate").toString();
                String formattedDate = DateTimeFormatter.ofPattern(DATE_TIME_FORMATTER)
                        .withZone(ZoneId.of("Asia/Taipei"))
                        .format(ZonedDateTime.parse(lastScanDate, DateTimeFormatter.ISO_OFFSET_DATE_TIME));
                map.put("lastScanDate", formattedDate);
                dataList.add(map);
            });
        }

        // 取得設備數量
        int total = dataList.size();

        // 將分頁資訊添加到res對象中
        Map<String, Object> res = new HashMap<>();
        res.put("total", total);
        res.put("pages", IntegerUtil.objectToInteger(Math.ceil((double) total / param.getPageSize())));

        int startIndex = PageUtil.getStart(param.getPageNum(), param.getPageSize());
        int endIndex = Math.min(startIndex + param.getPageSize(), total); // 避免 endIndex 超過資料集大小
        if (startIndex > (total - 1)) {
            dataList.clear();
        }

        List<Map<String, Object>> processedDataList = new ArrayList<>();
        if (dataList.size() > 0) {
            List<Map<String, Object>> filteredDataList = dataList.subList(startIndex, endIndex);

            // 對每個資料項目進行處理
            List<Map<String, Object>> agentIps = edrApplicationMapper.getAgentIp(filteredDataList.stream()
                    .map(data -> {
                        Map<String, Object> agentIpParams = new HashMap<>();
                        agentIpParams.put("agentId", data.get("endpointId"));
                        return agentIpParams;
                    })
                    .collect(Collectors.toList()));

            processedDataList = filteredDataList.stream().map(data -> {
                String lastReportedIP = agentIps.stream()
                        .filter(a -> data.get("endpointId").toString().equals(a.get("agentId").toString()) && a.get("lastReportedIP") != null)
                        .map(a -> Objects.toString(a.get("lastReportedIP")))
                        .findFirst().orElse("");

                data.put("lastReportedIP", lastReportedIP);
                data.remove("endpointId");
                return data;
            }).collect(Collectors.toList());
        }
        res.put("list", processedDataList);
        res.put("pageNum", param.getPageNum());
        res.put("pageSize", param.getPageSize());
        return BaseResponse.ok(res);
    }

    @Override
    public BaseResponse queryCriticalVulnerabilityTotalAmount(String eid, Date timeFrom, Date timeTo) {
        List<Map<String, Object>> totalAmountList = edrApplicationMapper.queryCriticalVulnerabilityTotalAmount(eid, timeFrom, timeTo);
        return BaseResponse.ok(totalAmountList);
    }

    @Override
    public BaseResponse queryAllSeverityTotalAmount(String eid, Date timeFrom, Date timeTo) {
        List<Map<String, Object>> totalAmountList = edrApplicationMapper.queryAllSeverityTotalAmount(eid, timeFrom, timeTo);

        // 若title為空值，轉為None回傳
        totalAmountList = totalAmountList.stream().map(data -> {
            if (StringUtils.isEmpty(data.getOrDefault("title", ""))) {
                data.put("title", "NONE");
            }
            return data;
        }).collect(Collectors.toList());

        return BaseResponse.ok(totalAmountList);
    }

    private static <T> Map<String, Object> convertToMap(T data) {
        Map<String, Object> map = new HashMap<>();
        try {
            Field[] fields = data.getClass().getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                map.put(field.getName(), field.get(data));
            }
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        return map;
    }
}
