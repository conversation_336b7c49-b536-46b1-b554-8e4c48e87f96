package com.digiwin.escloud.aiouser.service.impl;

import com.digiwin.escloud.aiouser.service.IVerificationService;
import com.digiwin.escloud.integration.api.emc.res.common.BaseResultRes;
import com.digiwin.escloud.integration.api.emc.res.common.EmcResultRes;
import com.digiwin.escloud.integration.service.EmcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
@RefreshScope
public class VerificationService implements IVerificationService {
    @Value("${digiwin.user.defaultlanguage}")
    private String defaultLanguage;
    @Autowired
    private EmcService emcService;

    @Override
    public BaseResultRes getVerificationCodeByEmail(String lang, String account, String scene) {
        if(StringUtils.isEmpty(lang)){
            lang = defaultLanguage;
        }
        return emcService.getVerificationCodeByEmail(lang, account, scene);
    }

    @Override
    public BaseResultRes getVerificationCodeByPhone(String lang, String phone, String scene) {
        if(StringUtils.isEmpty(lang)){
            lang = defaultLanguage;
        }
        return emcService.getVerificationCodeByPhone(lang, phone, scene);
    }

    @Override
    public EmcResultRes checkVerificationCode(String account, String scene, String code) {
        Map<String,String> map = new HashMap<>();
        map.put("account",account);
        map.put("scene",scene);
        map.put("identifyingCode",code);
        return emcService.checkVerificationCodeV2(map);
    }
}
