package com.digiwin.escloud.aiocmdb.asset.controller;

import com.digiwin.escloud.aiocmdb.asset.model.AssetCategory;
import com.digiwin.escloud.aiocmdb.asset.model.AssetCategoryClassification;
import com.digiwin.escloud.aiocmdb.asset.model.CmdbModelDataFieldRelationMapping;
import com.digiwin.escloud.aiocmdb.asset.service.IAssetCategoryService;
import com.digiwin.escloud.common.model.ResponseBase;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 资产类别分类表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Api(value = "资产类别管理", tags = {"资产类别管理"})
@RestController
@RequestMapping("/asset/assetCategory")
public class AssetCategoryController {

    @Autowired
    private IAssetCategoryService assetCategoryService;

    // ==================== AssetCategoryClassification 相关接口 ====================

    @ApiOperation("资产类别分类新增")
    @PostMapping("/classification/save")
    public ResponseBase saveAssetCategoryClassification(@RequestBody AssetCategoryClassification classification) {
        return assetCategoryService.saveAssetCategoryClassification(classification);
    }

    @ApiOperation("资产类别分类查询")
    @GetMapping("/classification/list")
    public ResponseBase getAssetCategoryClassificationList() {
        return assetCategoryService.getAssetCategoryClassificationList();
    }

    @ApiOperation("资产类别分类编辑")
    @PostMapping("/classification/update")
    public ResponseBase updateAssetCategoryClassification(@RequestBody AssetCategoryClassification classification) {
        return assetCategoryService.updateAssetCategoryClassification(classification);
    }

    @ApiOperation("资产类别分类删除")
    @PostMapping("/classification/delete/{id}")
    public ResponseBase deleteAssetCategoryClassification(@PathVariable Long id) {
        return assetCategoryService.deleteAssetCategoryClassification(id);
    }

    // ==================== AssetCategory 相关接口 ====================

    @ApiOperation("资产类别新增")
    @PostMapping("/save")
    public ResponseBase saveAssetCategory(@RequestBody AssetCategory category) {
        return assetCategoryService.saveAssetCategory(category);
    }

    @ApiOperation("资产类别编辑")
    @PostMapping("/update")
    public ResponseBase updateAssetCategory(@RequestBody AssetCategory category) {
        return assetCategoryService.updateAssetCategory(category);
    }

    @ApiOperation("资产类别状态编辑")
    @PostMapping("/updateStatus")
    public ResponseBase updateAssetCategoryStatus(@RequestParam Long id, @RequestParam String status) {
        return assetCategoryService.updateAssetCategoryStatus(id, status);
    }

    @ApiOperation("资产类别删除")
    @PostMapping("/delete/{id}")
    public ResponseBase deleteAssetCategory(@PathVariable Long id) {
        return assetCategoryService.deleteAssetCategory(id);
    }

    @ApiOperation("资产类别查询")
    @GetMapping("/list")
    public ResponseBase getAssetCategoryList(@RequestParam(required = false) Long classificationId) {
        return assetCategoryService.getAssetCategoryList(classificationId);
    }

    // ==================== CmdbModelDataFieldRelationMapping 相关接口 ====================

    @ApiOperation("模型数据字段关系映射新增")
    @PostMapping("/fieldMapping/save")
    public ResponseBase saveCmdbModelDataFieldRelationMapping(@RequestBody List<CmdbModelDataFieldRelationMapping> mappingList) {
        return assetCategoryService.saveCmdbModelDataFieldRelationMapping(mappingList);
    }

    @ApiOperation("模型数据字段关系映射查询")
    @GetMapping("/fieldMapping/list")
    public ResponseBase getCmdbModelDataFieldRelationMappingList(@RequestParam String targetModelCode) {
        return assetCategoryService.getCmdbModelDataFieldRelationMappingList(targetModelCode);
    }

    // ==================== AssetCategoryCodingRule 相关接口 ====================

    @ApiOperation("资产类别编码规则查询")
    @GetMapping("/codingRule/list")
    public ResponseBase getAllAssetCategoryCodingRule() {
        return assetCategoryService.getAllAssetCategoryCodingRule();
    }

}
