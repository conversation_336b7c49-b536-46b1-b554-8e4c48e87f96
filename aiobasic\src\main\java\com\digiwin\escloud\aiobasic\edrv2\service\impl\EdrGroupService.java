package com.digiwin.escloud.aiobasic.edrv2.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.digiwin.escloud.aiobasic.edr.model.edr.EdrOrgCollectorProcessRecordSaveDTO;
import com.digiwin.escloud.aiobasic.edr.service.edr.impl.EdrReportService;
import com.digiwin.escloud.aiobasic.edrv2.constant.Edrv2Const;
import com.digiwin.escloud.aiobasic.edrv2.dao.EdrAgentMapper;
import com.digiwin.escloud.aiobasic.edrv2.dao.EdrApplicationMapper;
import com.digiwin.escloud.aiobasic.edrv2.model.EdrAgentGroup;
import com.digiwin.escloud.aiobasic.edrv2.model.EdrCustomerOrg;
import com.digiwin.escloud.aiobasic.edrv2.model.EdrGroupMoveRequest;
import com.digiwin.escloud.aiobasic.edrv2.service.IEdrGroupService;
import com.digiwin.escloud.aiobasic.edrv2.utils.Edrv2Util;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.LongUtil;
import com.digiwin.escloud.common.util.RequestUtil;
import com.digiwin.escloud.common.util.StringUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.stream.Collectors;

import static com.digiwin.escloud.aiobasic.edrv2.constant.Edrv2Const.*;

@Slf4j
@Service
public class EdrGroupService implements IEdrGroupService {
    @Autowired
    RestTemplate restTemplate;
    @Autowired
    EdrReportService edrReportService;
    @Autowired
    EdrAgentMapper edrAgentMapper;
    @Autowired
    EdrApplicationMapper edrApplicationMapper;
    @Autowired
    Edrv2Util edrv2Util;
    private static final String serverId = "365750770160999"; // EDRv2專用
    private static final List<String> s1UrlList = Arrays.asList(SENTINELONE_URI, SENTINELONE_URI_NEW);

    @Override
    public BaseResponse getGroupList(List<String> siteIds) {

        List<Map<String, Object>> dataList = s1UrlList.stream().map(sourceUrl -> {
                    Boolean isNewSource = SENTINELONE_URI_NEW.equals(sourceUrl);
                    String url = sourceUrl + GROUPLIST_URI + "?limit=300&siteIds=" + String.join(",", siteIds);
                    String token = isNewSource ? TOKEN_NEW : TOKEN;

                    // http請求
                    HttpHeaders headers = new HttpHeaders();
                    headers.setContentType(MediaType.APPLICATION_JSON);
                    headers.set("Authorization", token);
                    HttpEntity<String> request = new HttpEntity<>(headers);
                    ResponseEntity<JSONObject> response = restTemplate.exchange(url, HttpMethod.GET, request, JSONObject.class);

                    // 解析數據
                    JSONObject jo = response.getBody();
                    if (Objects.isNull(jo)) {
                        return null;
                    }

                    return (List<Map<String, Object>>) jo.getOrDefault("data", null);
                })
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .collect(Collectors.toList());

        // 批量查詢對應accountId
        List<EdrCustomerOrg> accountIdList = edrApplicationMapper.getAccountIdListBySiteIds(siteIds);
        Map<String, String> accountIdMap = accountIdList.stream()
                .collect(Collectors.toMap(
                        org -> StringUtil.toString(org.getId()),
                        org -> StringUtil.toString(org.getAccountId())
                ));

        // 只取組織名稱及agent數量欄位
        List<Map<String, Object>> result = dataList.stream().map(data -> {
            String siteId = StringUtil.toString(data.get("siteId"));
            Map<String, Object> map = new HashMap<>();
            map.put("name", data.getOrDefault("name", ""));
            map.put("totalAgents", data.getOrDefault("totalAgents", ""));
            map.put("groupId", data.getOrDefault("id", ""));
            map.put("siteId", siteId);
            map.put("accountId", accountIdMap.get(siteId));

            return map;
        }).collect(Collectors.toList());

        return BaseResponse.ok(result);
    }

    @Override
    public BaseResponse moveGroup(EdrGroupMoveRequest groupMoveRequest) {
        try {
            String targetAccountId = groupMoveRequest.getTargetAccountId();

            // 合法條件：
            // 1. 來源都是新 → 目標是新
            // 2. 來源都是舊 → 目標不是新
            Boolean isValidMove = checkIsValidMove(groupMoveRequest, targetAccountId);
            if (!isValidMove) {
                return BaseResponse.error(ResponseCode.EDR_AGENT_ACCOUNT_ID_CANNOT_CROSS_SOURCES);
            }

            // 聚合agentIds
            List<String> agentIds = groupMoveRequest.getOriginalAgentGroupList().stream()
                    .flatMap(edrAgentGroup -> edrAgentGroup.getAgentIds().stream())
                    .collect(Collectors.toList());

            // 因已檢查不可跨來源且新來源只有一個accountId，僅需檢查targetAccountId是否為新來源accountId
            String sourceUrl = ACCOUNTID_NEW.equals(targetAccountId) ? SENTINELONE_URI_NEW : SENTINELONE_URI;
            String token = ACCOUNTID_NEW.equals(targetAccountId) ? Edrv2Const.TOKEN_NEW : Edrv2Const.TOKEN;

            String url = String.format(sourceUrl + MOVEGROUP_URI, groupMoveRequest.getGroupId());

            // 建立Body數據
            Map<String, Object> requestBody = buildRequestMap(agentIds);

            // 将 Map 转换为 JSON 字符串
            ObjectMapper objectMapper = new ObjectMapper();
            String jsonRequestBody = objectMapper.writeValueAsString(requestBody);

            // http請求
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", token);
            HttpEntity<String> request = new HttpEntity<>(jsonRequestBody, headers);
            ResponseEntity<JSONObject> response = restTemplate.exchange(url, HttpMethod.PUT, request, JSONObject.class);

            // 解析數據
            JSONObject jo = response.getBody();
            Map<String, Object> res = (Map<String, Object>) jo.getOrDefault("data", null);

            if (res.getOrDefault("agentsMoved", 0).equals(0)) {
                return BaseResponse.error(ResponseCode.EDR_MOVE_GROUP_FAILED_AGENTMODED_COUNT_0);
            }

            // 操作紀錄Log 及 調整同步數據
            groupMoveRequest.getOriginalAgentGroupList().forEach(originalAgentGroup -> {
                originalAgentGroup.getAgentIds().forEach(agentId -> {
                    edrAgentMapper.moveAgent(agentId, originalAgentGroup.getOrganization(), groupMoveRequest.getTargetGroup());

                    EdrOrgCollectorProcessRecordSaveDTO edrOrgCollectorProcessRecordSaveDTO = new EdrOrgCollectorProcessRecordSaveDTO();
                    Long eid = Optional.ofNullable(LongUtil.objectToLong(groupMoveRequest.getEid())).orElse(RequestUtil.getHeaderEid());
                    edrOrgCollectorProcessRecordSaveDTO.setEid(eid);
                    edrOrgCollectorProcessRecordSaveDTO.setOrgId(LongUtil.objectToLong(originalAgentGroup.getSiteId()));
                    edrOrgCollectorProcessRecordSaveDTO.setServerId(LongUtil.objectToLong(serverId));
                    edrOrgCollectorProcessRecordSaveDTO.setOrganization(originalAgentGroup.getOrganization());
                    edrOrgCollectorProcessRecordSaveDTO.setCollectorId(LongUtil.objectToLong(agentId));
                    edrOrgCollectorProcessRecordSaveDTO.setUserId(groupMoveRequest.getUserId());
                    edrOrgCollectorProcessRecordSaveDTO.setUserName(groupMoveRequest.getUserName());
                    edrOrgCollectorProcessRecordSaveDTO.setOperation("MOVE");
                    edrOrgCollectorProcessRecordSaveDTO.setOriginalGroup(originalAgentGroup.getOriginalGroup());
                    edrOrgCollectorProcessRecordSaveDTO.setCurrentGroup(groupMoveRequest.getTargetGroup());
                    edrReportService.saveProcessRecord(edrOrgCollectorProcessRecordSaveDTO);
                });
            });

            return BaseResponse.ok(res);

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private Map<String, Object> buildRequestMap(List<String> agentIds) {
        Map<String, Object> requestBody = new HashMap<>();
        Map<String, String> dataMap = new HashMap<>();
        dataMap.put("from", "NETWORK");

        Map<String, Object> filterMap = new HashMap<>();
        filterMap.put("agentIds", agentIds);

        requestBody.put("data", dataMap);
        requestBody.put("filter", filterMap);

        return requestBody;
    }

    private Boolean checkIsValidMove(EdrGroupMoveRequest groupMoveRequest, String targetAccountId) {
        Set<String> sourceAccountIds = groupMoveRequest.getOriginalAgentGroupList().stream()
                .map(EdrAgentGroup::getAccountId)
                .collect(Collectors.toSet());

        // 檢查是否為多來源，為多來源則報錯誤不可執行
        // 是否來源全部為新帳號
        boolean isAllNew = sourceAccountIds.size() == 1 && sourceAccountIds.contains(ACCOUNTID_NEW);

        // 是否來源全部為舊帳號(全部不是新帳號)
        boolean isAllOld = !sourceAccountIds.contains(ACCOUNTID_NEW);

        // 合法條件：
        // 1. 來源都是新 → 目標是新
        // 2. 來源都是舊 → 目標不是新
        return (isAllNew && ACCOUNTID_NEW.equals(targetAccountId)) || (isAllOld && !ACCOUNTID_NEW.equals(targetAccountId));
    }
}
