package com.digiwin.escloud.aioitms.report.service.db.impl.product;

import com.digiwin.escloud.aioitms.device.dao.DeviceV2Mapper;
import com.digiwin.escloud.aioitms.device.model.AiopsKitDeviceDataSource;
import com.digiwin.escloud.aioitms.device.model.DeviceDataSourceInfo;
import com.digiwin.escloud.aioitms.report.model.db.DbReportRecord;
import com.digiwin.escloud.aioitms.report.model.db.SqlServerErpReport;
import com.digiwin.escloud.aioitms.report.service.db.AdvBaseDbData;
import com.digiwin.escloud.common.util.LongUtil;
import com.digiwin.escloud.common.util.StringUtil;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.client.support.BasicAuthenticationInterceptor;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.function.Supplier;
import java.util.stream.Collectors;

public class SqlServerErpBase<T extends SqlServerErpReport> extends AdvBaseDbData<T> {

    @Autowired
    private DeviceV2Mapper deviceV2Mapper;

    protected Callable<Object> buildEnvironment(DbReportRecord dbReportRecord) {
        List<SqlServerErpReport.environment> environmentList = new ArrayList<>();
        String eid = StringUtil.toString(dbReportRecord.getEid());
        // 取得設備 deviceId, 以及相對應 MSSQL -> dbId
        List<Map<String, Object>> deviceIdAndDbIdList = getDeviceIdAndDbId(dbReportRecord.getDeviceIdList());

        deviceIdAndDbIdList.forEach(item -> {
            String deviceId = StringUtil.toString(item.get("deviceId"));
            String sourceDbId = StringUtil.toString(item.get("scoreDbId"));

            // 加入 environment 資料
            SqlServerErpReport.environment environment = new SqlServerErpReport.environment();
            environment.setDeviceId(deviceId);
            environment.setSrvSpec(getSrvSpec(eid, deviceId, sourceDbId));
            environment.setWebSites(getWebSites(eid, deviceId));
            environment.setNetworks(getNetworks(eid, deviceId));
            environment.setErpSetting(getErpSetting(eid, deviceId, sourceDbId));
            environmentList.add(environment);
        });

        return () -> environmentList;
    }

    protected Callable<Object> buildDatabase(DbReportRecord dbReportRecord) {
        List<SqlServerErpReport.database> databaseList = new ArrayList<>();
        String eid = StringUtil.toString(dbReportRecord.getEid());
        // 取得設備底下 MSSQL -> dbId
        List<String> dbIdList = getDeviceIdAndDbId(dbReportRecord.getDeviceIdList()).stream()
                .map(item -> StringUtil.toString(item.get("scoreDbId")))
                .filter(StringUtil::isNotEmpty)
                .distinct()
                .collect(Collectors.toList());

        dbIdList.forEach(dbId -> {
            // 加入 database 資料
            SqlServerErpReport.database database = new SqlServerErpReport.database();
            database.setSourceDbId(dbId);
            database.setServerInfo(getServerInfo(eid, dbId));
            database.setDbNameSize(getDbNameSize(eid, dbId));
            databaseList.add(database);
        });

        return () -> databaseList;
    }

    private Map<String, Object> getSrvSpec(String eid, String deviceId, String SourceDbId) {
        Map<String, Object> srvSpec = new HashMap<>();
        srvSpec.put("osInfo", getOsInfo(eid, deviceId, SourceDbId));
        srvSpec.put("hardwareInfo", getHardwareInfo(eid, deviceId));

        return srvSpec;
    }

    private List<Map<String, Object>> getWebSites(String eid, String deviceId) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        sb.append("SiteName, propertyName, propertyLocation, state, bindings ");
        sb.append("from servicecloud.Windows_IISSitesInfo_sr_primary ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("deviceId = '").append(deviceId).append("' and ");
        sb.append("collectedTime = (");
        sb.append("select max(collectedTime) ");
        sb.append("from servicecloud.Windows_IISSitesInfo_sr_primary ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("deviceId = '").append(deviceId).append("'");
        sb.append(") ");
        String sql = StringUtil.toString(sb);

        return bigDataUtil.srQuery(sql);
    }

    private List<Map<String, Object>> getNetworks(String eid, String deviceId) {
        // todo 在 6/27 後驗證
        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        sb.append("get_json_string(parse_json(network_adapter_info), '$.network_adapter_name') as interfaceAlias, ");
        sb.append("get_json_string(parse_json(network_adapter_info), '$.network_adapter_mac') as macAddress, ");
        sb.append("get_json_string(parse_json(network_adapter_info), '$.network_adapter_dhcp_enabled') as dhcpEnabled, ");
        sb.append("get_json_string(parse_json(network_adapter_info), '$.network_adapter_ip') as ipv4Address, ");
        sb.append("get_json_string(parse_json(network_adapter_info), '$.network_adapter_subnet_mask') as subnetMask, ");
        sb.append("get_json_string(parse_json(network_adapter_info), '$.network_adapter_default_gateway') as defaultGateway, ");
        sb.append("get_json_string(parse_json(network_adapter_info), '$.network_adapter_dns_servers') as dnsServers ");
        sb.append("from servicecloud.DeviceAdvancedInfoCollected_sr_primary ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("deviceId = '").append(deviceId).append("' and ");
        sb.append("collectedTime = (");
        sb.append("select max(collectedTime) ");
        sb.append("from servicecloud.DeviceAdvancedInfoCollected_sr_primary ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("deviceId = '").append(deviceId).append("'");
        sb.append(") ");
        String sql = StringUtil.toString(sb);

        return bigDataUtil.srQuery(sql);
    }

    private Map<String, Object> getErpSetting(String eid, String deviceId, String sourceDbId) {
        Map<String, Object> erpSetting = new HashMap<>();
        erpSetting.put("software", getSoftware(eid, deviceId, sourceDbId));
        erpSetting.put("modules", getModules(eid, deviceId, sourceDbId));
        erpSetting.put("companyNameSize", getCompanyNameSize(eid, deviceId, sourceDbId));

        return erpSetting;
    }

    private Map<String, Object> getServerInfo(String eid, String sourceDbId) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        sb.append("server_name, concat(product, ' ', edition) as product_name, platform, product_version, language, physical_memory_total_gb as physical_memory, ");
        sb.append("processor_count as processors, collation, datapath, logPath, backupPath, ");
        sb.append("null as sqlBinarySort ");
        sb.append("from servicecloud.MsSQLInstanceInfo msli ");
        sb.append("left join (select * from servicecloud.MsSQL_PresetPathInfo where (source_db_id, collectedTime) in ");
        sb.append("(select source_db_id, MAX(collectedTime) from servicecloud.MsSQL_PresetPathInfo where eid = '").append(eid).append("' and source_db_id = '").append(sourceDbId).append("' group by source_db_id)) mspp ");
        sb.append("on msli.eid = mspp.eid and msli.source_db_id = mspp.source_db_id ");
        sb.append("where ");
        sb.append("msli.eid = '").append(eid).append("' and ");
        sb.append("msli.source_db_id = '").append(sourceDbId).append("' and ");
        sb.append("msli.collectedTime = (");
        sb.append("select max(collectedTime) ");
        sb.append("from servicecloud.MsSQLInstanceInfo ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("source_db_id = '").append(sourceDbId).append("'");
        sb.append(") ");
        String sql = StringUtil.toString(sb);

        List<Map<String, Object>> data = bigDataUtil.srQuery(sql);
        if (data.isEmpty()) {
            return Collections.emptyMap();
        }

        return data.get(0);
    }

    private Map<String, Object> getDbNameSize(String eid, String sourceDbId) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        sb.append("database_name as database_id, ");
        sb.append("(size / 1024) as database_size, ");
        sb.append("physical_name, ");
        sb.append("case ");
        sb.append("when disk_fstype = 0 then 'ROWS' ");
        sb.append("when disk_fstype = 1 then 'LOG' ");
        sb.append("when disk_fstype = 2 then 'FILESTREAM' ");
        sb.append("when disk_fstype = 3 then 'FULLTEXT' ");
        sb.append("end as type_desc ");
        sb.append("from servicecloud.MsSQLDataBaseFileCollected ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("source_db_id = '").append(sourceDbId).append("' and ");
        sb.append("collectedTime = (");
        sb.append("select max(collectedTime) ");
        sb.append("from servicecloud.MsSQLDataBaseFileCollected ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("source_db_id = '").append(sourceDbId).append("'");
        sb.append(") order by binary(lower(database_name)) ");
        String sql = StringUtil.toString(sb);

        List<Map<String, Object>> data = bigDataUtil.srQuery(sql);

        Map<String, Object> map = new HashMap<>();
        map.put("totalCount", data.size());
        map.put("totalSize", data.stream().map(m -> new BigDecimal(ObjectUtils.toString(m.get("database_size")))).reduce(BigDecimal.ZERO, BigDecimal::add));
        map.put("list", data);

        return map;
    }

    @Override
    protected Map<String, Object> getOtherDeviceInfo(String appCode, long eid, String deviceId, String dbId) {
        return Collections.emptyMap();
    }

    private Map<String, Object> getOsInfo(String eid, String deviceId, String SourceDbId) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        sb.append("get_json_string(parse_json(domain_info), '$.domain_machine_name') as domain_machine_name, ");
        sb.append("null as db_or_ap, ");
        sb.append("get_json_string(parse_json(domain_info), '$.domain_groups') as domain_groups, ");
        sb.append("get_json_string(parse_json(os_info), '$.os_product_name') as os_product_name, ");
        sb.append("case ");
        sb.append("when get_json_string(parse_json(os_info), '$.os_bits') = 'win64' then '64-bit' ");
        sb.append("when get_json_string(parse_json(os_info), '$.os_bits') = 'win32' then '32-bit' ");
        sb.append("else get_json_string(parse_json(os_info), '$.os_bits') end as os_bits, ");
        sb.append("get_json_string(parse_json(processor_info), '$.processor_name') as processor_name, ");
        sb.append("get_json_string(parse_json(physical_memory_info), '$.[0].physical_memory_total_gb') as physical_memory_total_gb, ");
        sb.append("ipAddress, concat(msli.product, ' ', msli.product_version) as product_version ");
        sb.append("from servicecloud.DeviceAdvancedInfoCollected_sr_primary daicsp ");
        sb.append("left join servicecloud.AiopsDevice ad ");
        sb.append("on daicsp.deviceId = ad.deviceId ");
        sb.append("left join servicecloud.MsSQLInstanceInfo msli ");
        sb.append("on daicsp.deviceId = msli.deviceId ");
        sb.append("and msli.source_db_id = '").append(SourceDbId).append("' ");
        sb.append("where ");
        sb.append("daicsp.eid = '").append(eid).append("' and ");
        sb.append("daicsp.deviceId = '").append(deviceId).append("'");
        String sql = StringUtil.toString(sb);

        List<Map<String, Object>> data = bigDataUtil.srQuery(sql);
        if (data.isEmpty()) {
            return Collections.emptyMap();
        }

        return data.get(0);
    }

    private List<Map<String, Object>> getHardwareInfo(String eid, String deviceId) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        sb.append("disk__path, disk__fstype, disk__total, disk__free, disk__used, disk__used_percent ");
        sb.append("from servicecloud.DiskCollected_sr_duplicate ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("deviceId = '").append(deviceId).append("' and ");
        sb.append("collectedTime = (");
        sb.append("select max(collectedTime) ");
        sb.append("from servicecloud.DiskCollected_sr_duplicate ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("deviceId = '").append(deviceId).append("'");
        sb.append(") ");
        String sql = StringUtil.toString(sb);

        return bigDataUtil.srQuery(sql);
    }

    private List<Map<String, Object>> getDeviceIdAndDbId(List<String> deviceIdList) {
        List<Map<String, Object>> result = new ArrayList<>();

        deviceIdList.forEach(deviceId -> {
            Map<String, Object> map = new HashMap<>();
            map.put("deviceId", deviceId);
            deviceV2Mapper.selectDeviceDataSourceByDeviceId(deviceId).stream()
                    .filter(data -> "mssql_v2".equals(data.getDbType()) && !data.getIsDelete())
                    .forEach(data -> map.put("scoreDbId", data.getDbId()));
            if (!map.containsKey("scoreDbId")) {
                map.put("scoreDbId", "");
            }
            result.add(map);
        });

        return result;
    }

    private Map<String, Object> getSoftware(String eid, String deviceId, String sourcedbId) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        sb.append("database_erp as productName, productVersion, '' as licensesCount, isExistERP, '' as posLicensesCount, ");
        sb.append("rtBuilderDataOption, rtBuilderDataPath, ");
        sb.append("case when admnt_id = 'VoucherServer' then admnt_setting else datFromServerManagerValue end as datFromServerManagerValue, ");
        sb.append("case when admnt_id = 'VoucherServer' then 'WebDAV' else datFromServerManagerEnable end as datFromServerManagerEnable, ");
        sb.append("case when admnt_id = 'DMSServer' then admnt_setting else fileServerUNCPath end as fileServerUNCPath, ");
        sb.append("case when admnt_id = 'DMSServer' then admnt_setting else fileServerUNCOption end as fileServerUNCOption, ");
        sb.append("case when admnt_id = 'DMSServer' then admnt_setting else relationDataPath end as relationDataPath, ");
        sb.append("case when admnt_id = 'DMSServer' then 'WebDAV' else relationDataOption end as relationDataOption, ");
        sb.append("aus_ip ");
        sb.append("from servicecloud.ERP_SoftSetting_sr_primary esssp ");
        sb.append("left join (select * from servicecloud.ERP_WebDAVInfo_sr_primary where (source_db_id, collectedTime) ");
        sb.append("in (select source_db_id, MAX(collectedTime) from servicecloud.ERP_WebDAVInfo_sr_primary ");
        sb.append("where eid = '").append(eid).append("' and source_db_id = '").append(sourcedbId).append("' group by source_db_id)) eaisp ");
        sb.append("on esssp.source_db_id = eaisp.source_db_id ");
        sb.append("where ");
        sb.append("esssp.eid = '").append(eid).append("' and ");
        sb.append("esssp.deviceId = '").append(deviceId).append("' and ");
        sb.append("esssp.collectedTime = (");
        sb.append("select max(collectedTime) ");
        sb.append("from servicecloud.ERP_SoftSetting_sr_primary ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("deviceId = '").append(deviceId).append("'");
        sb.append(") ");
        String sql = StringUtil.toString(sb);

        List<Map<String, Object>> data = bigDataUtil.srQuery(sql);
        if (data.isEmpty()) {
            return Collections.emptyMap();
        }

        return data.get(0);
    }

    private Map<String, Object> getModules(String eid, String deviceId, String sourceDbId) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        sb.append("moduleName as title, moduleCode as code");
        sb.append("from servicecloud.DatabaseModuleInfo_sr_primary ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("deviceId = '").append(deviceId).append("' and ");
        sb.append("source_db_id = '").append(sourceDbId).append("' and ");
        sb.append("collectedTime = (");
        sb.append("select max(collectedTime) ");
        sb.append("from servicecloud.DatabaseModuleInfo_sr_primary ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("deviceId = '").append(deviceId).append("' and ");
        sb.append("source_db_id = '").append(sourceDbId).append("'");
        sb.append(") ");
        String sql = StringUtil.toString(sb);

        List<Map<String, Object>> data = bigDataUtil.srQuery(sql);

        Map<String, Object> map = new HashMap<>();
        map.put("purchased", data);

        // 設定預設值
        Supplier<Map<String, Object>> createDefaultContent = () -> {
            Map<String, Object> content = new HashMap<>();
            content.put("isWith", null);
            content.put("comment", null);
            return content;
        };

        // 判定是否有 EIN 模組
        Map<String, Object> eInvoiceContent = createDefaultContent.get();
        boolean isWith = data.stream().anyMatch(item -> "EIN".equals(item.get("code")));
        eInvoiceContent.put("isWith", isWith);

        map.put("eInvoice", eInvoiceContent);
        map.put("eInvoiceUsed", createDefaultContent.get());
        map.put("useTransmit", createDefaultContent.get());
        map.put("triangleTrade", createDefaultContent.get());
        map.put("triangleTradeUsed", createDefaultContent.get());

        return map;
    }

    private Map<String, Object> getCompanyNameSize(String eid, String deviceId, String sourceDbId) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        sb.append("database_erp, database_id, erp_dscmb002, database_size ");
        sb.append("from servicecloud.ERP_CompanyDatabaseInfo_sr_primary ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("deviceId = '").append(deviceId).append("' and ");
        sb.append("source_db_id = '").append(sourceDbId).append("' and ");
        sb.append("collectedTime = (");
        sb.append("select max(collectedTime) ");
        sb.append("from servicecloud.ERP_CompanyDatabaseInfo_sr_primary ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("source_db_id = '").append(sourceDbId).append("' and ");
        sb.append("deviceId = '").append(deviceId).append("'");
        sb.append(") ");
        String sql = StringUtil.toString(sb);

        List<Map<String, Object>> data = bigDataUtil.srQuery(sql);

        Map<String, Object> map = new HashMap<>();
        map.put("totalCount", data.size());
        map.put("totalSize", data.stream().map(m -> new BigDecimal(ObjectUtils.toString(m.get("database_size")))).reduce(BigDecimal.ZERO, BigDecimal::add));
        map.put("list", data);

        return map;
    }
}
