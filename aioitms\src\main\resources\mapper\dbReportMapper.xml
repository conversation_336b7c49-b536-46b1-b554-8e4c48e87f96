<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.digiwin.escloud.aioitms.report.dao.DbReportMapper">

    <select id="getDbReports" resultType="com.digiwin.escloud.aioitms.report.model.db.DbReportRecord">
        SELECT * FROM (SELECT adrr.id, adrr.appCode, adrr.sid, adrr.eid, adrr.serviceCode, adrr.customerName, adrr.customerFullName,
            adrr.reportDate, adrr.userId,
            <choose>
                <when test="isMis">
                    CASE
                        WHEN adrr.userId LIKE '%@digiwin.com' THEN
                        <choose>
                            <when test="area == 'TW'">
                                '鼎新人員'
                            </when>
                            <otherwise>
                                '鼎捷人员'
                            </otherwise>
                        </choose>
                        WHEN adrr.userId = '' THEN
                        <choose>
                            <when test="area == 'TW'">
                                'Ai智管家'
                            </when>
                            <otherwise>
                                '智管家'
                            </otherwise>
                        </choose>
                        ELSE adrr.userName
                    END userName,
                </when>
                <otherwise>
                    adrr.userName,
                </otherwise>
            </choose>
            adrr.dataStartDate, adrr.dataEndDate, adrr.reportGenerateTime,
            adrr.reportType, adrr.deviceId, adrr.reportStatus, adrr.dbType, adrr.dbInstanceName, adrr.dbServerName,
            adrr.dbId, adrr.dbName, adrr.deviceIds, adrr.deviceNames, adrr.productCode, GROUP_CONCAT(errslr.receiverMail SEPARATOR ',') AS receiverMail,
            adrr.edrReportRecordId, adrr.reportSender, adrr.reportSendTime as misReportSendTime,
            errsl.sendTime as reportSendTime,adrr.createTime
        FROM aiops_db_report_record adrr
        LEFT JOIN edr_report_record err ON err.id = adrr.edrReportRecordId
        LEFT JOIN edr_report_record_send_log errsl ON errsl.errId = err.id
        LEFT JOIN edr_report_record_send_log_receivers errslr ON errslr.errslId = errsl.Id
        <where>
            reportType=#{reportType}
            <if test="eid > 0">
                and adrr.eid=#{eid}
            </if>
            <if test="content != null and content != ''">
                and (adrr.serviceCode like CONCAT('%',#{content},'%') or adrr.customerName like CONCAT('%',#{content},'%'))
            </if>
            <if test="reportStartDate != null">
                and adrr.reportDate <![CDATA[>=]]> #{reportStartDate}
            </if>
            <if test="reportEndDate != null">
                and adrr.reportDate <![CDATA[<=]]> #{reportEndDate}
            </if>
            <if test="reportGenerateStartDate != null">
                and adrr.reportGenerateTime <![CDATA[>=]]> #{reportGenerateStartDate}
            </if>
            <if test="reportGenerateEndDate != null">
                and adrr.reportGenerateTime <![CDATA[<=]]> #{reportGenerateEndDate}
            </if>
            <if test="dbType != null and dbType != ''">
                <choose>
                    <when test="dbType == 'ORACLE'">
                        and adrr.dbType in ('ORACLE','ORACLE_V2')
                    </when>
                    <otherwise>
                        and adrr.dbType=#{dbType}
                    </otherwise>
                </choose>
            </if>
            <if test="reportStatus != null and reportStatus.length > 0">
                and adrr.reportStatus in
                <foreach collection="reportStatus" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="productCodes != null and productCodes.length > 0">
                and (
                <foreach collection="productCodes" item="item" separator=" OR ">
                    FIND_IN_SET(#{item}, adrr.productCode) > 0
                </foreach>
                )
            </if>
            <if test="deviceIds != null and deviceIds.length > 0">
                <foreach collection="deviceIds" item="item" open="and (" separator=" or " close=")">
                    adrr.deviceIds like CONCAT('%',#{item},'%')
                </foreach>
            </if>
            <if test="eidList != null and eidList.size() > 0">
                <foreach collection="eidList" item="item" open=" and adrr.eid in (" separator=" , " close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        group by adrr.id
        ) AS T
        WHERE
            1=1
        <if test="userName != null and userName != ''">
            and userName like CONCAT('%',#{userName},'%')
        </if>
        <if test="isMis">
            AND ((userId NOT LIKE '%@digiwin.com' AND userId != '')
            OR ((userId LIKE '%@digiwin.com' OR userId = '') AND reportStatus = 3))
        </if>
        order by reportDate desc, reportGenerateTime desc
    </select>

    <select id="selectDbReportById" resultType="com.digiwin.escloud.aioitms.report.model.db.DbReportRecord">
    SELECT a.* FROM aiops_db_report_record a where a.id= #{id}
    </select>

    <insert id="saveDbReportRecord" parameterType="com.digiwin.escloud.aioitms.report.model.db.DbReportRecord">
        INSERT INTO aiops_db_report_record(id, appCode, reportType, sid, eid, deviceId, serviceCode, customerName,customerFullName,
                                           reportDate,reportStatus, userId, userName, dataStartDate, dataEndDate, reportGenerateTime,
                                           dbType, dbInstanceName, dbServerName, dbId, dbName, deviceIds, productCode)
        VALUES (#{id}, #{appCode}, #{reportType}, #{sid}, #{eid}, #{deviceId}, #{serviceCode}, #{customerName}, #{customerFullName},#{reportDate},
                #{reportStatus}, #{userId}, #{userName}, #{dataStartDate}, #{dataEndDate}, #{reportGenerateTime}, #{dbType},
                #{dbInstanceName}, #{dbServerName}, #{dbId}, #{dbName}, #{deviceIds}, #{productCode})
        ON DUPLICATE KEY UPDATE reportGenerateTime=VALUES(reportGenerateTime)
    </insert>

    <select id="getDbReportStatus" resultType="java.lang.Integer">
        SELECT reportStatus FROM aiops_db_report_record
        where id = #{id}
    </select>

    <update id="updateDbReportStatus">
        update aiops_db_report_record
        set reportStatus = #{reportStatus}, reportSendTime = #{reportSendTime}
        <if test="edrReportRecordId != null">
            , edrReportRecordId = #{edrReportRecordId}
        </if>
        <if test="reportSender != null">
            , reportSender = #{reportSender}
        </if>
        where id = #{id}
    </update>

    <update id="updateDeviceNames">
        update aiops_db_report_record
        set deviceNames = #{deviceNames}
        where id = #{id}
    </update>

    <delete id="deleteById">
        delete from aiops_db_report_record where id = #{id}
    </delete>

    <insert id="readDbReport" parameterType="com.digiwin.escloud.aioitms.report.model.db.LogDbRecord">
        INSERT INTO log_db_record(id, sid, eid, dbReportId, serviceCode, userId, userName, readTime)
        VALUES (#{id}, #{sid}, #{eid}, #{dbReportId}, #{serviceCode}, #{userId}, #{userName}, #{readTime})
    </insert>

    <select id="getReadLogs" resultType="com.digiwin.escloud.aioitms.report.model.db.LogDbRecord">
        SELECT * FROM log_db_record
        <where>
            dbReportId=#{id}
        </where>
        order by readTime desc
    </select>

    <select id="getModuleUsedCount" resultType="java.lang.Integer">
        SELECT tmcd.usedCount
        from tenant_module_contract tmc
        left join tenant_module_contract_detail tmcd on tmc.id=tmcd.tmcId
        left join supplier_aiops_module_class samc on samc.id=tmcd.samcId
        left join supplier_aiops_module_class_detail samcd on samc.id=samcd.samcId
        <where>
            tmc.sid=#{sid} and tmc.eid=#{eid}
            and samcd.aiopsItemType=#{aiopsItemType} and samcd.aiopsItem=#{aiopsItem}
        </where>
        limit 1;
    </select>

    <!-- 取得參考值的資料 -->
    <select id="getReferenceValueData" resultType="com.digiwin.escloud.aioitms.report.model.ReportReferenceValue">
        select id, reportType, appType, platform, itemCode, itemName, referenceValue, actualValueFieldJson, checkTagIdJson
             , checkTagValueJson, actualValueDefaultValue, abnormalDescription
             , rrvtt.checkTagTable, rrvtt.checkTagDeviceTable, rrvtt.checkTagCondTemplateScript, rrvtt.checkTagCondFieldJson
             , rrv.isFindLatestTag
        from report_reference_value rrv
        left join report_reference_value_tag_type rrvtt on rrv.checkTagType = rrvtt.checkTagType
        where reportType = #{reportType}
    </select>

    <select id="getReportType" resultType="String">
        SELECT reportType
        FROM report_reference_value
        WHERE reportType NOT IN ('9999_MSSQL','9999_ORACLE')
        GROUP by reportType
    </select>

    <select id="checkBiosManufacturer" resultType="java.lang.Integer">
        select count(*) from db_host_bios_manufacturer a where a.code = #{bios_manufacturer} and a.enable = 1
    </select>
    <select id="checkBiosFamily" resultType="java.lang.Integer">
        select count(*) from db_host_bios_family a where a.code = #{bios_family} and a.enable = 1
    </select>
    
    <select id="getPlatform" resultType="list">
        SELECT
            platform
        FROM aiops_device
        WHERE
        <if test="deviceIdList != null and deviceIdList.size() > 0">
            <foreach collection="deviceIdList" item="item" open=" and deviceId in (" separator=" , " close=")">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>