package com.digiwin.escloud.aiobasic.edrv2.constant;

public class Edrv2Const {
    public final static Long SERVERID = 365750770160999L;
    public final static String ACCOUNTID_NEW = "2235445651875227142";

    public final static String TOKEN = "ApiToken eyJraWQiOiJ0b2tlblNpZ25pbmciLCJhbGciOiJFUzI1NiJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************.0gKKBYkhlEmyyRQSa-J630x67Lqg9WgeO5xR3FhakA1y5bQ2R5TwMGMrtVwx8utHCBmNcgkoQC5agn6BMAvSSA";
    public final static String TOKEN_NEW = "ApiToken eyJraWQiOiJhcC1zb3V0aGVhc3QtMS1wcm9kLTAiLCJhbGciOiJFUzI1NiJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************.VJqzCFH0-zxjobOZ26UOAC6UL_TxJy4tqD4hlaS-nx59IbwZhrlvUGRMc4qmlctEK6PgPGntcGeai1GpgFQOhQ";
    private final static String ceAccessKey = "2780f1ea45724f0eb8bf4f7a4eeda246";
    public final static String SENTINELONE_URI = "https://usea1-cyberforce-msp01.sentinelone.net/";
    public final static String SENTINELONE_URI_NEW = "https://apne1-corecloud.sentinelone.net/";
    public final static String IPASS_SENTINELONE_EDR_URI = "https://api.solinkup.com/trigger/apimanage/f9c8c635ad68429d9670c1aabafe09f9/SentinelOneEDR";

    public final static String GROUPLIST_URI = "web/api/v2.1/groups";
    public final static String MOVEGROUP_URI = "web/api/v2.1/groups/%s/move-agents";
    public final static String ENABLE_AGENT_URI = "web/api/v2.1/agents/actions/enable-agent";
    public final static String DISABLE_AGENT_URI = "web/api/v2.1/agents/actions/disable-agent";
    public final static String AGENT_COUNT_URI = "web/api/v2.1/agents/count";
    public final static String THREAT_RELEASE_URI = "web/api/v2.1/exclusions";
    public final static String THREAT_MITIGATE_UNGUARANTINE_URI = "web/api/v2.1/threats/mitigate/un-quarantine";
    public final static String THREAT_RELEASE_CANCEL_URI = "web/api/v2.1/exclusions";
    public final static String APPLICATION_ENDPOINTS_URI = "web/api/v2.1/application-management/risks/endpoints";
    public final static String REMOVE_AGENT_URI = "web/api/v2.1/agents/actions/decommission";
    public final static String THREAT_CHANGE_STATUS_URI = "web/api/v2.1/threats/analyst-verdict";
    public final static String INITIATE_SCAN_URI = "web/api/v2.1/agents/actions/initiate-scan";
    public final static String ABORT_SCAN_URI = "web/api/v2.1/agents/actions/abort-scan";
    public final static String NETWORK_CONNECT_URI = "web/api/v2.1/agents/actions/connect";
    public final static String NETWORK_DISCONNECT_URI = "web/api/v2.1/agents/actions/disconnect";
    public final static String APPROVE_UNINSTALL = "web/api/v2.1/agents/actions/approve-uninstall";
    public final static String REJECT_UNINSTALL = "web/api/v2.1/agents/actions/reject-uninstall";


    public final static String SYNC_THREATS_URI = IPASS_SENTINELONE_EDR_URI + "/SyncThreats?ceAccessKey=" + ceAccessKey;
    public final static String SYNC_WHITELIST_URI = IPASS_SENTINELONE_EDR_URI + "/SyncWhiteList?ceAccessKey=" + ceAccessKey;
    public final static String SYNC_APPLICATION_URI = IPASS_SENTINELONE_EDR_URI + "/SyncApplication?ceAccessKey=" + ceAccessKey;
    public final static String SYNC_AGENT_URI = IPASS_SENTINELONE_EDR_URI + "/SyncAgent?ceAccessKey=" + ceAccessKey;
    public final static String SYNC_AGENT_BY_AGENTID_URI = IPASS_SENTINELONE_EDR_URI + "/SyncAgentByAgentId?ceAccessKey=" + ceAccessKey;
}
