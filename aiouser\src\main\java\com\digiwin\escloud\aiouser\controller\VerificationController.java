package com.digiwin.escloud.aiouser.controller;

import com.digiwin.escloud.aiouser.model.verification.VerificationCodeGetResponse;
import com.digiwin.escloud.aiouser.service.IVerificationService;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.integration.api.emc.res.common.BaseResultRes;
import com.digiwin.escloud.integration.api.emc.res.common.EmcResultRes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(value = "验证码接口", tags = {"验证码接口"})
@RestController
@RequestMapping("/api/verification")
@Slf4j
public class VerificationController {

    @Autowired
    private IVerificationService verificationService;

    @ApiOperation(value = "通过邮箱获取验证码")
    @PostMapping("/email/{account}/{scene}")
    public VerificationCodeGetResponse getVerificationCodeByEmail(@ApiParam(required = true, value = "语系") @RequestParam(value = "lang", required = false ) String lang,
                                                                  @ApiParam(required = true, value = "账号") @PathVariable(value = "account", required = true) String account,
                                                                  @ApiParam(required = true, value = "场景") @PathVariable(value = "scene", required = true) String scene) {
        VerificationCodeGetResponse res = new VerificationCodeGetResponse();
        try {
            res.setCode(ResponseCode.SUCCESS.toString());
            BaseResultRes baseResultRes = verificationService.getVerificationCodeByEmail(lang, account, scene);
            if (!"success".equals(baseResultRes.getResult())) {
                res.setCode(ResponseCode.VERIfICODE_GET_ERROR.toString());
                res.setErrMsg(ResponseCode.VERIfICODE_GET_ERROR.getMsg());
                res.setBaseResultRes(baseResultRes);
                return res;
            }
            res.setBaseResultRes(baseResultRes);
        } catch (Exception ex) {
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg(ex.getMessage());
            log.error("getVerificationCodeByEmail",ex);
        }
        return res;
    }

    @ApiOperation(value = "通过手机获取验证码")
    @PostMapping("/mobilephone/{phone}/{scene}")
    public VerificationCodeGetResponse getVerificationCodeByPhone(@ApiParam(required = true, value = "语系") @RequestParam(value = "lang", required = false ) String lang,
                                                                  @ApiParam(required = true, value = "手机号") @PathVariable(value = "phone", required = true) String phone,
                                                                  @ApiParam(required = true, value = "场景") @PathVariable(value = "scene", required = true) String scene) {
        VerificationCodeGetResponse res = new VerificationCodeGetResponse();
        try {
            res.setCode(ResponseCode.SUCCESS.toString());
            BaseResultRes baseResultRes = verificationService.getVerificationCodeByPhone(lang, phone, scene);
            if (!"success".equals(baseResultRes.getResult())) {
                res.setCode(ResponseCode.VERIfICODE_GET_ERROR.toString());
                res.setErrMsg(ResponseCode.VERIfICODE_GET_ERROR.getMsg());
                res.setBaseResultRes(baseResultRes);
                return res;
            }
            res.setBaseResultRes(baseResultRes);
        } catch (Exception ex) {
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg(ex.getMessage());
            log.error("getVerificationCodeByEmail",ex);
        }
        return res;
    }

    @ApiOperation(value = "检查验证码")
    @PostMapping("/check/{account}/{scene}/{code}")
    public VerificationCodeGetResponse checkVerificationCode(@ApiParam(required = true, value = "账号") @PathVariable(value = "account", required = true) String account,
                                                             @ApiParam(required = true, value = "场景") @PathVariable(value = "scene", required = true) String scene,
                                                             @ApiParam(required = true, value = "验证码") @PathVariable(value = "code", required = true) String code) {
        VerificationCodeGetResponse res = new VerificationCodeGetResponse();
        try {
            res.setCode(ResponseCode.SUCCESS.toString());
            EmcResultRes baseResultRes = verificationService.checkVerificationCode(account, scene, code);
            if (!"success".equals(baseResultRes.getResult())) {
                res.setCode(ResponseCode.VERIfICODE_CHECK_ERROR.toString());
                res.setErrMsg(ResponseCode.VERIfICODE_CHECK_ERROR.getMsg());
                res.setData(baseResultRes);
                return res;
            }
            res.setData(baseResultRes.getCode());
        } catch (Exception ex) {
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg(ex.getMessage());
            log.error("checkVerificationCode",ex);
        }
        return res;
    }
}
