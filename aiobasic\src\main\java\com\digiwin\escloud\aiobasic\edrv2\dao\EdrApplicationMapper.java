package com.digiwin.escloud.aiobasic.edrv2.dao;

import com.digiwin.escloud.aiobasic.edrv2.model.EdrApplication;
import com.digiwin.escloud.aiobasic.edrv2.model.EdrApplicationParam;
import com.digiwin.escloud.aiobasic.edrv2.model.EdrCustomerOrg;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Mapper
public interface EdrApplicationMapper {
    Integer upsertApplication(@Param("edrApplicationList") List<EdrApplication> edrApplicationList);

    List<EdrApplication> getApplicationList(EdrApplicationParam param);

    List<Map<String, Object>> queryCriticalVulnerabilityTotalAmount(@Param("eid") String eid,
                                                                    @Param("timeFrom") Date timeFrom,
                                                                    @Param("timeTo") Date timeTo);

    List<Map<String, Object>> queryAllSeverityTotalAmount(@Param("eid") String eid,
                                                                    @Param("timeFrom") Date timeFrom,
                                                                    @Param("timeTo") Date timeTo);
    Integer removeApplication(@Param("siteId") Long siteId,
                              @Param("applicationIdList") List<String> applicationIdList);

    String getAccountIdsBySiteId(@Param("siteId") String siteId);

    List<EdrCustomerOrg> getAccountIdListBySiteIds(@Param("siteIds") List<String> siteIds);

    List<Map<String, Object>> getAgentIp(@Param("agentIpParamsList") List<Map<String, Object>> agentIpParamsList);
}
