package com.digiwin.escloud.aiocmdb.asset.model;

import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 资产类别编码规则表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@ApiModel(value = "AssetCategoryCodingRule对象", description = "资产类别编码规则表")
public class AssetCategoryCodingRule implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("规则编号")
    private String ruleNumber;

    @ApiModelProperty("规则名称")
    private String ruleName;

    @ApiModelProperty("规则名称")
    private String rulenameCn;

    @ApiModelProperty("规则名称")
    private String rulenameTw;

    private LocalDateTime createDate;

    private LocalDateTime updateDate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getRuleNumber() {
        return ruleNumber;
    }

    public void setRuleNumber(String ruleNumber) {
        this.ruleNumber = ruleNumber;
    }

    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    public String getRulenameCn() {
        return rulenameCn;
    }

    public void setRulenameCn(String rulenameCn) {
        this.rulenameCn = rulenameCn;
    }

    public String getRulenameTw() {
        return rulenameTw;
    }

    public void setRulenameTw(String rulenameTw) {
        this.rulenameTw = rulenameTw;
    }

    public LocalDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

    public LocalDateTime getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(LocalDateTime updateDate) {
        this.updateDate = updateDate;
    }

    @Override
    public String toString() {
        return "AssetCategoryCodingRule{" +
            "id = " + id +
            ", ruleNumber = " + ruleNumber +
            ", ruleName = " + ruleName +
            ", rulenameCn = " + rulenameCn +
            ", rulenameTw = " + rulenameTw +
            ", createDate = " + createDate +
            ", updateDate = " + updateDate +
        "}";
    }
}
