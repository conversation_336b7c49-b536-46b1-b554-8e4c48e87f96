package com.digiwin.escloud.aiobasic.edrv2.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class EdrAgentCheckStatus {
    private String id;
    private String eid;
    private String accountId;
    private String siteId;
    private String agentId;
    private String agentUUId;
    private String os;
    private String groupTitle;
    private String siteName;
    private String endPointName;
    private String lastReportedIP;
    private String checkStatus;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastSuccessfulScanDate;
}
