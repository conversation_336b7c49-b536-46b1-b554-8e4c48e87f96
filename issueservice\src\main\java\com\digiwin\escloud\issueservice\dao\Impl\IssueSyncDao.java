package com.digiwin.escloud.issueservice.dao.Impl;

import com.digiwin.escloud.issueservice.dao.IIssueSyncDao;
import com.digiwin.escloud.issueservice.model.*;
import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.session.SqlSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by z<PERSON><PERSON><PERSON> on 2018-01-09.
 */
@Service
public class IssueSyncDao implements IIssueSyncDao {
    @Autowired
    SqlSession sqlSession;

    @Override
    public List<Issue> SelectUnloadedIssues(int count, String inProductCode,String excludeProductCode) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("end", count);
        if (!StringUtils.isEmpty(inProductCode)) {
            map.put("inProductCodes", inProductCode.split(","));
        }
        if (!StringUtils.isEmpty(excludeProductCode)) {
            map.put("excludeProductCodes", excludeProductCode.split(","));
        }
        return sqlSession.selectList("escloud.issuesyncmapper.selectUndownedIssueList", map);
    }
    @Override
    public List<Issue> SelectUnloadedIssuesForTW(int count,List<String>whiteListinfo){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("end", count);
        map.put("whiteListInfo", whiteListinfo);
        return sqlSession.selectList("escloud.issuesyncmapper.selectUndownedIssueListForTW", map);
    }
    @Override
    public List<Issue> SelectUnloadedIssuesForTW_new(int count,boolean allServiceCode,List<String>whiteListinfo){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("end", count);
        map.put("allServiceCode", allServiceCode);
        map.put("whiteListInfo", whiteListinfo);
        return sqlSession.selectList("escloud.issuesyncmapper.selectUndownedIssueListForTW_new", map);
    }
    @Override
    public List<Issue> SelectUnloadedIssuesProgressForTW_new(int count){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("end", count);
        return sqlSession.selectList("escloud.issuesyncmapper.selectUndownedIssueProgressListForTW_new", map);
    }
    @Override
    public List<String>getPluginWhiteListinfo(int pluginId){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("pluginId", pluginId);
        return sqlSession.selectList("escloud.issuesyncmapper.getPluginWhiteListinfo", map);
    }
    @Override
    public List<Issue> SelectSyncFailIssuesForTW(int count,List<String>whiteListinfo){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("end", count);
        map.put("whiteListInfo", whiteListinfo);
        return sqlSession.selectList("escloud.issuesyncmapper.selectSyncFailIssueListForTW", map);
    }
    @Override
    public List<Issue> SelectSyncFailIssuesForTW_new(int count,boolean allServiceCode,List<String>whiteListinfo){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("end", count);
        map.put("allServiceCode", allServiceCode);
        map.put("whiteListInfo", whiteListinfo);
        return sqlSession.selectList("escloud.issuesyncmapper.selectSyncFailIssueListForTW_new", map);
    }

    @Override
    public int UpdateIssueCrmId(String issueId, String crmId, String issueStatus, String attachmentUploadStatus){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueId", issueId);
        map.put("crmId", crmId);
        map.put("issueStatus", org.springframework.util.StringUtils.isEmpty(issueStatus) ? "N" : issueStatus);
        map.put("attachmentUploadStatus", StringUtils.isBlank(attachmentUploadStatus) ? "" : attachmentUploadStatus);
        return sqlSession.update("escloud.issuesyncmapper.updateIssueCRMID", map);
    }
    @Override
    public int UpdateIssueSyncStatus(String issueId, String syncStatus){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueId", issueId);
        map.put("syncStatus", syncStatus);
        return sqlSession.update("escloud.issuesyncmapper.updateIssueSyncStatus", map);
    }
    @Override
    public int UpdateIssueTwCrmId(String issueId, String crmId, String issueStatus, String syncStatus, String attachmentUploadStatus){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueId", issueId);
        map.put("crmId", crmId);
        map.put("issueStatus", org.springframework.util.StringUtils.isEmpty(issueStatus) ? "N" : issueStatus);
        map.put("syncStatus", syncStatus);
        map.put("attachmentUploadStatus", StringUtils.isBlank(attachmentUploadStatus) ? "" : attachmentUploadStatus);
        return sqlSession.update("escloud.issuesyncmapper.updateIssueTWCRMID", map);
    }

    @Override
    public int UpdateIssueBodySyncStatusTwCrmId(String issueId, String crmId,String progressId, String syncStatus, String crm_BR002,String crm_BR003){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueId", issueId);
        map.put("crmId", crmId);
        map.put("progressId", progressId);
        map.put("syncStatus", syncStatus);
        map.put("crm_BR002", StringUtils.isBlank(crm_BR002) ? "" : crm_BR002);
        map.put("crm_BR003", StringUtils.isBlank(crm_BR003) ? "" : crm_BR003);
        return sqlSession.update("escloud.issuesyncmapper.updateIssueBodySyncStatusTWCRMID", map);
    }
    @Override
    public int InsertIssueProgress(long issueId, String crmId, IssueProgress issueProgress){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("IssueId", issueId);
        map.put("CrmId", crmId);
        map.put("SequenceNum", issueProgress.getSequeceNum());
        map.put("ProcessType", issueProgress.getProcessType());
        map.put("Processor", issueProgress.getProcessor());
        map.put("Description", issueProgress.getDescription());
        map.put("ProcessTime", issueProgress.getProcessTime());
        return sqlSession.insert("escloud.issuesyncmapper.insertIssueProgress", map);
    }

    @Override
    public long InsertIssueProgressNew(long issueId, String crmId, IssueProgress issueProgress){
        issueProgress.setIssueId(issueId);
        issueProgress.setCrmId(crmId);
        int i = sqlSession.insert("escloud.issuesyncmapper.insertIssueProgressNew", issueProgress);
        if(i > 0)
            return  issueProgress.getId();
        else
            return  -1;
    }

    @Override
    public long IsEsxistIssueProgressNew(long issueId, String crmId, IssueProgress issueProgress){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("IssueId", issueId);
        map.put("CrmId", crmId);
        map.put("SequenceNum", issueProgress.getSequeceNum());
        map.put("ProcessTime", issueProgress.getProcessTime().substring(0,10) + "%");
        Object o =  sqlSession.selectOne("escloud.issuesyncmapper.isExistIssueProgressNew", map);
        if (o == null)
            return -1;
        else
            return Long.parseLong(o.toString());
    }
    @Override
    public boolean IsEsxistIssueProgress(long issueId, String crmId, IssueProgress issueProgress){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("IssueId", issueId);
        map.put("CrmId", crmId);
        map.put("SequenceNum", issueProgress.getSequeceNum());
        map.put("ProcessTime", issueProgress.getProcessTime().substring(0,10) + "%");
        Object o =  sqlSession.update("escloud.issuesyncmapper.isExistIssueProgress", map);
        if (o == null)
            return false;
        else
            return Long.parseLong(o.toString()) != -1;
    }
    @Override
    public int UpdateIssueProgress(long issueId, String crmId, IssueProgress issueProgress){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("IssueId", issueId);
        map.put("CrmId", crmId);
        map.put("SequenceNum", issueProgress.getSequeceNum());
        map.put("ProcessType", issueProgress.getProcessType());
        map.put("Processor", issueProgress.getProcessor());
        map.put("Description", issueProgress.getDescription());
        map.put("ProcessTime", issueProgress.getProcessTime().substring(0,10) + "%");
        map.put("ReplyType", issueProgress.getReplyType());
        map.put("ProcessHours", issueProgress.getProcessHours());
        return sqlSession.update("escloud.issuesyncmapper.updateIssueProgress", map);
    }

    @Override
    public int UpdateIssueStatus(long issueId, String issueStatus){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueStatus", issueStatus);
        map.put("issueId", issueId);
        return sqlSession.update("escloud.issuesyncmapper.updateIssueStatus", map);
    }

    @Override
    public int UpdateIssueStatus(IssueStatusSyncData issueStatusSyncData){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueStatus", issueStatusSyncData.getIssueStatus());
        map.put("crmId", issueStatusSyncData.getCrmId());
        map.put("programVersion", issueStatusSyncData.getProgramVersion());
        map.put("issueDescription", issueStatusSyncData.getIssueDescription());
        map.put("serviceDepartment", issueStatusSyncData.getServiceDepartment());
        map.put("supportDepartment", issueStatusSyncData.getSupportDepartment());
        map.put("serviceStaff", issueStatusSyncData.getServiceStaff());
        map.put("supportStaff", issueStatusSyncData.getSupportStaff());
        map.put("productCode", issueStatusSyncData.getProductCode());
        map.put("serviceCode", issueStatusSyncData.getServiceCode());
        map.put("userId", issueStatusSyncData.getUserId()== null ? "": issueStatusSyncData.getUserId());
        map.put("userContactId", issueStatusSyncData.getUserContactId()== null ? "": issueStatusSyncData.getUserContactId() );
        int i = sqlSession.update("escloud.issuesyncmapper.updateIssueStatusbycrmid", map);
        try {
            if (i > 0) {
                long issueId = IsExistsIssue(issueStatusSyncData.getCrmId());
                if (issueId > 0) {
                    //更新子单身
                    InsertOrUpdateIssueCaseDetailByIssueId(issueStatusSyncData, issueId);
                    // 更新issue_summary
                    if(StringUtils.isNotEmpty(issueStatusSyncData.getCloseTime()) && ( IssueStatus.Closed.toString().equals( issueStatusSyncData.getIssueStatus()) || IssueStatus.Evaluated.toString().equals(issueStatusSyncData.getIssueStatus() ) ) ) {
                        map.clear();
                        map.put("issueId", issueId);
                        map.put("closeTime", issueStatusSyncData.getCloseTime());
                        sqlSession.insert("escloud.issuesyncmapper.InsertIssueSummaryCloseTime", map);
                    }
                    //更新单身
                    map.clear();
                    map.put("issueId", issueId);
                    map.put("programCode", issueStatusSyncData.getProgramCode());
                    map.put("programVersion", issueStatusSyncData.getProgramVersion());
                    return sqlSession.update("escloud.issuesyncmapper.updateIssueDetailbyissueId", map);
                } else
                    return i;
            }
        }catch (Exception e){
            System.out.println(e.toString());
        }/*finally {  //huly: 修复漏洞/bug 注释代码
            return i;
        }*/
        return i;
    }
    @Override
    public String checkIssueIsEdrEvent(String crmId) {
        Map<String, Object> map = new HashMap<>();

        if (org.apache.commons.lang3.StringUtils.isBlank(crmId)) {
            throw new RuntimeException("checkIssueIsEdrEvent() error: crmId is empty.");
        }
        map.put("crmId", crmId);
        return sqlSession.selectOne("escloud.issuesyncmapper.checkIssueIsEdrEvent",map);
    }

    @Override
    public int InsertOrUpdateIssueCaseDetailByIssueId(IssueStatusSyncData issueStatusSyncData, long issueId) {
        if(issueId < 0){
            issueId = IsExistsIssue(issueStatusSyncData.getCrmId());
        }
        if (issueId < 0){
            return 0;
        }
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueId", issueId);
        map.put("issueClassification", issueStatusSyncData.getIssueClassification());
        map.put("erpSystemCode", issueStatusSyncData.getErpSystemCode());
        map.put("programCode", issueStatusSyncData.getProgramCode());
        map.put("agreeDate", issueStatusSyncData.getAgreeDate());
        map.put("agreeDateSubmiter", issueStatusSyncData.getAgreeDateSubmiter());
        map.put("agreeDateReply", issueStatusSyncData.getAgreeDateReply());
        map.put("sendReplyToClient", issueStatusSyncData.getSendReplyToClient());
        map.put("chatfileHelp", issueStatusSyncData.getChatfileHelp());
        map.put("totalWorkHours", issueStatusSyncData.getTotalWorkHours());

        return sqlSession.insert("escloud.issuesyncmapper.InsertOrUpdateIssueCaseDetailByIssueId", map);
    }

    @Override
    public int InsertIssueProgress(IssueProgressSyncData issueProgress){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("CrmId", issueProgress.getCrmId());
        map.put("SequenceNum", issueProgress.getSequeceNum());
        map.put("ProcessType", issueProgress.getProcessType());
        map.put("Processor", issueProgress.getProcessor());
        map.put("Description", issueProgress.getDescription());
        map.put("ProcessTime", issueProgress.getProcessTime());
        map.put("processHours", issueProgress.getProcessHours());
        map.put("replyType", issueProgress.getReplyType());
        map.put("BR002", issueProgress.getBR002());
        map.put("BR003", issueProgress.getBR003());
        map.put("innerDes", issueProgress.getInnerDes());
        return sqlSession.insert("escloud.issuesyncmapper.insertIssueProgresstw", map);
    }

    @Override
    public long InsertIssueProgressCN(IssueProgressSyncData issueProgress) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("CrmId", issueProgress.getCrmId());
        map.put("SequenceNum", issueProgress.getSequeceNum());
        map.put("ProcessType", issueProgress.getProcessType());
        map.put("Processor", issueProgress.getProcessor());
        map.put("Description", issueProgress.getDescription());
        map.put("ProcessTime", issueProgress.getProcessTime());
        map.put("processHours", issueProgress.getProcessHours());
        map.put("replyType", issueProgress.getReplyType());
        map.put("syncStatus", issueProgress.getSyncStatus());
        map.put("workno", issueProgress.getWorkno());
        map.put("handlerId", issueProgress.getHandlerId());
        map.put("CurrentStatus", issueProgress.getCurrentStatus());
        int i = sqlSession.insert("escloud.issuesyncmapper.insertIssueProgressCN", map);
        if(i > 0)
            return  (long)map.get("id");
        else
            return  -1;
    }

    @Override
    public int InsertIssueProgress(List<Issue> issueList) {
        List<Map<String,Object>> listMap = new ArrayList<>();
        for (Issue issue:issueList) {
            String issueId = Long.toString(issue.getIssueId());
            for (IssueProgress issueProgress:issue.getIssueProgresses()) {
                Map<String, Object> map = new HashMap<>();
                map.put("issueId", issueId);
                map.put("crmId", issueProgress.getCrmId());
                map.put("sequenceNum", issueProgress.getSequeceNum());
                map.put("processType", issueProgress.getProcessType());
                map.put("processor", issueProgress.getProcessor());
                map.put("description", issueProgress.getDescription());
                map.put("processTime", issueProgress.getProcessTime());
                map.put("processHours", issueProgress.getProcessHours());
                map.put("replyType", issueProgress.getReplyType());
                listMap.add(map);
            }
        }
        return sqlSession.insert("escloud.issuesyncmapper.batchInsertIssueProgresstw", listMap);
    }

    @Override
    public int InsertIssueProgressCN(List<IssueProgress> issueProgresses) {
        return sqlSession.insert("escloud.issuesyncmapper.batchInsertIssueProgressCN", issueProgresses);
    }


    @Override
    public boolean IsEsxistIssueProgress(IssueProgressSyncData issueProgress){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("CrmId", issueProgress.getCrmId());
        map.put("SequenceNum", issueProgress.getSequeceNum());
        map.put("ProcessTime", issueProgress.getProcessTime().substring(0,10) + "%");
        String o =  sqlSession.selectOne("escloud.issuesyncmapper.isExistIssueProgresstw", map);
        if (o == null || o.isEmpty())
            return false;
        else
            return true;
    }
    @Override
    public int UpdateIssueProgress(IssueProgressSyncData issueProgress){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("CrmId", issueProgress.getCrmId());
        map.put("SequenceNum", issueProgress.getSequeceNum());
        map.put("ProcessType", issueProgress.getProcessType());
        map.put("Processor", issueProgress.getProcessor());
        map.put("Description", issueProgress.getDescription());
        map.put("ProcessTime", issueProgress.getProcessTime().substring(0,10) + "%");
        map.put("ReplyType", issueProgress.getReplyType());
        map.put("ProcessHours", issueProgress.getProcessHours());
        return sqlSession.update("escloud.issuesyncmapper.updateIssueProgresstw", map);
    }
    @Override
    public int DeleteIssueProgress(IssueProgressSyncData issueProgress) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("CrmId", issueProgress.getCrmId());
        map.put("SequenceNum", issueProgress.getSequeceNum());
        map.put("ProcessTime", issueProgress.getProcessTime().substring(0, 10) + "%");
        return sqlSession.delete("escloud.issuesyncmapper.deleteIssueProgresstw", map);
    }
    @Override
    public String IsEsxistIssueProgressTW(IssueProgressSyncData issueProgress) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("CrmId", issueProgress.getCrmId());
        map.put("SequenceNum", issueProgress.getSequeceNum());
        map.put("ProcessTime", issueProgress.getProcessTime().substring(0, 10) + "%");
        map.put("BR002", issueProgress.getBR002());
        map.put("BR003", issueProgress.getBR003());
        return sqlSession.selectOne("escloud.issuesyncmapper.isExistIssueProgresstw_new", map);
    }
    @Override
    public int UpdateIssueProgressTW(IssueProgressSyncData issueProgress,String id){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("Id", id);
        map.put("CrmId", issueProgress.getCrmId());
        map.put("SequenceNum", issueProgress.getSequeceNum());
        map.put("ProcessType", issueProgress.getProcessType());
        map.put("Processor", issueProgress.getProcessor());
        map.put("Description", issueProgress.getDescription());
        map.put("ProcessTime", issueProgress.getProcessTime().substring(0,10) + "%");
        map.put("ReplyType", issueProgress.getReplyType());
        map.put("ProcessHours", issueProgress.getProcessHours());
        map.put("BR002", issueProgress.getBR002());
        map.put("BR003", issueProgress.getBR003());
        map.put("innerDes", issueProgress.getInnerDes());
        return sqlSession.update("escloud.issuesyncmapper.updateIssueProgresstw_new", map);
    }
    @Override
    public int DeleteIssueProgressTW(String id) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("Id", id);
        return sqlSession.delete("escloud.issuesyncmapper.deleteIssueProgresstw_new", map);
    }
    @Override
    public IssueProgress getLastIssueProgress(String id) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("id", id);
        return sqlSession.selectOne("escloud.issuesyncmapper.getLastIssueProgress", map);
    }
    @Override
    public List<Issue> SelectUnSyncIssues(String inProductCode, String excludeProductCode){
        Map<String, Object> map = new HashMap<String, Object>();
        if(!StringUtils.isEmpty(inProductCode)){
            map.put("inProductCodes", inProductCode.split(","));
        }
        if(!StringUtils.isEmpty(excludeProductCode)){
            map.put("excludeProductCodes", excludeProductCode.split(","));
        }
        return sqlSession.selectList("escloud.issuesyncmapper.SelectUnSyncIssues",map);
    }

    @Override
    public int UpdateIssueSync(String id) {
        return sqlSession.update("escloud.issuesyncmapper.updateIssueSync", id);
    }

    @Override
    public int UpdateIssueProgressSync(IssueProgressSyncResult issueProgressSyncResult) {
        return sqlSession.update("escloud.issuesyncmapper.updateIssueProgressSync", issueProgressSyncResult);
    }

    @Override
    public int UpdateIssueDetailSync(String id) {
        return sqlSession.update("escloud.issuesyncmapper.updateIssueDetailSync", id);
    }
    @Override
    public int UpdateIssueSyncV2(String id) {
        return sqlSession.update("escloud.issuesyncmapper.updateIssueSyncV2", id);
    }

    @Override
    public int UpdateIssueDetailSyncV2(String id) {
        return sqlSession.update("escloud.issuesyncmapper.updateIssueDetailSyncV2", id);
    }

    @Override
    public int UpdateIssueProgressSyncV2(String id) {
        return sqlSession.update("escloud.issuesyncmapper.updateIssueProgressSyncV2", id);
    }

    @Override
    public int UpdateIssueSyncForX(String issueId) {
        return sqlSession.update("escloud.issuesyncmapper.UpdateIssueSyncForX", issueId);
    }

    @Override
    public int UpdateIssueDetailSyncForX(String issueId) {
        return sqlSession.update("escloud.issuesyncmapper.UpdateIssueDetailSyncForX", issueId);
    }

    @Override
    public int UpdateIssueProgressSyncForX(String issueId) {
        return sqlSession.update("escloud.issuesyncmapper.UpdateIssueProgressSyncForX", issueId);
    }

    @Override
    public int UpdateIssueSyncStatus(String issueId) {
        return sqlSession.update("escloud.issuesyncmapper.UpdateIssueSyncStatus", issueId);
    }

    @Override
    public List<String> getIssueSync() {
        return sqlSession.selectList("escloud.issuesyncmapper.SelectIssueSync");
    }
    @Override
    public List<String> getIssueXSync(String productCode,String crmId) {
        Map<String, Object> map = new HashMap<String, Object>();
        if(!StringUtils.isEmpty(productCode)){
            map.put("productCodes", productCode.split(","));
        }
        if(!StringUtils.isEmpty(crmId)){
            map.put("crmIds", crmId.split(","));
        }
        return sqlSession.selectList("escloud.issuesyncmapper.SelectIssuesXSync",map);
    }
    @Override
    public int InsertIssueSync(IssueUnSyncResult issueUnSyncResult) {
        return sqlSession.insert("escloud.issuesyncmapper.InsertIssueSync", issueUnSyncResult);
    }

    /**
     * 同步非服务云案件
     * 是否已经存在次案件
     * @param crmId
     * @return
     */
    @Override
    public long IsExistsIssue(String crmId){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("crmid", crmId);
        Object o = sqlSession.selectOne("escloud.issuesyncmapper.IsExitsIssue", map);
        if(o == null)
            return -1;
        else
            return Long.parseLong(o.toString());
    }
    /**
     * 同步非服务云案件
     * 插入案件
     * @param issue
     * @return
     */
    @Override
    public long InsertOtherIssue(Issue issue){
        sqlSession.insert("escloud.issuesyncmapper.insertIssue", issue);
        return issue.getIssueId();
    }

    @Override
    public long InsertOtherIssueCN(Issue issue) {
        sqlSession.insert("escloud.issuesyncmapper.insertIssueCN", issue);
        return issue.getIssueId();
    }

    @Override
    public int InsertOtherIssue(List<Issue> issueList) {
        return sqlSession.insert("escloud.issuesyncmapper.batchInsertIssue", issueList);
    }

    /**
     * 同步非服务云案件
     * 更新案件
     * @param issue
     * @return
     */
    @Override
    public int UpdateOtherIssue(Issue issue){
        return sqlSession.update("escloud.issuesyncmapper.updateIssue", issue);
    }

    @Override
    public int UpdateOtherIssueCN(Issue issue) {
        return sqlSession.update("escloud.issuesyncmapper.updateIssueCN", issue);
    }

    @Override
    public int DeleteOtherIssue(Issue issue) {
        return sqlSession.delete("escloud.issuesyncmapper.deleteIssue", issue);
    }

    /**
     * 同步非服务云案件
     * 插入或更新案件子单头
     * @param issueCasedetail
     * @return
     */
    @Override
    public int InsertOtherIssueDetail(IssueCasedetail issueCasedetail){
        return sqlSession.insert("escloud.issuesyncmapper.InsertIssueCaseDetail", issueCasedetail);
    }
     /**
      * 同步非服务云案件
      * 插入或更新案件Summary，僅寫入closeTime
     * */
    @Override
    public int InsertOtherIssueSummaryCloseTime(IssueCasedetail issueCasedetail){
        return sqlSession.insert("escloud.issuesyncmapper.InsertIssueSummaryCloseTime", issueCasedetail);
    }

    @Override
    public int InsertOtherIssueDetailCN(IssueCasedetail issueCasedetail) {
        return sqlSession.insert("escloud.issuesyncmapper.InsertIssueCaseDetailCN", issueCasedetail);
    }

    @Override
    public int InsertOtherIssueDetail(List<Issue> issueList) {
        List<Map<String,Object>> listMap = new ArrayList<>(issueList.size());
        for (Issue issue:issueList) {
            IssueCasedetail issueCasedetail = issue.getIssueCasedetail();
            Map<String, Object> map = new HashMap<>();
            map.put("issueId",issue.getIssueId());
            map.put("issueClassification",issueCasedetail.getIssueClassification());
            map.put("erpSystemCode",issueCasedetail.getErpSystemCode());
            map.put("programCode",issueCasedetail.getProgramCode());
            listMap.add(map);
        }
        return sqlSession.insert("escloud.issuesyncmapper.batchInsertIssueCaseDetail", listMap);
    }

    @Override
    public Issue SelectIssue(String crmId){
        return sqlSession.selectOne("escloud.issuemapper.selectIssue",crmId);
    }

    /**
     * 同步新注册用户CRM案件
     * 获取还未更新CRM案件的使用者
     * @return 未更新CRM案件的使用者
     */
    @Override
    public List<String> GetNewRegisteredUser() {
        return sqlSession.selectList("escloud.issuesyncmapper.selectNewRegisteredUser");
    }

    /**
     * 同步新注册用户CRM案件
     * 插入已更新CRM案件的使用者
     * @return
     */
    @Override
    public int InsertNewUserIssueSynced(List<Map<String, Object>> emails) {
        return sqlSession.insert("escloud.issuesyncmapper.batchInsertNewUserIssueSynced", emails);
    }

    @Override
    public String GetIssueServiceID(long issueId){
        return sqlSession.selectOne("escloud.issuesyncmapper.getIssueServiceID",issueId);
    }

    @Override
    public int UpdateServiceIDandDepartment(long issueId,StaffUserInfo staffUserInfo){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("IssueId", issueId);
        map.put("ServiceId", staffUserInfo.getUserId());
        map.put("ServiceDepartment", staffUserInfo.getDepartment());
        return sqlSession.update("escloud.issuesyncmapper.updateIssueServiceIDandDepartment", map);
    }

    @Override
    public String getIssueSyncStatus(String issueId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueId", issueId);
        return sqlSession.selectOne("escloud.issuesyncmapper.getIssueSyncStatus",map);
    }


    @Override
    public String getIssueDetailSyncStatus(String issueId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueId", issueId);
        return sqlSession.selectOne("escloud.issuesyncmapper.getIssueDetailSyncStatus",map);
    }

    @Override
    public String getIssueProgressSyncStatus(String Id) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("Id", Id);
        return sqlSession.selectOne("escloud.issuesyncmapper.getIssueProgressSyncStatus",map);
    }

    @Override
    public List<Issue> SelectUnSyncCallCenterIssueList(){
        return sqlSession.selectList("escloud.issuesyncmapper.SelectUnSyncCallCenterIssueList");
    }

    @Override
    public List<Issue> SelectUnSyncCallCenterProgressList(){
        return sqlSession.selectList("escloud.issuesyncmapper.SelectUnSyncCallCenterIssueProgressList");
    }

    @Override
    public String GetLastIssueSyncTime(String syncItem) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("syncItem", syncItem);
        return sqlSession.selectOne("escloud.issuesyncmapper.getIssueSyncToCrmLastTime",map);
    }
    @Override
    public List<Issue> SelectUnDownloadCustomizedBugIssues(int count, String lastIssueSyncTime){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("end", count);
        map.put("lastIssueSyncTime", lastIssueSyncTime);
        return sqlSession.selectList("escloud.issuesyncmapper.selectUnDownloadCustomizedBugIssueList", map);
    }

    @Override
    public int UpdateCustomizedBugIssues(List<Map<String, Object>> successIssues){
        return sqlSession.update("escloud.issuesyncmapper.updateCustomizedBugIssues", successIssues);
    }


    @Override
    public List<Issue> SelectUnDownloadCustomizedIssues(){
        Map<String, Object> map = new HashMap<String, Object>();
        return sqlSession.selectList("escloud.issuesyncmapper.selectUnDownloadCustomizedIssueList", map);
    }

    @Override
    public int UpdateCustomizedIssuesStatus(String issueId){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueId", issueId);
        return sqlSession.update("escloud.issuesyncmapper.updateCustomizedIssuesStatus", map);
    }

    @Override
    public List<CustomizedIssue> SelectUnDownloadAcceptancepassCustomizedIssues(int count,String lastSyncTime){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("end", count);
        map.put("lastSyncTime", lastSyncTime);
        return sqlSession.selectList("escloud.issuesyncmapper.selectUnDownloadAcceptancepassCustomizedIssuesList", map);
    }

    @Override
    public int CheckRelatedCustomizedIssuesAcceptancepass(String crmId){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("crmId", crmId);
        return sqlSession.selectOne("escloud.issuesyncmapper.selectRelatedCustomizedIssuesAcceptancepass", map);
    }

    @Override
    public int UpdateIssueSyncToCrmTime(String syncItem, String lastIssueTime){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("syncItem", syncItem);
        map.put("lastIssueTime", lastIssueTime);
        return sqlSession.update("escloud.issuesyncmapper.updateIssueSyncToCrmTime", map);
    }


    @Override
    public String CheckCustomizedIssue(String requNo, String requSeq){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("requNo", requNo);
        map.put("requSeq", requSeq);
        return sqlSession.selectOne("escloud.issuesyncmapper.checkCustomizedIssue",map);
    }
    @Override
    public String GetUserIdByWorknoOrItcode(String userName){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("userName", userName);
        return sqlSession.selectOne("escloud.issuesyncmapper.getUserIdByWorknoOrItcode",map);
    }
    @Override
    public String GetServiceCodeByCustomerCode(String customerCode){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("customerCode", customerCode);
        return sqlSession.selectOne("escloud.issuesyncmapper.getServiceCodeByCustomerCode",map);
    }
    @Override
    public int GetMaxReuqId(){
        return sqlSession.selectOne("escloud.issuesyncmapper.getMaxReuqId");
    }
    @Override
    public int UpdateCustomizedIssue(CustomizedIssue customizedIssue){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("requId", customizedIssue.getRequId());
        map.put("productCode", customizedIssue.getProductCode());
        map.put("erpSystemCode", customizedIssue.getErpSystemCode());
        map.put("programCode", customizedIssue.getProgramCode());
        map.put("title", customizedIssue.getTitle().length()>255 ?customizedIssue.getTitle().substring(0,255):customizedIssue.getTitle());
        map.put("description", customizedIssue.getDescription().length()>255? customizedIssue.getDescription().substring(0,255):customizedIssue.getDescription() );
        map.put("priority", "Y".equals(customizedIssue.getPriority())?"1":"4"); //huly: 修复漏洞/bug == 改成 equals

        map.put("sdUserId", customizedIssue.getSdUserId()== null ?"":customizedIssue.getSdUserId());
        map.put("sdDispatchDate", customizedIssue.getSdDispatchDate());
        map.put("sdPlannedCompletionDate", customizedIssue.getSdPlannedCompletionDate());
        map.put("sdCompletionDate", customizedIssue.getSdCompletionDate());
        map.put("sdEstimatedCost", customizedIssue.getSdEstimatedCost());
        map.put("sdCost", customizedIssue.getSdCost());
        map.put("initialTotalEstimatedCost",customizedIssue.getInitialTotalEstimatedCost());
        map.put("submitTime",customizedIssue.getSubmitTime());
        map.put("modifyTime",customizedIssue.getModifyTime());
        map.put("prEstimatedCost",customizedIssue.getPrEstimatedCost());
        int updateCustomizedIssueCount =  sqlSession.update("escloud.issuesyncmapper.updateCustomizedIssue", map);
        return sqlSession.update("escloud.issuesyncmapper.updateCustomized_sIssue", map);
    }
    @Override
    public int InsertCustomizedIssue(CustomizedIssue customizedIssue){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("requId", customizedIssue.getRequId());
        map.put("requNo", customizedIssue.getRequNo());
        map.put("requSeq", customizedIssue.getRequSeq());
        map.put("crmId", customizedIssue.getCrmId());
        map.put("userId", customizedIssue.getUserId()== null ?"": customizedIssue.getUserId());
        map.put("userName",customizedIssue.getUserId_name());
        map.put("status", customizedIssue.getStatus());
        map.put("serviceCode", customizedIssue.getCustomerServiceCode());
        map.put("productCode", customizedIssue.getProductCode());
        map.put("erpSystemCode", customizedIssue.getErpSystemCode());
        map.put("programCode", customizedIssue.getProgramCode());
        map.put("title", customizedIssue.getTitle().length()>255 ?customizedIssue.getTitle().substring(0,255):customizedIssue.getTitle());
        map.put("description", customizedIssue.getDescription().length()>255? customizedIssue.getDescription().substring(0,255):customizedIssue.getDescription() );
        map.put("priority", "Y".equals(customizedIssue.getPriority())?"1":"4"); //huly: 修复漏洞/bug == 改成 equals

        map.put("sdUserId", customizedIssue.getSdUserId()== null ?"":customizedIssue.getSdUserId());
        map.put("sdDispatchDate", customizedIssue.getSdDispatchDate());
        map.put("sdPlannedCompletionDate", customizedIssue.getSdPlannedCompletionDate());
        map.put("sdCompletionDate", customizedIssue.getSdCompletionDate());
        map.put("sdEstimatedCost", customizedIssue.getSdEstimatedCost());
        map.put("sdCost", customizedIssue.getSdCost());
        map.put("isCheckPassWithoutIssue", customizedIssue.isCheckPassWithoutIssue());
        map.put("isAcceptWithoutIssue", customizedIssue.getIsAcceptWithoutIssue());
        map.put("initialTotalEstimatedCost",customizedIssue.getInitialTotalEstimatedCost());
        map.put("modifyTime",customizedIssue.getModifyTime());
        map.put("submitTime",customizedIssue.getSubmitTime());
        map.put("prEstimatedCost",customizedIssue.getPrEstimatedCost());

        int insertCustomizedIssueCount =  sqlSession.insert("escloud.issuesyncmapper.insertCustomizedIssue", map);
        return sqlSession.insert("escloud.issuesyncmapper.insertCustomized_sIssue", map);
    }
    @Override
    public int InsertCrmidRelationship(CustomizedIssue customizedIssue){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("crmId", customizedIssue.getCrmId());
        map.put("requNo", customizedIssue.getRequNo());
        return sqlSession.insert("escloud.issuesyncmapper.insertCrmidRelationship", map);
    }
    @Override
    public int InsertCustomizedProgress(CustomizedIssue customizedIssue){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("requId", customizedIssue.getRequId());
        map.put("userId", customizedIssue.getUserId()== null ?"": customizedIssue.getUserId());
        map.put("userName",customizedIssue.getUserId_name());
        map.put("status", customizedIssue.getStatus());
        map.put("sdName", customizedIssue.getSdUserId_name());
        map.put("sdUserId", customizedIssue.getSdUserId()== null ?"":customizedIssue.getSdUserId());
        map.put("sdDispatchDate", customizedIssue.getSdDispatchDate());
        map.put("submitTime",customizedIssue.getSubmitTime());
        map.put("prEstimatedCost",customizedIssue.getPrEstimatedCost());
        return sqlSession.insert("escloud.issuesyncmapper.insertCustomizedProgress", map);
    }
    
    @Override
    public List<Issue> SelectCallCenterUnuploadFiles(int count){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("end", count);
        return sqlSession.selectList("escloud.issuesyncmapper.selectCallCenterUnuploadFiles", map);
    }
    @Override
    public int UpdateIssueAdditionalExplanationSyncAnnex(String issueId,String progressId, String syncStatus) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("issueId", issueId);
        map.put("progressId", progressId);
        map.put("syncStatus", syncStatus);
        return sqlSession.update("escloud.issuesyncmapper.UpdateIssueAdditionalExplanationSyncAnnex", map);
    }

    @Override
    public List<Issue> SelectUnSearchChatFileIssueTW(int count){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("end", count);
        return sqlSession.selectList("escloud.issuesyncmapper.selectUnSearchChatFileIssueForTW", map);
    }


    @Override
    public String SelectIssueSearchContent(String crmId) {
        Map<String, Object> map = new HashMap<>();

        if (org.apache.commons.lang3.StringUtils.isBlank(crmId)) {
            throw new RuntimeException("selectIssueSearchContent() error: crmId is empty.");
        }
        map.put("crmId", crmId);
        return sqlSession.selectOne("escloud.issuesyncmapper.selectIssueSearchContent",map);
    }

    @Override
    public Issue SelectIssueInfoByCrmId(String crmId) {
        Map<String, Object> map = new HashMap<>();

        if (org.apache.commons.lang3.StringUtils.isBlank(crmId)) {
            throw new RuntimeException("selectIssueSearchContent() error: crmId is empty.");
        }
        map.put("crmId", crmId);
        return sqlSession.selectOne("escloud.issuesyncmapper.selectIssueInfoByCrmId",map);
    }

    @Override
    public int updateIssueKbShare(String crmId,String chatFileSearchText,String chatFileContent, String chatFileError,boolean invalidChatFileAnswer, String chatfileKnowledgeList,Boolean invalidChatFileKnowledgeNo) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("crmId", crmId);
        map.put("chatFileSearchText", chatFileSearchText);
        map.put("chatFileContent", chatFileContent);
        map.put("chatFileError", chatFileError);
        map.put("finishSearchChatFile", "T");
        map.put("invalidChatFileAnswer", invalidChatFileAnswer);
        map.put("chatfileKnowledgeList", chatfileKnowledgeList);
        map.put("invalidChatFileKnowledgeNo", invalidChatFileKnowledgeNo);
        map.put("aiSource", "chatfile");
        return sqlSession.update("escloud.issuesyncmapper.updateIssueKbShare", map);
    }

    @Override
    public int updateIssueKbShare_smart(String crmId,String searchText,Kb kb,String kbUrl) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("crmId", crmId);
        map.put("searchText", searchText);
        map.put("kbId", kb== null ? "": kb.getId());
        map.put("shareUrl", StringUtils.isEmpty(kbUrl)?"":kbUrl);
        String shareContent = kb== null ? "知識中台:沒有搜索到符合的知識" : kbUrl +"\r\n"+"Q:"+kb.getTitle()+"\r\n"+"A:"+kb.getContentNoHtml();

        shareContent= shareContent.length()>250?shareContent.substring(0,250)+"【解答過長，請點連結查看詳文】"+"\r\n" :shareContent+"\r\n";
        map.put("shareContent", shareContent);
        return sqlSession.update("escloud.issuesyncmapper.updateIssueKbShare_smart", map);
    }

    @Override
    public ChatFileConfig getCharFileConfig(String productCode ,String serviceRegion){
        // 案件內部說明 chatfile之配置，故會有 issueChatFileSearch = true之條件
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("productCode", productCode);
        map.put("serviceRegion", serviceRegion == null ?"": serviceRegion);
        return sqlSession.selectOne("escloud.issuesyncmapper.getCharFileConfig",map);
    }

    @Override
    public List<Issue> SelectUnDownloadedIssuekbshareForTW(int count){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("end", count);
        return sqlSession.selectList("escloud.issuesyncmapper.selectUnDownloadedIssuekbshareForTW", map);
    }

    @Override
    public int UpdateTWIssuekbshareSyncStatus(String crmId,  String syncStatus){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("crmId", crmId);
        map.put("syncStatus", syncStatus);
        DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        map.put("syncTime", sdf.format(new Date()));
        return sqlSession.update("escloud.issuesyncmapper.updateTWIssuekbshareSyncStatus", map);
    }

    @Override
    public boolean checkIssueHasNoKbId(String crmId){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("CrmId", crmId);
        String o =  sqlSession.selectOne("escloud.issuesyncmapper.checkIssueHasNoKbId", map);
        if (o == null || o.isEmpty() || "-1".equals(o))
            return true;
        else
            return false;
    }

    @Override
    public List<String>getChatfileInvalidAnswerKeyword(){
        return sqlSession.selectList("escloud.issuesyncmapper.getChatfileInvalidAnswerKeyword");
    }

    @Override
    public int  insertChatfileFailLog(String source,String productCode,String serviceRegion,String searchContent,String originalAnswer, String errorContent,boolean invalidAnswer,Boolean invalidChatFileKnowledgeNo) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("source", source);
        map.put("productCode", productCode);
        map.put("serviceRegion", serviceRegion);
        map.put("searchContent", searchContent);
        map.put("resultContent", originalAnswer);
        map.put("errorInfo", errorContent);
        map.put("invalidAnswer", invalidAnswer);
        map.put("invalidChatFileKnowledgeNo", invalidChatFileKnowledgeNo);
        return sqlSession.update("escloud.issuesyncmapper.insertChatfileFailLog", map);
    }

}
