package com.digiwin.escloud.common.model;

public enum ResponseCode {
    SUCCESS("success", "0"),
    INTERNAL_ERROR("server internal error", "1"),
    MAIL_SEND_ERROR("email failed to send, please try again later", "2"),
    SMS_SEND_ERROR("failed to send mobile verification code. please try again later", "3"),
    EXPIRED("the token has expired. please resend it", "4"),
    INVALID_PARAM("illegal parameter", "5"),
    VERIFY_FAIL("validation failed", "6"),
    USERNAME_DUPLICATED("the es user name(iam user id) already exists", "7"),
    MAIL_DUPLICATED("the email address is already in use by another user", "8"),
    CELLPHONE_DUPLICATED("mobile phone number used by other users", "9"),
    User_Not_Found("registered user not found", "10"),
    LICENSE_ERROR("authorization information exception", "11"),
    USERMAIL_EMPTY("the email information of registered user is incomplete", "12"),
    UserPhone_Empty("the mobile phone information of registered users is incomplete", "13"),
    LOGOUT_ERROR("user logout error", "14"),
    WEIXIN_DUPLICATED("the account has been bound to wechat", "15"),
    WEIXIN_UserInfo_Not_Found("wechat user information not found", "16"),
    User_Not_Bind("the user has been unbound with the service cloud. Please click OK to verify the information and complete the binding again", "17"),
    CustomerService_Not_Found("customer service information of registered user not found", "18"),
    CustomerCode_Null("customer number is empty", "19"),
    CustomerAP_Not_Found("customer not found ApServer", "20"),
    SERVICECODE_ERROR("incorrect service code", "21"),
    INBLACKLIST_ERROR("you do not have permission to use the service cloud temporarily", "22"),
    USERNAMEPASSWORD_ERROR("your user name or password is incorrect", "23"),
    USERDUPLICATELOGIN_ERROR("your account has been logged in to another computer. It is forbidden to use it", "24"),
    USERDUPLICATELOGIN_WARNING("your account has been logged in to another computer. Warning", "25"),
    WEIXIN_USER("the registered user is a wechat user, so the function cannot be used", "26"),
    ERP_USER("the registered user is an ERP user, so the function cannot be used", "27"),
    EXIST_WORKNO("the workNo already exists", "28"),
    INVITATION_NOT_EXIST("the invitationInfo not exists", "29"),
    INVITATED_TENANTID_EMPTY("the invitedTenantId is empty", "30"),
    INSERT_FAILD("Insert Faild", "31"),
    UPDATE_FAILD("Update Failed", "32"),
    DELETE_FAILD("Delete Failed", "33"),
    PARAM_VERIFY("Param Verify Failed", "34"),
    QUERY_VERIFY("Query Failed", "35"),
    RESET_VERIFY("Reset Failed", "36"),
    REMIND_FAILD("Remind Failed", "37"),
    TENANT_EMPTY("the tenant is null", "38"),
    ORG_EXIST_EMPLOYEE("the org exist employee", "39"),
    USER_ITMS_NULL("the user not in aio system", "40"),
    TOKEN_NOT_EXIST("the token not exist", "41"),
    ORG_EXIST_CHILDORG("the org exist child org", "42"),
    USER_NOTIN_SUPPLIER("the user not in supplier", "43"),
    USER_NOTIN_TENANT("the user not in tenant", "44"),
    VERIfICODE_GET_ERROR("the verificationCode get failed", "45"),
    VERIfICODE_CHECK_ERROR("the account or verificationCode is error", "46"),
    PHONE_DUPLICATED("the telephone is already in use by another user", "47"),
    EDRSERVER_USED("the edr server is already used by org", "48"),
    EDRORG_USED("the org is already used by another customer", "49"),
    EXIST_SERVERCODE("the serverCode already exists", "50"),
    VISITOR_NOT_EXIST("visitor not exist", "51"),
    EXIST_PRODUCT("productCode is exist", "52"),
    NOT_EXIST_PRODUCT("productCode is not exist", "53"),
    EXIST_PRODUCT_RELATED_CONTRACTS("productCode has related contracts", "54"),
    PARAM_LACK("param \"%s\" lack %s", "55"),
    PARAM_INVALID("param \"%s\" invalid", "56"),
    SOMTHING_ALREADY_EXISTS("\"%s\" already exists", "57"),
    HEADER_IS_EMPTY("param header \"%s\" is empty.", "100"),
    PARAM_IS_EMPTY("param \"%s\" is empty.", "101"),
    CANCLE_FAILD("Cancle Failed", "102"),
    FAILD_DAY("Faild With Same Day", "103"),
    FAILD_DAY_TIME("Faild With Same Day AND Time", "104"),
    FAILD_TIME("Faild With Start Time >= End Time", "105"),
    TENANT_EXISTS("the tenant is exists", "106"),
    SET_VERIFY("Set Failed", "107"),
    USER_REGISTER_ERROR("user register error", "108"),
    TENANT_ITMS_NULL("the tenant not in aio system", "109"),
    TENANT_IAM_NULL("the tenant not in iam system", "110"),
    AUTH_PRODUCT_IS_NULL("the authorizedProductCode is null", "111"),
    AUTH_TENANT_IS_NULL("the authorizedTenant is null", "112"),
    USER_IAM_NULL("the user not in iam system", "113"),
    USER_HAS_NO_ATTENTION_AUTH("the user has no auth,so the tenant cannot be attention", "114"),
    CUSTOMER_SERVER_ORG_EXIST("the customer server org mapping is exists", "115"),
    CUSTOMER_ORG_CN_NAME_EXIST("the customer org cn name mapping is exists", "116"),
    OF_SERVER_USED("the openfind server is already used", "117"),
    EXIST_SERVER_URI("the server uri already exists", "118"),
    EXIST_SERVER_NAME("the server name already exists", "119"),
    TRIAL_APPLY_SOURCE_CODE_EXIST("the trial apply source code exists", "120"),
    USER_HAS_NO_ATTENTION_TENANT("the user has no attention the tenant", "121"),
    TENANT_NO_APP_AUTH("the tenant has no app auth", "122"),
    INDEX_CODE_EXIST("the index code exists", "123"),
    INDEX_NAME_EXIST("the index name exists", "124"),
    SCENE_CODE_EXIST("the scene code exists", "125"),
    SCENE_NAME_EXIST("the scene name exists", "126"),
    index_USED_BY_SCENE("the index used by scene", "127"),
    MAIL_IS_NOT_VALID("email is not valid", "131"),
    PHONE_IS_NOT_VALID("phone is not valid", "132"),
    CLOUD_GOOD_AIEOM_NOT_FOUND("cloud good aieom not found", "133"),
    SERVICE_CODE_NOT_EXIST("the serviceCode is not existed", "134"),
    SERVICE_CODE_IN_ESCLOUD("the serviceCode in escloud", "135"),
    USER_IN_AIO_TENANT("the user is in aio tenant", "136"),
    USER_NOT_IN_AIO_TENANT("the user is not in aio tenant", "137"),
    USER_NOT_IN_ES("the user is not in es", "138"),
    USER_IN_ES_TENANT("the user is in es tenant", "139"),
    USER_NOT_IN_ES_TENANT("the user is not in es tenant", "140"),
    USER_NOT_IN_ES_AIO("the user is not in es and aio", "141"),
    USER_IS_NEW("the user is new user", "142"),
    ID_DUPLICATED("id is already in use by another user", "149"),
    //体验
    UNIFIEDCODE_EMAIL_IS_EXIST("unifiedcode and email is exist", "143"),
    //角色
    USER_IS_NOT_AIEOMDEVMANAGER("the user is not aieomDevManager", "144"),
    LOGIN_TENANT_IS_NOT_SUPPLIER("the login tenant is not supplier", "145"),
    SERVICE_TENANT_CAN_NOT_LOGIN("the service tenant can not login", "146"),
    GUI_NUMBER_IS_ERROR("the gui number is error", "147"),
    INVITATION_IS_INVALID("the invitation is invalid", "148"),
    CUST_LEVEL_IS_EMPTY("the cust level is empty or inconformity", "149"),

    //cmdb
    //批量删除备份排程
    ASSET_IS_NOT_SELECTED("asset is not selected", "201"),
    ASSET_IS_NULL("asset is null", "202"),
    MODELCODE_IS_NULL("modelCode is null", "203"),
    REMOVE_FAILD("Remove Failed", "204"),
    FIELDCODE_IS_EXIST("FieldCode is exist", "205"),
    FIELDSET_IS_EXIST("FieldSet is exist", "206"),
    FIELDSETCODE_IS_EXIST("FieldSetCode is exist", "207"),
    FIELD_IS_EXIST_IN_FIELDSET_OR_MODEL("Field is exist in fieldset or model", "208"),
    MODEL_IN_MODELGROUP("Model is exist in modelGroup", "209"),
    MODELCODE_IS_EXIST("ModelCode is exist", "210"),
    MODERELATE_IS_EXIST("ModelRelate is exist", "211"),
    MODELFIELDMAPPING_IN_MODELFIELDGROUP("ModelFieldMapping is exist in modelFieldGroup", "212"),
    FIELD_IS_NULL("Field is null", "213"),
    FIELDSET_IS_NULL("FieldSet is null", "214"),
    FIELDSETCODE_IS_NULL("FieldSetCode is null", "215"),
    FIELDSETNAME_IS_NULL("FieldSetName is null", "216"),
    FILESETGROUPCODE_IS_NULL("FieldSetGroupCode is null", "217"),
    FILESETGROUPNAME_IS_NULL("FieldSetGroupName is null", "218"),
    FIELDCODE_IS_NULL("FieldCode is null", "219"),
    FIELDNAME_IS_NULL("FieldName is null", "220"),
    FIELDTYPE_IS_NULL("FieldName is null", "221"),
    SUMMARYMETHOD_IS_NULL("SummaryMethod is null", "222"),
    EDIT_REQUIRE_AUTOCOLLECTION_ALL_NULL("Edit Required Autocollection are null", "223"),
    CURRENT_YEARMONTH_MAINTENANCE_IS_EXIST("Current YearMonth Maintenance is exist", "224"),
    SAVE_CUSTOMER_FILE_FAIL("Save Customer File Fail", "225"),
    DELETE_CUSTOMER_FILE_FAIL("Delete Customer File Fail", "226"),
    SERVICECODE_IS_NULL("serviceCode is null", "227"),
    DEVICEID_IS_NULL("deviceId is null", "228"),
    MAINTENANCE_YEARMONTH_IS_NULL("maintenanceYearMonth is null", "229"),
    FILE_ID_IS_NULL("File ID is null", "230"),
    RELATION("Relation is null", "231"),
    RELATE_MODELCODE("Relate modelCode is null", "232"),
    RELATE_DEVICEID("Relate deviceId is null", "233"),
    RELATE_DEVICENAME("Relate deviceName is null", "234"),
    MAINTENANCECODE("maintenanceCode is null", "235"),
    DEVICENAME_IS_NULL("deviceName is null", "236"),
    ASSET_IS_EXIST("asset is exist", "237"),
    ASSET_BE_RELATED("asset is related", "238"),
    ASSET_CREATE_FAIL("asset create fail", "239"),
    ASSET_RELATE_CREATE_FAIL("asset relate create fail", "240"),
    SOURCE_ASSET_IS_NULL("source asset is null", "241"),
    MODELFIELDGROUPCODE_IS_EXIST("modelFieldGroupCode is exist", "242"),
    MODELRELATECODE_IS_EXIST("modelRelateCode is exist", "243"),
    MODELGROUP_FIELD_NOT_SET("modelGroup not set fields", "244"),
    MODELGROUP_FIELD_NOT_KEY("modelGroup fields not set key", "245"),
    FIELDSET_NOT_SET_KEY("fieldset not set key", "246"),
    SOURCEFIELDVALUE_IS_NULL("sourceFieldValue is null", "247"),
    FIELDSET_USED_IN_MODEL("FieldSet used in model", "248"),
    RELATEINSTANCE_IS_NULL("modelRelateInstances is null", "249"),
    MODELGROUPNAME_IS_EXIST("modelGroupName is exist", "250"),
    MODEL_RELATE_INSTANCE_SOME_FIELD_IS_NULL("modelRelateInstances some field is null", "251"),
    MONTH_REMARK_IS_EXIST("month remark is exist", "252"),
    ETL_IS_EXIST("etl storage is exist", "253"),
    //SIEM
    SIEM_QUERY_ERROR("siem query error", "-1"),
    SIEM_QUERY_PARAM_IS_NULL("siem query param is null", "-1"),

    //Clue
    CLUE_QUERY_IS_NULL("clue query is null", "-1"),
    CLUE_QUERY_ERROR("clue query error", "-1"),
    CLUE_QUERY_MAIL_IS_NULL("clue query mail is null", "-1"),
    CLUE_SAVE_ERROR("clue save error", "-1"),

    CLUE_UPDATE_ERROR("clue update error", "-1"),
    CLUE_UPDATE_CUSTOMER_INFO_ERROR("clue update Clue_Customer_Info error", "-1"),
    CLUE_DELETE_CUSTOMER_CONTACT_ERROR("clue delete Clue_Customer_Contact error", "-1"),
    CLUE_DELETE_ERROR("clue delete error", "-1"),
    CLUE_PARAM_IS_NULL("clue query param is null", "-1"),
    CLUE_CUSTOMERCODE_NOT_EXIST("clue customercode not exist", "-2"),

    //aiopsitms相关返回Code
    EID_DEVICEID_VERIFY_FAIL("Eid and deviceId verify failed! ", "9001"),
    EID_VERIFY_FAIL("Eid  verify failed! ", "9002"),

    //资产类别分类相关返回Code
    ASSET_CATEGORY_CLASSIFICATION_NAME_EXISTS("Asset category classification name already exists", "9003"),
    ASSET_CATEGORY_CLASSIFICATION_HAS_CATEGORIES("Asset category classification has related categories", "9004"),
    ASSET_CATEGORY_UNIQUE_CONSTRAINT_VIOLATION("Asset category unique constraint violation", "9005"),
    ASSET_CATEGORY_CLASSIFICATION_NOT_EDIT_DELETE("Asset category not edit delete", "9006"),

    //收集项相关
    COLLECTCODE_IS_NULL("collectCode is null", "301"),
    COLLECTNAME_IS_NULL("collectName is null", "302"),
    COLLECTCODE_IS_EXIST("collectCode is exist", "303"),
    COLLECTNAME_IS_EXIST("collectName is exist", "304"),
    COLLECT_REMOVE_CACHE_LIMIT("collect remove cache limit, reson:%s", "305"),

    //产品应用服务
    RELATIONCODE_IS_EXIST("relationCode is exist", "401"),
    PRODUCT_IS_EXIST("product is exist", "402"),
    APA_EXIST_COLLECT("apa has collect", "403"),
    APA_CONTAINS_THE_COLLECT("apa contains the collect", "404"),
    APA_EXIST_DEVICE("device relate apa", "405"),

    //预警项相关
    ACCID_IS_NULL("accid is null", "501"),
    WARNINGCODE_IS_NULL("warningCode is null", "502"),
    WARNINGCODE_IS_EXIST("warningCode is exist", "503"),
    WARNING_LEVEL_IS_EXIST("warning level is exist", "504"),
    WARNING_SCOPE_ID_NULL("warning scope is null", "505"),
    WARNING_DEVICE_ADCD_NULL("warning device and adcd is empty", "506"),

    //设备相关
    DEVICE_HAS_COLLECT("device add collect", "601"),
    DEVICE_NOT_FOUND("device %s not found", "611"),
    DEVICE_NOT_UNDEFINED("device %s not undefined", "612"),
    BATCH_FIX_DEVICE_DB_ID_ERROR("batch fix device db id error", "615"),
    INNER_RUNNER_UNSUPPORTED_CUSTOMIZE("inner runner unsupported customize", "616"),
    AIOPS_DEVICE_COLLECT_DETAIL_ID_NOT_EXIST("adcdId(or id) %s not exist", "621"),

    //拓扑图相关
    NOT_FOUND_APP_SERVICE_MODEL_CODE("app service model code not found", "651"),
    NOT_QUERY_APP_SERVICE("not query app service", "652"),
    NOT_FOUND_APP_SERVICE_INSTANCE("not found app service instance", "653"),
    NOT_FOUND_DEVICE_ID_BY_ADTM_ID("not found device id by adtm id", "654"),

    //案件
    REMIND_ISSUE_FAIL("remind issue fail", "701"),
    ISSUE_STATUS_IS_NOT_PROCRESS("issue status is not process", "702"),
    ISSUE_STATUS_IS_NOT_CLOSE("issue status is not close", "703"),
    ISSUE_IS_NOT_FOLLOW("issue is not follow", "704"),
    ISSUE_IS_NOT_REPLY("issue is not reply", "705"),
    DATA_IS_NOT_FIND("data is not find", "706"),
    ISSUE_EXIST_IN_CLASSIFICATION("issue exist in classification", "707"),
    CLASSIFICATION_IS_EXIST("classification is exist", "708"),
    FOLLOWUP_FAIL("followup fail","810"),
    FREQUENT_FOLLOWUP("frequent followup issue","811"),
    //日历
    SPECIAL_RULE_TYPE_IS_NULL("special rule type is null", "801"),

    //userv2
    USER_ES_NULL("the user not in es system", "901"),
    TENANT_ES_NULL("the tenant not in es system", "902"),
    USER_ES_INCOMPLETE("the es user info is incomplete", "903"),
    USER_PERSONAL_PHONE_INCOMPLETE("the es user personal phone is incomplete", "904"),
    DIGIWIN_USER_ES_NULL("the digiwin user not in es system", "905"),

    //日志记录相关
    DISPLAY_FIELD_NOT_EXIST("display field \"%s\" not exist in \"%s\" type", "1001"),
    CONDITION_FIELD_NOT_EXIST("condition field \"%s\" not exist in \"%s\" type", "1002"),

    //运维模组相关
    GET_MODULE_CLASS_ERROR("get module class error", "2001"),
    TENANT_MODULE_CONTRACT_ALREADY_EXIST("eid %s a valid tenant module contract, module code %s already exists", "2010"),
    TENANT_MODULE_CONTRACT_DETAIL_AVAILABLE_COUNT_INVALID("tenant module contract detail available count invalid", "2011"),
    TENANT_MODULE_CONTRACT_NOT_EXIST("eid %s has not valid tenant module contract, aiops item %s", "2020"),
    AIOPS_ITEM_NOT_EXIST("aiops item %s not exist", "2021"),
    TENANT_MODULE_CONTRACT_INVALID("eid %s invalid tenant module contract", "2022"),
    DEVICE_MAPPING_INSTANCE_ERROR_BY_SOURCE_DEVICE_ID_IS_EMPTY("device mapping instance error, source device id is empty", "2031"),
    DEVICE_ADD_INSTANCE_COLLECT_ERROR_BY_SOURCE_DEVICE_ID_IS_EMPTY("device add instance collect error, source device id is empty", "2041"),
    AIOPS_AUTH_OVERFLOW("eid %s auth overflow", "2051"),
    SNMP_INSTANCE_ALREADY_EXIST("snmp instance already exists", "2061"),
    HTTP_INSTANCE_ALREADY_EXIST("http instance already exists", "2063"),
    APP_AUTO_UPDATE_INSTANCE_ALREADY_EXIST("app_auto_update instance already exists", "2065"),
    AIOPS_INSTANCE_NOT_EXIST("aiops instance %s not exist", "2071"),
    DEVICE_MAPPING_INSTANCE_ERROR_NOT_DEVICE_TYPE("device mapping instance error, %s not device type", "2081"),
    NOT_ALLOW_DUPLICATE_MAPPING_ERROR("aiops item has been mapped by the device, cannot add aiops item", "2082"),
    TRANSFER_ERROR_BY_TARGET_DB_NOT_EXIST("transfer error by target local db setting not exist", "2091"),
    TRANSFER_ERROR_BY_NOT_SUPPORT("transfer error by not support aiops item type %s", "2092"),
    AUTHED_AIOPS_INSTANCE_CAN_NOT_DELETE("authed aiops instance can not delete", "2101"),
    RECALCULATE_TMCD_USED_COUNT_PART_FAILED("recalculate tmcd used count part failed", "2111"),
    EDR_INSTANCE_BATCH_SAVE_ERROR("aiops edr instance batch save error:%s", "2121"),
    EDR_AUTO_SET_EXIST("edr auto set is exists", "2122"),
    DATABASE_AUTO_SET_EXIST("database auto set is exists", "2123"),
    AUTO_SET_EXIST("auto set is exists", "2124"),
    AUTHORIZATION_HAS_EXPIRED("authorization has expired", "2125"),
    SMART_METER_INSTANCE_BATCH_SAVE_ERROR("aiops smart meter instance batch save error:%s", "2131"),

    EAI_CLOUD_INSTANCE_BATCH_SAVE_ERROR("aiops eai cloud instance batch save error:%s", "2132"),

    GET_TENANT_VALID_SAMCD_ID_LIST_ERROR("get tenant valid samcd id list aiopsItemList:%s error:%s", "2141"),

    //appupdate
    FILE_TYPE_IS_NULL("file type is null", "3001"),
    BASE_VERSION_IS_EXIST("base version is exist", "3002"),
    CURRENT_STATUS_CAN_NOT_DELETE("current status can not delete", "3003"),
    BASE_VERSION_HAS_RELEASE_UPDATE("base version has release update", "3004"),
    RELEASE_VERSION_IS_EXIST("release version is exist", "3005"),
    FILE_IS_EXISTS_IN_RELEASE("file is exists in release", "3006"),
    CURRENT_STATUS_CAN_NOT_HAND_STOP("current status can not hand stop", "3007"),
    NEW_VERSION_IS_NOT_EXIST("new version is not exist", "3008"),
    UPDATE_REQ_NO_NOT_EXIST("updateReqNo not exist", "3009"),

    //dmp
    DIMENSION_CODE_EXIST("the dimension code exists", "6001"),
    DIMENSION_USED_BY_TAG("the dimension used by tag", "6002"),
    DIMENSION_USED_BY_GROUP("the dimension used by group", "6003"),
    DIMENSION_ATTRIBUTE_MODEL_EXIST("the dimension attribute model exists", "6004"),
    DIMENSION_NAME_EXIST("the dimension name exists", "6005"),
    DIMENSION_ATTRIBUTE_CODE_EXIST("the dimension attribute code exists", "6006"),
    DIMENSION_ATTRIBUTE_NAME_EXIST("the dimension attribute name exists", "6007"),
    DIMENSION_ATTRIBUTE_MODEL_NOT_SET("the dimension attribute base model not set", "6008"),
    DIMENSION_USED_BY_EVENT("the dimension used by event", "6009"),
    DIMENSION_ATTRIBUTE_USED_BY_CONDITION("the dimension attribute used by condition", "6010"),
    EVENT_USED_BY_CONDITION("the event used by condition", "6011"),
    EVENT_TYPE_CODE_EXIST("the event type code exists", "6012"),
    EVENT_TYPE_NAME_EXIST("the event type name exists", "6013"),
    EVENT_TYPE_USED_BY_EVENT("the event type used by event", "6014"),
    EVENT_TYPE_HAS_CHILD("the event type has child ", "6015"),
    EVENT_CODE_EXIST("the event code exists", "6016"),
    EVENT_NAME_EXIST("the event name exists", "6017"),
    CATALOGUE_CODE_EXIST("the catalogue code exists", "6018"),
    CATALOGUE_NAME_EXIST("the catalogue name exists", "6019"),
    CATALOGUE_USED_BY_TAG("the catalogue used by tag", "6020"),
    CATALOGUE_USED_BY_GROUP("the catalogue used by group", "6021"),
    CATALOGUE_HAS_CHILD("the catalogue has child ", "6022"),
    TAG_CODE_EXIST("the tag code exists", "6023"),
    TAG_NAME_EXIST("the tag name exists", "6024"),
    TAG_USED_BY_CONDITION("the tag used by condition", "6025"),
    GROUP_CODE_EXIST("the group code exists", "6026"),
    GROUP_NAME_EXIST("the group name exists", "6027"),
    SCHEDULER_NOT_CREATE("the scheduler not create", "6028"),
    SCHEDULER_CREATE_FAILED("the scheduler create failed", "6029"),
    SCHEDULER_ONLINE_FAILED("the scheduler online failed", "6030"),
    SCHEDULER_OFFLINE_FAILED("the scheduler offline failed", "6031"),
    SCHEDULER_UPDATE_FAILED("the scheduler update failed", "6032"),
    SCHEDULER_DELETE_FAILED("the scheduler delete failed", "6033"),
    PLAN_TYPE_CODE_EXIST("the plan type code exists", "6034"),
    PLAN_TYPE_NAME_EXIST("the plan type name exists", "6035"),
    PLAN_TYPE_USED_BY_PLAN("the plan type used by plan", "6036"),
    PLAN_TYPE_HAS_CHILD("the plan type has child ", "6037"),
    PLAN_CODE_EXIST("the plan code exists", "6038"),
    PLAN_NAME_EXIST("the plan name exists", "6039"),
    PLAN_STATUS_NOT_FOUND("plan status not found ", "6060"),
    PLAN_STATUS_CHANGE_NOT_ALLOWED("plan status change not allowed ", "6061"),
    PLAN_TAG_NOT_FOUND("plan tag not found ", "6062"),

    //EAI-溫濕度平台
    TMP_RH_HOST_NO_DUPLICATE("host no is exists", "7001"),
    TMP_RH_HOST_NAME_DUPLICATE("host_name is exists", "7002"),
    TMP_RH_HOST_URL_DUPLICATE("host url is exists", "7003"),
    TMP_RH_PLATFORM_TEST_CONNECT_FAIL("test connect to tem/rh platform fail.", "7004"),
    TMP_RH_PLATFORM_IS_USE("tem/rh platform is using, can't delete.", "7005"),
    TMP_RH_DEVICE_SERIAL_DUPLICATE("device_serial is exists.", "7006"),
    TMP_RH_DEVICE_NAME_DUPLICATE("device_name is exists.", "7007"),
    TMP_RH_DEVICE_TEST_CONNECT_FAIL("test connect to device fail.", "7008"),
    TMP_RH_CUSTMOER_NO_CONTRACT("customer no contract.", "7009"),
    TMP_RH_ADD_VIRTUAL_DEVICE_FAIL("add virtual device fail.", "7010"),
    TMP_RH_AUTH_FAIL_IS_SCRAPPED("The device is scrapped, cann't auth or unAuth.", "7011"),
    TMP_RH_PLATFORM_EDIT_ADDRESS_FAIL("tem/rh platform is using, can't edit host address.", "7012"),
    TMP_RH_AUTH_FAIL_IS_DUPLICATE("tem/rh device duplicate auth", "7013"),
    TMP_RH_CUSTMOER_NO_CONTRACT_AUTHORIZATION("customer no enough contract authorization.", "7014"),
    TMP_RH_DEVICE_BE_USE_OR_INVALID("device already be use or invalid.", "7015"),

    //移转驻派员数据相关
    TRANSFER_ESCLIENT_SETTING_ERROR("transfer esclient setting error", "8001"),
    TRANSFER_ESCLIENT_AUTH_KEY_IS_EMPTY("transfer esclient auth key is empty", "8011"),
    TRANSFER_ESCLIENT_NO_UPGRADE("transfer esclient not upgrade", "8021"),
    TRANSFER_BASIC_DATA_ERROR_BY_DEVICE_EID_IS_EMPTY("transfer basic data error by device %s eid is empty", "8031"),
    TRANSFER_SNMP_ERROR("transfer snmp error %s", "8041"),
    TRANSFER_BACKUP_SOFTWARE_ERROR("transfer backup software error %s", "8042"),
    TRANSFER_PRODUCT_APP_ERROR("transfer product app data error: %s", "8051"),
    TRANSFER_ADD_WARNING_TASK_ERROR_BY_AEDM_LIST_EMPTY("transfer add warning task error by aedm list is empty", "8061"),

    //aiopskit安装更新相关
    TRANSFORM_KEY_NOT_EXIST("transform key \"%s\" not exist ", "9001"),
    NOT_FOUND_INSTALL_INFO("not found install info by platform:\"%s\" types %s", "9011"),

    //规则引擎相关
    RULE_ENGINE_NOT_FOUND_BY_ADCD_ID("rule engine not found by adcdId:%s", "10001"),
    RULE_SETTING_NOT_FOUND_BY_ACW_ID("rule setting not found by acwId:%s", "10002"),

    //事件相關
    EVENTID_AND_SERVERID_IS_EMPTY("eventId and serverId is empty","11001"),
    EVENTID_IS_EMPTY("eventId is empty","11002"),
    SERVERID_IS_EMPTY("serverId is empty","11003"),
    EVENT_RELEASE_FAIL("event release fail","11004"),
    SERVER_CONF_IS_EMPTY("server config is empty","11005"),
    EVENT_INFO_IS_EMPTY("event info is empty","11006"),
    SERVICECODE_IS_EMPTY("serviceCode is empty","11007"),
    ORGANIZATION_IS_EMPTY("organization is empty","11008"),
    FORTINET_API_ERROR_400("Fortinet api return error statusCode 400","11009"),
    FORTINET_API_ERROR_500("Fortinet api return error statusCode 500","11010"),

    //知识库机器人设定
    EXIST_ROBOT_BUSINESSNAME("exist robot businessname","12001"),
    SERVICE_REGION_PRODUCT_EXIST_ROBOT_WELCOME("serviceRegion and product exist robot welcome","12002"),
    SERVICE_REGION_PRODUCT_EXIST_ROBOT_TURN_REAL_PERSON("serviceRegion and product exist turn real person","12003"),
    SERVICE_REGION_PRODUCT_EXIST_ROBOT_KEYWORD("serviceRegion and product exist keyword","12004"),
    SERVICE_REGION_PRODUCT_EXIST_COEFFICIENT_LEVEL("serviceRegion and product exist coefficient level","12005"),
    RISK_IS_USED("ID is be used , not allow delete","13001"),
    RISK_IS_NEED("ID is required","13002"),
    RISK_DELETE_FAILED("Delete or Update failed ","13003"),
    RISK_IMPROVE_PLAN_PARAM_ERROR("Tracker and Id is required","13004"),
    RISK_ASSESSMENT_NEED("AssessmentId is required","13006"),
    RISK_NAME_DUPLICATE("Duplicate system name", "13007"),
    RISK_ASSESSMENT_SYSTEM_NEED("AssessmentId and system is required","13008"),
    RISK_ASSET_VALUE_ID_NEED("Asset value criterion id cannot be empty","13009"),
    RISK_SYSTEM_ID_NEED("Asset SystemId cannot be empty","13010"),
    RISK_ASSESSMENT_DUPLICATE("Duplicate asset criterion scores","13020" ),
    RISK_ASSET_CRITERION_EMPTY("Asset criterion empty","13021" ),
    RISK_CURR_PROJECT_DIFFERENT("Batch addition can only add content under one project","13029" ),
    RISK_CURR_PROJECT_NAME_EMPTY("Curr project name empty","13030" ),
    RISK_CURR_PROJECT_NAME_EXIST("Curr project name exist", "13031"),
    RISK_CURR_PROJECT_NAME_NOT_EXIST("Curr project name not exist", "13032"),
    RISK_CURR_PROJECT_NAME_BOTH_EXIST("Curr project name and old name both must exist", "13033"),
    RISK_CURR_PROJECT_NAME_ASSESSMENT_EXIST("AssessmentId and project name is required", "13034"),
    RISK_PROJECT_EXIST_CONTENT("There is content under the project that cannot be deleted", "13035"),
    RISK_CONTROLPLAN_TYPEID_CONTENT("ControlPlanTypeId is required", "13036"),
    RISK_CONTROLPLAN_TYPEID_DUPLICATE("Risk Improve project name is duplicate", "13037"),
    RISK_PROJECT_TYPEID_NEED("Risk assessment project risk assessment type ID is required", "13039"),
    RISK_PROJECT_TYPEID_NOT_EXIST("Risk assessment project risk assessment type ID is absent", "13040"),
    RISK_PROJECT_TYPEID_DUPLICATE("Risk assessment project type name is duplicate", "13041"),
    RISK_PROJECT_TYPE_NOT_ALLOW_DELETE("Risk assessment project type not allow delete", "13042"),
    RISK_CURR_PROJECT_EXIST_CONTROL("Curr project is used, not allow delete", "13043" ),
    RISK_CURR_PLAN_EXIST_CONTROL("Curr project plan is used, not allow delete", "13044" ),
    RISK_IMPROVE_PLAN_DUPLICATE("Improve plan duplicate","13045" ),
    RISK_EID_EMPTY("eid not empty","13046" ),
    RISK_PROJECT_TYPEID_IS_USED("type id is used","13047" ),
    RISK_ASSESSMENT_DISCLAIMER("disclaimer must agree","13048" ),
    RISK_IMPROVE_PLAN_STATUS_ERROR("plan status error", "13050"),
    RISK_IMPROVE_PLAN_REVIEW_MAIL_ERROR("review mail error", "13051"),
    RISK_STANDARD_ERROR("standard not allow update", "13052"),
    //13053 已经存在
    RISK_IMPROVE_PLAN_STATUS_NEED_DO("need do plan", "13054"),
    RISK_ASSESSMENT_NO_UD("assessment complete can not UD", "13055"),

    ISV_TENANT_IS_EMPTY("isv tenant id is empty", "14001"),
    ISVTENANT_AND_SUBMITDATERANGE_IS_EMPTY("isv tenant id and submitDate range is empty", "14002"),

    VULNERABILITY_ASSET_DUP("asset number or ip duplicate", "15001"),
    VULNERABILITY_PROJECT_ID_IS_NEED("project id is need", "15002"),
    VULNERABILITY_PROJECT_SUMMARY_CAN_NOT_EMPTY("project summary can not empty", "15003"),
    VULNERABILITY_PROJECT_SUMMARY_IS_EMPTY("project summary is empty", "15004"),
    VULNERABILITY_PROJECT_IS_EMPTY("project is empty", "15005"),

    VULNERABILITY_ASSET_ID_IS_NEED("asset id is need", "15013"),
    VULNERABILITY_ASSET_USED("asset is used ,can not delete", "15014"),
    VULNERABILITY_STOREHOUSE_USED("vulnerability is used ,can not delete", "15015"),
    VULNERABILITY_STOREHOUSE_NUMBER_IS_NEED("vulnerability number is need", "15016"),
    VULNERABILITY_STOREHOUSE_NUMBER_EXIST("vulnerability number exist can not add", "15025"),

    VULNERABILITY_ASSET_IP_DUP("asset ip duplicate", "15003"),
    VULNERABILITY_ASSET_IP_USED("asset ip is used ,can not delete", "15004"),
    VULNERABILITY_SCAN_URL("scanUrl can not empty", "15005"),
    VULNERABILITY_SCAN_DATA("scanData can not empty", "15006"),
    VULNERABILITY_LANGUAGE("language can not empty", "15007"),
    VULNERABILITY_REPORT_EXIST("report exist, can not delete", "15008"),
    VULNERABILITY_SCAN_DUPLICATE("asset ip or vulnerability number duplicate", "15009"),
    VULNERABILITY_REPORT_MAN_IS_NEED("report man is need", "15010"),
    VULNERABILITY_REPORT_DATE_IS_NEED("report date is need", "15011"),
    VULNERABILITY_PROJECT_NOT_EDIT("report exist, project can not edit","15012"),
    VULNERABILITY_SCAN_TEMPLATE_ERROR("template error", "15017"),
    VULNERABILITY_SCAN_IP_ERROR("Incorrect IP format", "15018"),
    VULNERABILITY_SCAN_FILE_ERROR("vulnerability scan file error", "15019"),
    VULNERABILITY_SCAN_LIST("vulnerability scan list is empty",  "15026"),
    VULNERABILITY_SCAN_LIST_VULNERABILITY("vulnerability scan vulnerability is empty",  "15027"),
    VULNERABILITY_PROJECT_ID_IS_EXIST("vulnerability project number is exist",  "15028"),
    VULNERABILITY_REPAIR_PLAN_NOT_EXIST("vulnerability repair plan is not exist",  "15029"),
    VULNERABILITY_REPAIR_PLAN_EXECUTOR_MAIL_NOT_EXIST("vulnerability repair plan executor mail not exist",  "15030"),
    VULNERABILITY_REPORT_SUMMARY_IS_NOT_EXIST("vulnerability report summary is not exist",  "15031"),
    VULNERABILITY_REPORT_EID_IS_NOT_EXIST("vulnerability report eid is not exist",  "15032"),
    VULNERABILITY_REPORT_ID_IS_NOT_EXIST("vulnerability report id is not exist",  "15033"),
    VULNERABILITY_REPAIR_RECORD_CAN_NOT_ADD("vulnerability repair record can not add",  "15031"),
    VULNERABILITY_SCAN_DATA_IS_BEING_UPLOADED("Vulnerability scan data is being uploaded","15035"),
    VULNERABILITY_SCAN_SUGGEST_PARAM_IS_NOT_EXIST("Vulnerability scan suggest param is not exist","15036"),
    VULNERABILITY_SCAN_SUGGEST_UPDATE_ERROR("Vulnerability scan suggest update error","15037"),


    DATA_EXAMINATION_TEMPLATE_ERROR("template error",  "16001"),
    DATA_EXAMINATION_GENERATE_REPORT_ERROR("generate report error",  "16002"),
    DATA_EXAMINATION_REPORT_PARAM_ERROR(" report param  error",  "16003"),
    DATA_EXAMINATION_CUSTOMER_PARAM_ERROR(" generate report param  error",  "16004"),
    DATA_EXAMINATION_SAVE_REPORT_PARAM_ERROR(" save report template param  error",  "16005"),
    DATA_EXAMINATION_MENU_REPORT_TEMPLATE_ERROR(" menu report template  error",  "16006"),
    DATA_EXAMINATION_MENU_ID_ERROR(" menu id error",  "16007"),
    DATA_EXAMINATION_MENU_ID_WARNING(" menu id error",  "16008"),
    DATA_EXAMINATION_REPORT_GENERATE_FAIL("report generate fail",  "16009"),
    DATA_EXAMINATION_MENU_DUP("menu dup",  "16010"),
    DATA_EXAMINATION_NOT_CUSTOM_MENU("only custom menus can be deleted",  "16015"),
    DATA_EXAMINATION_EXIST_SUB("existing submenus",  "16016"),
    DATA_EXAMINATION_PANEL_BOUND("panel bound",  "16017"),
    DATA_EXAMINATION_REPORT_USE("the menu is reported to be used",  "16018"),
    DATA_EXAMINATION_MENU_OR_REPORT_NUMBER_DUP("menuId or reportNumber dup",  "16011"),
    DATA_EXAMINATION_REPORT_NAME_DUP("report name dup",  "16012"),
    DATA_EXAMINATION_TBB_TOKEN_ERROR("tbb token get error",  "16013"),

    DATA_EXAMINATION_REPORT_MENU_STATUS_ERROR("menu status error",  "16014"),

    UPGRADE_FAIL("upgrade fail", "16101"),
    UPGRADE_ALREADY_EXISTS("upgrade already exists", "16102"),

    SQL_SYNTAX_ERROR("sql syntax error",  "17001"),
    SQL_LIMIT_ERROR("sql limit error",  "17002"),

    //aioitms
    STATUS_CHANGE_NOT_ALLOWED("status change not allowed", "20000"),
    STATUS_NOT_FOUND("status not found ", "20001"),

    MAIL_IS_REGISTERED("RiskRegisterInvitation : mail is registered","14015"),
    PASSWORD_IS_EMPTY("RiskRegisterInvitation : password is empty", "14018"),

    //EDR Customer
    ORG_NOT_EXIST("org not exist", "20101"),
    ORG_INVALID_FAILED("Org invalidation failed", "20102"),
    EDR_EVENTKB_DETAIL_COUNT_ERROR("edr eventKb detail count error", "20103"),
    EDR_EVENTKB_DETAIL_COUNT_TIMEOUT("edr eventKb detail count timeout", "20104"),
    EDR_EVENTKB_DETAIL_LIST_ERROR("edr eventKb detail list error", "20105"),
    EDR_EVENTKB_DETAIL_LIST_TIMEOUT("edr eventKb detail list timeout", "20106"),

    //EDR Customer V2
    EDR_SELECT_ORG_FAILED("select org failed", "-1"),
    EDR_SAVE_ORG_FAILED("save org failed", "-1"),
    EDR_UPDATE_ORG_FAILED_EID_ALREADY_IN_USE("update org failed eid already in use", "0000000"),
    EDR_UPDATE_ORG_FAILED_CUSTOMER_FULL_NAME_ALREADY_IN_USE("update customerFullName failed eid already in use", "149"),
    EDR_UPDATE_FAILED_ORG_IS_DEPRECATED("update failed org is deprecated", "000001"),
    EDR_UPDATE_ORG_FAILED("save org failed", "4040400"),
    EDR_THREAT_LIST_IS_EMPTY("threat list is empty", "000002"),
    EDR_SAVE_THREATS_FAILED("save threats failed", "-1"),
    EDR_SELECT_THREAT_LIST_FAILED("select threat list failed", "-1"),
    EDR_SELECT_THREAT_DETAIL_LIST_FAILED("select threat detail list failed", "-1"),
    EDR_SELECT_THREAT_CLASSIFICATION_COUNT_FAILED("select threat classification count failed", "-1"),
    EDR_DELETE_ORG_FAILED("delete org failed", "-1"),
    EDR_DEPRECATED_ORG_FAILED("deprecated org failed", "-1"),
    EDR_GET_AGENT_FAILED("get agent failed", "-1"),
    EDR_GET_AGENT_OS_TOTAL_AMOUNT_FAILED("get agent os total amount failed", "-1"),
    EDR_AGENT_WHITELIST_LIST_IS_EMPTY("agent whitelist is empty", "000002"),
    EDR_SAVE_AGENT_WHITELIST_FAILED("save agent whitelist failed", "-1"),
    EDR_SAVE_AGENT_FAILED("save agent failed", "-1"),
    EDR_UPDATE_AGENT_FAILED("update agent failed", "4040001"),
    EDR_GET_CONNECTION_TOTAL_AMOUNT_FAILED("get connection total amount failed", "-1"),
    EDR_GET_AGENT_HEALTH_TOTAL_AMOUNT_FAILED("get agent health total amount failed", "-1"),
    EDR_GET_AGENT_CONNECTION_TOTAL_AMOUNT_FAILED("get agent health total amount failed", "-1"),
    EDR_AGENT_ENABLE_OR_DISABLE_FAILED("agent enable or disable failed", "-1"),
    EDR_AGENT_IS_OFFLINE("agent is offline", "4040400"),
    EDR_AGENT_ENABLE_FAILED("agent enable failed", "-1"),
    EDR_AGENT_DISABLE_FAILED("agent disable failed", "-1"),
    EDR_GET_GROUP_LIST_FAILED("get group list failed", "-1"),
    EDR_MOVE_GROUP_FAILED("move group failed", "-1"),
    EDR_MOVE_GROUP_FAILED_AGENTMODED_COUNT_0("move group failed agentsMoved count 0", "-1"),
    EDR_RELEASE_CANCEL_FAILED("release cancel failed", "11004"),
    EDR_APPLICATION_LIST_IS_EMPTY("application list is empty", "000002"),
    EDR_APPLICATION_ENDPOINT_LIST_FAILED("get application endpoint list failed", "-1"),
    EDR_AGENT_REMOVE_FAILED("agent remove failed", "-1"),
    EDR_GET_AGENT_OS_LIST_FAILED("get agent os list failed", "-1"),
    EDR_SELECT_ORG_AUTHORIZATION_INFO_FAILED("select org authorization info failed", "-1"),
    EDR_AGENT_IS_ONLINE("agent is online", "-1"),
    EDR_AGENT_REMOVE_SYNC_DATA_FAILED("agent remove sync data failed", "-1"),
    EDR_EXPORT_FAILED_WITH_MESSAGE("Export failed: %s", "10001"),
    EDR_SYNC_EVENT_FAILED("Sync event failed", "-1"),
    EDR_SYNC_AGENT_FAILED("sync agent failed", "-1"),
    EDR_AGENT_CHECK_STATUS_FAILED("agent check status failed", "20010"),
    EDR_SCAN_FAILED("scan failed", "-1"),
    EDR_SCAN_ABORT_FAILED("scan failed", "-1"),
    EDR_IDENTITY_SEND_FAILED("identity send failed", "-1"),
    EDR_UNINSTALL_FAILED("uninstall failed", "-1"),
    EDR_CANCEL_UNINSTALL_FAILED("cancel uninstall failed", "-1"),
    EDR_UNINSTALL_IDENTITY_TIMEOUT("uninstall identity timeout", "30001"),
    EDR_TASKS_EXEC_ERROR("edr tasks exec error", "30002"),
    EDR_TASKS_SAVE_ERROR("edr tasks save error", "30003"),
    EDR_SCANPLAN_SAVE_ERROR("edr scanplan save error", "30004"),
    EDR_AGENT_IS_NOT_PENDING_UNINSTALL("agent is not pending uninstall", "30005"),
    EDR_AGENT_FOUND_ZERO("agent found zero", "-1"),
    EDR_UNINSTALL_IDENTITY_RECORDS_FAILED("edr uninstall identity records failed", "30006"),
    EDR_TASKS_GET_CYCLE_ERROR("edr get task cycle failed", "-1"),
    EDR_TASKS_GET_LIST_ERROR("edr get task list failed", "-1"),
    EDR_TASKS_GET_LOG_ERROR("edr get task log failed", "-1"),
    EDR_AGENT_SAVE_ISWARNING_ERROR("edr save iswarning failed", "-1"),
    EDR_AGENT_GET_ISWARNING_ERROR("edr get iswarning failed", "-1"),
    EDR_AGENT_ACCOUNT_ID_CANNOT_CROSS_SOURCES("edr agent account id cannot cross sources", "30007"),

    //Operate Log
    OPERATE_LOG_SAVE_ERROR("operate log save error","-1"),
    OPERATION_LOG_EXPORT_ERROR("operate log export error", "-2"),

    //Oracle V3
    GET_SCHEMA_PASSWORD_EXPIRY_DATE_FAILED("get schema password expiry date failed", "-1"),
    GET_ORACLE_EXP_DASHBOARD_FAILED("get oracle exp dashBoard failed", "-1"),
    GET_ORACLE_SQL_TOP10_FAILED("get oracle sql top10 failed", "-1"),

    //group profiles
    PROFILES_NAME_EXISTS("the customer base analysis name already exists","10001"),
    PROFILES_NUM_EXISTS("the customer base analysis number already exists","10002"),
    PROFILES_CATEGORY_CHILD_NODES_EXISTS("exist child nodes cannot be deleted directly.","10003"),
    PROFILES_ANALYSIS_DATA_EXISTS("group analysis data exists","10004"),
    PROFILES_CATEGORY_NAME_EXISTS("category name already exists","10001"),
    PROFILES_CATEGORY_NUM_EXISTS("category number already exists","10002"),
    PROFILES_TEMPLATE_CATEGORY_NAME_EXISTS("Category name already exists","10001"),
    PROFILES_TEMPLATE_CATEGORY_NUM_EXISTS("Category number already exists","10002"),
    PROFILES_TEMPLATE_NAME_EXISTS("duplicate template name","10001"),
    PROFILES_TEMPLATE_NUM_EXISTS("duplicate template number","10002"),

    //prediction_model
    PREDICTION_TARGET_MODEL_NAME_EXISTS("prediction model name already exists","10001"),
    PREDICTION_TARGET_MODEL_CODE_EXISTS("prediction model code already exists","10002"),
    PREDICTION_AI_SCENE_ID_EXISTS("ai scene id already exists","10003"),
    PREDICTION_AI_SCENE_NAME_EXISTS("ai scene name already exists","10004"),
    PREDICTION_AI_ASYNC_INTERFACE("ai async interface is empty","10005"),
    PREDICTION_MODEL_USED_BY_ACW("the prediction model used by acw","10002"),
    PREDICTION_CREATE_PROCESS_ERROR("create process error","11001"),
    PREDICTION_UPDATE_PROCESS_ERROR("update process error","11002"),
    PREDICTION_ONLINE_PROCESS_ERROR("online process error","11003"),
    PREDICTION_OFFLINE_PROCESS_ERROR("offline process error","11004"),
    PREDICTION_DELETE_PROCESS_ERROR("delete process error","11005"),


    //Asset Maintenance
    REPORT_ASSET_MAINTENANCE_READ_LOG_DATA_IS_EMPTY("report asset maintenance read log data is empty","20200"),
    REPORT_ASSET_MAINTENANCE_PARAM_IS_EMPTY("report asset maintenance param is empty","20201"),
    REPORT_ASSET_MAINTENANCE_SAVE_ERROR("report asset maintenance save error","20202"),
    MAINTENANCE_ASSET_LIST_PARAM_IS_EMPTY("maintenance asset list param is empty","20203"),
    MAINTENANCE_ASSET_LIST_SAVE_ERROR("maintenance asset list save error","20204"),
    MAINTENANCE_ASSET_LIST_IS_EMPTY("maintenance asset list is empty","20205"),
    REPORT_ASSET_MAINTENANCE_READ_LOG_SAVE_ERROR("report asset maintenance read log save error","20206"),

    TP_INSTANCE_ALREADY_EXIST("tp instance already exists", "21001"),
    TP_TENANT_NOT_EXIST("tp tenant not exists", "21002"),
    TP_TENANT_DOWNLOAD_NOT_EXIST("tp tenant download url not exists", "21003"),

    CUSTOMER_CONTACT_SAVE_ERROR("clue Contact save error", "22000"),
    CLUE_CONTENT_NOT_FILL("clue content not fill", "22001"),
    CLUE_CONTACTS_CONFLICT("clue contacts conflict", "22002"),
    CLUE_CONFLICT("clue conflict", "22003"),

    COLLECT_CONFIG_IDS_IS_NULL("collect config ids is null", "22101"),
    COLLECT_CONFIG_NOT_FOUND("collect config not found", "22102"),
    WORK_NO_NOT_EXIST("the workNo not exists", "22103"),
    ACP_API_ERROR("acp api error", "22104"),
    CDP_MODEL_ERROR("cdp model error", "22105"),
    CDP_CLUE_DATA_UPLOAD_ERROR("cdp clue upload error", "22106"),
    CDP_CLUE_CUSTOMER_CODE_ERROR("cdp clue customer code error", "22107"),
    CDP_CLUE_DATA_PRIMARY_DUP_ERROR("clue primary key dup", "22108"),
    UPGRADE_ACC_TO_DCDP_FAIL("upgrade acc to dcdp fail", "22501"),
    UPGRADE_ACC_ALREADY_EXISTS("upgrade acc already exists", "22502"),
    LOW_ROLE_PRIORITY("Low role priority error", "22600"),
    AIOPS_EXAM_ERROR("Aiops exam error", "22700"),
    AIOPS_EXAM_STATUS_ERROR("Aiops exam status error", "22701"),
    AIOPS_EXAM_TAG_DATA_ERROR("Aiops exam tag data error", "22702"),
    AIOPS_EXAM_INDEX_IS_EXISTS("Aiops exam index is exists", "22703"),
    AIOPS_EXAM_ITEM_TYPE_PARENT_IS_EXISTS("Aiops exam item type parent is exists", "22704"),
    AIOPS_EXAM_ITEM_AIOPS_ITEM_IS_REPEAT("Aiops exam item aiopsItem is repeat", "22705"),
    AIOPS_EXAM_NULL("Aiops exam code or Name is empty", "22706"),
    AIOPS_EXAM_CODE_IS_REPEAT("Aiops exam code is repeat", "22707"),
    AIOPS_EXAM_INDEX_TYPE_CODE_IS_REPEAT("Aiops exam index type code is repeat", "22708"),
    AIOPS_EXAM_INDEX_CODE_IS_REPEAT("Aiops exam index code is repeat", "22709"),
    CODE_IS_NULL("code is null", "22710"),
    AIOPS_EXAM_ITEM_MAP_IS_EXISTS("Aiops exam item map is exists", "22711"),
    AIOPS_EXAM_NAME_IS_REPEAT("Aiops exam Name is repeat", "22712"),
    AIOPS_EXAM_INDEX_NAME_IS_REPEAT("Aiops exam index name is repeat", "22713"),
    NAME_IS_NULL("name is null", "22714"),
    AIOPS_EXAM_ITEM_TYPE_CODE_IS_REPEAT("Aiops exam item type code is repeat", "22715"),
    AIOPS_EXAM_ITEM_TYPE_NAME_IS_REPEAT("Aiops exam item type name is repeat", "22716"),
    AIOPS_EXAM_ITEM_INDEX_IS_EMPTY("Aiops exam item index is empty", "22717"),
    AIOPS_EXAM_REPORT_DELETE_ERROR("Aiops exam report delete error", "22718"),
    AIOPS_EXAM_RECORD_SAVE_ERROR("Aiops exam record save error", "22719"),
    AIOPS_EXAM_RECORD_TITLE_DUPLICATE_ERROR("Aiops exam record title duplicate error", "22720"),
    AIOPS_EXAM_INSTANCE_USED("Aiops exam instance used", "22720"),
    AIOPS_EXAM_NETWORK_SECURITY_DATA_ERROR("Aiops exam network security data error", "22721"),
    AIOPS_CDP_GROUP_IS_ERROR("Aiops cdp group gaarId error", "23100"),
    AUTO_DATA_SOME_FIELDS_FAILED("Automatic retrieval of some fields failed", "77100"),
    AUTO_DATA_SOME_ALREADY_RUN("already run auto update, plz wait 10 min.", "77101"),
    AUTO_DATA_NOT_FOUND_USER_ID("not found userId", "77102"),

    //環評表 EnvironmentAssess
    GET_ENVIRONMENT_ASSESS_ERROR("get environment assess error", "-1"),

    // AuthorizationCollected
    UPLOAD_AUTHORIZATION_COLLECTED_ERROR("upload authorization collected error", "-1"),
    ADD_MODULE_MAPPING_ERROR("add module mapping error", "-1"),
    DELETE_MODULE_MAPPING_ERROR("delete module mapping error", "-1"),

    // BCP問卷
    GET_BCP_QUESTIONNAIRE_ERROR("get bcp questionnaire error", "80001"),
    GET_BCP_RESULT_ERROR("get bcp result error", "80002"),
    GET_BCP_RESULT_LIST_ERROR("get bcp result list error", "80003"),
    GET_CUSTOMER_DATA_ERROR("get customer data error", "80004"),
    SAVE_BCP_RESULT_ERROR("save bcp result error", "80005"),
    ;

    // 成员变量
    private String msg;
    private String code;

    // 构造方法
    ResponseCode(String msg, String code) {
        this.msg = msg;
        this.code = code;
    }

    /**
     * 获取描述信息
     *
     * @return 描述信息
     */
    public String getMsg() {
        return msg;
    }

    /**
     * 获取格式化后描述
     *
     * @param params 格式化参数
     * @return 描述
     */
    public String getDynamicMsg(Object... params) {
        return String.format(this.msg, params);
    }

    /**
     * 获取代号
     *
     * @return 代号
     */
    public String getCode() {
        return code;
    }

    //覆盖方法
    @Override
    public String toString() {
        return this.code;
    }

    public boolean isSameCode(String code) {
        return this.code.equals(code);
    }
}