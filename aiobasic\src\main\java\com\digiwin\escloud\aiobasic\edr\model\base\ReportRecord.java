package com.digiwin.escloud.aiobasic.edr.model.base;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Data
public class ReportRecord {
    private Long eid;
    private long id;
    private long sid;
    private String serviceCode;
    private String customerName;
    private int reportGenerateType;
    private String orgCnNames;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date reportStartTime;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date reportEndTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date reportGenerateTime;
    private String userId;
    private String userName;
    private String orgIds;

    private String sendTime;
    private List<Map<String, Object>> receiversList;
    private String receivers;

    private int type;//报告类型 1 例行维护报告 | 2 整体事件报告 | 3 风险事件报告 | 4 年度事件报告 |5 例行维护报告的过期报告 | 6 弱點掃描報告
    // 7 資料庫評估報告 | 8 資料庫巡檢報告 | 13 資安事件報告 | 14 其它報告
    private String typeName;
    private String url;
    private String fileName;//文件名
    private String description;
    private String sendMethod;//报告寄送方式 0 立即寄送 | 1 预约寄送
    private String appointmentTime;//预约寄送时间
    private String sendStatus; // 0 未发送 | 1 已发送 | 2 已预约 ; S1EDR 0 評估中 | 1 生成中 | 2 已發送 | 3 已預約
    private boolean auto = false; // true 自动产生的数据 | false 手动（通过页面）产生的数据

    private ReportRecordSendLog sendLog;

    private Long reportId; //附件報告Id
    private String modelVersion = "1.0"; //模組 NULL、空值、1.0 => EDR 1.0 | 2.0=> EDR 2.0 | ORACLE | MSSQL

    private List<ReportFile> files;
    private List<ReportFile> removeFiles;

    private String dbType = "Oracle";
    private Integer reportType = 1; // 1 評估報告 | 2 巡檢報告

    private String dbId; // 資料庫巡檢報告使用
    private String deviceId;

    public static ReportRecord objectToReportRecord(Object object) {
        if (Objects.nonNull(object) && object instanceof ReportRecord) {
            return (ReportRecord) object;
        }
        return null;
    }
}