package com.digiwin.escloud.aioitms.report.handler.impl;

import com.digiwin.escloud.aiobasic.operatelog.model.OperateLogSaveParam;
import com.digiwin.escloud.aiobasic.operatelog.model.OperateLogType;
import com.digiwin.escloud.aioitms.report.handler.DbReportParamHandler;
import com.digiwin.escloud.aioitms.report.model.db.DbReportRecord;
import com.digiwin.escloud.common.util.DateUtil;
import com.digiwin.escloud.common.util.IntegerUtil;
import com.digiwin.escloud.common.util.StringUtil;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

import static com.digiwin.escloud.common.util.DateUtil.DATE_TIME_FORMATTER;

@Component
public class GenerateReportParamHandler implements DbReportParamHandler {
    @Override
    public boolean isSupport(OperateLogType op) {
        return OperateLogType.Generate.equals(op);
    }

    @Override
    public void handle(List<Object> param, OperateLogSaveParam logParam) {
        DbReportRecord dto = DbReportRecord.objectToDbReportRecord(param.get(0));

        // 檢查參數不為空及是否為版更評估(reportType = 4)
        if (Objects.isNull(dto) || IntegerUtil.isEmpty(dto.getReportType()) || dto.getReportType() != 4) {
            return;
        }

        logParam.setEid(StringUtil.toString(dto.getEid()));
        logParam.setProcessUserId(dto.getUserId());
        logParam.setProcessUserName(dto.getUserName());
        logParam.setReportId(StringUtil.toString(dto.getId()));
        logParam.setOperateType(OperateLogType.Generate.getCode());
        logParam.setStartTime(DateUtil.getNowFormatString(DATE_TIME_FORMATTER));
    }
}
